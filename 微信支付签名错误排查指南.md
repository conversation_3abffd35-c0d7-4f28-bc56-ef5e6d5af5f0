# 微信支付签名错误排查指南

## 问题描述
微信公众号支付下单失败，报错：`返回代码：FAIL，返回信息：签名错误，请检查后再试`

## 错误分析
根据错误堆栈，问题出现在 `WxPayServiceImpl.checkResult()` 方法的签名验证环节。

## 排查步骤

### 1. 检查微信支付配置参数

需要验证以下配置项是否正确：

```properties
# 微信公众号AppID
payment.wx.appid=你的AppID

# 微信支付商户号
payment.wx.mchid=你的商户号

# 微信支付API密钥（32位）
payment.wx.apikey=你的API密钥

# 微信公众号AppSecret
payment.wx.appsecret=你的AppSecret

# 微信支付网关地址
payment.wx.getway=https://api.mch.weixin.qq.com

# 回调通知地址
payment.wx.notify.order=你的回调地址
```

### 2. 验证配置值的正确性

#### 2.1 AppID验证
- 登录微信公众平台：https://mp.weixin.qq.com
- 在"开发"->"基本配置"中查看AppID
- 确保配置文件中的AppID与公众号AppID完全一致

#### 2.2 商户号验证
- 登录微信支付商户平台：https://pay.weixin.qq.com
- 在"账户中心"->"商户信息"中查看商户号
- 确保配置文件中的商户号正确

#### 2.3 API密钥验证
- 在微信支付商户平台的"账户中心"->"API安全"中设置API密钥
- API密钥必须是32位字符串
- 确保配置文件中的API密钥与商户平台设置的一致

#### 2.4 AppSecret验证
- 在微信公众平台的"开发"->"基本配置"中查看AppSecret
- 确保配置文件中的AppSecret正确

#### 2.5 **重要：AppID授权验证**
**这是最容易被忽略但非常关键的步骤！**

##### 在微信支付商户平台进行授权：
1. 登录微信支付商户平台：https://pay.weixin.qq.com
2. 进入"产品中心" -> "开发配置"
3. 在"公众号支付"或"JSAPI支付"配置中：
   - **添加公众号AppID**：将您的公众号AppID添加到授权列表
   - **设置支付授权目录**：配置支付页面的域名路径
   - 例如：`https://yourdomain.com/pay/`

##### 在微信公众平台确认关联：
1. 登录微信公众平台：https://mp.weixin.qq.com
2. 进入"微信支付"功能
3. 确认商户号已经正确关联
4. 检查支付配置状态是否为"已开通"

##### 常见授权问题：
- **AppID未授权**：公众号AppID没有在商户平台添加授权
- **授权目录不匹配**：支付页面域名不在授权目录范围内
- **商户号关联错误**：公众号关联了错误的商户号
- **授权状态异常**：授权已过期或被撤销

### 3. 检查环境配置

#### 3.1 确认当前环境
```bash
# 查看当前激活的配置环境
grep "spring.profiles.active" pay-core/src/main/resources/bootstrap.properties
```

#### 3.2 验证配置中心
- 当前使用配置中心：`http://configserver.duibatest.com.cn`
- 确认配置中心中的微信支付配置是否正确
- 检查是否存在配置缓存问题

### 4. 代码层面检查

#### 4.1 签名算法确认
从代码可以看出，当前使用MD5签名算法：
```java
public static final String SIGN_TYPE = "MD5";
```

#### 4.2 签名参数检查
确认以下参数在签名时是否正确：
- appid
- mch_id  
- nonce_str
- body
- out_trade_no
- total_fee
- spbill_create_ip
- notify_url
- trade_type
- openid (公众号支付必需)

### 5. 调试建议

#### 5.1 开启签名日志
在 `WxPaySignUtils.createSign()` 方法中已有日志输出：
```java
LOGGER.info("签名类型为：{}  签名源串是 {}", signType, signStringBuilder.toString());
```

查看日志中的签名源串，确认参数是否正确。

#### 5.2 对比签名
1. 记录本地生成的签名
2. 记录微信返回的签名  
3. 对比两者是否一致

### 6. 常见解决方案

#### 6.1 **优先检查：AppID授权问题**
这是导致签名错误最常见的原因：

1. **立即检查授权状态**：
   ```bash
   # 检查当前使用的AppID和商户号
   grep -r "payment.wx.appid" pay-core/src/main/resources/
   grep -r "payment.wx.mchid" pay-core/src/main/resources/
   ```

2. **验证授权配置**：
   - 登录微信支付商户平台
   - 检查"产品中心"->"开发配置"->"公众号支付"
   - 确认AppID是否在授权列表中
   - 确认支付授权目录是否正确

3. **如果AppID未授权，立即添加**：
   - 在商户平台添加公众号AppID
   - 设置正确的支付授权目录
   - 等待配置生效（通常5-10分钟）

#### 6.2 重新设置API密钥
1. 登录微信支付商户平台
2. 进入"账户中心"->"API安全"
3. 重新设置API密钥（32位随机字符串）
4. 更新配置文件中的API密钥
5. 重启应用

#### 6.3 检查字符编码
确保所有配置参数使用UTF-8编码，避免特殊字符导致的签名错误。

#### 6.4 验证时间同步
确保服务器时间与标准时间同步，时间偏差可能导致签名验证失败。

### 7. 测试验证

#### 7.1 使用微信支付官方工具
使用微信支付官方提供的签名验证工具验证签名算法是否正确。

#### 7.2 简化测试
创建最简单的支付请求进行测试，逐步排查问题。

## 紧急处理建议

如果是生产环境问题，建议：
1. 立即检查配置中心的微信支付配置
2. 确认最近是否有配置变更
3. 如有必要，回滚到上一个可用的配置版本
4. 联系微信支付技术支持确认商户状态

## 预防措施

1. 建立配置变更审核流程
2. 定期备份重要配置
3. 建立监控告警机制
4. 定期验证支付功能
