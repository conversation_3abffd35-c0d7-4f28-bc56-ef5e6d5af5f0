package cn.com.duiba.paycenter.remoteservice.duibaaccount;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.duibaaccount.AppAccountDetailPageDto;
import cn.com.duiba.paycenter.dto.duibaaccount.AppMarkAccountDetailDto;
import cn.com.duiba.paycenter.params.duibaaccount.AppAccountDetailQryParams;

import java.util.List;

/**
 * 开发者标记账户明细remote
 * author z<PERSON><PERSON><PERSON>
 * date 2018-11-27-14:18
 */
@AdvancedFeignClient
public interface RemoteAppMarkAccountDetailService {

    /**
     * 分页查询
     * @param params
     * @return
     */
    List<AppAccountDetailPageDto> find4Page(AppAccountDetailQryParams params);

    /**
     * 分页查询总数
     * @param params
     * @return
     */
    Long count4page(AppAccountDetailQryParams params);

    /**
     * 根据主键和开发者信息查询
     * @param id
     * @return
     */
    AppMarkAccountDetailDto findByIdAndAppId(Long id, Long appId);

    /**
     * 根据业务单号获取
     * @param relationId
     * @param appId
     * @return
     */
    List<AppMarkAccountDetailDto> findByRelationAndAppId(String relationId, List<Integer> relationTypes, Long appId);

    List<AppAccountDetailPageDto> find4Export(AppAccountDetailQryParams params);
}
