package cn.com.duiba.paycenter.constant;

import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * @author: pengyi
 * @description:
 * @date: 2022/11/25 下午5:07
 */
public enum WxCouponSubjectEnum {

    FJ_DB_WXLJJ("fjdbwxljj", "福建兑吧微信立减金"),
    DB_FJ_WXLJJ("dbfjwxljj", "兑吧福建微信立减金"),
    DB_FJ_WXLJJ_002("dbfjwxljj002", "兑吧福建微信立减金002"),
    ANHUI_DB_WXLJJ_001("ahdbwxljj001", "安徽兑吧微信立减金001"),

    ANHUI_DB_WXLJJ_002("ahdbwxljj002", "安徽兑吧微信立减金002"),
    ANHUI_DB_WXLJJ_003("ahdbwxljj003", "安徽兑吧微信立减金003"),
    ANHUI_DB_WXLJJ_004("ahdbwxljj004", "安徽兑吧微信立减金004"),
    ANHUI_DB_WXLJJ_005("ahdbwxljj005", "安徽兑吧微信立减金005"),
    ANHUI_DB_WXLJJ_006("ahdbwxljj006", "安徽兑吧微信立减金006"),
    ANHUI_DB_WXLJJ_007("ahdbwxljj007", "安徽兑吧微信立减金007"),
    ANHUI_DB_WXLJJ_008("ahdbwxljj008", "安徽兑吧微信立减金008"),
    SHANDONG_DT_WXLJJ_001("sddtwxljj001", "山东兑推微信立减金001"),
    SHANDONG_DT_WXLJJ_002("sddtwxljj002", "山东兑推微信立减金002"),
    ANHUI_ZY_WXLJJ_001("ahzywxljj001", "安徽知予微信立减金001"),
    FUJIAN_DA_WXLJJ_001("fjdawxljj001", "福建兑啊微信立减金001"),
    FUJIAN_DA_WXLJJ_002("fjdawxljj002", "福建兑啊微信立减金002"),
    HZDBSY_DB_WXLJJ_001("hzdbsywxljj001", "杭州兑吧沭阳分微信立减金001"),
    JIANGSU_DB_WXLJJ_001("jsdbwxljj001", "江苏兑吧微信立减金001"),
    ;

    private String code;
    private String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static WxCouponSubjectEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        WxCouponSubjectEnum[] values = WxCouponSubjectEnum.values();
        for (WxCouponSubjectEnum value : values) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }

    WxCouponSubjectEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
