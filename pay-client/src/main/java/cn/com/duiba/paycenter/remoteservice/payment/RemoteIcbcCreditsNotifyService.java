package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeNotifyParams;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeNotifyResp;

/**
 * <AUTHOR>
 */
@AdvancedFeignClient
public interface RemoteIcbcCreditsNotifyService {
    /**
     * 处理支付通知
     *
     * @param params 请求数据
     * @return 响应
     * @throws Exception -
     */
    IcbcCreditsChargeNotifyResp handleChargeNotify(IcbcCreditsChargeNotifyParams params) throws Exception;
}
