package cn.com.duiba.paycenter.dto.payment.refund.icbc.elife;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 江苏工行-e生活支付-退款request
 *
 * <AUTHOR>
 * @date 2024/5/11 10:11 上午
 */
public class IcbcELifeRefundRequest implements Serializable {

    private static final long serialVersionUID = 2952438902892462723L;

    /**
     * 退款原因
     */
    @NotNull(message = "退款原因不能为空")
    private String rejectReason;

    /**
     * 退款金额(单位：分)
     */
    @NotNull(message = "金额不能为空")
    @Min(value = 1, message = "金额必须大于1分")
    private Integer rejectAmt;

    /**
     * 星速台下单支付订单号
     */
    @NotNull(message = "星速台订单号不能为空")
    private String bizOrderNo;

    /**
     * 交易时间戳
     */
    @NotNull(message = "交易时间不能为空")
    private Long transactionTimestamp;

    /**
     * 退款订单号
     */
    @NotNull(message = "退款订单号不能为空")
    private String refundOrderNo;

    /**
     * 商户代码
     * 唯一确定一个商户的代码，由商户在工行开户时，由工行告知商户。
     */
    private String merID;

    public Integer getRejectAmt() {
        return rejectAmt;
    }

    public void setRejectAmt(Integer rejectAmt) {
        this.rejectAmt = rejectAmt;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public Long getTransactionTimestamp() {
        return transactionTimestamp;
    }

    public void setTransactionTimestamp(Long transactionTimestamp) {
        this.transactionTimestamp = transactionTimestamp;
    }

    public String getRefundOrderNo() {
        return refundOrderNo;
    }

    public void setRefundOrderNo(String refundOrderNo) {
        this.refundOrderNo = refundOrderNo;
    }

    public String getMerID() {
        return merID;
    }

    public void setMerID(String merID) {
        this.merID = merID;
    }
}
