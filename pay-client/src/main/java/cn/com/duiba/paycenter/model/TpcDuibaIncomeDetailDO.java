package cn.com.duiba.paycenter.model;

import java.io.Serializable;
import java.util.Date;

public class TpcDuibaIncomeDetailDO implements Serializable{

	private static final long serialVersionUID = 1L;

	private Long id;
	private String relationType;
	private Long relationId;
	private String actionType;
	private Long changeMoney;
	private String changeKind;
	private Long beforeBalance;
	private Long afterBalance;
	private String remark;
	private Date gmtCreate;
	private Date gmtModified;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getRelationType() {
		return relationType;
	}
	public void setRelationType(String relationType) {
		this.relationType = relationType;
	}
	public Long getRelationId() {
		return relationId;
	}
	public void setRelationId(Long relationId) {
		this.relationId = relationId;
	}
	public String getActionType() {
		return actionType;
	}
	public void setActionType(String actionType) {
		this.actionType = actionType;
	}
	public Long getChangeMoney() {
		return changeMoney;
	}
	public void setChangeMoney(Long changeMoney) {
		this.changeMoney = changeMoney;
	}
	public String getChangeKind() {
		return changeKind;
	}
	public void setChangeKind(String changeKind) {
		this.changeKind = changeKind;
	}
	public Long getBeforeBalance() {
		return beforeBalance;
	}
	public void setBeforeBalance(Long beforeBalance) {
		this.beforeBalance = beforeBalance;
	}
	public Long getAfterBalance() {
		return afterBalance;
	}
	public void setAfterBalance(Long afterBalance) {
		this.afterBalance = afterBalance;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public Date getGmtCreate() {
		return gmtCreate;
	}
	public void setGmtCreate(Date gmtCreate) {
		this.gmtCreate = gmtCreate;
	}
	public Date getGmtModified() {
		return gmtModified;
	}
	public void setGmtModified(Date gmtModified) {
		this.gmtModified = gmtModified;
	}
}
