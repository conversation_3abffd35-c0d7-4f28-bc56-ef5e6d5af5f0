package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.boc.BocChargeNotifyResponse;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/03/13
 */
@AdvancedFeignClient
public interface RemoteBocNotifyService {
    /**
     * 中国支付通知
     * @param map params
     * @return response
     * @throws BizException exception
     */
    BocChargeNotifyResponse orderNotify(Map<String, String> map) throws BizException;
}
