package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponNotifyUrlSetResponse;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponUsedMessage;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponUsedNotifyRequest;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponUserFlowDetail;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponUserFlowResponse;
import cn.com.duiba.paycenter.enums.WxCouponSubjectEnum;

import java.util.List;

/**
 * Description:
 * <p>
 * date: 2022/10/12 10:39 上午
 *
 * <AUTHOR>
 */
@AdvancedFeignClient
public interface RemoteWxCouponNotifyService {

    /**
     * 微信立减金核销通知地址设置
     * @return 修改时间、被修改后的地址
     * @throws BizException 业务异常
     */
    WxCouponNotifyUrlSetResponse setNotifyUrl(WxCouponSubjectEnum wxCouponSubjectEnum) throws BizException;

    /**
     * 微信立减金核销前置通知处理（验证签、解密）
     * @param notifyRequest 通知请求
     * @return 通知的全部数据
     */
    WxCouponUsedMessage usedNotifyPreHandle(WxCouponUsedNotifyRequest notifyRequest, WxCouponSubjectEnum wxCouponSubjectEnum);

    /**
     * 微信立减金批次核销明细下载链接
     * @param stockId
     * @param wxCouponSubjectEnum
     * @return
     */
    void getWxCouponUserFlow(String stockId, WxCouponSubjectEnum wxCouponSubjectEnum) throws BizException;
}
