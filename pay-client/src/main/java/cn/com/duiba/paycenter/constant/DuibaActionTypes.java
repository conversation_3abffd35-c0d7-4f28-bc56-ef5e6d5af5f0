package cn.com.duiba.paycenter.constant;
/**
 * 兑吧账号资金的动作
 * <AUTHOR>
 *
 */
public class DuibaActionTypes {

	public static final String DATOrdersPay="pay";
	public static final String DATOrdersBackpay="backpay";
	
	public static final String DATManualChargeCharge="manual-charge";
	public static final String DATManualChargeReduce="manula-reduce";

	public static final String DATAccountDrawPay = "draw-pay";
	public static final String DATAccountDrawBack = "draw-back";

	/**
	 * 主订单的动作类型
	 * <AUTHOR>
	 *
	 */
	public enum DOrdersActionType{
		PayOrder(DATOrdersPay),PaybackOrder(DATOrdersBackpay);
		private String key;
		private DOrdersActionType(String key){
			this.key=key;
		}
		public String getKey(){
			return key;
		}
	}
	/**
	 * 人工充值的动作类型
	 * <AUTHOR>
	 *
	 */
	public enum DManualChargeActionType{
		Charge(DATManualChargeCharge),Reduce(DATManualChargeReduce);
		
		private String key;
		private DManualChargeActionType(String key){
			this.key=key;
		}
		public String getKey(){
			return key;
		}
	}

	/**
	 * 全局账户提现 的动作类型
	 * <AUTHOR>
	 *
	 */
	public enum DAccountDrawActionType{
		PAY(DATAccountDrawPay),BACK(DATAccountDrawBack);

		private String key;
		DAccountDrawActionType(String key){
			this.key=key;
		}
		public String getKey(){
			return key;
		}
	}
}
