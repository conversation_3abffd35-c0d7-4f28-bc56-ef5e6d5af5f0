package cn.com.duiba.paycenter.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Security;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class WeiboRsaUtil {
    private static String paramsToStr(Map<String, String> params) {
        StringBuilder param = new StringBuilder();
        List<String> keys = new ArrayList(params.keySet());
        // key排序
        Collections.sort(keys);
        int index = 0;
        Iterator<String> iterator = keys.iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            String value = params.get(key);
            param.append(index == 0 ? "" : "&").append(key).append("=").append(value);
            index++;
        }
        return param.toString();
    }
    public static void main(String[] args){

        String rsaPrivate = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAOdx2hsT7G2qMpn/G0qYYROqnKQwr/sUXktFd9rb1cDdilxjciVqRFcuvJFTE2q+7h4gO2oK8VbTAH2zU/p75N9ZUGmohEUEJ3g0jfUDovmRQBdBz2yG9ZJC4y+Dx1GX0RWdk5zjOBinWcFHEQ894iL+b0GtVWAiVNuLERUKnmkRAgMBAAECgYAN51AD6ml8mQnbKHEaRRcn2Er6rhYUTj/v46d0lVo88NIcBLlZuA2M8zjOvh/ffMLPOn53O1sOLTTNWFWGKaUxDGs624Ec0j4EgyH8FIHcnAJcaEXEkivU3MMEGTHIRmIoikUk8ZB5D4dS2naFBKSwbBSg8dX08mOQEfHdrtVJQQJBAPg1xg8DpEELlhMqU/kFXN94B1JvIFDWlwT7J1jexJRQYadmwg4Ci67nQt6eWoiBk9mIhstVZW1RZQqyuu9FeFkCQQDutWDm8rliGngAUxlegCYIp9kkv+Sya8XRhOETDWOVIO1qQ4jKlUpivJixDkcAkGmj494H2bG6P1H9agP4K995AkEAlbKo30lM+UXpn2l598h6OPUFRXxsOxURRBnNo1XHrcPFAdeB1j1GzZgAxo8u/wFXkG09DslUQNhEXm1pPQHjEQJALwPybgpokPX9pLK5UG+VDidhEWfqN2oasEWnvwToFDtEB6X9j0kQOZpDyLuzKF1JMUp0NL+fzbsi/vKejYa2oQJAQvMlQWsh8AMACIOFw2vfoszQXxW/dicxBi8UBPkV0t81GyQDl8NAkr6c9uoUJ65i2VGiPGXt7vvhlsT3gA/tng==";
        Map<String,String> hashMap1 = new HashMap<>();
        hashMap1.put("out_verify_id","2023042019005935");
        hashMap1.put("channel","wechatsft");
        hashMap1.put("apply_type","0");

        String sign = WeiboRsaUtil.sign(paramsToStr(hashMap1), rsaPrivate, "Utf-8", "RSA");
        System.out.println(sign);

        String rsaPublic = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDncdobE+xtqjKZ/xtKmGETqpykMK/7FF5LRXfa29XA3YpcY3IlakRXLryRUxNqvu4eIDtqCvFW0wB9s1P6e+TfWVBpqIRFBCd4NI31A6L5kUAXQc9shvWSQuMvg8dRl9EVnZOc4zgYp1nBRxEPPeIi/m9BrVVgIlTbixEVCp5pEQIDAQAB";
        Map<String,String> hashMap = new HashMap<>();
        hashMap.put("out_verify_id","2023042019005935");
        hashMap.put("channel","wechatsft");
        hashMap.put("apply_type","0");



        boolean boo = WeiboRsaUtil.verify(paramsToStr(hashMap), sign, rsaPublic, "Utf-8", "RSA");
        System.out.println(boo);
    }


    private WeiboRsaUtil() {
    }


    public static final String SIGN_ALGORITHMS = "SHA1WithRSA";

    public static final String SIGN_ALGORITHMS256 = "SHA256WithRSA";

    private static final int MAX_ENCRYPT_BLOCK = 117;
    private static final int MAX_DECRYPT_BLOCK = 128;

    static {
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    public static String sign(String content, String privateKey, String charset, String signType) {
        try {
            if (content == null) {
                return null;
            }
            PKCS8EncodedKeySpec privatePKCS8 = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey key = keyFactory.generatePrivate(privatePKCS8);

            java.security.Signature signature = null;
            if ("RSA256".equals(signType)) {
                signature = java.security.Signature.getInstance(SIGN_ALGORITHMS256);
            } else {
                signature = java.security.Signature.getInstance(SIGN_ALGORITHMS);
            }
            signature.initSign(key);
            signature.update(content.getBytes(charset));

            return new String(Base64.getEncoder().encode(signature.sign()));
        } catch (Exception e) {
            log.error("sign error:", e);
        }
        return null;
    }

    public static String sign(Map<String, Object> contentMap, String privateKey, String charset, String signType) {
        return sign(getSignFormatContent(contentMap), privateKey, charset, signType);
    }

    public static String sign(Map<String, Object> contentMap, String privateKey) {
        String signFormatContent = getSignFormatContent(contentMap);
        log.info("signFormatContent:{}", signFormatContent);
        return sign(signFormatContent, privateKey, "utf-8", "rsa");
    }

    public static boolean verify(Map<String, Object> contentMap, String sign, String publicKey, String signType) {
        return verify(contentMap, sign, publicKey, "utf-8", signType);
    }

    public static boolean verify(Map<String, Object> contentMap, String sign, String publicKey) {
        return verify(contentMap, sign, publicKey, "utf-8", "rsa");
    }

    public static boolean verify(Map<String, Object> contentMap, String sign, String publicKey, String charset, String signType) {
        return verify(getSignFormatContent(contentMap), sign, publicKey, charset, signType);
    }

    public static boolean verify(String content, String sign, String publicKey, String charset, String signType) {
        try {
            if (StringUtils.isBlank(content) || StringUtils.isBlank(sign) || StringUtils.isBlank(publicKey) || StringUtils.isBlank(charset) || StringUtils.isBlank(signType)) {
                return false;
            }
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] encodedKey = Base64.getDecoder().decode(publicKey);
            PublicKey key = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));

            java.security.Signature signature = null;
            if ("RSA256".equals(signType)) {
                signature = java.security.Signature.getInstance(SIGN_ALGORITHMS256);
            } else {
                signature = java.security.Signature.getInstance(SIGN_ALGORITHMS);
            }

            signature.initVerify(key);
            signature.update(content.getBytes(charset));
            return signature.verify(Base64.getDecoder().decode(sign));
        } catch (Exception e) {
            log.error("verify error:", e);
        }

        return false;
    }

    public static PrivateKey getPrivateKey(String key) {
        try {
            byte[] keys = Base64.getDecoder().decode(key);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keys);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePrivate(keySpec);
        } catch (Exception ex) {
            log.error("getPrivateKey error:", ex);
        }
        return null;
    }

    public static PublicKey getPublicKey(String key) {
        try {
            byte[] keys = Base64.getDecoder().decode(key);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keys);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePublic(keySpec);
        } catch (Exception ex) {
            log.error("getPublicKey error:", ex);
        }
        return null;
    }

    public static String encryptByPublicKey(String data, String publicKey) {
        try (ByteArrayOutputStream bops = new ByteArrayOutputStream();) {
            PublicKey key = getPublicKey(publicKey);
            // RSA加密
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, key);

            byte[] bytes = data.getBytes();
            int inputLen = bytes.length;
            int offLen = 0;
            int i = 0;
            while (inputLen - offLen > 0) {
                byte[] cache;
                if (inputLen - offLen > MAX_ENCRYPT_BLOCK) {
                    cache = cipher.doFinal(bytes, offLen, MAX_ENCRYPT_BLOCK);
                } else {
                    cache = cipher.doFinal(bytes, offLen, inputLen - offLen);
                }
                bops.write(cache);
                i++;
                offLen = MAX_ENCRYPT_BLOCK * i;
            }
            return new String(Base64.getEncoder().encode(bops.toByteArray()));
        } catch (Exception e) {
            log.error("encryptByPublicKeyExp", e);
            return null;
        }
    }

    public static String decryptByPrivateKey(String data, String privateKey) {
        try (ByteArrayOutputStream bops = new ByteArrayOutputStream();) {
            PrivateKey key = getPrivateKey(privateKey);
            // RSA解密
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, key);
            byte[] bytes = Base64.getDecoder().decode(data);
            int inputLen = bytes.length;
            int offLen = 0;
            int i = 0;
            while (inputLen - offLen > 0) {
                byte[] cache;
                if (inputLen - offLen > MAX_DECRYPT_BLOCK) {
                    cache = cipher.doFinal(bytes, offLen, MAX_DECRYPT_BLOCK);
                } else {
                    cache = cipher.doFinal(bytes, offLen, inputLen - offLen);
                }
                bops.write(cache);
                i++;
                offLen = MAX_DECRYPT_BLOCK * i;
            }
            return new String(bops.toByteArray(), "utf-8");
        } catch (Exception e) {
            log.error("decryptByPrivateKeyExp", e);
            return null;
        }
    }

    private static String getSignFormatContent(Map<String, Object> params) {
        if (params == null) {
            return null;
        } else {
            StringBuilder content = new StringBuilder();
            ArrayList<String> keys = new ArrayList<>(params.keySet());
            Collections.sort(keys);
            boolean firstKey = true;
            for (int i = 0; i < keys.size(); ++i) {
                String key = keys.get(i);
                Object value = params.get(key);
                if (Objects.isNull(value)) {
                    continue;
                }
                if (firstKey) {
                    content.append(key + "=" + value);
                    firstKey = false;
                } else {
                    content.append("&" + key + "=" + value);
                }
            }
            return content.toString();
        }
    }
}
