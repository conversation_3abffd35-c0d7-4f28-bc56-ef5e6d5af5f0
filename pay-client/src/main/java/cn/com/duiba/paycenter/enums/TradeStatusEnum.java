package cn.com.duiba.paycenter.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by Administrator on 2018/5/14.
 */
public enum TradeStatusEnum {
    /**
     * 订单状态
     *
     */
    TRADE_CREATE("1","订单创建"),
    TRADE_WAIT_PAY("2","订单等待支付"),
    TRADE_FINISHED("3","订单完成");

    private static final Map<String, TradeStatusEnum> enumMap = new HashMap<>();
    static{
        for(TradeStatusEnum TradeStatusEnum : values()){
            enumMap.put(TradeStatusEnum.getCode(), TradeStatusEnum);
        }
    }

    public static TradeStatusEnum getByCode(String code) {
        TradeStatusEnum TradeStatusEnum = enumMap.get(code);
        return TradeStatusEnum;
    }
    private String code;
    private String desc;

    TradeStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
