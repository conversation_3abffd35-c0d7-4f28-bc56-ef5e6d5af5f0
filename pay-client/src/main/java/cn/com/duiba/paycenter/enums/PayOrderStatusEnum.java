package cn.com.duiba.paycenter.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付订单状态
 * Created by xiaoxuda on 2017/11/1.
 */
public enum PayOrderStatusEnum {
    INIT("初始化"),
    PROCESSING("处理中"),
    SUCCESS("成功"),
    FAIL("失败"),
    EXCEPTION("异常");


    private static final Map<String, PayOrderStatusEnum> ENUM_MAP = new HashMap<>();

    static{
        for(PayOrderStatusEnum tmp : values()){
            ENUM_MAP.put(tmp.name(), tmp);
        }
    }

    public static PayOrderStatusEnum getByType(String type) {
        return ENUM_MAP.get(type);
    }

    private String desc;

    PayOrderStatusEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
