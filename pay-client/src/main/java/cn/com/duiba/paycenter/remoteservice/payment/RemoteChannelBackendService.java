package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.ChannelDto;
import cn.com.duiba.paycenter.dto.payment.config.AlipayConfigDto;
import cn.com.duiba.paycenter.dto.payment.config.WxPayConfigDto;

/**
 * <AUTHOR>
 * @date 2018/11/06
 */
@AdvancedFeignClient
public interface RemoteChannelBackendService {
    /**
     * 更新微信渠道配置
     * id 必传
     *
     * @param wxPayConfigDto 需要更新的渠道配置
     * @return 更新是否成功
     * @throws BizException bizException
     */
    boolean updateWxPayConfig(WxPayConfigDto wxPayConfigDto) throws BizException;

    /**
     * 更新微信渠道配置
     * id 必传
     *
     * @param alipayConfigDto 需要更新的配置
     * @return 更新是否成功
     * @throws BizException bizException
     */
    boolean updateAlipayConfig(AlipayConfigDto alipayConfigDto) throws BizException;

    /**
     * 获取该app的渠道配置列表(微信配置和支付宝配置)
     *
     * @param appId appId
     * @return ChannelDto
     * @throws BizException bizException
     */
    ChannelDto listByAppId(Long appId) throws BizException;
}
