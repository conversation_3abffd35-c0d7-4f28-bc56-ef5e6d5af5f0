package cn.com.duiba.paycenter.client;

import cn.com.duiba.paycenter.params.WithdrawParams;
import cn.com.duiba.paycenter.remoteservice.RemoteWithdrawService;
import cn.com.duiba.paycenter.result.WithdrawResult;
import cn.com.duiba.paycenter.util.SignUtil;

import java.util.HashMap;
import java.util.Map;

public class WithdrawServiceClient {
    private RemoteWithdrawService remoteWithdrawService;

    public RpcResult<WithdrawResult> userWithdrawCashApply(WithdrawParams requestParams) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("developerId", requestParams.getDeveloperId().toString());
            params.put("withdrawId", requestParams.getBizType().name() + "_" + requestParams.getBizId());
            params.put("money", String.valueOf(requestParams.getMoney()));
            params.put("consumerId", requestParams.getConsumerId().toString());
            String sign = SignUtil.sign(params);

            return remoteWithdrawService.userWithdrawCashApply(requestParams, sign);

        } catch (Exception e) {
            return new RpcResult<>(e);
        }
    }

    public RpcResult<WithdrawResult> userWithdrawCashPaybackApply(WithdrawParams requestParams) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("developerId", requestParams.getDeveloperId().toString());
            params.put("withdrawId", requestParams.getBizType().name() + "_" + requestParams.getBizId());
            params.put("money", String.valueOf(requestParams.getMoney()));
            params.put("consumerId", requestParams.getConsumerId().toString());
            String sign = SignUtil.sign(params);

            return remoteWithdrawService.userWithdrawCashPaybackApply(requestParams, sign);

        } catch (Exception e) {
            return new RpcResult<>(e);
        }
    }

    public RemoteWithdrawService getRemoteWithdrawService() {
        return remoteWithdrawService;
    }

    public void setRemoteWithdrawService(RemoteWithdrawService remoteWithdrawService) {
        this.remoteWithdrawService = remoteWithdrawService;
    }
}
