package cn.com.duiba.paycenter.dto.payment.charge.lshm;

import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeResponse;
import com.alibaba.fastjson.JSONObject;
import net.sf.json.JSON;

public class LshmChargeResponse extends BaseChargeResponse {
    private String payParams;

    /**
     * 门店编码
     * 必填项
     */
    private String storeCode;



    public String getPayParams() {
        return payParams;
    }

    public void setPayParams(String payParams) {
        this.payParams = payParams;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    @Override
    public String getExtra() {
        return new JSONObject()
                .fluentPut("storeCode",storeCode)
                .fluentPut("payParams",payParams)
                .toJSONString();
    }


}
