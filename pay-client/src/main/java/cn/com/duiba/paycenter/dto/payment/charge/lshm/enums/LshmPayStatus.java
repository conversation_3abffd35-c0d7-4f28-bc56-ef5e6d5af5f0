package cn.com.duiba.paycenter.dto.payment.charge.lshm.enums;

/**
 /**
 * 支付状态
 * 001 - 支付中
 * 003 - 待支付
 * 004 - 支付成功
 * FAIL - 失败
 */

public enum LshmPayStatus {
    PAYING("001", "支付中"),
    WAITING("003", "待支付"),
    SUCCESS("004", "支付成功"),
    FAIL("FAIL", "失败");

    private String status;

    private String desc;

    LshmPayStatus(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
