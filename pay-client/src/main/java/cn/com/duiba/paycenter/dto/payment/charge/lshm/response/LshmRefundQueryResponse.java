package cn.com.duiba.paycenter.dto.payment.charge.lshm.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 退款结果数据传输对象 (DTO)
 */
@Data
public class LshmRefundQueryResponse implements Serializable {

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 退款流水号
     */
    private String refundTradeNo;

    /**
     * 退款商户流水号
     */
    private String refundOrderNo;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款状态
     * 001 - 退款中
     * 002 - 退款成功
     * 003 - 退款失败
     */
    private String refundStatus;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 支付订单号
     */
    private String tradeNo;
}