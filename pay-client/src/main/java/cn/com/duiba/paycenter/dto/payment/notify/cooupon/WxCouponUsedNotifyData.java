package cn.com.duiba.paycenter.dto.payment.notify.cooupon;


import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Description:
 * <p>
 * date: 2022/10/9 3:11 下午
 *
 * <AUTHOR>
 */
public class WxCouponUsedNotifyData implements Serializable {
    private static final long serialVersionUID = -120020352129619718L;

    /**
     * 通知Id
     */
    @JSONField(name = "id")
    private String id;
    /**
     * 通知创建的时间
     */
    @JSONField(name = "create_time")
    private String createTime;
    /**
     * 通知的类型
     * 代金券用券回调通知的类型为COUPON.USE。
     */
    @JSONField(name = "event_type")
    private String eventType;
    /**
     * 通知的资源数据类型
     * 代金券用券回调通知为encrypt-resource。
     */
    @JSONField(name = "resource_type")
    private String resourceType;
    /**
     * 回调摘要
     */
    @JSONField(name = "summary")
    private String summary;
    /**
     * 通知资源数据。
     */
    @JSONField(name = "resource")
    private WxCouponUsedNotifyResource resource;

    public String getId() {
        return id;
    }

    public WxCouponUsedNotifyData setId(String id) {
        this.id = id;
        return this;
    }

    public String getCreateTime() {
        return createTime;
    }

    public WxCouponUsedNotifyData setCreateTime(String createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getEventType() {
        return eventType;
    }

    public WxCouponUsedNotifyData setEventType(String eventType) {
        this.eventType = eventType;
        return this;
    }

    public String getResourceType() {
        return resourceType;
    }

    public WxCouponUsedNotifyData setResourceType(String resourceType) {
        this.resourceType = resourceType;
        return this;
    }

    public String getSummary() {
        return summary;
    }

    public WxCouponUsedNotifyData setSummary(String summary) {
        this.summary = summary;
        return this;
    }

    public WxCouponUsedNotifyResource getResource() {
        return resource;
    }

    public WxCouponUsedNotifyData setResource(WxCouponUsedNotifyResource resource) {
        this.resource = resource;
        return this;
    }
}
