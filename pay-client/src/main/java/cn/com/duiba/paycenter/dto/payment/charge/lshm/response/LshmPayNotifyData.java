package cn.com.duiba.paycenter.dto.payment.charge.lshm.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 支付结果数据传输对象 (DTO)
 */
public class LshmPayNotifyData implements Serializable {

    /**
     * 支付中心订单号
     */
    private String tradeNo;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 商家请求流水号
     */
    private String merchantOrderNo;

    /**
     * 订单金额
     */
    private BigDecimal payAmount;

    /**
     * 支付金额
     */
    private BigDecimal actualPayAmount;

    /**
     * 支付方式
     */
    private String payMethod;

    /**
     * 支付状态
     * 01：待支付
     * 00：支付成功
     * 02：支付失败
     * 03：支付中
     */
    private String payStatus;

    /**
     * 支付时间
     */
    private Long payTime;

    /**
     * 备注1
     */
    private String remark1;

    /**
     * 备注2
     */
    private String remark2;

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getMerchantOrderNo() {
        return merchantOrderNo;
    }

    public void setMerchantOrderNo(String merchantOrderNo) {
        this.merchantOrderNo = merchantOrderNo;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public BigDecimal getActualPayAmount() {
        return actualPayAmount;
    }

    public void setActualPayAmount(BigDecimal actualPayAmount) {
        this.actualPayAmount = actualPayAmount;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public Long getPayTime() {
        return payTime;
    }

    public void setPayTime(Long payTime) {
        this.payTime = payTime;
    }

    public String getRemark1() {
        return remark1;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1;
    }

    public String getRemark2() {
        return remark2;
    }

    public void setRemark2(String remark2) {
        this.remark2 = remark2;
    }
}