package cn.com.duiba.paycenter.dto.payment.charge;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/12/11
 */
public class BaseChargeNotifyResponse implements Serializable {
    private static final long serialVersionUID = -8506398809317599844L;
    /**
     * 流程是否成功
     */
    private boolean success;
    /**
     * 用户支付是否成功
     */
    private boolean chargeSuccess;

    /**
     * 用户退款是否成功
     */
    private boolean refundSuccess;
    /**
     * 支付流水号
     */
    private String chargeOrderNo;
    /**
     * 第三方支付支付流水号
     */
    private String transactionNo;

    private Integer bizType;
    private String bizOrderNo;


    public boolean isRefundSuccess() {
        return refundSuccess;
    }

    public void setRefundSuccess(boolean refundSuccess) {
        this.refundSuccess = refundSuccess;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public boolean isChargeSuccess() {
        return chargeSuccess;
    }

    public void setChargeSuccess(boolean chargeSuccess) {
        this.chargeSuccess = chargeSuccess;
    }

    public String getChargeOrderNo() {
        return chargeOrderNo;
    }

    public void setChargeOrderNo(String chargeOrderNo) {
        this.chargeOrderNo = chargeOrderNo;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }
}
