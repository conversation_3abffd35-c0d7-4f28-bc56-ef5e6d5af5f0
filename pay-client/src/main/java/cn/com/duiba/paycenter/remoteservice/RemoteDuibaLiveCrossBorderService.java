package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.params.crossBorder.CancelOrderParam;
import cn.com.duiba.paycenter.params.crossBorder.CommitOrderParam;
import cn.com.duiba.paycenter.params.crossBorder.CommitPayOrderParam;

/**
 * 兑吧直播跨境调用相关接口
 * <AUTHOR>
 * @Date 2022/1/13 4:46 下午
 */
@AdvancedFeignClient
public interface RemoteDuibaLiveCrossBorderService {

    /**
     * 申请提交订单
     * @param param
     */
    void commitOrder(CommitOrderParam param);

    /**
     * 申请取消订单
     * @param param
     */
    void cancelOrder(CancelOrderParam param);

    /**
     * 申请提交支付单
     * @param param
     */
    void commitPayOrder(CommitPayOrderParam param);
}
