package cn.com.duiba.paycenter.params;

import java.io.Serializable;

/**
 * 充值时携带的额外参数
 * 部分参数是必选的
 * <AUTHOR>
 *
 */
public class PayChargeExtraParams implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7133329234377049028L;
	
	private Long adminId;
	private String memo;
	private String rechargeType;
	//扣费账户类型。1：余额账户，2：收入账户，默认为1
	private String accountType = "1";
	//主体 1:兑吧，2：兑啊，3：兑捷
	private String subject = "1";
	public Long getAdminId() {
		return adminId;
	}
	public void setAdminId(Long adminId) {
		this.adminId = adminId;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getRechargeType() {
		return rechargeType;
	}

	public void setRechargeType(String rechargeType) {
		this.rechargeType = rechargeType;
	}

	public String getAccountType() {
		return accountType;
	}

	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}
}
