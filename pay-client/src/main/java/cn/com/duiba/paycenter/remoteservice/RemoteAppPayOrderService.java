package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.result.PayCenterResult;

/**
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/13-8:31 PM
 */
@AdvancedFeignClient
public interface RemoteAppPayOrderService {

    /**
     * 订单结算
     * @param orderId
     * @param duibaMoney
     * @param devMoney
     * @param sign
     * @return
     */
    PayCenterResult orderSettle(Long orderId, Long duibaMoney, Long devMoney, String memo, String sign);

    /**
     * 采购退款
     * @param developerId
     * @param appId
     * @param money
     * @param relationId
     * @param memo
     * @return
     */
    PayCenterResult purchasePayback(Long developerId, Long appId, Long money, String relationId, String memo, String sign);
}
