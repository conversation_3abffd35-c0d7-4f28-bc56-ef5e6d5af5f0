package cn.com.duiba.paycenter.dto.duibaaccount;

import java.io.Serializable;
import java.util.Date;

/**
 * 兑吧后台充值管理返回数据
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2019-01-29-11:42
 */
public class RechargeDetailPageDto implements Serializable {

    private static final long serialVersionUID = -4621508278851318490L;

    /**
     * 开发者ID
     */
    private Long developerId;

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 变更金额
     */
    private Long changeMoney;

    /**
     * 备注
     */
    private String memo;

    /**
     * 账单类型
     * @see cn.com.duiba.paycenter.enums.duibaaccount.AppAccountRelationTypeEnum
     */
    private Integer relationType;

    public Long getDeveloperId() {
        return developerId;
    }

    public void setDeveloperId(Long developerId) {
        this.developerId = developerId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Long getChangeMoney() {
        return changeMoney;
    }

    public void setChangeMoney(Long changeMoney) {
        this.changeMoney = changeMoney;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getRelationType() {
        return relationType;
    }

    public void setRelationType(Integer relationType) {
        this.relationType = relationType;
    }
}
