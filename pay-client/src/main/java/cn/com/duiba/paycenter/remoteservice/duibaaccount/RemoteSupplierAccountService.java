package cn.com.duiba.paycenter.remoteservice.duibaaccount;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.duibaaccount.SupplierRemainingMoneyDto;
import cn.com.duiba.paycenter.params.duibaaccount.SupplierAccountChangeParams;
import cn.com.duiba.paycenter.result.PayCenterResult;

/**
 * 供应商余额账户remote
 * author z<PERSON><PERSON><PERSON>
 * date 2018-12-13-14:38
 */
@AdvancedFeignClient
public interface RemoteSupplierAccountService {

    /**
     * 供应商余额账户扣款
     * @param param
     * @return
     */
    PayCenterResult reduceMoney(SupplierAccountChangeParams param, String sign);

    /**
     * 供应商余额账户回款
     * @param param
     * @return
     */
    PayCenterResult addMoney(SupplierAccountChangeParams param,String sign);

    /**
     * 获取余额账户金额
     * @param supplierId
     * @return
     */
    Long findMoney(Long supplierId);

    /**
     * 创建供应商余额账户
     * @param supplierId 供应商ID
     * @return
     */
    SupplierRemainingMoneyDto create(Long supplierId);
}
