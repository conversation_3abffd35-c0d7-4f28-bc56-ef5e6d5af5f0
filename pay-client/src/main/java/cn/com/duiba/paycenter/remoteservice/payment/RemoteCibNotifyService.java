package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.cib.CibPayNotifyRequestDTO;
import cn.com.duiba.paycenter.dto.payment.charge.cib.CibPayNotifyRespDTO;

/**
 * 兴业银行通知服务
 *
 * <AUTHOR>
 * @date 2022/10/26
 */
@AdvancedFeignClient
public interface RemoteCibNotifyService {

    /**
     * 订单通知
     *
     * @param payNotifyRequest 支付通知请求
     * @return {@link CibPayNotifyRespDTO}
     * @throws BizException 业务异常
     */
    CibPayNotifyRespDTO orderNotify(CibPayNotifyRequestDTO payNotifyRequest) throws BizException;
}
