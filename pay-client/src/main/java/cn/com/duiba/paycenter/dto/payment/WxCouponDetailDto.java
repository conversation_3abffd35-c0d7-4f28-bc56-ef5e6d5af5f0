package cn.com.duiba.paycenter.dto.payment;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;

public class WxCouponDetailDto implements Serializable {


    private static final long serialVersionUID = 6114768396879223712L;
    private String stockCreatorMchid;
    private String stockId;
    private String couponId;
    private String couponName;
    private String status;
    private String description;
    private String couponType;
    private String availableBeginTime;
    private String availableEndTime;

    public String getStockCreatorMchid() {
        return stockCreatorMchid;
    }

    public void setStockCreatorMchid(String stockCreatorMchid) {
        this.stockCreatorMchid = stockCreatorMchid;
    }

    public String getStockId() {
        return stockId;
    }

    public void setStockId(String stockId) {
        this.stockId = stockId;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    public String getAvailableBeginTime() {
        return availableBeginTime;
    }

    public void setAvailableBeginTime(String availableBeginTime) {
        this.availableBeginTime = availableBeginTime;
    }

    public String getAvailableEndTime() {
        return availableEndTime;
    }

    public void setAvailableEndTime(String availableEndTime) {
        this.availableEndTime = availableEndTime;
    }
}
