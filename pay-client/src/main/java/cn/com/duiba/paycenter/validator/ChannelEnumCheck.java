package cn.com.duiba.paycenter.validator;

import cn.com.duiba.paycenter.validator.impl.ChannelEnumCheckConstraintValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2018/11/13
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = { ChannelEnumCheckConstraintValidator.class })
public @interface ChannelEnumCheck {
    String message() default "非法channelType";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };
}
