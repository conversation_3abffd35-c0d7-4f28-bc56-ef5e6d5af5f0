package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.WxTransferPayDto;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxTransferPaySendRequest;
import cn.com.duiba.paycenter.enums.PayOrderBizTypeEnum;
import cn.com.duiba.paycenter.message.FundTransferRequestMessage;
import cn.com.duiba.paycenter.params.FundTransferRequestParams;
import cn.com.duiba.paycenter.result.FundTransferResult;

/**
 * Created by xiaoxuda on 2017/11/1.
 */
@AdvancedFeignClient
public interface RemoteFundTransferService {
    /**
     * 请求资金转账,本接口承诺不返回null值，不抛出异常（接口调用超时除外）
     * 1.根据bizType,bizNo做幂等校验，防止重复提交
     * 2.订单状态为PayOrderStatusEnum.EXCEIPTION时会重新发起资金转账请求，最多允许重试5次
     * 3.其他状态直接返回当前状态，PayOrderStatusEnum.FAIL状态请做好原因备注
     * @param params
     * @return
     */
    FundTransferResult fundTransfer(FundTransferRequestParams params);

    /**
     * 异步请求资金转账,本接口承诺不返回null值，不抛出异常（接口调用超时除外），处理完成会通过消息通知调用方
     * 1.根据bizType,bizNo做幂等校验，防止重复提交
     * 2.订单状态为PayOrderStatusEnum.EXCEIPTION时会重新发起资金转账请求，最多允许重试5次
     * 3.其他状态直接返回当前状态，PayOrderStatusEnum.FAIL状态请做好原因备注
     * @param message
     * @return
     */
    FundTransferResult asynFundTransfer(FundTransferRequestMessage message);

    /**
     * 通过业务类型与业务方编号查询转账状态
     * @param bizType
     * @param bizNo
     * @return
     */
    FundTransferResult findByBizTypeAndBizNo(PayOrderBizTypeEnum bizType, String bizNo);

    /**
     * 发送微信企业转账金额
     * @param request
     * @return
     */
    FundTransferResult asynWxTransfer(WxTransferPaySendRequest request);

    /**
     * 查询微信企业转账信息
     * @param request
     * @return
     * @throws BizException
     */
//    WxTransferPayDto queryWxTransfer(WxTransferQueryRequest request) throws BizException;


    /**
     * 发送微信企业转账金额（同步功能，提供给星速台系统）
     * @param request
     * @return
     */
    FundTransferResult wxTransfer(WxTransferPaySendRequest request);

    /**
     * 查询微信企业转账信息
     * @param bizOrderNo
     * @param bizRelationId
     * @param bizRelationType
     * @return
     */
    WxTransferPayDto queryWxTransfer(String bizOrderNo, String bizRelationId, Integer bizRelationType);


}
