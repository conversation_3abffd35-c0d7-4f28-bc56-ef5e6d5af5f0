package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.DevChargeNotifyResponse;

import java.util.List;

/**
 * Created by 刘凯 on 2020/3/27.
 */
@AdvancedFeignClient
public interface RemotePayWaitConfirmService {

    /**
     * 通用功能 - 主动查询开发者订单进行支付确认
     * @param
     * @return List<DevChargeNotifyResponse>  主动查询成功的结果
     */
    List<DevChargeNotifyResponse> queryWaitConfirmChargeOrder();

    /**
     * 查询微信红包转账结果
     *
     */
    void queryWxWaitConfirmTransferOrder();
}
