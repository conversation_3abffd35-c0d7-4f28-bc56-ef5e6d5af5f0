package cn.com.duiba.paycenter.dto.duibaaccount;

import cn.com.duiba.paycenter.enums.duibaaccount.SupplierAccountChangeTypeEnum;
import cn.com.duiba.paycenter.enums.duibaaccount.SupplierAccountRelationTypeEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * 供应商账户明细分页查询结果
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018-11-26-20:06
 */
public class SupplierAccountDetailPageDto implements Serializable {

    private static final long serialVersionUID = 7365010947716961613L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 账单类型
     * @see SupplierAccountRelationTypeEnum
     */
    private Integer relationType;

    /**
     * 业务单号
     */
    private String relationId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 金额（分）
     */
    private Long changeMoney;

    /**
     * 变更类型
     * @see SupplierAccountChangeTypeEnum
     */
    private Integer changeType;

    /**
     * 备注
     */
    private String memo;

    /**
     * 变更后余额
     */
    private Long afterBalance;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getRelationType() {
        return relationType;
    }

    public void setRelationType(Integer relationType) {
        this.relationType = relationType;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Long getChangeMoney() {
        return changeMoney;
    }

    public void setChangeMoney(Long changeMoney) {
        this.changeMoney = changeMoney;
    }

    public Long getAfterBalance() {
        return afterBalance;
    }

    public void setAfterBalance(Long afterBalance) {
        this.afterBalance = afterBalance;
    }
}
