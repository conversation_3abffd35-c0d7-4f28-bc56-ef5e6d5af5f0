package cn.com.duiba.paycenter.message;

import cn.com.duiba.paycenter.params.FundTransferRequestParams;

import java.io.Serializable;

/**
 * 资金转账请求消息
 * Created by xiaoxuda on 2017/12/13.
 */
public class FundTransferRequestMessage implements Serializable{
    private static final long serialVersionUID = -330534242843405658L;
    /**
     * 请求信息
     */
    private FundTransferRequestParams requestParams;
    /**
     * 消息主题
     */
    private String topic;
    /**
     * 消息标签
     */
    private String tag;
    /**
     * 消息标识
     */
    private String key;

    public FundTransferRequestParams getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(FundTransferRequestParams requestParams) {
        this.requestParams = requestParams;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
