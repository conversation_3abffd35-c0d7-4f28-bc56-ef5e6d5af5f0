package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.citic.CiticCallbackNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.citic.CiticChargeNotifyResponse;

/**
 * Created by xutao on 2020/4/24.
 */
@AdvancedFeignClient
public interface RemoteCiticNotifyService {
    /**
     * 中信支付通知
     * @param bizOrderNo
     * @return response
     * @throws BizException exception
     */
    CiticChargeNotifyResponse queryOrderNotify(String bizOrderNo) throws BizException;

    /**
     * 中信支付通知
     * @param bizOrderNo
     * @param bizType
     * @return
     * @throws BizException
     */
    CiticChargeNotifyResponse queryOrderNotifyNew(String bizOrderNo, Integer bizType) throws BizException;

    /**
     * 中信支付通知 支持不同的供应商
     * @param bizOrderNo
     * @return response
     * @throws BizException exception
     */
    CiticChargeNotifyResponse queryOrderNotifyWithSupplier(String bizOrderNo, Integer supplierTag) throws BizException;

    /**
     * 中信支付回调通知
     *
     * @param content 开发者回调通知信息
     * @return
     */
    CiticCallbackNotifyResponse callbackPayNotify(String content);
}
