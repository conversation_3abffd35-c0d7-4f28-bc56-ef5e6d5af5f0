package cn.com.duiba.paycenter.client;

import java.io.Serializable;

public class RpcResult<T> implements Serializable{
	private static final long serialVersionUID = -8572191054057717585L;
	private Exception rpcException;
	
	private T result;
	
	private boolean opSuccess=false;
	
	public RpcResult(T r){
		result=r;
		opSuccess=true;
	}
	
	public RpcResult(Exception rpcException){
		this.rpcException=rpcException;
	}

	public Exception getRpcException() {
		return rpcException;
	}

	public void setRpcException(Exception rpcException) {
		this.rpcException = rpcException;
	}

	public T getResult() {
		return result;
	}
	/**
	 * 是否rpc调用成功
	 * @return
	 */
	public boolean isOpSuccess() {
		return opSuccess;
	}
}
