package cn.com.duiba.paycenter.constant;

/**
 * 招商银行-掌上生活-开放平台-H5支付-常量
 *
 * <AUTHOR>
 * @date 2024/2/18 9:47 下午
 */
public class CmbLifeConstants {

    public static final String PROJECT = "掌上生活-H5支付";

    // 合作方唯一ID，32位定长
    public static final String MID = "mid";

    // 应用唯一ID，32位定长
    public static final String AID = "aid";

    // 时间戳，格式yyyyMMddHHmmss
    public static final String DATE = "date";

    // 随机字符串，保证签名不可预测，不长于32位，签名验签用
    public static final String RANDOM = "random";

    // 合作方订单号，同一合作方需要唯一（合作方自己生成），不长于32位
    public static final String BILL_NO = "billNo";

    // 订单名称（建议十个字符内）
    public static final String PRODUCT_NAME = "productName";

    // 订单金额（单位为分）
    public static final String AMOUNT = "amount";

    // 订单积分，无积分则传0
    public static final String BONUS = "bonus";

    // 掌上生活客户端支付结果重定向页面地址，App支付时不需要传入，需开通白名单权限，请提前告知相关对接人员
    public static final String RETURN_URL = "returnUrl";

    // 后台通知接口地址，需要开通网络权限，请提前告知相关对接人员，请使用https协议
    public static final String NOTIFY_URL = "notifyUrl";

    // 订单详情页，用于订单中心展示订单详情，请使用https协议
    public static final String ORDER_DETAIL_URL = "orderDetailUrl";

    // 剩余的可支付时间(秒)，建议24小时内，最长7天
    public static final String PAY_PERIOD = "payPeriod";

    // 合作方密钥对别名
    public static final String KEY_ALIAS = "keyAlias";

    // 掌上生活密钥对别名
    public static final String CMB_KEY_ALIAS = "cmbKeyAlias";

    // 签名，具体详见《签名与验签》文档
    public static final String SIGN = "sign";

    // 退款流水号，与退款时入参一致
    public static final String REFUND_TOKEN = "refundToken";
}
