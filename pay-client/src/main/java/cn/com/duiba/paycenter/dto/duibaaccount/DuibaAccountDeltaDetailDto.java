package cn.com.duiba.paycenter.dto.duibaaccount;

import cn.com.duiba.paycenter.model.BaseDO;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/2/21
 */
public class DuibaAccountDeltaDetailDto implements Serializable {

    public static final String DUIBA_OPERATION_STATUS_CREATE="create";
    public static final String DUIBA_OPERATION_STATUS_SUCCESS="success";
    public static final String DUIBA_OPERATION_STATUS_PAYBACK="payback";
    public static final String DUIBA_OPERATION_STATUS_UNKNOW="unknow";
    public static final String DUIBA_OPERATION_STATUS_FAILED="failed";  //支付中心告知 失败

    public static Integer      AddMoney        = 1;                                                //财务加款
    public static Integer      CutMoney        = 2;                                                //财务减款

    private Long               id;

    private Integer            operationType;                                                      // 1：冲值。2：减款

    private Integer             operationMoney;                                                     // 操作金额

    private String             operationStatus;                                                    // 最终处理状态create success payback,unknow

    private String             remarks;

    private Date gmtCreate;                                                          // 记录创建时间

    private Date               gmtModified;                                                        // 记录最后修改时间

    private Long adminid;

    private Long applyId;

    public DuibaAccountDeltaDetailDto() {
    }

    public DuibaAccountDeltaDetailDto(Long id) {
        this.id = id;
        this.gmtModified = new Date();
    }

    public DuibaAccountDeltaDetailDto(boolean init4insert) {
        if (init4insert) {
            gmtCreate = new Date();
            gmtModified = new Date();
        }
    }

    public void beforeUpdate() {
        gmtModified = new Date();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public Integer getOperationMoney() {
        return operationMoney;
    }

    public void setOperationMoney(Integer operationMoney) {
        this.operationMoney = operationMoney;
    }

    public String getOperationStatus() {
        return operationStatus;
    }

    public void setOperationStatus(String operationStatus) {
        this.operationStatus = operationStatus;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Long getAdminid()
    {
        return adminid;
    }

    public void setAdminid(Long adminid)
    {
        this.adminid = adminid;
    }

    public Long getApplyId()
    {
        return applyId;
    }

    public void setApplyId(Long applyId)
    {
        this.applyId = applyId;
    }


}

