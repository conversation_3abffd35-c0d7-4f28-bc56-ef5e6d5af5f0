package cn.com.duiba.paycenter.enums;

import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 银联分期支付银行简称枚举
 *
 * <AUTHOR>
 * @date 2022/6/13 11:32
 */
public enum DuibaLiveInstallmentPayBankTypeEnum {

    /**
     * 银联分期支付支持的银行枚举
     * 分期只支持信用卡
     */
    ICBC("ICBC","工商银行(信用卡)"),
    ABC("ABC","农业银行(信用卡)"),
    CCB("CCB","建设银行(信用卡)"),
    PSBC("PSBC","邮储银行(信用卡)"),
    CITIC("CITIC","中信银行(信用卡)"),
    CEB("CEB","光大银行(信用卡)"),
    HXBANK("HXBANK","华夏银行(信用卡)"),
    CMBC("CMBC","民生银行(信用卡)"),
    GDB("GDB","广发银行(信用卡)"),
    CIB("CIB","兴业银行(信用卡)"),
    SPDB("SPDB","浦发银行(信用卡)"),
    CMB("CMB","招商银行(信用卡)"),
    PAB("PAB","平安银行(信用卡)"),
    BOS("BOS","上海银行(信用卡)"),
    BOB("BOB","北京银行(信用卡)"),
    BON("BON","宁波银行(信用卡)"),
    BOJ("BOJ","江苏银行(信用卡)"),
    BRCB("BRCB","北京农商(信用卡)"),
    GRCB("GRCB","广州农商(信用卡)"),
    BOZ("BOZ","浙商银行(信用卡)"),
    SRCB("SRCB","深圳农村商业银行(信用卡)"),
    SHRCB("SHRCB","上海农村商业银行(信用卡)"),
    CGNB("CGNB","四川天府银行(信用卡)"),
    BCM("BCM","交通银行(信用卡)"),
    CABANK("CABANK","长安银行(信用卡)"),
    HSBC("HSBC","汇丰银行(信用卡)"),
    ZBCB("ZBCB","齐商银行(信用卡)"),
    ZJNX("ZJNX","浙江农信(信用卡)"),
    NXBANK("NXBANK","宁夏银行(信用卡)"),
    WHB("WHB","威海市商业银行(信用卡)"),
    ZYB("ZYB","中原银行(信用卡)"),
    DLB("DLB","大连银行(信用卡)"),
    JZB("JZB","锦州银行(信用卡)"),
    MSB("MSB","蒙商银行(信用卡)"),
    JYNSB("JYNSB","江阴农商银行(信用卡)"),
    WFB("WFB","潍坊银行(信用卡)"),
    QLB("QLB","齐鲁银行(信用卡)"),
    ;

    /**
     * 银行简称
     */
    private final String bankCode;

    /**
     * 银行全称
     */
    private final String bankName;

    DuibaLiveInstallmentPayBankTypeEnum(String bankCode, String bankName) {
        this.bankCode = bankCode;
        this.bankName = bankName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    private static final Map<String, String> ENUM_MAP;


    static {
        ENUM_MAP = Collections.unmodifiableMap(Arrays.stream(DuibaLiveInstallmentPayBankTypeEnum.values()).collect(Collectors.toMap(DuibaLiveInstallmentPayBankTypeEnum::getBankCode, DuibaLiveInstallmentPayBankTypeEnum::getBankName)));
    }

    public static String getByCode(String bankCode) {
        if (StringUtils.isBlank(bankCode)) {
            return null;
        }
        return ENUM_MAP.get(bankCode);
    }
}
