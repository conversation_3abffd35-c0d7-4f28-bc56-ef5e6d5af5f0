package cn.com.duiba.paycenter.dto;

import cn.com.duiba.paycenter.enums.EquityOnlyOrderBizTypeEnum;
import cn.com.duiba.paycenter.enums.PayAccountTypeEnum;
import cn.com.duiba.paycenter.enums.PayOrderBizTypeEnum;
import cn.com.duiba.paycenter.enums.PayOrderStatusEnum;

import java.io.Serializable;
import java.util.Date;

public class EquityOnlyOrderDto implements Serializable {

    private static final long serialVersionUID = 1206417193391882780L;

    /**
     * 自增主键
     **/
    private Long id;
    /**
     * 业务标识
     **/
    private EquityOnlyOrderBizTypeEnum bizType;
    /**
     * 业务方的业务唯一编号
     **/
    private String bizNo;
    /**
    /**
     * 支付状态
     **/
    private PayOrderStatusEnum orderStatus;
    /**
     * 已重试次数
     **/
    private Integer retryCount;
    /**
     * 支付执行人业务唯一标识（支付宝/银行支付流水号）
     **/
    private String executorBizId;
    /**
     * 业务方的备注信息，会传递给支付宝
     */
    private String bizRemark;
    /**
     * 支付订单信息
     */
    private String remark;
    /**
     * 创建时间
     **/
    private Date gmtCreate;
    /**
     * 修改时间
     **/
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public String getExecutorBizId() {
        return executorBizId;
    }

    public void setExecutorBizId(String executorBizId) {
        this.executorBizId = executorBizId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getBizRemark() {
        return bizRemark;
    }

    public void setBizRemark(String bizRemark) {
        this.bizRemark = bizRemark;
    }

    public EquityOnlyOrderBizTypeEnum getBizType() {
        return bizType;
    }

    public void setBizType(EquityOnlyOrderBizTypeEnum bizType) {
        this.bizType = bizType;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }

    public PayOrderStatusEnum getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(PayOrderStatusEnum orderStatus) {
        this.orderStatus = orderStatus;
    }
}
