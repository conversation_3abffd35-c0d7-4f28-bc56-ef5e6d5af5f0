package cn.com.duiba.paycenter.dto.payment;

import java.io.Serializable;

public class WxTransferPayDto implements Serializable {
    private static final long serialVersionUID = -8519939587870678931L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 兑吧appId
     */
    private Long appId;

    /**
     * 微信支付分配的商户号
     */
    private String mchId;

    /**
     * 微信分配的公众账号ID（企业号corpid即为此appId）
     */
    private String wxappid;

    /**
     * 业务方关联活动类型
     */
    private Integer bizRelationType;

    /**
     * 业务方关联活动id
     */
    private String bizRelationId;

    /**
     * 业务方订单号
     */
    private String bizOrderNo;

    /**
     * 商户订单号
     */
    private String mchTradeNo;

    /**
     * 支付渠道
     */
    private String channelType;

    /**
     * 渠道返回的交易流水号
     */
    private String transactionNo;

    /**
     * 0:不校验真实姓名 , 1:强校验真实姓名
     */
    private Integer checkName;

    /**
     * 用户openid
     */
    private String openId;

    /**
     * 支付金额，必须大于0, 单位为分
     */
    private Integer amount;

    /**
     * 备注
     */
    private String body;

    /**
     * 调用接口的机器Ip地址
     */
    private String clientIp;

    /**
     * @see WxTransferPayStatusEnum
     */
    private Integer chargeStatus;

    /**
     * 渠道支付失败码
     */
    private String failureCode;

    /**
     * 渠道支付失败描述
     */
    private String failureMsg;

    /**
     * 渠道返回企业付款成功时间
     */
    private String paidTime;

    /**
     * 渠道发起交易时需要的数据和额外的数据，JSON格式
     */
    private String extra;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getWxappid() {
        return wxappid;
    }

    public void setWxappid(String wxappid) {
        this.wxappid = wxappid;
    }

    public Integer getBizRelationType() {
        return bizRelationType;
    }

    public void setBizRelationType(Integer bizRelationType) {
        this.bizRelationType = bizRelationType;
    }

    public String getBizRelationId() {
        return bizRelationId;
    }

    public void setBizRelationId(String bizRelationId) {
        this.bizRelationId = bizRelationId;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getMchTradeNo() {
        return mchTradeNo;
    }

    public void setMchTradeNo(String mchTradeNo) {
        this.mchTradeNo = mchTradeNo;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public Integer getCheckName() {
        return checkName;
    }

    public void setCheckName(Integer checkName) {
        this.checkName = checkName;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public Integer getChargeStatus() {
        return chargeStatus;
    }

    public void setChargeStatus(Integer chargeStatus) {
        this.chargeStatus = chargeStatus;
    }

    public String getFailureCode() {
        return failureCode;
    }

    public void setFailureCode(String failureCode) {
        this.failureCode = failureCode;
    }

    public String getFailureMsg() {
        return failureMsg;
    }

    public void setFailureMsg(String failureMsg) {
        this.failureMsg = failureMsg;
    }

    public String getPaidTime() {
        return paidTime;
    }

    public void setPaidTime(String paidTime) {
        this.paidTime = paidTime;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }
}
