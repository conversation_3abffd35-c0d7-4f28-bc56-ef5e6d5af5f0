package cn.com.duiba.paycenter.dto.duibaaccount;

import java.io.Serializable;
import java.util.Date;

/**
 * 开发者应用待结算账户明细dto
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018-11-27-14:25
 */
public class AppSettleAccountDetailDto implements Serializable {
    private static final long serialVersionUID = 6910007329943137287L;

    private Long id;

    /**
     * 开发者ID
     */
    private Long developerId;

    /**
     * 应用ID
     */
    private Long appId;

    private Long beforeBalance;

    private Long afterBalance;
    /**
     * 备注
     */
    private String memo;

    /**
     * 变更金额
     */
    private Long changeMoney;

    /**
     * 来源类型
     */
    private Integer relationType;

    /**
     * 关联的类型ID
     */
    private String relationId;

    /**
     * 类型 1:入账，加 2：出账、减
     */
    private Integer changeType;

    /**
     * 转出方
     */
    private Integer transferOut;

    /**
     * 转入方
     */
    private Integer transferIn;

    private Date gmtCreate;

    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDeveloperId() {
        return developerId;
    }

    public void setDeveloperId(Long developerId) {
        this.developerId = developerId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getRelationType() {
        return relationType;
    }

    public void setRelationType(Integer relationType) {
        this.relationType = relationType;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getChangeMoney() {
        return changeMoney;
    }

    public void setChangeMoney(Long changeMoney) {
        this.changeMoney = changeMoney;
    }

    public Integer getTransferOut() {
        return transferOut;
    }

    public void setTransferOut(Integer transferOut) {
        this.transferOut = transferOut;
    }

    public Integer getTransferIn() {
        return transferIn;
    }

    public void setTransferIn(Integer transferIn) {
        this.transferIn = transferIn;
    }

    public Long getBeforeBalance() {
        return beforeBalance;
    }

    public void setBeforeBalance(Long beforeBalance) {
        this.beforeBalance = beforeBalance;
    }

    public Long getAfterBalance() {
        return afterBalance;
    }

    public void setAfterBalance(Long afterBalance) {
        this.afterBalance = afterBalance;
    }
}
