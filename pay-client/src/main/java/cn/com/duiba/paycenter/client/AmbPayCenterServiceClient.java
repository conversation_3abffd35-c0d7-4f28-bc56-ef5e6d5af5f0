package cn.com.duiba.paycenter.client;

import cn.com.duiba.paycenter.result.AmbResult;
import cn.com.duiba.paycenter.service.AmbPayCenterService;
import cn.com.duiba.paycenter.service.AmbPayCenterService.AmbPayChargeExtraParams;
import cn.com.duiba.paycenter.service.AmbPayCenterService.AmbPayParams;
import cn.com.duiba.paycenter.util.SignUtil;
import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class AmbPayCenterServiceClient {
	
	private AmbPayCenterService ambPayCenterService;

	public RpcResult<AmbResult> consumerPay(Long developerId, Long orderId,
			Long consumerPrice, AmbPayParams p) {
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("orderId", orderId+"");
			params.put("consumerPrice", consumerPrice+"");
			String sign=SignUtil.sign(params);
			
			AmbResult ret= ambPayCenterService.consumerPay(developerId, orderId, consumerPrice, p, sign);
			
			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}

	@Deprecated
	/**
	 * 兼容已有的暂时不能删除
	 * 这个方法会丢弃，请用consumerPayBackByOrderItemId
	 */
	public RpcResult<AmbResult> consumerPayBack(Long developerId, Long orderId,
												Long paybackMoney, AmbPayParams p) {
		return consumerPayBackNew(developerId, orderId, paybackMoney, null, p);
	}

	public RpcResult<AmbResult> consumerPayBackNew(Long developerId, Long orderId,
			Long paybackMoney, Long ordersItemId, AmbPayParams p) {
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("orderId", orderId+"");
			params.put("paybackMoney", paybackMoney+"");
			params.put("ordersItemId", ordersItemId+"");
			String sign=SignUtil.sign(params);
			
			AmbResult ret= ambPayCenterService.consumerPayBack(developerId, orderId, paybackMoney, ordersItemId, p, sign);

			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}

	public RpcResult<AmbResult> consumerPayBackNewByOrderItemIds(Long developerId, Long orderId, Map<Long, Long> orderItemIdsAndMoney, AmbPayParams p) {
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("orderId", orderId+"");
			params.put("orderItemIdsAndMoney", JSONObject.toJSONString(orderItemIdsAndMoney));
			String sign=SignUtil.sign(params);

			AmbResult ret= ambPayCenterService.consumerPayBackList(developerId, orderId, orderItemIdsAndMoney, p, sign);

			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}

	public RpcResult<AmbResult> dlpWithdrawToRemaining(Long developerId,
			Long withdrawCashOrderId, Long money, AmbPayChargeExtraParams p) {
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("withdrawCashOrderId", withdrawCashOrderId+"");
			params.put("money", money+"");
			String sign=SignUtil.sign(params);
			
			AmbResult ret= ambPayCenterService.dlpWithdrawToRemaining(developerId, withdrawCashOrderId, money, p, sign);

			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}

	public RpcResult<AmbResult> orderSettle(Long orderId, Long duibaMoney, Long devMoney,
			AmbPayChargeExtraParams p) {
		try {
			Map<String, String> params=new HashMap<>();
			params.put("orderId", orderId+"");
			params.put("duibaMoney", duibaMoney+"");
			params.put("devMoney", devMoney+"");
			String sign=SignUtil.sign(params);
			
			AmbResult ret= ambPayCenterService.orderSettle(orderId, duibaMoney, devMoney, p, sign);

			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}

	public RpcResult<AmbResult> orderSettleWithFee(Long orderId, Long duibaMoney, Long devMoney,Long fee,
											AmbPayChargeExtraParams p) {
		try {
			Map<String, String> params=new HashMap<>();
			params.put("orderId", orderId+"");
			params.put("duibaMoney", duibaMoney+"");
			params.put("devMoney", devMoney+"");
			params.put("fee", fee+"");
			String sign=SignUtil.sign(params);

			AmbResult ret= ambPayCenterService.orderSettleWithFee(orderId, duibaMoney, devMoney,fee, p, sign);

			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}

	public RpcResult<AmbResult> dlpWithdrawCashApply(Long developerId,
			Long ambDeveloperWithdrawCashOrderId, Long drawCachMoney,
			AmbPayChargeExtraParams p) {
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("ambDeveloperWithdrawCashOrderId", ambDeveloperWithdrawCashOrderId+"");
			params.put("drawCachMoney", drawCachMoney+"");
			String sign=SignUtil.sign(params);
			
			AmbResult ret= ambPayCenterService.dlpWithdrawCashApply(developerId, ambDeveloperWithdrawCashOrderId, drawCachMoney, p, sign);

			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}

	public RpcResult<AmbResult> dlpWithdrawCashRefuse(Long developerId,
			Long ambDeveloperWithdrawCashOrderId, Long drawCachMoney,
			AmbPayChargeExtraParams p) {
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("ambDeveloperWithdrawCashOrderId", ambDeveloperWithdrawCashOrderId+"");
			params.put("drawCachMoney", drawCachMoney+"");
			String sign=SignUtil.sign(params);
			
			AmbResult ret= ambPayCenterService.dlpWithdrawCashRefuse(developerId, ambDeveloperWithdrawCashOrderId, drawCachMoney, p, sign);

			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}

	public RpcResult<AmbResult> dlpWithdrawCashRefuseBySubject(Long developerId,
													  Long ambDeveloperWithdrawCashOrderId, Long drawCachMoney,
													  AmbPayChargeExtraParams p, String subjectType) {
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("ambDeveloperWithdrawCashOrderId", ambDeveloperWithdrawCashOrderId+"");
			params.put("drawCachMoney", drawCachMoney+"");
			String sign=SignUtil.sign(params);

			AmbResult ret= ambPayCenterService.dlpWithdrawCashRefuseBySubject(developerId, ambDeveloperWithdrawCashOrderId, drawCachMoney, p, sign, subjectType);

			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}

	public void setAmbPayCenterService(AmbPayCenterService ambPayCenterService) {
		this.ambPayCenterService = ambPayCenterService;
	}

}
