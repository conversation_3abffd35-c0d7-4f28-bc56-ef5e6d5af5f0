package cn.com.duiba.paycenter.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付业务类型
 */
public enum EquityOnlyOrderBizTypeEnum {
    ALIPAY_LINK_COUPON("支付宝立减金链接券"),
    DICOUNT_INTERFACE_COUPON("满减优惠券接口发放"),
    ;


    private static final Map<String, EquityOnlyOrderBizTypeEnum> ENUM_MAP = new HashMap<>();

    static{
        for(EquityOnlyOrderBizTypeEnum tmp : values()){
            ENUM_MAP.put(tmp.name(), tmp);
        }
    }

    public static EquityOnlyOrderBizTypeEnum getByType(String type) {
        return ENUM_MAP.get(type);
    }

    private String desc;

    EquityOnlyOrderBizTypeEnum(String desc){
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
