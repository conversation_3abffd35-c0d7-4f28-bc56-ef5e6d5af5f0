package cn.com.duiba.paycenter.enums.equity;

import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collections;
import java.util.Set;

/**
 * 权益订单状态
 * <AUTHOR>
 * @date 2023/4/10 11:38 AM
 */
@Getter
@AllArgsConstructor
public enum EquityOrderStatusEnum {
    // 状态解释：
    // 1-初始化：接受到业务方请求，新增订单数据
    // 2-处理中：订单、订单记录都写库成功，提交异步任务（发放权益流程）
    // 3-内部校验不通过：调用第三方之前，参数校验等前置逻辑未通过
    // 4-调用第三方发生异常：调用第三方时发生超时等异常
    // 5-第三方中间状态：第三方返回结果无法判断成功/失败，或者第三方是异步接口，需要查询才知道结果
    // 6-第三方失败-不可重试：第三方返回结果为失败，且该状态不可能重试成功
    // 7-第三方失败-可重试：第三方返回结果为失败，但重试可能会成功
    // 8-第三方成功：第三方返回结果为成功
    // 9-内部发生异常：任何步骤发生不可预期的异常，都会变更到当前状态，比如redis连接异常等。
    // 状态流转：
    // 新增订单：1-初始化
    // 开始处理权益发放：2-处理中
    // 调用第三方前置校验：3-内部校验不通过
    // 调用第三方：4-调用第三方发生异常  5-第三方中间状态  6-第三方失败-不可重试 7-第三方失败-可重试  8-第三方成功
    // 其他：9-内部发生异常
    INIT(1, "初始化"),
    PROCESSING(2, "处理中"),
    CHECK_FAIL_BEFORE_CALL_THIRD(3, "调用第三方之前，内部校验不通过"),
    CALL_THIRD_EXCEPTION(4, "调用第三方发生异常"),
    THIRD_PROCESSING(5, "第三方中间状态"),
    THIRD_FAIL(6, "第三方失败-不能自动重试"),
    THIRD_FAIL_RETRY(7, "第三方失败-可自动重试"),
    THIRD_SUCCESS(8, "第三方成功"),
    EXCEPTION(9, "内部发生异常"),
    ;
    
    private static final Set<Integer> NEED_QUERY_SET = Collections.unmodifiableSet(Sets.newHashSet(CALL_THIRD_EXCEPTION.getType(), THIRD_PROCESSING.getType(), EXCEPTION.getType()));
    
    private static final Set<Integer> CAN_RETRY_SET = Collections.unmodifiableSet(Sets.newHashSet(CHECK_FAIL_BEFORE_CALL_THIRD.getType(), THIRD_FAIL.getType(), THIRD_FAIL_RETRY.getType()));

    /**
     * 类型
     */
    private final Integer type;

    /**
     * 描述
     */
    private final String desc;
    
    public static boolean needQuery(Integer type) {
        if (type == null) {
            return false;
        }
        return NEED_QUERY_SET.contains(type);
    }
    
    public static boolean canRetry(Integer type) {
        if (type == null) {
            return false;
        }
        return CAN_RETRY_SET.contains(type);
    }
}
