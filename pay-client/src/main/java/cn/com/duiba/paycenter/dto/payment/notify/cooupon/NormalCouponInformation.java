package cn.com.duiba.paycenter.dto.payment.notify.cooupon;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Description:
 * <p>
 * date: 2022/10/11 7:17 下午
 *
 * <AUTHOR>
 */
public class NormalCouponInformation implements Serializable {
    private static final long serialVersionUID = 6164197784227664543L;
    /**
     * 面额
     */
    @JSONField(name = "coupon_amount")
    private int couponAmount;
    /**
     * 门槛
     */
    @JSONField(name = "transaction_minimum")
    private int transactionMinimum;

    public int getCouponAmount() {
        return couponAmount;
    }

    public NormalCouponInformation setCouponAmount(int couponAmount) {
        this.couponAmount = couponAmount;
        return this;
    }

    public int getTransactionMinimum() {
        return transactionMinimum;
    }

    public NormalCouponInformation setTransactionMinimum(int transactionMinimum) {
        this.transactionMinimum = transactionMinimum;
        return this;
    }
}
