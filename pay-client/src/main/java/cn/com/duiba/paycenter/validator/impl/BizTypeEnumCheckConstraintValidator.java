package cn.com.duiba.paycenter.validator.impl;

import cn.com.duiba.paycenter.enums.BizTypeEnum;
import cn.com.duiba.paycenter.validator.BizTypeEnumCheck;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2018/11/13
 */
public class BizTypeEnumCheckConstraintValidator implements ConstraintValidator<BizTypeEnumCheck, Object> {
    private String message;
    @Override
    public void initialize(BizTypeEnumCheck constraintAnnotation) {
        this.message = constraintAnnotation.message();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        if (Arrays.stream(BizTypeEnum.values()).anyMatch(bizTypeEnum -> bizTypeEnum.getCode().equals(value))) {
            return true;
        }
        context.disableDefaultConstraintViolation();
        ConstraintValidatorContext.ConstraintViolationBuilder builder =
                context.buildConstraintViolationWithTemplate(message);
        builder.addConstraintViolation();
        return false;
    }
}
