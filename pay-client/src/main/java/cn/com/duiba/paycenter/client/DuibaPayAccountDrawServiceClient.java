package cn.com.duiba.paycenter.client;

import cn.com.duiba.paycenter.params.DuibaPayOrdersExtraParams;
import cn.com.duiba.paycenter.result.PayOrdersResult;
import cn.com.duiba.paycenter.service.DuibaPayAccountDrawService;
import cn.com.duiba.paycenter.util.SignUtil;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 全局账户提现  兑吧账户扣款api
 */
public class DuibaPayAccountDrawServiceClient {

	private DuibaPayAccountDrawService duibaPayAccountDrawService;
	
	/**
	 * 全局账户  发起支付宝提现前调用
	 * 
	 * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
	 * 
	 * @param orderId
	 * @param money
	 * @param p
	 */
	public RpcResult<PayOrdersResult> payOrder(Long orderId, Long money, DuibaPayOrdersExtraParams p){
		try {
			Map<String, String> params = Maps.newHashMap();
			params.put("orderId", orderId + "");
			params.put("money", money + "");
			String sign=SignUtil.sign(params);
			
			PayOrdersResult ret = duibaPayAccountDrawService.pay(orderId, money,sign,p);

			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}
	/**
	 * 全局账户  发起支付宝提现失败后调用
	 * 
	 * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
	 * 
	 * @param orderId
	 * @param money
	 * @return
	 */
	public RpcResult<PayOrdersResult> backPayOrder(Long orderId,Long money){
		try {
			Map<String, String> params = Maps.newHashMap();
			params.put("orderId", orderId + "");
			params.put("money", money + "");
			String sign=SignUtil.sign(params);
			
			PayOrdersResult ret = duibaPayAccountDrawService.backPay( orderId, money, sign);
			
			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}

	public void setDuibaPayAccountDrawService(DuibaPayAccountDrawService duibaPayAccountDrawService) {
		this.duibaPayAccountDrawService = duibaPayAccountDrawService;
	}
	
	
}
