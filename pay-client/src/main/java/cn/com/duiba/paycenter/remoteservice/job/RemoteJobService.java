package cn.com.duiba.paycenter.remoteservice.job;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.ADeveloperFlowQuery;
import cn.com.duiba.paycenter.dto.AccountDetailDto;
import cn.com.duiba.paycenter.dto.AccountDetailTotalDto;
import cn.com.duiba.paycenter.dto.AccountPageInfo;
import cn.com.duiba.paycenter.params.AccountChangeParam;

import java.util.Date;
import java.util.List;
import java.util.Map;

@AdvancedFeignClient
public interface RemoteJobService {

	public void equityOrderBatchDetailSyncJob();

	public void equityOrderSyncJob();
	

}
