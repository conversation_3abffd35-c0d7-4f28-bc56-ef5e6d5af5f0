package cn.com.duiba.paycenter.client;

import cn.com.duiba.paycenter.params.AppAccountPayChargeParams;
import cn.com.duiba.paycenter.remoteservice.RemoteAppAccountPayChargeService;
import cn.com.duiba.paycenter.result.PayCenterResult;
import cn.com.duiba.paycenter.util.SignUtil;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 开发者账户人工、在线充值
 * author z<PERSON><PERSON><PERSON>
 * date 2018/11/6-11:57 AM
 */
public class AppPayChargeServiceClient {

    private static final String DEVELOPER_ID = "developerId";
    private static final String RELATION_ID = "relationId";
    private static final String MONEY = "money";
    private static final String APP_ID = "appId";
    @Resource
    private RemoteAppAccountPayChargeService remoteAppAccountPayChargeService;
    /**
     * 人工充值
     * @param payChargeParams
     * @return
     */
    public RpcResult<PayCenterResult> chargeMoneyByManual(AppAccountPayChargeParams payChargeParams){
        try {
            PayCenterResult ret= remoteAppAccountPayChargeService.chargeMoneyByManual(payChargeParams,getSign(payChargeParams));

            return new RpcResult<>(ret);
        } catch (Exception e) {
            return new RpcResult<>(e);
        }
    }
    /**
     * 在线充值
     * @param params
     * @return
     */
    public RpcResult<PayCenterResult> chargeMoneyByOnline(AppAccountPayChargeParams params){
        try {

            PayCenterResult ret=remoteAppAccountPayChargeService.chargeMoneyByOnline(params,getSign(params));

            return new RpcResult<>(ret);
        } catch (Exception e){
            return new RpcResult<>(e);
        }
    }
    /**
     * 当充值错误，需要减钱时，调用此方法
     * 正常情况不会使用此方法
     * @param payChargeParams
     * @return
     */
    public RpcResult<PayCenterResult> reduceMoneyByManual(AppAccountPayChargeParams payChargeParams){
        try {

            PayCenterResult ret = remoteAppAccountPayChargeService.reduceMoneyByManual(payChargeParams,getSign(payChargeParams));

            return new RpcResult<>(ret);
        } catch (Exception e){
            return new RpcResult<>(e);
        }
    }

    /**
     * 在线充值手续费
     * @param payChargeParams
     * @return
     */
    public RpcResult<PayCenterResult> chargeMoneyFeeByOnline(AppAccountPayChargeParams payChargeParams){
        try {
            PayCenterResult ret = remoteAppAccountPayChargeService.chargeMoneyFeeByOnline(payChargeParams,getSign(payChargeParams));

            return new RpcResult<>(ret);
        } catch (Exception e){
            return new RpcResult<>(e);
        }
    }

    private String getSign(AppAccountPayChargeParams payChargeParams){
        Map<String, String> params=new HashMap<>();
        params.put(DEVELOPER_ID, Objects.toString(payChargeParams.getDeveloperId()));
        params.put(RELATION_ID, payChargeParams.getRelationId());
        params.put(MONEY, Objects.toString(payChargeParams.getChangeMoney()));
        params.put(APP_ID,Objects.toString(payChargeParams.getAppId()));
        return SignUtil.sign(params);
    }
}
