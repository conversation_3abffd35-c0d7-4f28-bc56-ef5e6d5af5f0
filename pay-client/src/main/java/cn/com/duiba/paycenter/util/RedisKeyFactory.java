package cn.com.duiba.paycenter.util;

/**
 * Created by xiaoxuda on 2018/6/22.
 */
public enum RedisKeyFactory {
    K001("支付宝APP_ID"),
    K002("app微信渠道配置"),
    K003("公众号id和商户号的微信渠道配置"),
	K004("支付宝渠道配置"),
    K005("支付宝app维度渠道配置"),
    K006("创建开发者应用账户 分布式锁"),
    K007("微信现金红包发放，用于构建商户唯一订单号"),
    K008("微信现金红包，分布式锁"),
    K009("中信后端回调通知，分布式锁"),
    K012("微信企业付款到零钱，用于构建商户唯一订单号"),
    K013("微信企业付款到零钱,用于查询订单状态"),
    K014("创建订单锁,避免并发"),
    K016("权益发放分布式锁,避免并发"),
    K017("权益发放明细分布式锁,避免并发"),
    K110("宁波银行-支付"),


    K015("易办事 通知"),
    K111("工商银行积分抵扣兑换通知锁"),
    K112("厦门国际银行-支付"),

    K113("兴业银行-支付"),
    K114("兴业银行-支付主动查询通知"),
    /** ------ 开发者定制业务key start---------*/
    K100("招行一网通支付公钥key"),
    K101("工行e生活一更新支付订单状态，分布式锁"),
    K102("微信核销明细记录"),

    K103("建总行定制补发锁"),

    K104("支付宝发放卡券锁"),
    K115("深圳银联小程序云闪付支付-获取accessToken"),

    K116("深圳银联小程序云闪付支付-获取accessToken-锁"),

    K117("微信发放幂等锁"),
    K118("微博支付-支付主动查询"),
    K119("微博支付-退款主动查询"),
    K120("零食很忙支付-退款主动查询"),
    K121("零食很忙支付-支付主动查询"),


    /** ------ 开发者定制业务key end---------*/
    ;
    private static final String SPACE = "PayCenter";
    private static final String SEPARATOR = "_";
	
    String desc;

    RedisKeyFactory(String desc){
        this.desc = desc;
    }

    public String join(Object... args) {
        StringBuilder key = new StringBuilder(SPACE).append(SEPARATOR).append(super.toString());
        for (Object arg :
                args) {
            key.append(SEPARATOR).append(arg);
        }
        return key.toString();
    }

    @Override
    public String toString(){
        return "PayCenter_" + this.name() + "_";
    }
}
