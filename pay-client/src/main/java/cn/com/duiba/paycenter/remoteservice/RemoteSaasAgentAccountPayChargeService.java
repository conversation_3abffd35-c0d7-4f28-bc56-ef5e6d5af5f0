package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.params.PayChargeExtraParams;
import cn.com.duiba.paycenter.result.PayChargeResult;


/**
 * 代理商账号充值相关服务
 *
 * <AUTHOR>
 */
@AdvancedFeignClient(qualifier = "remoteSaasAgentAccountChargeService")
public interface RemoteSaasAgentAccountPayChargeService {
    /**
     * 人工充值
     * <p>
     * 参数签名生成方式
     * Map&lt;String, String&gt; params = new HashMap<>();
     * params.put("agentId", agentId + "");
     * params.put("manualApplyId", manualApplyId + "");
     * params.put("money", money + "");
     * String checksign = cn.com.duiba.paycenter.util.SignUtil.sign(params);
     *
     * @param agentId       代理商ID
     * @param manualApplyId 人工充值单ID
     * @param money         充值金额
     * @param sign          数字签名
     * @param p             额外信息
     * @return 充值结果
     * @see cn.com.duiba.paycenter.util.SignUtil#sign
     */
    PayChargeResult chargeMoneyByManual(Long agentId, Long manualApplyId, Long money, String sign, PayChargeExtraParams p);

    /**
     * 当充值错误，需要减钱时，调用此方法
     * 正常情况不会使用此方法
     * <p>
     * 参数签名生成方式
     * Map&lt;String, String&gt; params = new HashMap<>();
     * params.put("agentId", agentId+"");
     * params.put("manualApplyId", manualApplyId+"");
     * params.put("money", money+"");
     * String checksign = cn.com.duiba.paycenter.util.SignUtil.sign(params);
     *
     * @param agentId       代理商ID
     * @param manualApplyId 人工充值单ID
     * @param money         充值金额
     * @param sign          数字签名
     * @param p             额外信息
     * @return 扣款结果
     * @see cn.com.duiba.paycenter.util.SignUtil#sign
     */
    PayChargeResult reduceMoneyByManual(Long agentId, Long manualApplyId, Long money, String sign, PayChargeExtraParams p);
}
