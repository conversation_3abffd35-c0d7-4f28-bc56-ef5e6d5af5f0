package cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/7/20 14:24
 */
public class UnionPayUmsChargeNotifyConstants implements Serializable {
    /**
     * 商户号
     */
    public static final String mid = "mid";

    /**
     * 终端号
     */
    public static final String tid = "tid";

    /**
     * 业务类型
     */
    public static final String instMid = "instMid";

    /**
     * 附加数据
     */
    public static final String attachedData = "attachedData";

    /**
     * 支付银行信息
     */
    public static final String bankCardNo = "bankCardNo";

    /**
     * 资金渠道
     */
    public static final String billFunds = "billFunds";

    /**
     * 资金渠道说明
     */
    public static final String billFundsDesc = "billFundsDesc";

    /**
     * 买家ID
     */
    public static final String buyerId = "buyerId";

    /**
     * 买家用户名
     */
    public static final String buyerUsername = "buyerUsername";

    /**
     * 实付金额
     */
    public static final String buyerPayAmount = "buyerPayAmount";

    /**
     * 订单金额，单位分
     */
    public static final String totalAmount = "totalAmount";

    /**
     * 开票金额
     */
    public static final String invoiceAmount = "invoiceAmount";

    /**
     * 商户订单号
     */
    public static final String merOrderId = "merOrderId";

    /**
     * 支付时间 格式yyyy-MM-dd HH:mm:ss
     */
    public static final String payTime = "payTime";

    /**
     * 实收金额
     */
    public static final String receiptAmount = "receiptAmount";

    /**
     * 支付银行卡参考号
     */
    public static final String refId = "refId";

    /**
     * 退款金额 退货交易
     */
    public static final String refundAmount = "refundAmount";

    /**
     * 退款说明 退货交易
     */
    public static final String refundDesc = "refundDesc";

    /**
     * 系统交易流水号
     */
    public static final String seqId = "seqId";

    /**
     * 结算日期 格式yyyy-MM-dd
     */
    public static final String settleDate = "settleDate";

    /**
     * 订单状态 取值说明
     */
    public static final String status = "status";

    /**
     * 买家子ID
     */
    public static final String subBuyerId = "subBuyerId";

    /**
     * 渠道订单号
     */
    public static final String targetOrderId = "targetOrderId";

    /**
     * 支付渠道 取值说明
     */
    public static final String targetSys = "targetSys";

    /**
     * 签名
     */
    public static final String sign = "sign";

    /**
     * 商户出资优惠金额
     */
    public static final String couponMerchantContribute = "couponMerchantContribute";

    /**
     * 其他出资优惠金额
     */
    public static final String couponOtherContribute = "couponOtherContribute";

    /**
     * 微信活动ID
     */
    public static final String activityIds = "activityIds";

    /**
     * 退货渠道订单号
     */
    public static final String refundTargetOrderId = "refundTargetOrderId";

    /**
     * 退货时间
     */
    public static final String refundPayTime = "refundPayTime";

    /**
     * 结算日期
     */
    public static final String refundSettleDate = "refundSettleDate";

    /**
     * 订单详情
     */
    public static final String orderDesc = "orderDesc";

    /**
     * 订单创建时间
     */
    public static final String createTime = "createTime";

    /**
     * 商户UUID
     */
    public static final String mchntUuid = "mchntUuid";

    /**
     * 转接系统
     */
    public static final String connectSys = "connectSys";

    /**
     * 商户所属分支机构代码
     */
    public static final String subInst = "subInst";

    /**
     * 联盟优惠金额
     */
    public static final String yxlmAmount = "yxlmAmount";

    /**
     * 退货外部订单号
     */
    public static final String refundExtOrderId = "refundExtOrderId";

    /**
     * 商品交易单号
     */
    public static final String goodsTradeNo = "goodsTradeNo";

    /**
     * 外部订单号
     */
    public static final String extOrderId = "extOrderId";

    /**
     * 担保交易状态
     */
    public static final String secureStatus = "secureStatus";

    /**
     * 担保完成金额
     */
    public static final String completeAmount = "completeAmount";

    /**
     * 退货订单号
     */
    public static final String refundOrderId = "refundOrderId";

    /**
     * 渠道优惠金额 单位：分
     */
    public static final String couponAmount = "couponAmount";

    /**
     * 随机字段，参与签名 随机value
     */
    public static final String randomKey = "randomKey";

    /**
     * 银行信息
     */
    public static final String bankInfo = "bankInfo";



}
