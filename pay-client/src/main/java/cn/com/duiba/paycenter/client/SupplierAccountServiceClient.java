package cn.com.duiba.paycenter.client;

import cn.com.duiba.paycenter.params.duibaaccount.SupplierAccountChangeParams;
import cn.com.duiba.paycenter.remoteservice.duibaaccount.RemoteSupplierAccountService;
import cn.com.duiba.paycenter.result.PayCenterResult;
import cn.com.duiba.paycenter.util.SignUtil;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 供应商余额账户client
 * author zhanghuifeng
 * date 2018/12/13-4:53 PM
 */
public class SupplierAccountServiceClient {

    @Resource
    private RemoteSupplierAccountService remoteSupplierAccountService;

    /**
     * 出账接口
     * @param param
     * @return
     */
    public RpcResult<PayCenterResult> reduceMoney(SupplierAccountChangeParams param){
        try {
            PayCenterResult ret = remoteSupplierAccountService.reduceMoney(param,getSign(param));
            return new RpcResult<>(ret);
        } catch (Exception e) {
            return new RpcResult<>(e);
        }
    }

    /**
     * 入账接口
     * @param param
     * @return
     */
    public RpcResult<PayCenterResult> addMoney(SupplierAccountChangeParams param){
        try {
            PayCenterResult ret = remoteSupplierAccountService.addMoney(param,getSign(param));
            return new RpcResult<>(ret);
        } catch (Exception e) {
            return new RpcResult<>(e);
        }
    }

    private String getSign(SupplierAccountChangeParams param){
        Map<String, String> params=new HashMap<>();
        params.put("supplier_id", Objects.toString(param.getSupplierId()));
        params.put("relationId", param.getRelationId());
        params.put("changeMoney", Objects.toString(param.getChangeMoney()));
        return SignUtil.sign(params);
    }

}
