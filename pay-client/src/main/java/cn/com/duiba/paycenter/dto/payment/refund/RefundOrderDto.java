package cn.com.duiba.paycenter.dto.payment.refund;

import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/02/26
 */
public class RefundOrderDto implements Serializable {
    private static final long serialVersionUID = 805300738844777818L;
    private Long id;
    /**
     * 支付系统退款流水号
     */
    private String orderNo;
    /**
     * 支付系统流水号
     */
    private String chargeOrderNo;
    /**
     * 第三方支付流水号
     */
    private String transactionNo;
    /**
     * 第三方退款流水号
     * 支付宝没有退款流水号
     */
    private String refundNo;
    /**
     * @see cn.com.duiba.paycenter.enums.RefundOrderStatusEnum
     */
    private Integer refundStatus;
    /**
     * 支付金额
     */
    private Integer chargeAmount;
    /**
     * 申请退款金额
     */
    private Integer applyRefundAmount;
    /**
     * 实际退款金额
     */
    private Integer refundAmount;
    private Long appId;
    private Date refundTime;
    private Integer bizType;
    private String bizOrderNo;
    private String failureCode;
    private String failureMsg;

    //主订单子订单号, 支持购物车按每种商品退
    private Long ordersItemId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getChargeOrderNo() {
        return chargeOrderNo;
    }

    public void setChargeOrderNo(String chargeOrderNo) {
        this.chargeOrderNo = chargeOrderNo;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getRefundNo() {
        return refundNo;
    }

    public void setRefundNo(String refundNo) {
        this.refundNo = refundNo;
    }

    public Integer getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(Integer refundStatus) {
        this.refundStatus = refundStatus;
    }

    public Integer getChargeAmount() {
        return chargeAmount;
    }

    public void setChargeAmount(Integer chargeAmount) {
        this.chargeAmount = chargeAmount;
    }

    public Integer getApplyRefundAmount() {
        return applyRefundAmount;
    }

    public void setApplyRefundAmount(Integer applyRefundAmount) {
        this.applyRefundAmount = applyRefundAmount;
    }

    public Integer getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Integer refundAmount) {
        this.refundAmount = refundAmount;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Date getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(Date refundTime) {
        this.refundTime = refundTime;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getFailureCode() {
        return failureCode;
    }

    public void setFailureCode(String failureCode) {
        this.failureCode = failureCode;
    }

    public String getFailureMsg() {
        return failureMsg;
    }

    public void setFailureMsg(String failureMsg) {
        this.failureMsg = failureMsg;
    }

    public Long getOrdersItemId() {
        return ordersItemId;
    }

    public void setOrdersItemId(Long ordersItemId) {
        this.ordersItemId = ordersItemId;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
