package cn.com.duiba.paycenter.dto;

import cn.com.duiba.paycenter.enums.OrderBizTypeEnum;
import cn.com.duiba.paycenter.enums.TradeStatusEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by zyj on 2018/4/27.
 */
public class AlipayOrderDetailDto implements Serializable{
    private static final long serialVersionUID = 620346693010642343L;
    private Long id;
    private String alipayOrderId;//支付宝订单id
    private OrderBizTypeEnum orderBizType;//订单业务类型
    private Long amount;//金额
    private Long selfOrderId;//自有订单id
    private TradeStatusEnum tradeStaus;//订单状态
    private String subMsg;//返回码描述
    private Date gmtCreate;
    private Date gmtModified;

    public String getSubMsg() {
        return subMsg;
    }

    public void setSubMsg(String subMsg) {
        this.subMsg = subMsg;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAlipayOrderId() {
        return alipayOrderId;
    }

    public void setAlipayOrderId(String alipayOrderId) {
        this.alipayOrderId = alipayOrderId;
    }

    public OrderBizTypeEnum getOrderBizType() {
        return orderBizType;
    }

    public void setOrderBizType(OrderBizTypeEnum orderBizType) {
        this.orderBizType = orderBizType;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Long getSelfOrderId() {
        return selfOrderId;
    }

    public void setSelfOrderId(Long selfOrderId) {
        this.selfOrderId = selfOrderId;
    }

    public TradeStatusEnum getTradeStaus() {
        return tradeStaus;
    }

    public void setTradeStaus(TradeStatusEnum tradeStaus) {
        this.tradeStaus = tradeStaus;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
}
