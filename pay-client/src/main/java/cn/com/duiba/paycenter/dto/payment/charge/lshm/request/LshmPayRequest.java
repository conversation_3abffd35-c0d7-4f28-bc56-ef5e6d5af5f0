package cn.com.duiba.paycenter.dto.payment.charge.lshm.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 支付请求数据传输对象 (DTO)
 */
@Data
public class LshmPayRequest implements Serializable {
    /**
     * 门店编码
     * 必填项
     */
    private String merchantNo;

    /**
     * 业务订单号 (商户 + 订单号幂等性)
     * 必填项
     */
    private String orderNo;

    /**
     * 业务订单类型
     * 必填项
     */
    private String orderType;

    /**
     * 支付方式
     * PM011-小程序微信支付
     * PM012-小程序支付宝支付
     * 必填项
     */
    private String payMethodNo;

    /**
     * 订单来源
     * 必填项
     */
    private String orderFrom;

    /**
     * 支付金额
     * 必填项
     */
    private BigDecimal payAmount;

    /**
     * 渠道路由，传1则使用优先级为第二的渠道
     * （除非有什么特殊，建议不要传）
     * 非必填项
     */
    private String channelSort;

    /**
     * 请求时间
     * 必填项
     */
    private Long currentTime;

    /**
     * 拓展字段
     * 非必填项
     */
    private String extendedField;

    /**
     * 拓展字段2
     * 非必填项
     */
    private String extendedField2;

    /**
     * 操作人
     * 必填项
     */
    private String operator;

    /**
     * 终端来源
     * 必填项
     */
    private String clientFrom;

    /**
     * 关单时间（如果不填写默认5分钟关单）
     * 非必填项
     */
    private Long closeOrderTime;

    /**
     * 微信用户openId，微信支付的时候必传
     * 非必填项
     */
    private String wxOpenId;

    /**
     * 支付宝用户ID, 支付宝支付的时候必传
     * 非必填项
     */
    private String aliPayUserId;

    /**
     * 回调地址
     * 非必填项
     */
    private String notifyUrl;


}