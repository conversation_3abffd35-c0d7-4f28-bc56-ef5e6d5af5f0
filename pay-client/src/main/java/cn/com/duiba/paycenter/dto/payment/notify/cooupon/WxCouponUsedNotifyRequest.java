package cn.com.duiba.paycenter.dto.payment.notify.cooupon;

import java.io.Serializable;

/**
 * Description:
 * <p>
 * date: 2022/10/11 5:52 下午
 *
 * <AUTHOR>
 */
public class WxCouponUsedNotifyRequest implements Serializable {
    private static final long serialVersionUID = -120020352129619710L;

    /**
     * 请求证书序列号
     */
    private String wechatpaySerial;
    /**
     * 请求签名
     */
    private String wechatpaySignature;
    /**
     * 请求时间戳
     */
    private String wechatpayTimestamp;
    /**
     * 请求随机字符串
     */
    private String wechatpayNonce;
    /**
     * 请求体
     */
    private String body;

    public String getWechatpaySerial() {
        return wechatpaySerial;
    }

    public WxCouponUsedNotifyRequest setWechatpaySerial(String wechatpaySerial) {
        this.wechatpaySerial = wechatpaySerial;
        return this;
    }

    public String getWechatpaySignature() {
        return wechatpaySignature;
    }

    public WxCouponUsedNotifyRequest setWechatpaySignature(String wechatpaySignature) {
        this.wechatpaySignature = wechatpaySignature;
        return this;
    }

    public String getWechatpayTimestamp() {
        return wechatpayTimestamp;
    }

    public WxCouponUsedNotifyRequest setWechatpayTimestamp(String wechatpayTimestamp) {
        this.wechatpayTimestamp = wechatpayTimestamp;
        return this;
    }

    public String getWechatpayNonce() {
        return wechatpayNonce;
    }

    public WxCouponUsedNotifyRequest setWechatpayNonce(String wechatpayNonce) {
        this.wechatpayNonce = wechatpayNonce;
        return this;
    }

    public String getBody() {
        return body;
    }

    public WxCouponUsedNotifyRequest setBody(String body) {
        this.body = body;
        return this;
    }
}
