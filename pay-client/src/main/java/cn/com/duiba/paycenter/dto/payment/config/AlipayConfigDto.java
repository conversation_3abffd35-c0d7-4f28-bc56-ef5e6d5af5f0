package cn.com.duiba.paycenter.dto.payment.config;

import cn.com.duiba.paycenter.validator.ChannelEnumCheck;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/12/01
 */
public class AlipayConfigDto implements Serializable {
    private static final long serialVersionUID = 7831626264634374188L;
    @NotNull
    private Long id;
    /**
     * mapi or openapi
     */
    private Integer version;
    /**
     * 签名类型
     *
     * @see cn.com.duiba.paycenter.enums.AlipaySignTypeEnum
     */
    private Integer signType;
    /**
     * 合作伙伴id 只有version为mapi时有
     */
    @Size(max = 16, message = "partnerId不能超过16位")
    private String partnerId;
    /**
     * 支付宝应用id,version为openid时有
     */
    @Size(max = 16, message = "aliAppId不能超过16位")
    private String aliAppId;
    /**
     * 兑吧应用id
     */
    private Long appId;
    /**
     * 公钥
     */
    @Size(max = 1024, message = "publicKey不能超过1024")
    private String publicKey;
    /**
     * 私钥
     */
    @Size(max = 2048, message = "privateKey不能超过2048")
    private String privateKey;
    /**
     * md5值 version为mapi时有
     */
    @Size(max = 64, message = "md5Key不能超过64")
    private String md5Key;
    /**
     * 渠道类型
     *
     * @see cn.com.duiba.paycenter.enums.ChannelEnum
     */
    @ChannelEnumCheck
    private String channelType;
    /**
     * 渠道名称
     */
    @Size(max = 64, message = "渠道名称不能超过64位")
    private String channelName;
    /**
     * 渠道状态
     *
     * @see cn.com.duiba.paycenter.enums.ChannelStatusEnum
     */
    private Integer channelStatus;
    /**
     * 渠道模式
     *
     * @see cn.com.duiba.paycenter.enums.ChannelModeEnum
     */
    private Integer channelMode;
    /**
     * 渠道费率
     * 精确到万一
     */
    private Integer rate;
    /**
     * 渠道描述
     */
    @Size(max = 255)
    private String description;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getSignType() {
        return signType;
    }

    public void setSignType(Integer signType) {
        this.signType = signType;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public String getAliAppId() {
        return aliAppId;
    }

    public void setAliAppId(String aliAppId) {
        this.aliAppId = aliAppId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getMd5Key() {
        return md5Key;
    }

    public void setMd5Key(String md5Key) {
        this.md5Key = md5Key;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public Integer getChannelStatus() {
        return channelStatus;
    }

    public void setChannelStatus(Integer channelStatus) {
        this.channelStatus = channelStatus;
    }

    public Integer getChannelMode() {
        return channelMode;
    }

    public void setChannelMode(Integer channelMode) {
        this.channelMode = channelMode;
    }

    public Integer getRate() {
        return rate;
    }

    public void setRate(Integer rate) {
        this.rate = rate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
