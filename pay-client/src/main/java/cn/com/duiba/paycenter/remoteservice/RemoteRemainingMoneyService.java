package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.DeveloperMoneyInfoDto;
import cn.com.duiba.paycenter.dto.RemainingMoneyDto;
import cn.com.duiba.paycenter.exception.CodeException;
import cn.com.duiba.service.exception.BusinessException;

import java.util.List;

@AdvancedFeignClient
public interface RemoteRemainingMoneyService {
	/**
	 * 查询开发者账户信息
	 * @deprecated 请使用新开发者应用纬度余额查询接口RemoteAppRemainingMoneyService
	 * @param developerId 开发者ID
	 * @return
	 */
	@Deprecated
	public RemainingMoneyDto findByDeveloperId(Long developerId);


	/**
	 * 批量查询开发者账户信息
	 * @param developerIds 开发者ID集合
	 * @return
	 */
	List<RemainingMoneyDto> findByDeveloperIdList(List<Long> developerIds);
	
	
	/**
	 * 查询开发者账户信息(加更新锁)
	 * @param developerId 开发者ID
	 * @return
	 */
	public RemainingMoneyDto findByDeveloperId4update(Long developerId);
	
	/**
	 * 扣钱
	 * @param money
	 * @param rm
	 * @return
	 * @throws Exception
	 */
	public boolean reduceMoney(RemainingMoneyDto rm,Integer money) throws BusinessException,CodeException;
	
	/**
	 * 加钱
	 * @param money 金额
	 * @return 
	 */
	public boolean addMoney(RemainingMoneyDto rm,Integer money) throws BusinessException,CodeException;
	
	public RemainingMoneyDto insert(RemainingMoneyDto remainingMoney);

	/**
	 * 批量获取开发者余额账户金额
	 * @param developerIds 开发者ID集合
	 * @return
	 */
	List<DeveloperMoneyInfoDto> batchFindBalance(List<Long> developerIds);
}
