package cn.com.duiba.paycenter.params;

import java.io.Serializable;
import java.util.Date;

/**
 * 账户变更查询接口
 * Created by xia<PERSON>uda on 2018/7/5.
 */
public class AccountChangeParam implements Serializable {
    private static final long serialVersionUID = 2012307126745025155L;

    private Long developerId;
    private Long appId;
    private String type;//变更类型，@see AccountDetailDto::type
    private Date startDate;
    private Date endDate;

    public Long getDeveloperId() {
        return developerId;
    }

    public void setDeveloperId(Long developerId) {
        this.developerId = developerId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
}
