package cn.com.duiba.paycenter.enums;

/**
 * <AUTHOR>
 * @date 2018/11/16
 */
public enum WxPaySignTypeEnum {
    HMAC_SHA256("HMAC-SHA256"),
    MD5("MD5")
    ;
    private String signType;
    WxPaySignTypeEnum(String signType) {
        this.signType = signType;
    }

    public String getSignType() {
        return signType;
    }

    @Override
    public String toString() {
        return this.signType;
    }
}
