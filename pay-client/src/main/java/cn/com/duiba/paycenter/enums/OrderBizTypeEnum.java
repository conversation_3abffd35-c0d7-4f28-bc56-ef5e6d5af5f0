package cn.com.duiba.paycenter.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by zyj on 2018/4/27.
 */
public enum OrderBizTypeEnum {
    DUIBA(1,"兑吧业务"),
    AGENT(2,"代理商");
    private static final Map<Integer, OrderBizTypeEnum> enumMap = new HashMap<>();
    static{
        for(OrderBizTypeEnum OrderBizTypeEnum : values()){
            enumMap.put(OrderBizTypeEnum.getValue(), OrderBizTypeEnum);
        }
    }

    public static OrderBizTypeEnum getByCode(Integer code) {
        OrderBizTypeEnum OrderBizTypeEnum = enumMap.get(code);
        if(OrderBizTypeEnum == null){
            throw new RuntimeException("不支持的业务商城类型");
        }
        return OrderBizTypeEnum;
    }
    private int    value;
    private String desc;

    OrderBizTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
