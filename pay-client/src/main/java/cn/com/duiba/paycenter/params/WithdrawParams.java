package cn.com.duiba.paycenter.params;

import cn.com.duiba.paycenter.enums.WithdrawBizTypeEnum;

import java.io.Serializable;

/**
 * Created by xiaoxuda on 2018/1/30.
 */
public class WithdrawParams implements Serializable {
    private static final long serialVersionUID = -5790146461082536556L;

    private Long developerId;
    private Long money;
    private WithdrawBizTypeEnum bizType;
    private Long bizId;
    private Long appId;//开发者提现时不填
    private Long consumerId;//开发者提现时不填
    private String memo;//备注
    private String description;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getDeveloperId() {
        return developerId;
    }

    public void setDeveloperId(Long developerId) {
        this.developerId = developerId;
    }

    public Long getMoney() {
        return money;
    }

    public void setMoney(Long money) {
        this.money = money;
    }

    public WithdrawBizTypeEnum getBizType() {
        return bizType;
    }

    public void setBizType(WithdrawBizTypeEnum bizType) {
        this.bizType = bizType;
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getConsumerId() {
        return consumerId;
    }

    public void setConsumerId(Long consumerId) {
        this.consumerId = consumerId;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}
