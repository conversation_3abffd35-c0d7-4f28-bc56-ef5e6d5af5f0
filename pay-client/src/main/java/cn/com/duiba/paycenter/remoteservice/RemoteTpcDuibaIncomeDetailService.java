package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.model.TpcDuibaIncomeDetailDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/25
 */

/**
 * 兑吧收入资金明细
 */
@AdvancedFeignClient
public interface RemoteTpcDuibaIncomeDetailService {
    public List<TpcDuibaIncomeDetailDO> findPageList(
            Date startDate, Date endDate, Long relationId, Integer offset, Integer max);

    public Long findPageCount(
            Date startDate, Date endDate, Long relationId);

    public Long findCountIncome();
}
