package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.wanda.WanDaWxChargeResult;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayChargeNotifyResponse;

/**
 * @author: pengyi
 * @description:
 * @date: 2022/8/22 下午3:05
 */
@AdvancedFeignClient
public interface RemoteWanDaWxPayNotifyService {

    /**
     * 万达-微信小程序支付异步通知
     * @param result
     * @return
     */
    WxPayChargeNotifyResponse orderNotify(WanDaWxChargeResult result);
}
