package cn.com.duiba.paycenter.constant;

/**
 * <AUTHOR>
 * @Title: SettleStatusEnum
 * @Description: 账户变更记录-订单状态
 * @date 2018/8/179:53
 */
public enum SettleStatusEnum {
    UNKNOWN_STATUS(0,"状态未知，历史数据，需要后续订正"),
    NOT_SETTLED_STATUS(1,"未结算状态，即资金属于冻结状态，订单状态为结束，只会出现在扣减资金的记录类型中"),
    SETTLED_STATUS(2,"成功状态，订单成功，扣减资金状态会变成成功"),
    PAY_BACK_STATUS(3,"回款状态，订单失败，会对应一条payback的回款记录产生");

    private Integer status;
    private String desc;

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    SettleStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
