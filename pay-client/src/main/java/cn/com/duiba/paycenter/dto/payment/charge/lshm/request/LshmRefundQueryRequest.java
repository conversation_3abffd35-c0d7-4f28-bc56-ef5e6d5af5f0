package cn.com.duiba.paycenter.dto.payment.charge.lshm.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 退款请求数据传输对象 (DTO)
 */
@Data
public class LshmRefundQueryRequest implements Serializable {

    private static final long serialVersionUID = -287277620869590256L;
    /**
     * 门店编码
     * 必填项
     */
    private String merchantNo;

    /**
     * 退款流水号
     * 必填项（与商户退款流水号必须传一个）
     */
    private String refundTradeNo;

    /**
     * 商户退款流水号
     * 必填项（与退款流水号必须传一个）
     */
    private String refundNo;

}