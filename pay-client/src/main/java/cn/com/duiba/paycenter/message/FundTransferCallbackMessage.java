package cn.com.duiba.paycenter.message;

import cn.com.duiba.paycenter.params.FundTransferRequestParams;
import cn.com.duiba.paycenter.result.FundTransferResult;

import java.io.Serializable;

/**
 * 转账回调消息
 * Created by xiaoxuda on 2017/12/13.
 */
public class FundTransferCallbackMessage implements Serializable {
    private static final long serialVersionUID = 5861296735613983120L;

    private FundTransferRequestParams requestParams;

    private FundTransferResult result;

    public FundTransferCallbackMessage(){}

    public FundTransferCallbackMessage(FundTransferRequestParams requestParams, FundTransferResult result){
        this.requestParams = requestParams;
        this.result = result;
    }

    public FundTransferRequestParams getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(FundTransferRequestParams requestParams) {
        this.requestParams = requestParams;
    }

    public FundTransferResult getResult() {
        return result;
    }

    public void setResult(FundTransferResult result) {
        this.result = result;
    }
}
