package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboChargeNotifyData;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboSettleData;

@AdvancedFeignClient
public interface RemoteWeiboPayService {
    /**
     * 支付/退款回调
     * @param notifyRequest 支付回调请求
     * @return 支付回调响应
     */
    WeiboChargeNotifyResponse chargeAndRefundNotify(WeiboChargeNotifyData notifyRequest) throws BizException;


    /**
     * 支付结算
     * @param bizOrderNo 结算业务号
     * @return 结算结果
     */
    WeiboSettleData settle(String bizOrderNo);
}
