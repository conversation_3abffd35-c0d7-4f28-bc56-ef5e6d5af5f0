package cn.com.duiba.paycenter.model;

import java.io.Serializable;
import java.util.Date;

public class TpcDlpIncomeChangeDO implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 星宿台活动支付
	 */
	public static final String RelationTypeProjectxAct="projectxAct";
	/**
	 * 主订单结算
	 */
	public static final String RelationTypeOrders="orders";
	/**
	 * 提现到支付宝/银行卡
	 */
	public static final String RelationTypeWithdrawCash="withdraw_cash";
	/**
	 * 转账至余额
	 */
	public static final String RelationTypeWithdrawRemaining="withdraw_remaining";
	
	/**
	 * RelationTypeOrders.XX
	 * 结算
	 */
	public static final String ActionTypeSettle="settle";
	/**
	 * RelationTypeWithdrawCash.XX
	 * 提现申请
	 */
	public static final String ActionTypeWithdraw="withdraw";
	/**
	 * RelationTypeWithdrawCash.XX
	 * 提现驳回返回
	 */
	public static final String ActionTypeWithdrawBack="withdraw_back";
	/**
	 * RelationTypeWithdrawRemaining.XX
	 * 转账至余额
	 */
	public static final String ActionTypeTransform="transform";

	/**
	 * 星速台活动 支付类型
	 */
	public static final String ActionTypeProjectx ="projectx";
	
	public static final String KindAdd="add";
	public static final String KindSub="sub";

	private Long id;
	private Long developerId;
	private String relationType;
	private Long relationId;
	private String actionType;
	private Long changeMoney;
	private String changeKind;
	private Long beforeBalance;
	private Long afterBalance;
	private Date gmtCreate;
	private Date gmtModified;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getDeveloperId() {
		return developerId;
	}
	public void setDeveloperId(Long developerId) {
		this.developerId = developerId;
	}
	public String getRelationType() {
		return relationType;
	}
	public void setRelationType(String relationType) {
		this.relationType = relationType;
	}
	public Long getRelationId() {
		return relationId;
	}
	public void setRelationId(Long relationId) {
		this.relationId = relationId;
	}
	public String getActionType() {
		return actionType;
	}
	public void setActionType(String actionType) {
		this.actionType = actionType;
	}
	public Long getChangeMoney() {
		return changeMoney;
	}
	public void setChangeMoney(Long changeMoney) {
		this.changeMoney = changeMoney;
	}
	public String getChangeKind() {
		return changeKind;
	}
	public void setChangeKind(String changeKind) {
		this.changeKind = changeKind;
	}
	public Long getBeforeBalance() {
		return beforeBalance;
	}
	public void setBeforeBalance(Long beforeBalance) {
		this.beforeBalance = beforeBalance;
	}
	public Long getAfterBalance() {
		return afterBalance;
	}
	public void setAfterBalance(Long afterBalance) {
		this.afterBalance = afterBalance;
	}
	public Date getGmtCreate() {
		return gmtCreate;
	}
	public void setGmtCreate(Date gmtCreate) {
		this.gmtCreate = gmtCreate;
	}
	public Date getGmtModified() {
		return gmtModified;
	}
	public void setGmtModified(Date gmtModified) {
		this.gmtModified = gmtModified;
	}
}
