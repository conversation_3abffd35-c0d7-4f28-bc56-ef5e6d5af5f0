package cn.com.duiba.paycenter.dto.payment.notify.cooupon;


import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Description:
 * <p>
 * date: 2022/10/9 3:41 下午
 *
 * <AUTHOR>
 */
public class WxCouponUsedNotifyResource implements Serializable {
    private static final long serialVersionUID = -120920352129619718L;

    /**
     * 对开启结果数据进行加密的加密算法，目前只支持AEAD_AES_256_GCM。
     */
    @JSONField(name = "algorithm")
    private String algorithm;
    /**
     * Base64编码后的开启/停用结果数据密文。
     */
    @JSONField(name = "ciphertext")
    private String ciphertext;
    /**
     * 附加数据
     */
    @JSONField(name = "associated_data")
    private String associatedData;
    /**
     * 加密使用的随机串。
     */
    @JSONField(name = "nonce")
    private String nonce;
    /**
     * 原始回调类型
     * 券的原始回调类型为coupon
     */
    @JSONField(name = "original_type")
    private String originalType;

    public String getAlgorithm() {
        return algorithm;
    }

    public WxCouponUsedNotifyResource setAlgorithm(String algorithm) {
        this.algorithm = algorithm;
        return this;
    }

    public String getCiphertext() {
        return ciphertext;
    }

    public WxCouponUsedNotifyResource setCiphertext(String ciphertext) {
        this.ciphertext = ciphertext;
        return this;
    }

    public String getAssociatedData() {
        return associatedData;
    }

    public WxCouponUsedNotifyResource setAssociatedData(String associatedData) {
        this.associatedData = associatedData;
        return this;
    }

    public String getNonce() {
        return nonce;
    }

    public WxCouponUsedNotifyResource setNonce(String nonce) {
        this.nonce = nonce;
        return this;
    }

    public String getOriginalType() {
        return originalType;
    }

    public WxCouponUsedNotifyResource setOriginalType(String originalType) {
        this.originalType = originalType;
        return this;
    }
}
