package cn.com.duiba.paycenter.service;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.params.PayChargeExtraParams;
import cn.com.duiba.paycenter.result.PayChargeResult;


/**
 * 账号充值相关服务，实际上是RemoteService
 * <AUTHOR>
 *
 */
@AdvancedFeignClient(qualifier = "payChargeService")
public interface PayChargeService {
	/**
	 * 人工充值
	 * @param developerId
	 * @param manualApplyId
	 * @param money
	 * @param memo
	 * @return
	 */
	PayChargeResult chargeMoneyByManual(Long developerId,Long manualApplyId,Long money,String sign,PayChargeExtraParams p);
	/**
	 * 贝付在线充值
	 * @param developerId
	 * @param onlineApplyId
	 * @param money
	 * @param memo
	 * @return
	 */
	PayChargeResult chargeMoneyByOnline(Long developerId,Long onlineApplyId,Long money,String sign,PayChargeExtraParams p);
	/**
	 * 当充值错误，需要减钱时，调用此方法
	 * 正常情况不会使用此方法
	 * @param developerId
	 * @param manualApplyId
	 * @param money
	 * @param memo
	 * @return
	 */
	PayChargeResult reduceMoneyByManual(Long developerId,Long manualApplyId,Long money,String sign,PayChargeExtraParams p);
}
