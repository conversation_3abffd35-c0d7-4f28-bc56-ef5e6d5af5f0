package cn.com.duiba.paycenter.dto.payment;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/11/09
 */
public class ChannelParam implements Serializable {
    private static final long serialVersionUID = 5766501398660040305L;
    /**
     * 支付宝商户的partnerId
     */
    private String alipayPartnerId;
    /**
     * 支付宝商户AppId
     */
    private String alipayAppId;
    /**
     * 支付宝账户
     */
    private String alipayAccount;
    /**
     * 老版本mapi
     * 新版本openapi
     */
    private String alipayVersion;
    /**
     * 签名类型
     * RSA
     * RSA2
     */
    private String alipaySignType;
    /**
     * MD5密钥
     * 只有alipayVersion为mapi时才填写
     */
    private String alipayMD5Key;
    /**
     * 支付宝私钥
     */
    private String alipayPrivateKey;
    /**
     * 支付宝公钥
     */
    private String alipayPublicKey;
    /**
     * 微信公众号AppId
     */
    private String wxAppId;
    /**
     * 微信支付商户Id
     */
    private String wxMchId;
    /**
     * 微信商户支付API密钥
     */
    private String wxApiKey;
    /**
     * 微信商户支付API密钥
     */
    private String wxAppSecret;
    /**
     * 微信证书
     * 退款和取消支付时使用
     */
    private String wxApiCert;

    public String getAlipayPartnerId() {
        return alipayPartnerId;
    }

    public void setAlipayPartnerId(String alipayPartnerId) {
        this.alipayPartnerId = alipayPartnerId;
    }

    public String getAlipayAppId() {
        return alipayAppId;
    }

    public void setAlipayAppId(String alipayAppId) {
        this.alipayAppId = alipayAppId;
    }

    public String getAlipayAccount() {
        return alipayAccount;
    }

    public void setAlipayAccount(String alipayAccount) {
        this.alipayAccount = alipayAccount;
    }

    public String getAlipayVersion() {
        return alipayVersion;
    }

    public void setAlipayVersion(String alipayVersion) {
        this.alipayVersion = alipayVersion;
    }

    public String getAlipaySignType() {
        return alipaySignType;
    }

    public void setAlipaySignType(String alipaySignType) {
        this.alipaySignType = alipaySignType;
    }

    public String getAlipayMD5Key() {
        return alipayMD5Key;
    }

    public void setAlipayMD5Key(String alipayMD5Key) {
        this.alipayMD5Key = alipayMD5Key;
    }

    public String getAlipayPrivateKey() {
        return alipayPrivateKey;
    }

    public void setAlipayPrivateKey(String alipayPrivateKey) {
        this.alipayPrivateKey = alipayPrivateKey;
    }

    public String getAlipayPublicKey() {
        return alipayPublicKey;
    }

    public void setAlipayPublicKey(String alipayPublicKey) {
        this.alipayPublicKey = alipayPublicKey;
    }

    public String getWxAppId() {
        return wxAppId;
    }

    public void setWxAppId(String wxAppId) {
        this.wxAppId = wxAppId;
    }

    public String getWxMchId() {
        return wxMchId;
    }

    public void setWxMchId(String wxMchId) {
        this.wxMchId = wxMchId;
    }

    public String getWxApiKey() {
        return wxApiKey;
    }

    public void setWxApiKey(String wxApiKey) {
        this.wxApiKey = wxApiKey;
    }

    public String getWxAppSecret() {
        return wxAppSecret;
    }

    public void setWxAppSecret(String wxAppSecret) {
        this.wxAppSecret = wxAppSecret;
    }

    public String getWxApiCert() {
        return wxApiCert;
    }

    public void setWxApiCert(String wxApiCert) {
        this.wxApiCert = wxApiCert;
    }
}
