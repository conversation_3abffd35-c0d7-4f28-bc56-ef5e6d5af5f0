package cn.com.duiba.paycenter.dto.payment.refund;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/12/11
 */
public class BaseRefundNotifyResponse implements Serializable {
    private static final long serialVersionUID = 6504355499769273181L;
    /**
     * 流程是否处理成功
     */
    private boolean success;

    /**
     * 流程失败原因
     */
    private String failReason;

    /**
     * 退款是否成功
     */
    private boolean refundSuccess;
    /**
     * 支付中心退款流水号
     */
    private String refundOrderNo;

    private Integer bizType;

    private String bizOrderNo;

    private String chargeOrderNo;

    //主订单子订单号, 支持购物车按每种商品退
    private Long ordersItemId;

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public boolean isRefundSuccess() {
        return refundSuccess;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public void setRefundSuccess(boolean refundSuccess) {
        this.refundSuccess = refundSuccess;
    }

    public String getRefundOrderNo() {
        return refundOrderNo;
    }

    public void setRefundOrderNo(String refundOrderNo) {
        this.refundOrderNo = refundOrderNo;
    }

    public String getChargeOrderNo() {
        return chargeOrderNo;
    }

    public void setChargeOrderNo(String chargeOrderNo) {
        this.chargeOrderNo = chargeOrderNo;
    }

    public Long getOrdersItemId() {
        return ordersItemId;
    }

    public void setOrdersItemId(Long ordersItemId) {
        this.ordersItemId = ordersItemId;
    }
}
