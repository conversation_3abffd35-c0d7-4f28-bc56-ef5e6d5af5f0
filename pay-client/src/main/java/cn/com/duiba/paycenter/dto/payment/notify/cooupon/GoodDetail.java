package cn.com.duiba.paycenter.dto.payment.notify.cooupon;


import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Description:
 * <p>
 * date: 2022/10/11 7:30 下午
 *
 * <AUTHOR>
 */
public class GoodDetail implements Serializable {
    private static final long serialVersionUID = 4751986982605622228L;
    /**
     * 单品编码
     */
    @JSONField(name = "goods_id")
    private String goodsId;
    /**
     * 单品数量
     */
    @JSONField(name = "quantity")
    private int quantity;
    /**
     * 单品单价
     */
    @JSONField(name = "price")
    private int price;
    /**
     *优惠金额
     */
    @JSONField(name = "discount_amount")
    private int discountAmount;

    public String getGoodsId() {
        return goodsId;
    }

    public GoodDetail setGoodsId(String goodsId) {
        this.goodsId = goodsId;
        return this;
    }

    public int getQuantity() {
        return quantity;
    }

    public GoodDetail setQuantity(int quantity) {
        this.quantity = quantity;
        return this;
    }

    public int getPrice() {
        return price;
    }

    public GoodDetail setPrice(int price) {
        this.price = price;
        return this;
    }

    public int getDiscountAmount() {
        return discountAmount;
    }

    public GoodDetail setDiscountAmount(int discountAmount) {
        this.discountAmount = discountAmount;
        return this;
    }
}
