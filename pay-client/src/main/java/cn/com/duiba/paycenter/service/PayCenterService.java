package cn.com.duiba.paycenter.service;

import java.util.List;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.model.AccountChangeRecordDO;
import cn.com.duiba.paycenter.model.DuibaAccountChangeRecordDO;
/**
 * 实际上是RemoteService
 */
@AdvancedFeignClient(qualifier = "payCenterService")
public interface PayCenterService {
	/**
	 * 获取开发者的余额，单位分
	 * @param developerId
	 * @return
	 */
	Long getBalance(Long developerId);
	/**
	 * 查询兑吧的账号余额，单位分
	 * @return
	 */
	Long getDuibaBalance();

	/**
	 * 根据相关id查询资金明细
	 * 
	 * 主要用于对账查询
	 * 
	 * @param relationType
	 * @param relationId
	 * @return
	 */
	List<AccountChangeRecordDO> find(String relationType,Long relationId);
	/**
	 * 根据相关id查询兑吧的资金明细 
	 * 
	 * 主要用于对账
	 * @param relationType
	 * @param relationId
	 * @return
	 */
	List<DuibaAccountChangeRecordDO> findDuibaRecords(String relationType,Long relationId);
	
	/**
	 * 对账需求
	 * 
	 * 获取大于lastId的记录batchSize条
	 * 
	 * @param lastId
	 * @param batchSize
	 * @return
	 */
	List<AccountChangeRecordDO> findAllGreaterId(Long lastId,int batchSize);

	/**
	 * 对账需求
	 * 
	 * 兑吧付款的订单，获取打印lastId的记录 batchSize条
	 * @param lastId
	 * @param batchSize
	 * @return
	 */
	List<DuibaAccountChangeRecordDO> findAllDuibaGreaterId(Long lastId,
			int batchSize);

	/**
	 *
	 * 订单成功之后，更新 支付记录明细  状态 为  订单成功状态
	 * */
	Boolean successPayRecord(Long orderId);
}
