package cn.com.duiba.paycenter.client;

import cn.com.duiba.paycenter.params.AppAccountChangeParams;
import cn.com.duiba.paycenter.remoteservice.RemoteAppAccountService;
import cn.com.duiba.paycenter.result.PayCenterResult;
import cn.com.duiba.paycenter.util.SignUtil;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 开发者余额账户client
 * author z<PERSON><PERSON><PERSON>
 * date 2018/11/7-4:53 PM
 */
public class AppAccountServiceClient {

    private static final String DEVELOPER_ID = "developerId";
    private static final String RELATION_ID = "relationId";
    private static final String MONEY = "money";
    private static final String APP_ID = "appId";
    @Resource
    private RemoteAppAccountService remoteAppAccountService;

    /**
     * 出账接口
     * @param param
     * @return
     */
    public RpcResult<PayCenterResult> reduceMoney(AppAccountChangeParams param){
        try {
            String sign= getSign(param);
            PayCenterResult ret = remoteAppAccountService.reduceMoney(param,sign);

            return new RpcResult<>(ret);
        } catch (Exception e) {
            return new RpcResult<>(e);
        }
    }

    /**
     * 入账接口
     * @param param
     * @return
     */
    public RpcResult<PayCenterResult> addMoney(AppAccountChangeParams param){
        try {
            String sign= getSign(param);
            PayCenterResult ret = remoteAppAccountService.addMoney(param,sign);
            return new RpcResult<>(ret);
        } catch (Exception e) {
            return new RpcResult<>(e);
        }
    }

    private String getSign(AppAccountChangeParams param){
        Map<String, String> params=new HashMap<>();
        params.put(DEVELOPER_ID, Objects.toString(param.getDeveloperId()));
        params.put(RELATION_ID, param.getRelationId());
        params.put(MONEY, Objects.toString(param.getMoney()));
        params.put(APP_ID,Objects.toString(param.getAppId()));
        return SignUtil.sign(params);
    }

}
