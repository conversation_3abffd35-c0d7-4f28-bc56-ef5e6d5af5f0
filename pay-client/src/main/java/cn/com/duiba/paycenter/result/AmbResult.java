package cn.com.duiba.paycenter.result;

import java.io.Serializable;
/**
 * 加钱购，支付中心操作的返回结果类型
 * <AUTHOR>
 *
 */
public class AmbResult implements Serializable{

	private static final long serialVersionUID = 1L;

	/**
	 * 错误编号
	 */
	private Integer errorCode;
	private Exception errorException;
	
	/**
	 * 此操作是否最终成功
	 */
	private boolean bizSuccess;
	/**
	 * 此操作当次是否成功
	 * 
	 * 因为很多接口对外提供的方法，只关心状态结果，不关心当次操作结果。
	 * 
	 * 如果 一个操作以前执行过了，再次发同样的请求过来，我们会返回finallySuccess=true ，认为操作是成功的。
	 * 但这个时候 currentSuccess = false， 表明这次请求是重复请求.
	 * 
	 */
	private Boolean currentSuccess;
	
	
	public AmbResult(boolean bizSuccess,Integer errorCode,Exception errorException){
		this.bizSuccess=bizSuccess;
		this.errorCode=errorCode;
		this.errorException=errorException;
		this.currentSuccess=false;
	}
	
	public AmbResult(boolean bizSuccess){
		this.bizSuccess=bizSuccess;
	}

	public AmbResult(){
	}



	public boolean isCurrentSuccess(){
		if(currentSuccess==null){
			return false;
		}
		return currentSuccess;
	}
	public void setCurrentSuccess(Boolean currentSuccess) {
		this.currentSuccess = currentSuccess;
	}


	public Integer getErrorCode() {
		return errorCode;
	}


	public Exception getErrorException() {
		return errorException;
	}

	public boolean isBizSuccess() {
		return bizSuccess;
	}
}
