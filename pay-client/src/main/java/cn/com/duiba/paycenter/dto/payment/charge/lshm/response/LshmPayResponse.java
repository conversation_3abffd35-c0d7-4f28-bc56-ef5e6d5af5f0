package cn.com.duiba.paycenter.dto.payment.charge.lshm.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 支付结果数据传输对象 (DTO)
 */
@Data
public class LshmPayResponse implements Serializable {

    /**
     * 账户中心交易订单号
     */
    private String tradeNo;

    /**
     * 商家请求流水号
     */
    private String merchantOrderNo;

    /**
     * 交易金额
     */
    private BigDecimal totalAmount;

    /**
     * 支付方式
     */
    private String payMethodNo;

    /**
     * 支付参数(拿这个参数唤起微信收银台｜支付宝收银台)
     */
    private String payParams;

    /**
     * 支付状态
     * 001 - 支付中
     * 003 - 待支付
     * 004 - 支付成功
     * FAIL - 失败
     */
    private String payStatus;

    /**
     * 订单支付失败原因
     */
    private String failReason;


}