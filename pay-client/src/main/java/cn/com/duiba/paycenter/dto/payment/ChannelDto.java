package cn.com.duiba.paycenter.dto.payment;

import cn.com.duiba.paycenter.dto.payment.config.AlipayConfigDto;
import cn.com.duiba.paycenter.dto.payment.config.WxPayConfigDto;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/06
 */
public class ChannelDto implements Serializable {
    private static final long serialVersionUID = -6996519670074458718L;

    private List<WxPayConfigDto> wxPayConfigDtoList;
    private List<AlipayConfigDto> alipayConfigDtoList;

    public List<WxPayConfigDto> getWxPayConfigDtoList() {
        return wxPayConfigDtoList;
    }

    public void setWxPayConfigDtoList(List<WxPayConfigDto> wxPayConfigDtoList) {
        this.wxPayConfigDtoList = wxPayConfigDtoList;
    }

    public List<AlipayConfigDto> getAlipayConfigDtoList() {
        return alipayConfigDtoList;
    }

    public void setAlipayConfigDtoList(List<AlipayConfigDto> alipayConfigDtoList) {
        this.alipayConfigDtoList = alipayConfigDtoList;
    }
}
