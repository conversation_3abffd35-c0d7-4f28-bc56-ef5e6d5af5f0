package cn.com.duiba.paycenter.validator;


import cn.com.duiba.paycenter.validator.impl.BizTypeEnumCheckConstraintValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 验证是否在枚举定义中
 * <AUTHOR>
 * @date 2018/11/13
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {BizTypeEnumCheckConstraintValidator.class})
public @interface BizTypeEnumCheck {
    String message() default "非法bizType";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };
}
