package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay.UnionPayUmsOrderQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay.UnionPayUmsOrderQueryResponse;
import cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay.UnionPayUmsRefundQueryRequest;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/7/19 09:53
 */
@AdvancedFeignClient
public interface RemoteUnionPayUmsQueryService {
    /**
     * 订单支付结果查询
     *
     * @param request 请求
     * @return {@link UnionPayUmsOrderQueryResponse}
     */
    UnionPayUmsOrderQueryResponse queryOrder(UnionPayUmsOrderQueryRequest request);


    /**
     * 查询退款
     *
     * @param merOrderId mer订单id
     * @return {@link UnionPayUmsOrderQueryResponse}
     */
    UnionPayUmsOrderQueryResponse queryRefund(UnionPayUmsRefundQueryRequest refundQueryRequest);

}
