package cn.com.duiba.paycenter.dto.payment.refund.icbc;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Created by xutao on 2020/4/26.
 */
public class IcbcRefundResponse implements Serializable {
    private static final long serialVersionUID = -8003183448819570698L;

    //信息标识
    @JSONField(name = "RETCODE")
    private String retCode;
    //返回标识信息描述
    @JSONField(name = "RETMSG")
    private String retMsg;
    //商户退款订单号
    @JSONField(name = "REFUNDNO")
    private String refundNo;
    //申请退款金额
    @JSONField(name = "REFUNDAMT")
    private String refundAmt;
    //清算日期
    @JSONField(name = "SETTLEDATE")
    private String settleDate;
    //付款银行名称
    @JSONField(name = "PAYBANKNAME")
    private String payBankName;
    //交易完成时间
    @JSONField(name = "TRANENDDATE")
    private String tranEndDate;

    public String getRetCode() {
        return retCode;
    }

    public void setRetCode(String retCode) {
        this.retCode = retCode;
    }

    public String getRetMsg() {
        return retMsg;
    }

    public void setRetMsg(String retMsg) {
        this.retMsg = retMsg;
    }

    public String getRefundNo() {
        return refundNo;
    }

    public void setRefundNo(String refundNo) {
        this.refundNo = refundNo;
    }

    public String getRefundAmt() {
        return refundAmt;
    }

    public void setRefundAmt(String refundAmt) {
        this.refundAmt = refundAmt;
    }

    public String getSettleDate() {
        return settleDate;
    }

    public void setSettleDate(String settleDate) {
        this.settleDate = settleDate;
    }

    public String getPayBankName() {
        return payBankName;
    }

    public void setPayBankName(String payBankName) {
        this.payBankName = payBankName;
    }

    public String getTranEndDate() {
        return tranEndDate;
    }

    public void setTranEndDate(String tranEndDate) {
        this.tranEndDate = tranEndDate;
    }
}
