package cn.com.duiba.paycenter.enums;

/**
 * 微信现金红包-红包类型枚举
 *
 * <AUTHOR>
 * @date 2020/04/15
 */
public enum RedPacketTypeEnum {
    GROUP("GROUP", "裂变红包"),
    NORMAL("NORMAL", "普通红包");

    /**
     * 类型编码
     */
    private String code;

    /**
     * 类型描述
     */
    private String desc;


    RedPacketTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
