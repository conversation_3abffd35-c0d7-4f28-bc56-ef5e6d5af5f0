package cn.com.duiba.paycenter.dto.payment.config;

import java.io.Serializable;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Map;

/**
 * @date:2022/1/12 15:40
 * @author:zhaozhanghai
 * @Description: 兑吧直播宝付配置
 */
public class DuiBaLiveBaofuConfigDto implements Serializable {


    /**
     * 机构号/平台号
     */
    private String orgNo;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 终端号
     */
    private String terminalNo;

    /**
     * 回调通知通知地址
     */
    private String notifyUrl;

    /**
     * 退款回调通知通知地址
     */
    private String refundNotifyUrl;

    /**
     * appId - SimId [{"simId":"","appId":""}]
     */
    private String appIdSimIdString;

    /**
     * 微信appId 和 simId对应关系
     */
    private Map<String, String> appIdSimIdMap;

    /**
     * 商户私钥地址
     */
    private String pfxpath;

    /**
     * 宝付公钥地址
     */
    private String cerpath;

    /**
     * 私钥密码
     */
    private String pfxpwd;

    /**
     * 用户预注册地址
     */
    private String consumerPreRegisterUrl;

    /**
     * 支付url
     */
    private String payUrl;

    /**
     * 退款url
     */
    private String refundUrl ;

    /**
     * 3.2.2申请开户
     */
    private String applyOpenUrl;

    /**
     * 3.2.2上传资料
     */
    private String uploadDataUrl;

    /**
     * 开户通知url
     */
    private String openAccountNotifyUrl;


    /**
     * 余额接口
     */
    private String balanceUrl;


    /**
     * 提现接口
     */
    private String merchantWithdrawUrl="https://account.baofoo.com/api/order/v3.0.0/merchantWithdraw";

    /**
     * 获取已绑定卡信息
     */
    private String findBindBankCardsUrl="https://account.baofoo.com/api/cust/v2.0.0/findBindBankCards";
    /**
     * 商户提现通知回调
     */
    private  String merchantWithdrawNotifyUrl="https://s.duibatest.com.cn/supplier/baofu/withdraw/notify";



    private  PrivateKey privateKey;

    private  PublicKey publicKey;

    public PrivateKey getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(PrivateKey privateKey) {
        this.privateKey = privateKey;
    }

    public PublicKey getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(PublicKey publicKey) {
        this.publicKey = publicKey;
    }

    public String getConsumerPreRegisterUrl() {
        return consumerPreRegisterUrl;
    }

    public void setConsumerPreRegisterUrl(String consumerPreRegisterUrl) {
        this.consumerPreRegisterUrl = consumerPreRegisterUrl;
    }

    public String getMerchantWithdrawNotifyUrl() {
        return merchantWithdrawNotifyUrl;
    }

    public void setMerchantWithdrawNotifyUrl(String merchantWithdrawNotifyUrl) {
        this.merchantWithdrawNotifyUrl = merchantWithdrawNotifyUrl;
    }

    public String getMerchantWithdrawUrl() {
        return merchantWithdrawUrl;
    }

    public void setMerchantWithdrawUrl(String merchantWithdrawUrl) {
        this.merchantWithdrawUrl = merchantWithdrawUrl;
    }

    public String getFindBindBankCardsUrl() {
        return findBindBankCardsUrl;
    }

    public void setFindBindBankCardsUrl(String findBindBankCardsUrl) {
        this.findBindBankCardsUrl = findBindBankCardsUrl;
    }



    public String getBalanceUrl() {
        return balanceUrl;
    }

    public void setBalanceUrl(String balanceUrl) {
        this.balanceUrl = balanceUrl;
    }

    public String getOpenAccountNotifyUrl() {
        return openAccountNotifyUrl;
    }

    public void setOpenAccountNotifyUrl(String openAccountNotifyUrl) {
        this.openAccountNotifyUrl = openAccountNotifyUrl;
    }

    public String getApplyOpenUrl() {
        return applyOpenUrl;
    }

    public void setApplyOpenUrl(String applyOpenUrl) {
        this.applyOpenUrl = applyOpenUrl;
    }

    public String getUploadDataUrl() {
        return uploadDataUrl;
    }

    public void setUploadDataUrl(String uploadDataUrl) {
        this.uploadDataUrl = uploadDataUrl;
    }

    public String getRefundUrl() {
        return refundUrl;
    }

    public void setRefundUrl(String refundUrl) {
        this.refundUrl = refundUrl;
    }

    public String getPayUrl() {
        return payUrl;
    }

    public void setPayUrl(String payUrl) {
        this.payUrl = payUrl;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getOrgNo() {
        return orgNo;
    }

    public void setOrgNo(String orgNo) {
        this.orgNo = orgNo;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getTerminalNo() {
        return terminalNo;
    }

    public void setTerminalNo(String terminalNo) {
        this.terminalNo = terminalNo;
    }

    public String getAppIdSimIdString() {
        return appIdSimIdString;
    }

    public void setAppIdSimIdString(String appIdSimIdString) {
        this.appIdSimIdString = appIdSimIdString;
    }

    public Map<String, String> getAppIdSimIdMap() {
        return appIdSimIdMap;
    }

    public void setAppIdSimIdMap(Map<String, String> appIdSimIdMap) {
        this.appIdSimIdMap = appIdSimIdMap;
    }

    public String getPfxpath() {
        return pfxpath;
    }

    public void setPfxpath(String pfxpath) {
        this.pfxpath = pfxpath;
    }

    public String getCerpath() {
        return cerpath;
    }

    public void setCerpath(String cerpath) {
        this.cerpath = cerpath;
    }

    public String getPfxpwd() {
        return pfxpwd;
    }

    public void setPfxpwd(String pfxpwd) {
        this.pfxpwd = pfxpwd;
    }

    public String getRefundNotifyUrl() {
        return refundNotifyUrl;
    }

    public void setRefundNotifyUrl(String refundNotifyUrl) {
        this.refundNotifyUrl = refundNotifyUrl;
    }

}
