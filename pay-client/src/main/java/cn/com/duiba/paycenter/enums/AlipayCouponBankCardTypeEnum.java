package cn.com.duiba.paycenter.enums;

public enum AlipayCouponBankCardTypeEnum {


    CREDIT(1,"信用卡"),
    DEBIT(2,"借记卡"),
    DEBIT_CREDIT(3,"不限制"),
    ;

    private Integer code;

    private String desc;

    AlipayCouponBankCardTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AlipayCouponBankCardTypeEnum getCardType(Integer code) {
        if (code == null) {
            return null;
        }
        for (AlipayCouponBankCardTypeEnum bankCardType : AlipayCouponBankCardTypeEnum.values()) {
            if (bankCardType.getCode().equals(code)) {
                return bankCardType;
            }
        }
        return null;
    }
}
