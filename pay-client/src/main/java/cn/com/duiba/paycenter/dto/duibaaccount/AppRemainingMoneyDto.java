package cn.com.duiba.paycenter.dto.duibaaccount;

import cn.com.duiba.paycenter.enums.duibaaccount.AppAccountTypeEnum;

import java.io.Serializable;

/**
 * 开发者账户实体
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/6-3:38 PM
 */
public class AppRemainingMoneyDto implements Serializable {

    private static final long serialVersionUID = -290809813774536102L;
    private Long id;
    /**
     * 金额（分）
     */
    private Long money;
    /**
     * 版本号
     */
    private Long version;
    /**
     * 开发者ID
     */
    private Long developerId;

    /**
     * 应用appId
     */
    private Long appId;

    /**
     * 账户类型1：余额账户 2：待结算账户 3：标记账户
     * @see AppAccountTypeEnum
     */
    private Integer accountType;

    private String sign;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMoney() {
        return money;
    }

    public void setMoney(Long money) {
        this.money = money;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Long getDeveloperId() {
        return developerId;
    }

    public void setDeveloperId(Long developerId) {
        this.developerId = developerId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }
}
