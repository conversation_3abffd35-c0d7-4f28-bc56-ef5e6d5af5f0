package cn.com.duiba.paycenter.params;

import java.io.Serializable;

/**
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/7-11:32 AM
 */
public class AppAccountPayChargeParams implements Serializable {

    private static final long serialVersionUID = -3092405445357586729L;
    /**
     * 开发者ID
     */
    private Long developerId;

    /**
     * 开发者应用ID
     */
    private Long appId;

    /**
     * 业务ID
     */
    private String relationId;

    /**
     * 变更金额
     */
    private Long changeMoney;

    /**
     * 备注
     */
    private String memo;

    /**
     * 操作员ID
     */
    private Long operatorId;

    public Long getDeveloperId() {
        return developerId;
    }

    public void setDeveloperId(Long developerId) {
        this.developerId = developerId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getChangeMoney() {
        return changeMoney;
    }

    public void setChangeMoney(Long changeMoney) {
        this.changeMoney = changeMoney;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }
}
