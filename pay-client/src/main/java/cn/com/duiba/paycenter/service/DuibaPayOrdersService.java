package cn.com.duiba.paycenter.service;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.params.DuibaPayOrdersExtraParams;
import cn.com.duiba.paycenter.result.PayOrdersResult;
/**
 * 兑吧付款的接口，实际上是RemoteService
 * <AUTHOR>
 *
 */
@AdvancedFeignClient(qualifier = "duibaPayOrdersService")
public interface DuibaPayOrdersService {

	/**
	 * 主订单下单时的付款接口
	 * 
	 * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
	 * 
	 * @param developerId
	 * @param orderId
	 * @param money
	 */
	PayOrdersResult payOrder(Long orderId,Long money,String sign,DuibaPayOrdersExtraParams p) ;
	/**
	 * 主订单失败还款的接口
	 * 
	 * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
	 * 
	 * @param developerId
	 * @param orderId
	 * @param money
	 * @return
	 */
	PayOrdersResult backpayOrder(Long orderId,Long money,String sign) ;
	/**
	 * 检查一个操作是否执行成功
	 * @param orderId
	 * @return
	 * @throws PayCenterException
	 */
	boolean checkActionSuccess(Long orderId,String actionType);
}
