package cn.com.duiba.paycenter.remoteservice.duibaaccount;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.duibaaccount.AppRemainingMoneyDto;
import cn.com.duiba.paycenter.dto.DeveloperMoneyInfoDto;
import cn.com.duiba.paycenter.enums.duibaaccount.AppAccountTypeEnum;

import java.util.List;

/**
 * 开发者应用账户
 * author z<PERSON><PERSON><PERSON>
 * date 2018-11-27-14:44
 */
@AdvancedFeignClient
public interface RemoteAppRemainingMoneyService {

    /**
     * 获取开发者应用账户金额
     * @param developerId  开发者ID
     * @param appId        应用ID
     * @param accountType  开发者账户类型
     * @see cn.com.duiba.paycenter.enums.duibaaccount.AppAccountTypeEnum
     * @return 分
     */
    Long findMoney(Long developerId, Long appId, Integer accountType);

    /**
     * 批量查询
     * @param appIds
     * @param accountType
     * @see cn.com.duiba.paycenter.enums.duibaaccount.AppAccountTypeEnum
     * @return
     */
    List<AppRemainingMoneyDto> findByAppIdsAndType(List<Long> appIds, Integer accountType);

    /**
     * 获取存在应用纬度账户的开发者ID
     * @return
     */
    List<Long> findAllDeveloperId();

    /**
     * 获取开发者下所有应用纬度账户金额总和
     * @param developerIds 开发者ID集合
     * @param accountTypes 账户类型集合
     * @see AppAccountTypeEnum
     * @return
     */
    List<DeveloperMoneyInfoDto> findDeveloperMoney(List<Long> developerIds, List<Integer> accountTypes);
}
