package cn.com.duiba.paycenter.model;

import java.io.Serializable;
import java.util.Date;

public class TpcConsumerPayRecordDO implements Serializable{

	private static final long serialVersionUID = 1L;
	
	public static final String KindAdd="add";
	public static final String KindSub="sub";
	
	public static final String OrdersSourceDuiba="duiba";
	public static final String OrdersSourceDlp="dlp";

	private Long id;
	private Long consumerId;
	private Long orderId;
	private Long appId;
	private Long developerId;
	private Long money;
	private String kind;
	private String ordersSource;
	private Date gmtCreate;
	private Date gmtModified;

	//主订单子订单号
	private Long ordersItemId;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getConsumerId() {
		return consumerId;
	}
	public void setConsumerId(Long consumerId) {
		this.consumerId = consumerId;
	}
	public Long getOrderId() {
		return orderId;
	}
	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}
	public Long getAppId() {
		return appId;
	}
	public void setAppId(Long appId) {
		this.appId = appId;
	}
	public Long getDeveloperId() {
		return developerId;
	}
	public void setDeveloperId(Long developerId) {
		this.developerId = developerId;
	}
	public Long getMoney() {
		return money;
	}
	public void setMoney(Long money) {
		this.money = money;
	}
	public String getKind() {
		return kind;
	}
	public void setKind(String kind) {
		this.kind = kind;
	}
	public String getOrdersSource() {
		return ordersSource;
	}
	public void setOrdersSource(String ordersSource) {
		this.ordersSource = ordersSource;
	}
	public Date getGmtCreate() {
		return gmtCreate;
	}
	public void setGmtCreate(Date gmtCreate) {
		this.gmtCreate = gmtCreate;
	}
	public Date getGmtModified() {
		return gmtModified;
	}
	public void setGmtModified(Date gmtModified) {
		this.gmtModified = gmtModified;
	}

	public Long getOrdersItemId() {
		return ordersItemId;
	}

	public void setOrdersItemId(Long ordersItemId) {
		this.ordersItemId = ordersItemId;
	}
}
