package cn.com.duiba.paycenter.dto.payment.refund.shouxin;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date 2019/11/26
 */
public class ShouxinPayRefundRequest implements Serializable {

    private static final long serialVersionUID = -1515923820180964466L;
    /**
     * 退款金额
     */
    @NotNull(message = "金额不能为空")
    @Min(value = 1, message = "金额必须大于1分")
    private Integer amount;
    /**
     * 主订单号
     */
    @NotNull(message = "主订单号不能为空")
    private String bizOrderNo;

    /**
     * 开发者用户id
     */
    @NotNull(message = "开发者用户id不能为空")
    private String uid;

    /**
     * 异步跳转地址(包含域名)
     */
    @NotNull(message = "异步通知url不能为空")
    private String notifyUrl;

    //主订单子订单号, 支持购物车按每种商品退
    private Long ordersItemId;

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public Long getOrdersItemId() {
        return ordersItemId;
    }

    public void setOrdersItemId(Long ordersItemId) {
        this.ordersItemId = ordersItemId;
    }
}
