package cn.com.duiba.paycenter.result;

import java.io.Serializable;

/**
 * Created by xiaoxuda on 2018/1/30.
 */
public class WithdrawResult implements Serializable{
    private static final long serialVersionUID = -3880769345793501360L;

    public static final Integer RESUBMIT = 1;

    private Boolean success;
    private Integer errCode;
    private String description;

    public static WithdrawResult parseSuccess(){
        WithdrawResult result = new WithdrawResult();
        result.setSuccess(true);
        return result;
    }

    public static WithdrawResult parseFail(Integer errCode, String description){
        WithdrawResult result = new WithdrawResult();
        result.setSuccess(false);
        result.setErrCode(errCode);
        result.setDescription(description);
        return result;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public Integer getErrCode() {
        return errCode;
    }

    public void setErrCode(Integer errCode) {
        this.errCode = errCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
