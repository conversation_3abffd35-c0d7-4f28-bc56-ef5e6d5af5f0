package cn.com.duiba.paycenter.model;

import java.io.Serializable;
import java.util.Date;

public class DuibaAccountChangeLogDO implements Serializable{
	private static final long serialVersionUID = 1L;
	private Long id;
	private Long developerId;
	private Long appId;
	private String relationType;
	private Long relationId;
	private String actionType;
	private Long changeMoney;
	private String changeKind;
	private Long beforeBalance;
	private Long afterBalance;
	private Date gmtCreate;
	private Date gmtModified;
	
	private Integer status;
	private String failReason;
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getDeveloperId() {
		return developerId;
	}
	public void setDeveloperId(Long developerId) {
		this.developerId = developerId;
	}
	public Long getAppId() {
		return appId;
	}
	public void setAppId(Long appId) {
		this.appId = appId;
	}
	public String getRelationType() {
		return relationType;
	}
	public void setRelationType(String relationType) {
		this.relationType = relationType;
	}
	public Long getRelationId() {
		return relationId;
	}
	public void setRelationId(Long relationId) {
		this.relationId = relationId;
	}
	public String getActionType() {
		return actionType;
	}
	public void setActionType(String actionType) {
		this.actionType = actionType;
	}
	public Long getChangeMoney() {
		return changeMoney;
	}
	public void setChangeMoney(Long changeMoney) {
		this.changeMoney = changeMoney;
	}
	public String getChangeKind() {
		return changeKind;
	}
	public void setChangeKind(String changeKind) {
		this.changeKind = changeKind;
	}
	public Long getBeforeBalance() {
		return beforeBalance;
	}
	public void setBeforeBalance(Long beforeBalance) {
		this.beforeBalance = beforeBalance;
	}
	public Long getAfterBalance() {
		return afterBalance;
	}
	public void setAfterBalance(Long afterBalance) {
		this.afterBalance = afterBalance;
	}
	public Date getGmtCreate() {
		return gmtCreate;
	}
	public void setGmtCreate(Date gmtCreate) {
		this.gmtCreate = gmtCreate;
	}
	public Date getGmtModified() {
		return gmtModified;
	}
	public void setGmtModified(Date gmtModified) {
		this.gmtModified = gmtModified;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getFailReason() {
		return failReason;
	}
	public void setFailReason(String failReason) {
		this.failReason = failReason;
	}
}
