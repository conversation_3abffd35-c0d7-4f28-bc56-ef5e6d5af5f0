package cn.com.duiba.paycenter.dto;

import java.io.Serializable;


/**
 * 流水查询类
 */
public class ADeveloperFlowQuery implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 5007444183695066482L;

	private Long flowId;

	private Long developerId;

	private Integer offset; // 分页偏移量

	private Integer max; // 每页个数

	public Long getFlowId() {
		return flowId;
	}

	public void setFlowId(Long flowId) {
		this.flowId = flowId;
	}

	public Long getDeveloperId() {
		return developerId;
	}

	public void setDeveloperId(Long developerId) {
		this.developerId = developerId;
	}

	public Integer getOffset() {
		return offset;
	}

	public void setOffset(Integer offset) {
		this.offset = offset;
	}

	public Integer getMax() {
		return max;
	}

	public void setMax(Integer max) {
		this.max = max;
	}

}
