package cn.com.duiba.paycenter.constant;

public class PayCenterErrorCode {
	/**
	 * 其他异常
	 */
	public static final int CodeOther=0;
	/**
	 * 余额不足
	 */
	public static final int CodeBalanceNotEnough=1;
	/**
	 * 参数错误，有参数数据校验不一致
	 */
	public static final int CodeParamsNotRight=3;
	/**
	 * 余额不可用
	 */
	public static final int CodeBalanceDisabled=4;
	/**
	 * 参数签名错误
	 */
	public static final int CodeParamsSignError=5;

	/**
	 * 不支持的转账类型
	 */
	public static final int CodeUnsuportFundTransfer=6;

	/**
	 * 业务重复执行
	 */
	public static final int CodeDuplicateOperation=7;

	/**
	 * 参数异常
	 */
	public static final int PayMoneyParamErrir=8;

	private PayCenterErrorCode() {
	}
}
