package cn.com.duiba.paycenter.dto;

import java.io.Serializable;
import java.util.Date;

public class RemainingMoneyDto implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 2111140824398843221L;
	private Long id;
	private Long developerId;
	private Integer money;
	private Long version;
	private String sign;
	
	private Date gmtCreate;
	private Date gmtModified;
	
	public RemainingMoneyDto() {
		// Do nothing
	}


	public RemainingMoneyDto(boolean init4insert) {
		if (init4insert) {
			gmtCreate = new Date();
			gmtModified = gmtCreate;
		}
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getDeveloperId() {
		return developerId;
	}

	public void setDeveloperId(Long developerId) {
		this.developerId = developerId;
	}

	public Integer getMoney() {
		return money;
	}

	public void setMoney(Integer money) {
		this.money = money;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public Date getGmtCreate() {
		return gmtCreate;
	}

	public void setGmtCreate(Date gmtCreate) {
		this.gmtCreate = gmtCreate;
	}

	public Date getGmtModified() {
		return gmtModified;
	}

	public void setGmtModified(Date gmtModified) {
		this.gmtModified = gmtModified;
	}
}
