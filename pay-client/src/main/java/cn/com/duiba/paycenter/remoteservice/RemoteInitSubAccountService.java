package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.SubAccountDto;

import java.util.List;

/**
 * @Description
 * @Date 2023/2/22 15:50
 * <AUTHOR>
 */
@AdvancedFeignClient
public interface RemoteInitSubAccountService {

    /**
     * 初始化子账户
     * @param subAccountDtoList
     * @throws BizException 插入失败
     */
    void initSubAccount(List<SubAccountDto> subAccountDtoList) throws BizException;
}
