package cn.com.duiba.paycenter.service;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.model.AccountChangeRecordDO;
import cn.com.duiba.paycenter.params.PayOrdersExtraParams;
import cn.com.duiba.paycenter.result.PayOrdersResult;

import java.util.List;

/**
 * 主订单付款相关接口服务，实际上是RemoteService
 * <AUTHOR>
 *
 */
@AdvancedFeignClient(qualifier = "payOrdersService")
public interface PayOrdersService {

	
	/**
	 * 主订单下单时的付款接口
	 * 
	 * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
	 * 
	 * @param developerId
	 * @param orderId
	 * @param money
	 */
	PayOrdersResult payOrder(Long developerId,Long orderId,Long money,String sign,PayOrdersExtraParams p) ;
	/**
	 * 主订单失败还款的接口
	 * 
	 * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
	 * 
	 * @param developerId
	 * @param orderId
	 * @param money
	 * @return
	 */
	PayOrdersResult backpayOrder(Long developerId,Long orderId,Long money,String sign,PayOrdersExtraParams p) ;
	/**
	 * 检查一个操作是否执行成功
	 * @param orderId
	 * @return
	 * @throws PayCenterException
	 */
	boolean checkActionSuccess(Long orderId,String actionType);


	/**
	 *批量更新开发者 账户变更记录 字段 settleStatus
	 * */
	boolean batchUpdateRecordStatus(List<AccountChangeRecordDO> list);

	/**
	 *根据 开发者账户变更记录状态，批量查询
	 * */
	List<AccountChangeRecordDO> findByStatusLimit(Integer settleStatus,Integer limit);

	/**
	 * 取消发货或者售后返回库存
	 * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
	 * @param developerId
	 * @param orderId
	 * @param money
	 * @return
	 */
	PayOrdersResult backpayOrderByOrderItemIds(Long developerId,Long orderId,Long money, String orderItemIds,String sign,PayOrdersExtraParams p) ;
}
