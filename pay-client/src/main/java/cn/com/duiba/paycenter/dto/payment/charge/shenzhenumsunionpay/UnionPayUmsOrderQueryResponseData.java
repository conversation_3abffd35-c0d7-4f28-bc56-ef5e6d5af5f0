package cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 订单交易查询
 * @date 2023/7/19 10:40
 */
public class UnionPayUmsOrderQueryResponseData implements Serializable {
    private String errCode; // 平台错误码
    private String errMsg; // 平台错误信息
    private String msgId; // 消息ID，原样返回
    private String responseTimeStamp; // 报文应答时间 格式yyyy-MM-dd HH:mm:ss
    private String srcReserve; // 请求系统预留字段
    private String mid; // 商户号，原样返回
    private String tid; // 终端号，原样返回
    private String instMid; // 业务类型，原样返回 YUEDANDEFAULT
    private String seqId; // 平台流水号
    private String settleRefId; // 清分ID 如果来源方传了bankRefId就等于bankRefId，否则等于seqId
    private String refId; // 检索参考号 用在银联体系交易中
    private String status; // 交易状态
    private long totalAmount; // 支付总金额
    private String merName; // 商户名称
    private String merOrderId; // 商户订单号
    private String targetOrderId; // 目标平台单号
    private String targetSys; // 目标平台代码
    private String targetStatus; // 目标平台状态
    private String buyerId; // 买家ID
    private String targetMid; // 支付渠道商户号 各渠道情况不同，酌情转换
    private String bankCardNo; // 银行卡号
    private String bankInfo; // 银行信息
    private String billFunds; // 支付渠道列表 格式为：方式:金额（单位：分）
    private String billFundsDesc; // 支付渠道描述
    private long buyerPayAmount; // 买家付款的金额 支付宝会有
    private String buyerUsername; // 买家用户名
    private long couponAmount; // 网付计算的优惠金额
    private long invoiceAmount; // 交易中可给用户开具发票的金额
    private String payTime; // 支付时间 格式yyyy-MM-dd HH:mm:ss
    private long receiptAmount; // 商户实收金额 支付宝会有
    private String settleDate; // 结算日期 格式yyyy-MM-dd
    private String subBuyerId; // 子买家ID 如微信的subOpenId
    private String activityIds; // 微信活动ID
    private long yxlmAmount; // 营销联盟优惠金额 仅享受联盟优惠的订单，查询返回
    private long couponMerchantContribute; // 商户出资优惠金额，目前支持微信返回，其他渠道产品规划中
    private long couponOtherContribute; // 其他出资优惠金额，目前支持微信返回，其他渠道产品规划中

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public String getResponseTimeStamp() {
        return responseTimeStamp;
    }

    public void setResponseTimeStamp(String responseTimeStamp) {
        this.responseTimeStamp = responseTimeStamp;
    }

    public String getSrcReserve() {
        return srcReserve;
    }

    public void setSrcReserve(String srcReserve) {
        this.srcReserve = srcReserve;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getInstMid() {
        return instMid;
    }

    public void setInstMid(String instMid) {
        this.instMid = instMid;
    }

    public String getSeqId() {
        return seqId;
    }

    public void setSeqId(String seqId) {
        this.seqId = seqId;
    }

    public String getSettleRefId() {
        return settleRefId;
    }

    public void setSettleRefId(String settleRefId) {
        this.settleRefId = settleRefId;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public long getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(long totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getMerName() {
        return merName;
    }

    public void setMerName(String merName) {
        this.merName = merName;
    }

    public String getMerOrderId() {
        return merOrderId;
    }

    public void setMerOrderId(String merOrderId) {
        this.merOrderId = merOrderId;
    }

    public String getTargetOrderId() {
        return targetOrderId;
    }

    public void setTargetOrderId(String targetOrderId) {
        this.targetOrderId = targetOrderId;
    }

    public String getTargetSys() {
        return targetSys;
    }

    public void setTargetSys(String targetSys) {
        this.targetSys = targetSys;
    }

    public String getTargetStatus() {
        return targetStatus;
    }

    public void setTargetStatus(String targetStatus) {
        this.targetStatus = targetStatus;
    }

    public String getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId;
    }

    public String getTargetMid() {
        return targetMid;
    }

    public void setTargetMid(String targetMid) {
        this.targetMid = targetMid;
    }

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public String getBankInfo() {
        return bankInfo;
    }

    public void setBankInfo(String bankInfo) {
        this.bankInfo = bankInfo;
    }

    public String getBillFunds() {
        return billFunds;
    }

    public void setBillFunds(String billFunds) {
        this.billFunds = billFunds;
    }

    public String getBillFundsDesc() {
        return billFundsDesc;
    }

    public void setBillFundsDesc(String billFundsDesc) {
        this.billFundsDesc = billFundsDesc;
    }

    public long getBuyerPayAmount() {
        return buyerPayAmount;
    }

    public void setBuyerPayAmount(long buyerPayAmount) {
        this.buyerPayAmount = buyerPayAmount;
    }

    public String getBuyerUsername() {
        return buyerUsername;
    }

    public void setBuyerUsername(String buyerUsername) {
        this.buyerUsername = buyerUsername;
    }

    public long getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(long couponAmount) {
        this.couponAmount = couponAmount;
    }

    public long getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(long invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public String getPayTime() {
        return payTime;
    }

    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    public long getReceiptAmount() {
        return receiptAmount;
    }

    public void setReceiptAmount(long receiptAmount) {
        this.receiptAmount = receiptAmount;
    }

    public String getSettleDate() {
        return settleDate;
    }

    public void setSettleDate(String settleDate) {
        this.settleDate = settleDate;
    }

    public String getSubBuyerId() {
        return subBuyerId;
    }

    public void setSubBuyerId(String subBuyerId) {
        this.subBuyerId = subBuyerId;
    }

    public String getActivityIds() {
        return activityIds;
    }

    public void setActivityIds(String activityIds) {
        this.activityIds = activityIds;
    }

    public long getYxlmAmount() {
        return yxlmAmount;
    }

    public void setYxlmAmount(long yxlmAmount) {
        this.yxlmAmount = yxlmAmount;
    }

    public long getCouponMerchantContribute() {
        return couponMerchantContribute;
    }

    public void setCouponMerchantContribute(long couponMerchantContribute) {
        this.couponMerchantContribute = couponMerchantContribute;
    }

    public long getCouponOtherContribute() {
        return couponOtherContribute;
    }

    public void setCouponOtherContribute(long couponOtherContribute) {
        this.couponOtherContribute = couponOtherContribute;
    }
}
