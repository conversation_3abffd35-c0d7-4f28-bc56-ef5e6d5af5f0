package cn.com.duiba.paycenter.dto.payment.charge.lshm.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
@Data
public class LshmPayQueryResponse implements Serializable {
    /**
     * 订单号
     */
    private String tradeNo;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 商户流水号
     */
    private String merchantOrderNo;

    /**
     * 支付开始时间
     */
    private Date payBeginTime;

    /**
     * 支付结束时间
     */
    private Date payEndTime;

    /**
     * 支付状态
     * 001 - 支付中
     * 003 - 待支付
     * 004 - 支付成功
     * FAIL - 失败
     */
    private String payStatus;

    /**
     * 退款状态
     * 001 - 退款中
     * 002 - 退款成功
     * 003 - 退款失败
     */
    private String refundStatus;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 拓展字段
     */
    private String extendedField;

    /**
     * 拓展字段2
     */
    private String extendedField2;
}
