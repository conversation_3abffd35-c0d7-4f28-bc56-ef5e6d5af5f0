package cn.com.duiba.paycenter.dto.payment.notify.cooupon;

import java.io.Serializable;

/**
 * Description:
 * <p> 包含了核销回调的所有信息（包括了已解密的数据）
 * date: 2022/10/12 10:29 上午
 *
 * <AUTHOR>
 */
public class WxCouponUsedMessage implements Serializable {
    private static final long serialVersionUID = -73083760545091794L;
    /**
     * 通知Id
     */
    private String id;
    /**
     * 通知创建的时间
     */
    private String createTime;
    /**
     * 通知的类型
     * 代金券用券回调通知的类型为COUPON.USE。
     */
    private String eventType;
    /**
     * 回调摘要
     */
    private String summary;

    /**
     * 兑吧appId
     */
    private Long appId;

    /**
     * 兑吧订单号
     */
    private Long orderNo;

    /**
     * 通知数据
     */
    private WxCouponUsedNotifyDetail wxCouponUsedNotifyDetail;

    public String getId() {
        return id;
    }

    public WxCouponUsedMessage setId(String id) {
        this.id = id;
        return this;
    }

    public String getCreateTime() {
        return createTime;
    }

    public WxCouponUsedMessage setCreateTime(String createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getEventType() {
        return eventType;
    }

    public WxCouponUsedMessage setEventType(String eventType) {
        this.eventType = eventType;
        return this;
    }

    public String getSummary() {
        return summary;
    }

    public WxCouponUsedMessage setSummary(String summary) {
        this.summary = summary;
        return this;
    }

    public WxCouponUsedNotifyDetail getWxCouponUsedNotifyDetail() {
        return wxCouponUsedNotifyDetail;
    }

    public WxCouponUsedMessage setWxCouponUsedNotifyDetail(WxCouponUsedNotifyDetail wxCouponUsedNotifyDetail) {
        this.wxCouponUsedNotifyDetail = wxCouponUsedNotifyDetail;
        return this;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Long orderNo) {
        this.orderNo = orderNo;
    }
}
