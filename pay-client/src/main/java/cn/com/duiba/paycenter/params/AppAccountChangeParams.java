package cn.com.duiba.paycenter.params;


import cn.com.duiba.paycenter.enums.duibaaccount.AccountTransferInAndOutEnum;
import cn.com.duiba.paycenter.enums.duibaaccount.AppAccountChangeTypeEnum;
import cn.com.duiba.paycenter.enums.duibaaccount.AppAccountRelationTypeEnum;
import org.apache.commons.lang.builder.ToStringBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * author zhang<PERSON><PERSON>
 * date 2018/11/7-5:42 PM
 */
public class AppAccountChangeParams implements Serializable {

    private static final long serialVersionUID = 4125853897703245522L;
    /**
     * 开发者ID
     */
    @NotNull
    private Long developerId;

    /**
     * 应用ID
     */
    @NotNull
    private Long appId;

    /**
     * 业务ID
     */
    @NotBlank
    private String relationId;

    @NotNull
    private AppAccountRelationTypeEnum relationType;

    @NotNull
    private Long money;

    /**
     * 转出方
     * @see cn.com.duiba.paycenter.enums.duibaaccount.AccountTransferInAndOutEnum
     */
    private AccountTransferInAndOutEnum transferOut;

    /**
     * 转入方
     */
    private AccountTransferInAndOutEnum transferIn;

    /**
     * 金额变更类型
     */
    private AppAccountChangeTypeEnum changeType;

    /**
     * 账户类型。1：余额账户，2：收入账户，默认为1
     */
    private String accountType = "1";

    private String memo;

    public Long getDeveloperId() {
        return developerId;
    }

    public void setDeveloperId(Long developerId) {
        this.developerId = developerId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public Long getMoney() {
        return money;
    }

    public void setMoney(Long money) {
        this.money = money;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public AppAccountRelationTypeEnum getRelationType() {
        return relationType;
    }

    public void setRelationType(AppAccountRelationTypeEnum relationType) {
        this.relationType = relationType;
    }

    public AccountTransferInAndOutEnum getTransferOut() {
        return transferOut;
    }

    public void setTransferOut(AccountTransferInAndOutEnum transferOut) {
        this.transferOut = transferOut;
    }

    public AccountTransferInAndOutEnum getTransferIn() {
        return transferIn;
    }

    public void setTransferIn(AccountTransferInAndOutEnum transferIn) {
        this.transferIn = transferIn;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public AppAccountChangeTypeEnum getChangeType() {
        return changeType;
    }

    public void setChangeType(AppAccountChangeTypeEnum changeType) {
        this.changeType = changeType;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
