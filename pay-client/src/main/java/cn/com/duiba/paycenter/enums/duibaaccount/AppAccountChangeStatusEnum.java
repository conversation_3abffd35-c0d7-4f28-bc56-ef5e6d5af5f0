package cn.com.duiba.paycenter.enums.duibaaccount;

/**
 * 开发者余额变更结果状态枚举
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/8-1:37 PM
 */
public enum AppAccountChangeStatusEnum {

    STATUS_SUCCESS(1, "success"),
    STATUS_FAIL(2, "fail");

    private Integer code;

    private String desc;

    AppAccountChangeStatusEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
