package cn.com.duiba.paycenter.enums.duibaaccount;

import java.util.Arrays;
import java.util.Objects;

/**
 * 开发者账户相关来源类型枚举
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/6-7:14 PM
 */
public enum AppAccountRelationTypeEnum {

    SALE(12, "卖出"),
    SETTLE(8, "结算"),
    ONLINE_CHARGE(1, "充值"),
    ONLINE_CHARGE_FEE(2, "充值手续费"),
    WITHDRAW(3, "提现"),
    WITHDRAW_FEE(4, "提现手续费"),
    WITHDRAW_PAYBACK(5,"提现失败退回"),
    MANUAL_CHARGE(6, "人工充值"),
    MANUAL_REDUCE(7, "人工减款"),
    REFUND(9, "退款"),
    PURCHASE(10, "采购"),
    PURCHASE_PAYBACK(11, "采购退回"),
    USER_REWARD(13, "红包"),
    USER_REWARD_PAYBACK(14, "红包退回"),
    BUY_SAAS_SERVE(15, "购买服务"),
    FUND_COLLECTION(16, "资金归集"),
    ;

    private Integer code;

    private String desc;

    AppAccountRelationTypeEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }


    public static String getDescByCode(Integer code){
        if (Objects.isNull(code)){
            return "";
        }
        AppAccountRelationTypeEnum typeEnum = getByCode(code);
        if (Objects.nonNull(typeEnum)){
            return typeEnum.getDesc();
        }
        return "";
    }

    public static AppAccountRelationTypeEnum getByCode(Integer code){
        return Arrays.stream(AppAccountRelationTypeEnum.values()).filter(a -> a.code.equals(code)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
