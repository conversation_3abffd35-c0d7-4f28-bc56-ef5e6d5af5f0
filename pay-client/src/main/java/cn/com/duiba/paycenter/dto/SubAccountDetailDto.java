package cn.com.duiba.paycenter.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description
 * @Date 2023/2/20 14:59
 * <AUTHOR>
 */
public class SubAccountDetailDto implements Serializable {

    private static final long serialVersionUID = 4900226874253413285L;
    public static final String TypeIncome = "income"; // 收入
    public static final String TypePay = "pay"; // 支出

    public static final String RechargeTypeInit = "init";// 初始化
    public static final String RechargeTypeManualCharge = "manual_charge";// 人工充值
    public static final String RechargeTypeManualRefund = "manual_refund";// 人工退款
    public static final String RechargeTypeIncomeTransform = "income_transform";// 收入转余额
    public static final String RechargeTypeOnlineBlank = "online_charge";// 在线充值

    private Long id;
    private Integer version;
    private Long appId;
    private Long balance;
    private Date gmtCreate;
    private Date gmtModified;
    private Long subAccountId;
    private String description;
    private String memo;
    private Integer moneyChange;
    private Long operatorId;
    private Long orderId;
    private String type;
    private String rechargeType;
    private Long developerId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getBalance() {
        return balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getSubAccountId() {
        return subAccountId;
    }

    public void setSubAccountId(Long subAccountId) {
        this.subAccountId = subAccountId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getMoneyChange() {
        return moneyChange;
    }

    public void setMoneyChange(Integer moneyChange) {
        this.moneyChange = moneyChange;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRechargeType() {
        return rechargeType;
    }

    public void setRechargeType(String rechargeType) {
        this.rechargeType = rechargeType;
    }

    public Long getDeveloperId() {
        return developerId;
    }

    public void setDeveloperId(Long developerId) {
        this.developerId = developerId;
    }
}
