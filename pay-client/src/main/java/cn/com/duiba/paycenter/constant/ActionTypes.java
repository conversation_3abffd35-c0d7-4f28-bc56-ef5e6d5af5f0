package cn.com.duiba.paycenter.constant;

public class ActionTypes {

	public static final String ActionTypeOrdersPay="pay";
	public static final String ActionTypeOrdersBackpay="backpay";
	
	public static final String ActionTypeManualChargeCharge="manual-charge";
	public static final String ActionTypeManualChargeReduce="manula-reduce";
	
	public static final String ActionTypeOnlineChargeCharge="online-charge";
	
	public static final String ActionTypeTransform="transform";

	public static final String ActionTypeWithdrawReduce = "widthdraw-reduce";
	public static final String ActionTypeWithdrawPayback = "widthdraw-payback";

	/**
	 * 主订单的动作类型
	 * <AUTHOR>
	 *
	 */
	public enum OrdersActionType{
		PayOrder(ActionTypeOrdersPay),PaybackOrder(ActionTypeOrdersBackpay);
		private String key;
		private OrdersActionType(String key){
			this.key=key;
		}
		public String getKey(){
			return key;
		}
	}
	/**
	 * 人工充值的动作类型
	 * <AUTHOR>
	 *
	 */
	public enum ManualChargeActionType{
		Charge(ActionTypeManualChargeCharge),Reduce(ActionTypeManualChargeReduce);
		
		private String key;
		private ManualChargeActionType(String key){
			this.key=key;
		}
		public String getKey(){
			return key;
		}
	}
	/**
	 * 在线充值的动作类型
	 * <AUTHOR>
	 *
	 */
	public enum OnlineChargeActionType{
		Charge(ActionTypeOnlineChargeCharge);
		private String key;
		private OnlineChargeActionType(String key){
			this.key=key;
		}
		public String getKey(){
			return key;
		}
	}

	public enum WithdrawActionType{
		Payback(ActionTypeWithdrawPayback),Reduce(ActionTypeWithdrawReduce);

		private String key;
		WithdrawActionType(String key){
			this.key=key;
		}
		public String getKey(){
			return key;
		}
	}
}
