package cn.com.duiba.paycenter.enums;

/**
 * 支付业务类型
 * Created by xiaoxuda on 2017/10/31.
 */
public enum PayOrderBizTypeEnum {
    WITHDRAWAL_ALIPAY("开发者收入提现，提现到支付宝"),
    SUPPLIER_ALIPAY("供应商产品，支付宝红包奖品直充"),
    CONSUMER_WITHDRAW_MILLIONARIE("冲顶大会用户提现"),
    CONSUMER_WITHDRAW_GLOBAL_REWARD("总账户用户提现"),
    DUIBA_ALIPAY("兑吧虚拟商品支付宝直充"),
    CONSUMER_WITHDRAW_CUSTOM_ACC("自定义账户用户提现"),
    ;

    private String desc;

    PayOrderBizTypeEnum(String desc){
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
