package cn.com.duiba.paycenter.enums.equity;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 权益渠道类型
 * <AUTHOR>
 * @date 2023/4/10 11:38 AM
 */
@Getter
@AllArgsConstructor
public enum EquityChannelTypeEnum {
    ALI_PAY_COUPON(1, "支付宝立减金"),
    TRANSFER_BATCHES(2, "微信商家转账到零钱"),
    WX_FAVOR(3, "微信发放代金券"),
    JI_GAO(4, "继皋直充"),
    TEST(99, "测试抽象类逻辑"),
    ;

    /**
     * 类型
     */
    private final Integer type;

    /**
     * 描述
     */
    private final String desc;
}
