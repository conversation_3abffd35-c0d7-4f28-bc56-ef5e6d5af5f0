package cn.com.duiba.paycenter.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018/5/21 20:01
 */
public class SaasAgentAccountDto implements Serializable{

    private static final long serialVersionUID = -6368757545247280638L;

    private Long id;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 代理商账户余额
     */
    private Long money;

    /**
     * 数字签名
     */
    private String sign;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public Long getMoney() {
        return money;
    }

    public void setMoney(Long money) {
        this.money = money;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
}
