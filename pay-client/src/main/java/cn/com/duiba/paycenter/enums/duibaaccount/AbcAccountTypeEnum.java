package cn.com.duiba.paycenter.enums.duibaaccount;

import java.util.Arrays;
import java.util.Objects;

/**
 * 兑吧 农行账户类型
 * author liukai
 * date 2020/10-3:44 PM
 */
public enum AbcAccountTypeEnum {

    ABC_BIGEST(1, "农总行", "***************"),
    ABC_BEIJING(2, "农行-北京", "***************"),
    ABC_SHENZHEN(3, "农行-深圳", "***************"),
    ABC_SHANDONG(4, "农行-山东", "***************"),

    ;
    private Integer code;

    private String desc;

    private String marchantId;

    AbcAccountTypeEnum(Integer code, String desc, String marchantId){
        this.code = code;
        this.desc = desc;
        this.marchantId = marchantId;
    }

    public static String getDescByCode(Integer code){
        if (Objects.isNull(code)){
            return "";
        }
        AbcAccountTypeEnum accountTypeEnum = getByCode(code);
        if (Objects.nonNull(accountTypeEnum)){
            return accountTypeEnum.getDesc();
        }
        return "";
    }

    public static AbcAccountTypeEnum getByCode(Integer code){
        return Arrays.stream(AbcAccountTypeEnum.values()).filter(a -> a.code.equals(code)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getMarchantId() {
        return marchantId;
    }
}
