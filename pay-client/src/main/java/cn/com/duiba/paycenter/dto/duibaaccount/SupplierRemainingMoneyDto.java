package cn.com.duiba.paycenter.dto.duibaaccount;


import cn.com.duiba.paycenter.enums.duibaaccount.SupplierAccountTypeEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * 供应商账户dto
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/6-3:38 PM
 */
public class SupplierRemainingMoneyDto implements Serializable {

    private static final long serialVersionUID = 4694527636774917554L;
    private Long id;
    /**
     * 金额（分）
     */
    private Long money;
    /**
     * 版本号
     */
    private Long version;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 账户类型1：余额账户 2：待结算账户
     * @see SupplierAccountTypeEnum
     */
    private Integer accountType;

    private String sign;

    private Date gmtCreate;
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMoney() {
        return money;
    }

    public void setMoney(Long money) {
        this.money = money;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }
}
