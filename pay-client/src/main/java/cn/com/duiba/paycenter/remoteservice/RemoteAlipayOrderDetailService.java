package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.AlipayOrderDetailDto;

/**
 * Created by zyj on 2018/4/27.
 */
@AdvancedFeignClient
public interface RemoteAlipayOrderDetailService {
    /**
     * 新增支付宝订单
     * @param alipayOrderDetailDto
     * @return
     */
    public AlipayOrderDetailDto insert(AlipayOrderDetailDto alipayOrderDetailDto);
    /**
     * 根据业务订单id查询
     */
    public AlipayOrderDetailDto findBySelfOrderId(Long id);
    /**
     * update
     */
    public void update(AlipayOrderDetailDto alipayOrderDetailDto);

}
