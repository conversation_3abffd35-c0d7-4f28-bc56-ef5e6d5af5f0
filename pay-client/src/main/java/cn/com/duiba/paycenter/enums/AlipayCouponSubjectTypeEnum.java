package cn.com.duiba.paycenter.enums;

/**
 * <AUTHOR>
 * @date 2018/12/01
 */
public enum AlipayCouponSubjectTypeEnum {
    FUJIAN_DUIBA(1, "福建兑吧"),
    DUIBA_FUJIAN(2, "兑吧福建"),
    ;
    private Integer code;

    private String subject;

    AlipayCouponSubjectTypeEnum(Integer code, String subject) {
        this.code = code;
        this.subject = subject;
    }

    public Integer getCode() {
        return code;
    }

    public String getSubject() {
        return subject;
    }

    public static AlipayCouponSubjectTypeEnum getSubjectType(Integer code) {
        if (code == null) {
            return null;
        }
        for (AlipayCouponSubjectTypeEnum signTypeEnum : AlipayCouponSubjectTypeEnum.values()) {
            if (signTypeEnum.getCode().equals(code)) {
                return signTypeEnum;
            }
        }
        return null;
    }
}
