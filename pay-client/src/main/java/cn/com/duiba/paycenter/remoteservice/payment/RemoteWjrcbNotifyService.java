package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.wjrcb.WjrcbChargeNotifyResponse;

/**
 * 苏州农商行通知
 * <AUTHOR>
 * @date 2019/07/30
 */
@AdvancedFeignClient
public interface RemoteWjrcbNotifyService {
    /**
     * 支付通知
     * @param queryString query string
     * @return response
     */
    WjrcbChargeNotifyResponse orderNotify(String queryString);
}
