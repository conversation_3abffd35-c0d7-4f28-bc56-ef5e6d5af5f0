package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.WxCouponResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxCouponSendRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.coupon.WxCouponQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.coupon.WxCouponQueryResp;

/**
 * 微信立减金券服务信息
 *
 * <AUTHOR>
 * @date 2022/12/05
 */
@AdvancedFeignClient
public interface RemoteWechatCouponService {


    /**
     * 获取url
     *
     * @param subject     主体
     * @param redirectUrl 重定向url
     * @return {@link String}
     */
    String getAccessUrl(String subject, String redirectUrl);


    /**
     * 查询微信活动
     *
     * @param couponQueryRequest 查询信息
     * @return {@link WxCouponQueryResp}
     */
    WxCouponQueryResp getWechatActivityInfo(WxCouponQueryRequest couponQueryRequest);


    /**
     * 发放微信立减金
     *
     * @param request 请求
     * @return {@link WxCouponResponse}
     */
    WxCouponResponse sendWxCouponByWxSubject(WxCouponSendRequest request) throws BizException;


    /**
     * 根据authcode查询openId
     * @param subject
     * @param authCode
     * @return
     */
    String queryWechatUserId(String subject, String authCode);

    /**
     * 根据主体查询微信appid
     * @param subject
     * @return
     */
    String queryWxAppIdBySubject(String subject);

}
