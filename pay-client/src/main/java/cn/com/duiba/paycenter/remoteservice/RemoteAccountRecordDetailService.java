package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.constant.SettleStatusEnum;
import cn.com.duiba.paycenter.dto.AcountChangeTotalDto;
import cn.com.duiba.paycenter.model.AccountChangeRecordDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Title: RemoteAccountRecordDetailService
 * @Description:开发者账户变更记录
 * @date 2018/9/314:57
 */
@AdvancedFeignClient
public interface RemoteAccountRecordDetailService {

    /**
     * 批量查询开发者  冻结余额
     * */
    List<AcountChangeTotalDto> batchQueryFrozenCapital(List<Long> developIds, Date startDate, Date endDate, SettleStatusEnum statusEnum);

    /**
     * 批量查询开发者  消耗金额
     * */
    List<AcountChangeTotalDto> batchQueryConsumeCapital(List<Long> developIds, Date startDate, Date endDate, SettleStatusEnum statusEnum);


    /**
     * 批量查询
     * */
    List<AccountChangeRecordDO> findPageListByStartId(Long startId,SettleStatusEnum statusEnum, int limit);
}
