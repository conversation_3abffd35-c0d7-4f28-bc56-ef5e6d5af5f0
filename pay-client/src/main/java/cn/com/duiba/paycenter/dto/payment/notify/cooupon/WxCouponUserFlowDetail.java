package cn.com.duiba.paycenter.dto.payment.notify.cooupon;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: pengyi
 * @description:
 * @date: 2022/12/14 上午11:11
 */
public class WxCouponUserFlowDetail implements Serializable {

    private static final long serialVersionUID = -3912573159987730297L;
    /**
     * 批次号
     */
    private String stockId;

    /**
     * 优惠券id
     */
    private String couponId;

    /**
     * 优惠类型
     */
    private String couponType;

    /**
     * 优惠金额(元)
     */
    private String discountedPrice;

    /**
     * 订单金额(元)
     */
    private String totalPrice;

    /**
     * 交易类型
     */
    private String chargeType;

    /***
     * 支付单号
     */
    private String chargeNum;

    /**
     * 消耗时间
     */
    private Date useFlowDate;

    /**
     * 消耗商户号
     */
    private String useMchId;

    /**
     * 设备号
     */
    private String deviceNo;

    /**
     * 银行流水号
     */
    private String bankSerialNo;

    /**
     * 单品信息
     */
    private String couponDetail;

    public String getStockId() {
        return stockId;
    }

    public void setStockId(String stockId) {
        this.stockId = stockId;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    public String getDiscountedPrice() {
        return discountedPrice;
    }

    public void setDiscountedPrice(String discountedPrice) {
        this.discountedPrice = discountedPrice;
    }

    public String getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getChargeType() {
        return chargeType;
    }

    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    public String getChargeNum() {
        return chargeNum;
    }

    public void setChargeNum(String chargeNum) {
        this.chargeNum = chargeNum;
    }

    public Date getUseFlowDate() {
        return useFlowDate;
    }

    public void setUseFlowDate(Date useFlowDate) {
        this.useFlowDate = useFlowDate;
    }

    public String getUseMchId() {
        return useMchId;
    }

    public void setUseMchId(String useMchId) {
        this.useMchId = useMchId;
    }

    public String getDeviceNo() {
        return deviceNo;
    }

    public void setDeviceNo(String deviceNo) {
        this.deviceNo = deviceNo;
    }

    public String getBankSerialNo() {
        return bankSerialNo;
    }

    public void setBankSerialNo(String bankSerialNo) {
        this.bankSerialNo = bankSerialNo;
    }

    public String getCouponDetail() {
        return couponDetail;
    }

    public void setCouponDetail(String couponDetail) {
        this.couponDetail = couponDetail;
    }
}
