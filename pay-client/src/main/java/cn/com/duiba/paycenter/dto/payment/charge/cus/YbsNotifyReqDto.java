package cn.com.duiba.paycenter.dto.payment.charge.cus;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 深圳银联易办事通知
 * <AUTHOR>
 */
public class YbsNotifyReqDto implements Serializable {
    /**
     * 接口调用状态
     */
    @NotBlank(message = "接口调用状态不能为空")
    private String status;
    /**
     * 业务状态
     */
    private String resultCode;
    /**
     * 业务订单号
     */
    @NotBlank(message = "业务订单号不能为空")
    private String outTradeNo;
    /**
     * 接口调用信息
     */
    private String message;
    /**
     * 业务错误信息
     */
    private String errMsg;

    /**
     * 平台交易单号
     */
    private String transactionId;


    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }
}
