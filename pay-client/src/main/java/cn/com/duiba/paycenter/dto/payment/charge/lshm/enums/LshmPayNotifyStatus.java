package cn.com.duiba.paycenter.dto.payment.charge.lshm.enums;

/**
 *  01：待支付
 *  00：支付成功
 *  02：支付失败
 * 03:支付中
 */
public enum LshmPayNotifyStatus {
    WAITING("01", "待支付"),
    SUCCESS("00", "支付成功"),
    FAIL("02", "支付失败"),
    PAYING("03", "支付中");

    private String status;

    private String desc;

    LshmPayNotifyStatus(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
