package cn.com.duiba.paycenter.client;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.com.duiba.paycenter.constant.ActionTypes.OrdersActionType;
import cn.com.duiba.paycenter.model.AccountChangeRecordDO;
import cn.com.duiba.paycenter.params.PayOrdersExtraParams;
import cn.com.duiba.paycenter.result.PayOrdersResult;
import cn.com.duiba.paycenter.service.PayOrdersService;
import cn.com.duiba.paycenter.service.SubAccountPayOrdersService;
import cn.com.duiba.paycenter.util.SignUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PayOrdersServiceClient {

	private static final Logger log = LoggerFactory.getLogger(PayOrdersServiceClient.class);


	private PayOrdersService payOrdersService;

	private SubAccountPayOrdersService subAccountPayOrdersService;
	
	/**
	 * 主订单下单时的付款接口
	 * 
	 * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
	 * 
	 * @param developerId
	 * @param orderId
	 * @param money
	 */
	public RpcResult<PayOrdersResult> payOrder(Long developerId,Long orderId,Long money,PayOrdersExtraParams p){
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("orderId", orderId+"");
			params.put("money", money+"");
			String sign=SignUtil.sign(params);
			
			PayOrdersResult ret=payOrdersService.payOrder(developerId, orderId, money,sign,p);
			
			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}
	/**
	 * 主订单失败还款的接口
	 * 
	 * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
	 * 
	 * @param developerId
	 * @param orderId
	 * @param money
	 * @return
	 */
	public RpcResult<PayOrdersResult> backpayOrder(Long developerId,Long orderId,Long money,PayOrdersExtraParams p){
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("orderId", orderId+"");
			params.put("money", money+"");
			String sign=SignUtil.sign(params);
			
			PayOrdersResult ret=payOrdersService.backpayOrder(developerId, orderId, money,sign,p);
			
			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}
	/**
	 * 检查一个操作是否执行成功
	 * @param orderId
	 * @return
	 */
	public RpcResult<Boolean> checkActionSuccess(Long orderId,OrdersActionType actionType){
		try {
			boolean ret=payOrdersService.checkActionSuccess(orderId, actionType.getKey());
			
			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}
	public void setPayOrdersService(PayOrdersService payOrdersService) {
		this.payOrdersService = payOrdersService;
	}

	public void setSubAccountPayOrdersService(SubAccountPayOrdersService subAccountPayOrdersService){
		this.subAccountPayOrdersService = subAccountPayOrdersService;
	}

	/**
	 * 批量更新开发者 账户变更记录 字段 settleStatus
	 * */
	public boolean updateSettleStatus(List<AccountChangeRecordDO> list){
		try{
			return payOrdersService.batchUpdateRecordStatus(list);
		}catch (Exception e){
			log.warn("批量更新开发者 账户变更记录 字段 settleStatus 失败，",e);
			return false;
		}
	}


	/**
	 * 根据 开发者账户变更记录状态，批量查询
	 * */
	public List<AccountChangeRecordDO> findByStatusLimit(Integer settleStatus,Integer limit){
		try{
			return payOrdersService.findByStatusLimit(settleStatus,limit);
		}catch (Exception e){
			log.warn("根据 开发者账户变更记录状态，批量查询 异常，",e);
			return Collections.emptyList();
		}
	}

	/**
	 * 取消发货或者售后返开发者余额
	 * 这里通过子订单做了幂等性校验
	 * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
	 * @param developerId
	 * @param orderId
	 * @param money
	 * @return
	 */
	public RpcResult<PayOrdersResult> backpayOrderByOrderItemIds(Long developerId,Long orderId, String orderItemIds,Long money,PayOrdersExtraParams p){
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("orderId", orderId+"");
			params.put("money", money+"");
			params.put("orderItemIds", orderItemIds);
			String sign=SignUtil.sign(params);
			PayOrdersResult ret=payOrdersService.backpayOrderByOrderItemIds(developerId, orderId, money, orderItemIds, sign, p);
			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}

	/**
	 * 分账下单时的付款接口前置校验
	 * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
	 * @param developerId
	 * @param orderId
	 * @param money
	 */
	public RpcResult<PayOrdersResult> subAccountPayOrder(Long developerId,Long orderId,Long money,PayOrdersExtraParams p){
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("orderId", orderId+"");
			params.put("money", money+"");
			String sign=SignUtil.sign(params);

			PayOrdersResult ret=subAccountPayOrdersService.payOrder(developerId, orderId, money,sign,p);

			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}
	/**
	 * 主订单失败还款的接口
	 *
	 * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
	 *
	 * @param developerId
	 * @param orderId
	 * @param money
	 * @return
	 */
	public RpcResult<PayOrdersResult> subAccountBackpayOrder(Long developerId,Long orderId,Long money,PayOrdersExtraParams p){
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("orderId", orderId+"");
			params.put("money", money+"");
			String sign=SignUtil.sign(params);
			PayOrdersResult ret=subAccountPayOrdersService.backpayOrder(developerId, orderId, money,sign,p);
			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}


	/**
	 * 充值
	 * @param developerId
	 * @param orderId
	 * @param money
	 * @param p
	 * @return
	 */
	public RpcResult<PayOrdersResult> chargeMoney(Long developerId,Long orderId,Long money,PayOrdersExtraParams p){
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("orderId", orderId+"");
			params.put("money", money+"");
			String sign=SignUtil.sign(params);
			PayOrdersResult ret = subAccountPayOrdersService.chargeMoney(developerId, orderId, money, sign, p);
			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}

	/**
	 * 退款
	 * @param developerId
	 * @param orderId
	 * @param money
	 * @param p
	 * @return
	 */
	public RpcResult<PayOrdersResult> refundMoney(Long developerId,Long orderId,Long money,PayOrdersExtraParams p){
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("orderId", orderId+"");
			params.put("money", money+"");
			String sign=SignUtil.sign(params);
			PayOrdersResult ret = subAccountPayOrdersService.refundMoney(developerId, orderId, money, sign, p);
			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}

}
