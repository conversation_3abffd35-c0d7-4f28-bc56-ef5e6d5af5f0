package cn.com.duiba.paycenter.dto;

import java.io.Serializable;
import java.util.Date;

public class AccountDetailDto implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 620346693010642343L;
	public static final String TypeIncome = "income"; // 收入
	public static final String TypePay = "pay"; // 支出

	public static final String RechargeTypeManual = "manual";// 人工充值
	public static final String RechargeTypeEbatong = "ebatong";// 贝付充值
	public static final String RechargeTypeIncomeTransform = "income_transform";// 收入转余额

	private Long id;

	private Long developerId;

	private Long credits; // 消耗积分

	private Long appId; // 对应App

	private String type; // 类型（充值/支出）

	private Integer moneyChange; // 存入或支出金额，单位：分

	private String description;// 描述

	private String memo;// 备注

	private Long operatorId; // adminId

	private Integer balance; // 余额（可用），单位：分

	private Long orderId;

	private String rechargeType;

	private Date gmtCreate;

	private Date gmtModified;

	/**
	 * 默认构造函数
	 */
	public AccountDetailDto() {
		// Do nothing
	}

	public AccountDetailDto(Long id) {
		this.id = id;
		this.gmtModified = new Date();
	}

	public AccountDetailDto(boolean init4insert) {
		if (init4insert) {
			gmtCreate = new Date();
			gmtModified = new Date();
		}
	}

	public void beforeUpdate() {
		gmtModified = new Date();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getDeveloperId() {
		return developerId;
	}

	public void setDeveloperId(Long developerId) {
		this.developerId = developerId;
	}

	public Long getCredits() {
		return credits;
	}

	public void setCredits(Long credits) {
		this.credits = credits;
	}

	public Long getAppId() {
		return appId;
	}

	public void setAppId(Long appId) {
		this.appId = appId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Integer getMoneyChange() {
		return moneyChange;
	}

	public void setMoneyChange(Integer moneyChange) {
		this.moneyChange = moneyChange;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Long getOperatorId() {
		return operatorId;
	}

	public void setOperatorId(Long operatorId) {
		this.operatorId = operatorId;
	}

	public Integer getBalance() {
		return balance;
	}

	public void setBalance(Integer balance) {
		this.balance = balance;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getRechargeType() {
		return rechargeType;
	}

	public void setRechargeType(String rechargeType) {
		this.rechargeType = rechargeType;
	}

	public Date getGmtCreate() {
		return gmtCreate;
	}

	public void setGmtCreate(Date gmtCreate) {
		this.gmtCreate = gmtCreate;
	}

	public Date getGmtModified() {
		return gmtModified;
	}

	public void setGmtModified(Date gmtModified) {
		this.gmtModified = gmtModified;
	}
}
