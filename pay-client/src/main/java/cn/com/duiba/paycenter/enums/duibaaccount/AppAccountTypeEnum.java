package cn.com.duiba.paycenter.enums.duibaaccount;

import java.util.Arrays;
import java.util.Objects;

/**
 * 开发者账户类型枚举
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/6-3:44 PM
 */
public enum AppAccountTypeEnum {

    BALANCE(1, "余额账户"),
    SETTLE(2, "待结算账户"),
    MARK(3, "标记账户");

    private Integer code;

    private String desc;

    AppAccountTypeEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code){
        if (Objects.isNull(code)){
            return "";
        }
        AppAccountTypeEnum accountTypeEnum = getByCode(code);
        if (Objects.nonNull(accountTypeEnum)){
            return accountTypeEnum.getDesc();
        }
        return "";
    }

    public static AppAccountTypeEnum getByCode(Integer code){
        return Arrays.stream(AppAccountTypeEnum.values()).filter(a -> a.code.equals(code)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
