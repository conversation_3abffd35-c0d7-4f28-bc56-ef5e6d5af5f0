package cn.com.duiba.paycenter.constant;

import cn.com.duiba.paycenter.enums.WithdrawBizTypeEnum;

public class RelationTypes {

	public static final String RTOrders="orders";
	
	public static final String RTManualCharge="manual-charge";
	
	public static final String RTOnlineCharge="online-charge";

	public static final String  RTSaasOrders = "saas-orders";
	/**
	 * 收入到余额的转账
	 */
	public static final String RTTransform="transform";
	/**
	 * 提现前缀,具体提现类型见 WithdrawBizTypeEnum， 实际类型 = relationType + WithdrawBizTypeEnum.name()
	 */
	public static final String RT_WITHDRAW_PRE = "withdraw_";

	public static String withdrawRelationType(WithdrawBizTypeEnum bizType){
		return RT_WITHDRAW_PRE + bizType.name();
	}
}
