package cn.com.duiba.paycenter.enums.equity;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 权益业务类型
 * <AUTHOR>
 * @date 2023/4/10 11:38 AM
 */
@Getter
@AllArgsConstructor
public enum EquityBizTypeEnum {
    DUI_BA_POINTS_ALIPAY_COUPON(1, "积分商城支付宝立减金"),
    DXT_WX_COUPON(2, "兑信通微信立减金"),
    DXT_WX_POCKET_MONEY(3, "兑信通微信转账到零钱"),
    DUI_BA_GOODS(4, "兑吧商品库"),
    DXT_WX_COUPON_WITHDRAW(5, "兑信通微信立减金-提现"),
    PROJECTX_WX_POCKET_MONEY(6, "星速台商家自有服务-微信转账到零钱"),
    MERCHANT_WX_COUPON(7, "微信立减金-走商家证书"),

    ;

    /**
     * 类型
     */
    private final Integer type;

    /**
     * 描述
     */
    private final String desc;
    
    private static final Map<Integer, EquityBizTypeEnum> ENUM_MAP = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(EquityBizTypeEnum::getType, Function.identity(), (v1, v2) -> v2)));
    
    public static EquityBizTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return ENUM_MAP.get(type);
    }
}
