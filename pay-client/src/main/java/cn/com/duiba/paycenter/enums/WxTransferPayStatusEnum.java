package cn.com.duiba.paycenter.enums;

public enum WxTransferPayStatusEnum {
    INIT(0, "初始化"),
    UNKNOWN(1,"调用微信发生错误，请用原单号重试"),
    PROCESS(2, "处理中"),
    SUCCESS(3,"转账成功"),
    FAIL(4, "转账失败"),
    ;

    private Integer code;
    private String desc;

    WxTransferPayStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
