package cn.com.duiba.paycenter.service;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.model.AccountChangeRecordDO;
import cn.com.duiba.paycenter.params.PayOrdersExtraParams;
import cn.com.duiba.paycenter.result.PayOrdersResult;

import java.util.List;

/**
 * 分账订单付款相关接口服务，实际上是RemoteService
 * <AUTHOR>
 *
 */
@AdvancedFeignClient(qualifier = "subAccountPayOrdersService")
public interface SubAccountPayOrdersService {

	PayOrdersResult payOrder(Long developerId,Long orderId,Long money,String sign,PayOrdersExtraParams p);

	PayOrdersResult backpayOrder(Long developerId,Long orderId,Long money,String sign,PayOrdersExtraParams p);

	PayOrdersResult chargeMoney(Long developerId,Long orderId,Long money,String sign,PayOrdersExtraParams p);

	PayOrdersResult refundMoney(Long developerId,Long orderId,Long money,String sign,PayOrdersExtraParams p);
}
