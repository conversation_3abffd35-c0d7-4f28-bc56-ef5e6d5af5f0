package cn.com.duiba.paycenter.dto.payment.notify.cooupon;


import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Description:
 * <p>
 * date: 2022/10/11 6:12 下午
 *
 * <AUTHOR>
 */
public class WxCouponUsedNotifyDetail implements Serializable {
    private static final long serialVersionUID = -120920352129649718L;
    /**
     * 	批次创建方商户号。
     */
    @JSONField(name = "stock_creator_mchid")
    private String stockCreatorMchid;

    /**
     * 微信为每个代金券批次分配的唯一id。
     */
    @JSONField(name = "stock_id")
    private String stockId;

    /**
     * 微信为代金券唯一分配的id。
     */
    @JSONField(name = "coupon_id")
    private String couponId;

    /**
     * 	单品优惠特定信息。
     */
    @JSONField(name = "singleitem_discount_off")
    private WxCouponSingleItemDiscountOff singleitemDiscountOff;

    /**
     * 减至优惠限定字段，仅减至优惠场景有返回。
     */
    @JSONField(name = "discount_to")
    private WxCouponSingleItemDiscountTo discountTo;

    /**
     * 代金券名称
     */
    @JSONField(name = "coupon_name")
    private String couponName;

    /**
     * 代金券状态：
     * SENDED：可用(核销通知中不会头这个状态)
     * USED：已实扣
     * EXPIRED：已过期(核销通知中不会头这个状态)
     */
    @JSONField(name = "status")
    private String status;

    /**
     * 代金券描述说明字段。
     */
    @JSONField(name = "description")
    private String description;

    /**
     * 领券时间
     */
    @JSONField(name = "create_time")
    private String createTime;

    /**
     * 类型
     * NORMAL：满减券
     * CUT_TO：减至券
     */
    @JSONField(name = "coupon_type")
    private String couponType;

    /**
     *是否无资金流
     * true：是
     * false：否
     */
    @JSONField(name = "no_cash")
    private boolean noCash;

    /**
     * 可用开始时间
     */
    @JSONField(name = "available_begin_time")
    private String availableBeginTime;
    /**
     * 可用结束时间
     */
    @JSONField(name = "available_end_time")
    private String availableEndTime;


    /**
     * 是否单品优惠
     */
    @JSONField(name = "singleitem")
    private boolean singleitem;

    /**
     * 普通满减券面额、门槛信息。
     */
    @JSONField(name = "normal_coupon_information")
    private NormalCouponInformation normalCouponInformation;

    /**
     * 已实扣代金券信息。
     */
    @JSONField(name = "consume_information")
    private ConsumeInformation consumeInformation;

    public String getStockCreatorMchid() {
        return stockCreatorMchid;
    }

    public WxCouponUsedNotifyDetail setStockCreatorMchid(String stockCreatorMchid) {
        this.stockCreatorMchid = stockCreatorMchid;
        return this;
    }

    public String getStockId() {
        return stockId;
    }

    public WxCouponUsedNotifyDetail setStockId(String stockId) {
        this.stockId = stockId;
        return this;
    }

    public String getCouponId() {
        return couponId;
    }

    public WxCouponUsedNotifyDetail setCouponId(String couponId) {
        this.couponId = couponId;
        return this;
    }

    public WxCouponSingleItemDiscountOff getSingleitemDiscountOff() {
        return singleitemDiscountOff;
    }

    public WxCouponUsedNotifyDetail setSingleitemDiscountOff(WxCouponSingleItemDiscountOff singleitemDiscountOff) {
        this.singleitemDiscountOff = singleitemDiscountOff;
        return this;
    }

    public WxCouponSingleItemDiscountTo getDiscountTo() {
        return discountTo;
    }

    public WxCouponUsedNotifyDetail setDiscountTo(WxCouponSingleItemDiscountTo discountTo) {
        this.discountTo = discountTo;
        return this;
    }

    public String getCouponName() {
        return couponName;
    }

    public WxCouponUsedNotifyDetail setCouponName(String couponName) {
        this.couponName = couponName;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public WxCouponUsedNotifyDetail setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public WxCouponUsedNotifyDetail setDescription(String description) {
        this.description = description;
        return this;
    }

    public String getCreateTime() {
        return createTime;
    }

    public WxCouponUsedNotifyDetail setCreateTime(String createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getCouponType() {
        return couponType;
    }

    public WxCouponUsedNotifyDetail setCouponType(String couponType) {
        this.couponType = couponType;
        return this;
    }

    public boolean isNoCash() {
        return noCash;
    }

    public WxCouponUsedNotifyDetail setNoCash(boolean noCash) {
        this.noCash = noCash;
        return this;
    }

    public String getAvailableBeginTime() {
        return availableBeginTime;
    }

    public WxCouponUsedNotifyDetail setAvailableBeginTime(String availableBeginTime) {
        this.availableBeginTime = availableBeginTime;
        return this;
    }

    public String getAvailableEndTime() {
        return availableEndTime;
    }

    public WxCouponUsedNotifyDetail setAvailableEndTime(String availableEndTime) {
        this.availableEndTime = availableEndTime;
        return this;
    }

    public boolean isSingleitem() {
        return singleitem;
    }

    public WxCouponUsedNotifyDetail setSingleitem(boolean singleitem) {
        this.singleitem = singleitem;
        return this;
    }

    public NormalCouponInformation getNormalCouponInformation() {
        return normalCouponInformation;
    }

    public WxCouponUsedNotifyDetail setNormalCouponInformation(NormalCouponInformation normalCouponInformation) {
        this.normalCouponInformation = normalCouponInformation;
        return this;
    }

    public ConsumeInformation getConsumeInformation() {
        return consumeInformation;
    }

    public WxCouponUsedNotifyDetail setConsumeInformation(ConsumeInformation consumeInformation) {
        this.consumeInformation = consumeInformation;
        return this;
    }
}
