package cn.com.duiba.paycenter.dto.payment.refund.citic;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/03/12
 */
public class CiticRefundRequest implements Serializable {
    private static final long serialVersionUID = 2952438902892462724L;

    /**
     * 退款金额
     */
    @NotNull(message = "金额不能为空")
    @Min(value = 1, message = "金额必须大于1分")
    private Integer amount;
    private Integer bizType;
    /**
     * 主订单号
     */
    @NotNull(message = "主订单号不能为空")
    private String bizOrderNo;

    //供应商标识，南宁中信会有，南京中信没有
    private Integer supplierTag;

    //主订单子订单号, 支持购物车按每种商品退
    private Long ordersItemId;

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public Integer getSupplierTag() {
        return supplierTag;
    }

    public void setSupplierTag(Integer supplierTag) {
        this.supplierTag = supplierTag;
    }

    public Long getOrdersItemId() {
        return ordersItemId;
    }

    public void setOrdersItemId(Long ordersItemId) {
        this.ordersItemId = ordersItemId;
    }
}
