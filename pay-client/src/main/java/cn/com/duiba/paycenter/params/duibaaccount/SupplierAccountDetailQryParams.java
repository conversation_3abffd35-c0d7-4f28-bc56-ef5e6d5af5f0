package cn.com.duiba.paycenter.params.duibaaccount;

import cn.com.duiba.paycenter.enums.duibaaccount.SupplierAccountRelationTypeEnum;
import cn.com.duiba.paycenter.params.PageQueryParams;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 供应商账户明细分页查询条件
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018-11-26-18:02
 */
public class SupplierAccountDetailQryParams extends PageQueryParams {

    private static final long serialVersionUID = -6792674600681342655L;
    /**
     * 供应商ID
     */
    @NotNull(message = "供应商ID必填")
    private Long supplierId;

    /**
     * 创建时间：起始时间
     */
    private Date startTime;

    /**
     * 创建时间：结束时间
     */
    private Date endTime;

    /**
     * 账单类型
     * @see SupplierAccountRelationTypeEnum
     */
    private Integer relationType;

    /**
     * 业务单号
     */
    private String relationId;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getRelationType() {
        return relationType;
    }

    public void setRelationType(Integer relationType) {
        this.relationType = relationType;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }
}
