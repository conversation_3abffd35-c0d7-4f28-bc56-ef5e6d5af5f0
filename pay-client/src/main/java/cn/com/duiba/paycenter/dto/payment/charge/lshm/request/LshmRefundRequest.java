package cn.com.duiba.paycenter.dto.payment.charge.lshm.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 退款请求数据传输对象 (DTO)
 */
@Data
public class LshmRefundRequest implements Serializable {

    /**
     * 门店编码
     * 必填项
     */
    private String storeCode;

    /**
     * 支付订单号(原支付流水号)
     * 必填项
     */
    private String tradeNo;

    /**
     * 退款金额
     * 必填项
     */
    private BigDecimal refundAmount;

    /**
     * 退款业务订单号
     * 必填项
     */
    private String refundOrderNo;

    /**
     * 操作人
     * 非必填项
     */
    private String clientUser;

    /**
     * 操作来源
     * 非必填项
     */
    private String clientFrom;

    /**
     * 备注
     * 非必填项
     */
    private String remark;
}