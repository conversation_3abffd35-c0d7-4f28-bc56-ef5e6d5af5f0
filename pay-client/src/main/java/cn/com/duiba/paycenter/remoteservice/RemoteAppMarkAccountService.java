package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.params.AppAccountChangeParams;
import cn.com.duiba.paycenter.result.PayCenterResult;

/**
 * 开发者应用标记账户
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/20-11:02 AM
 */
@AdvancedFeignClient
public interface RemoteAppMarkAccountService {

    /**
     * 扣款
     * @param param
     * @param sign
     * @return
     */
    PayCenterResult reduceMoney(AppAccountChangeParams param, String sign);

    /**
     * 加款
     * @param param
     * @param sign
     * @return
     */
    PayCenterResult addMoney(AppAccountChangeParams param, String sign);
}
