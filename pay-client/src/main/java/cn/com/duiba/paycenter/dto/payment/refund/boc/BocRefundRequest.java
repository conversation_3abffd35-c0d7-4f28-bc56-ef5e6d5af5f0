package cn.com.duiba.paycenter.dto.payment.refund.boc;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/03/12
 */
public class BocRefundRequest implements Serializable {
    private static final long serialVersionUID = -6359467012078035648L;
    /**
     * 退款金额
     */
    @NotNull(message = "金额不能为空")
    @Min(value = 1, message = "金额必须大于1分")
    private Integer amount;
    /**
     * 主订单号
     */
    @NotNull(message = "主订单号不能为空")
    private String bizOrderNo;

    //主订单子订单号, 支持购物车按每种商品退
    private Long ordersItemId;

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public Long getOrdersItemId() {
        return ordersItemId;
    }

    public void setOrdersItemId(Long ordersItemId) {
        this.ordersItemId = ordersItemId;
    }
}
