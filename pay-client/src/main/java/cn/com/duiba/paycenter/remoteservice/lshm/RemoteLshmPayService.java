package cn.com.duiba.paycenter.remoteservice.lshm;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.LshmChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmPayNotifyRequest;

@AdvancedFeignClient
public interface RemoteLshmPayService {
    /**
     * 支付通知
     */
    LshmChargeNotifyResponse payNotify(Long appId, LshmPayNotifyRequest notifyRequest) throws BizException;
}
