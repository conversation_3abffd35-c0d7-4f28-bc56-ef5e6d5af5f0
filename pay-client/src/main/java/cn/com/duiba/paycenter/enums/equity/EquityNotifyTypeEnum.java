package cn.com.duiba.paycenter.enums.equity;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 权益通知类型
 * <AUTHOR>
 * @date 2023/4/26 10:36 AM
 */
@Getter
@AllArgsConstructor
public enum EquityNotifyTypeEnum {
    
    NOT_NOTIFY(1, "不需要通知"),
    
    NOTIFY_SUCCESS(2, "仅通知成功状态的"),
    
    NOTIFY_SUCCESS_AND_FAIL(3, "仅通知成功或失败状态的"),
    
    NOTIFY_BATCH_DETAIL_SUCCESS_AND_FAIL(4, "仅通知批量明细单号的成功或失败状态的"),

    NOTIFY_ALL(5, "所有状态回调"),
    
    ;
    private final int type;
    
    private final String desc;
    
    private static final Map<Integer, EquityNotifyTypeEnum> ENUM_MAP = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(EquityNotifyTypeEnum::getType, Function.identity(), (v1, v2) -> v2)));
    
    /**
     * 根据枚举值获取枚举
     * @param type 枚举值
     * @return 枚举
     */
    public static EquityNotifyTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return ENUM_MAP.get(type);
    }

    /**
     * 是否需要通知
     * @param type 枚举值
     * @return true-需要通知，false-不需要通知
     */
    public boolean needNotify() {
        return this != NOT_NOTIFY;
    }
}
