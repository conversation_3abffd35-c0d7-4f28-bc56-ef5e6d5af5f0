package cn.com.duiba.paycenter.enums.ybs;

/**
 * @ClassName YbsRefundStatusEnum
 * @Description 62vip退款枚举
 * <AUTHOR>
 * @Date 2022/8/29 0:06
 */
public enum YbsRefundStatusEnum {
    SUCCESS("SUCCESS", "处理成功"),
    PARAM_ERROR("PARAM_ERROR", "参数错误"),
    ORDER_NOT_EXIST("ORDER_NOT_EXIST", "支付订单不存在"),
    ORDER_NOT_PAID("ORDER_NOT_PAID", "订单未支付"),
    REFUND_AMOUNT_ERROR("REFUND_AMOUNT_ERROR", "退款金额大于支付金额"),
    REFUND_ORDER_STATUS_ERROR("REFUND_ORDER_STATUS_ERROR", "不是可退款状态"),
    HANDLER_NOT_EXIST("HANDLER_NOT_EXIST", "退款handler不存在"),
    DATA_ERROR("DATA_ERROR", "支付参数错误"),
    NOT_SUPPORT("NOT_SUPPORT", "自有模式不支持退款"),
    FAIL("FAIL", "失败"),
    REFUND_ORDER_NOT_EXIST("REFUND_ORDER_NOT_EXIST", "退款订单不存在"),
    REFUNDED("REFUNDED", "已退款"),
    REFUNDING("REFUNDING", "退款中"),
    SIGN_ERR("SIGN_ERR", "验签失败"),
            ;
    private String code;
    private String desc;

    YbsRefundStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
