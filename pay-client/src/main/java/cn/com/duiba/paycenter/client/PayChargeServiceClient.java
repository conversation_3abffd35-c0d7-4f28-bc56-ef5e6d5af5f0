package cn.com.duiba.paycenter.client;

import java.util.HashMap;
import java.util.Map;

import cn.com.duiba.paycenter.params.PayChargeExtraParams;
import cn.com.duiba.paycenter.result.PayChargeResult;
import cn.com.duiba.paycenter.service.PayChargeService;
import cn.com.duiba.paycenter.util.SignUtil;

public class PayChargeServiceClient {
	
	private PayChargeService payChargeService;
	/**
	 * 人工充值
	 * @param developerId
	 * @param manualApplyId
	 * @param money
	 * @return
	 */
	public RpcResult<PayChargeResult> chargeMoneyByManual(Long developerId,Long manualApplyId,Long money,PayChargeExtraParams p){
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("relationId", manualApplyId+"");
			params.put("money", money+"");
			String sign=SignUtil.sign(params);
			
			PayChargeResult ret= payChargeService.chargeMoneyByManual(developerId, manualApplyId, money,sign,p);
			
			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}
	/**
	 * 贝付在线充值
	 * @param developerId
	 * @param onlineApplyId
	 * @param money
	 * @return
	 */
	public RpcResult<PayChargeResult> chargeMoneyByOnline(Long developerId,Long onlineApplyId,Long money,PayChargeExtraParams p){
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("relationId", onlineApplyId+"");
			params.put("money", money+"");
			String sign=SignUtil.sign(params);
			
			PayChargeResult ret=payChargeService.chargeMoneyByOnline(developerId, onlineApplyId, money,sign,p);
			
			return new RpcResult<>(ret);
		} catch (Exception e){
			return new RpcResult<>(e);
		}
	}
	/**
	 * 当充值错误，需要减钱时，调用此方法
	 * 正常情况不会使用此方法
	 * @param developerId
	 * @param manualApplyId
	 * @param money
	 * @return
	 */
	public RpcResult<PayChargeResult> reduceMoneyByManual(Long developerId,Long manualApplyId,Long money,PayChargeExtraParams p){
		try {
			Map<String, String> params=new HashMap<>();
			params.put("developerId", developerId+"");
			params.put("relationId", manualApplyId+"");
			params.put("money", money+"");
			String sign=SignUtil.sign(params);
			
			PayChargeResult ret=payChargeService.reduceMoneyByManual(developerId, manualApplyId, money,sign,p);
			
			return new RpcResult<>(ret);
		} catch (Exception e){
			return new RpcResult<>(e);
		}
	}
	public void setPayChargeService(PayChargeService payChargeService) {
		this.payChargeService = payChargeService;
	}

}
