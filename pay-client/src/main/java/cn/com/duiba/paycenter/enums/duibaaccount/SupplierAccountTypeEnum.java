package cn.com.duiba.paycenter.enums.duibaaccount;

import java.util.Arrays;
import java.util.Objects;

/**
 * 供应商账户类型枚举
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/6-3:44 PM
 */
public enum SupplierAccountTypeEnum {

    BALANCE(1, "余额账户"),
    SETTLE(2, "待结算账户"),
    ;

    private Integer code;

    private String desc;

    SupplierAccountTypeEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code){
        if (Objects.isNull(code)){
            return "";
        }
        SupplierAccountTypeEnum accountTypeEnum = getByCode(code);
        if (Objects.nonNull(accountTypeEnum)){
            return accountTypeEnum.getDesc();
        }
        return "";
    }

    public static SupplierAccountTypeEnum getByCode(Integer code){
        return Arrays.stream(SupplierAccountTypeEnum.values()).filter(a -> a.code.equals(code)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
