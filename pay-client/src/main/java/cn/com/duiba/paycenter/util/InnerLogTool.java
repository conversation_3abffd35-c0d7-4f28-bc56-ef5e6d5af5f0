package cn.com.duiba.paycenter.util;

import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.request.AlipaySecurityRiskCustomerriskQueryRequest;
import com.alipay.api.response.AlipaySecurityRiskCustomerriskQueryResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * <AUTHOR>
 * @Title: InnerLogTool
 * @Description: inner日志打印工具
 * @date 2019/5/612:34
 */
public class InnerLogTool {

    private static final Logger INNERLOG = LoggerFactory.getLogger("innerLog");

    private InnerLogTool(){

    }

    /**
     * 风控预警 -- app账户支出 预警日志
     * @param appId
     * @param pay
     */
    public static void riskAppAccountOutLog(Long appId,Long pay){
        JSONObject json = new JSONObject();
        json.put("appId",appId);
        json.put("pay",pay);
        JSONObject jsonLog = new JSONObject();
        jsonLog.put("group", 2);//代表兑吧
        jsonLog.put("type",70);//代表 日志类型
        jsonLog.put("time", DateUtils.getSecondStr(new Date()));
        jsonLog.put("json", json);
        INNERLOG.info(jsonLog.toJSONString());
    }

    /**
     * 阿里同人日志
     */
    public static void alipaySecurityRiskCustomerriskLog(AlipaySecurityRiskCustomerriskQueryRequest request,
                                                         AlipaySecurityRiskCustomerriskQueryResponse response){
        JSONObject json = new JSONObject();
        json.put("request",request.getBizContent());
        json.put("response",response.getRiskResult());
        JSONObject jsonLog = new JSONObject();
        jsonLog.put("group", 2);//代表兑吧
        jsonLog.put("type",78);//代表 日志类型
        jsonLog.put("time", DateUtils.getSecondStr(new Date()));
        jsonLog.put("json", json);
        INNERLOG.info(jsonLog.toJSONString());
    }

}
