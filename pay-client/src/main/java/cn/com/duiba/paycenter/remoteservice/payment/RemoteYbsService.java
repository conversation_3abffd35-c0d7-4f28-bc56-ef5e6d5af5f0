package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cus.YbsNotifyReqDto;
import cn.com.duiba.paycenter.dto.payment.charge.cus.YbsOrderQueryReqDto;
import cn.com.duiba.paycenter.dto.payment.charge.cus.YbsOrderQueryRespDto;
import cn.com.duiba.paycenter.dto.payment.charge.cus.YbsRefundQueryResp;
import cn.com.duiba.paycenter.dto.payment.charge.cus.YbsRefundQueryDto;

/**
 * 深圳银联易办事支付通知
 *
 * <AUTHOR>
 */
@AdvancedFeignClient
public interface RemoteYbsService {

    /**
     * 订单支付通知
     *
     * @param ybsNotifyReqDto 易办事支付通知接口
     * @return 响应
     */
    BaseChargeNotifyResponse orderNotify(YbsNotifyReqDto ybsNotifyReqDto);


    /**
     * 订单查询接口
     *
     * @param ybsOrderQueryReqDto 查询条件
     * @return 订单详细信息
     */
    YbsOrderQueryRespDto orderQuery(YbsOrderQueryReqDto ybsOrderQueryReqDto);


    /**
     * 签名验证
     *
     * @param data       kv类型的参数(不包含sign、秘钥)
     * @param originSign 签名原串
     * @return true: 签名正确
     */
    boolean signVerify(String data, String originSign);


    /**
     * 测试
     * @return 结果
     */
    String chargeTest();

    /**
     * 退款进度查询
     */
    YbsRefundQueryResp doRefundQuery(YbsRefundQueryDto entity);
}
