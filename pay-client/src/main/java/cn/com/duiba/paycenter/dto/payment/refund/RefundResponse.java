package cn.com.duiba.paycenter.dto.payment.refund;

import cn.com.duiba.paycenter.enums.RefundErrorCodeEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/11/27
 */
public class RefundResponse implements Serializable {

    private static final long serialVersionUID = -4075353277189386123L;

    private String code;
    private String msg;
    private String refundOrderNo;

    public String getRefundOrderNo() {
        return refundOrderNo;
    }

    public void setRefundOrderNo(String refundOrderNo) {
        this.refundOrderNo = refundOrderNo;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public boolean isSuccess() {
        return RefundErrorCodeEnum.SUCCESS.getCode().equals(code);
    }
}
