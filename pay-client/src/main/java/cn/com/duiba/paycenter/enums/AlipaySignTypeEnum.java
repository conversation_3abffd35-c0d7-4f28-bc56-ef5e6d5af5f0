package cn.com.duiba.paycenter.enums;

/**
 * <AUTHOR>
 * @date 2018/12/01
 */
public enum AlipaySignTypeEnum {
    RSA(1),
    RSA2(2),
    MD5(3)
    ;
    private Integer code;

    AlipaySignTypeEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public static String getSignType(Integer code) {
        if (code == null) {
            return null;
        }
        for (AlipaySignTypeEnum signTypeEnum : AlipaySignTypeEnum.values()) {
            if (signTypeEnum.getCode().equals(code)) {
                return signTypeEnum.name();
            }
        }
        return null;
    }
}
