package cn.com.duiba.paycenter.enums;

import com.google.common.collect.ImmutableSet;

/**
 * Created by xiaoxuda on 2018/1/30.
 */
public enum WithdrawBizTypeEnum {
    USER_MILLIONAIRE("冲顶大会用户奖金提现"),
    USER_GLOBAL_REWARD("全局红包用户奖金提现"),
    DUIBA_SUPPLY_ORDER("兑吧采购单扣款"),
    ;

    public static ImmutableSet userWithdraw = ImmutableSet.of(USER_MILLIONAIRE, USER_GLOBAL_REWARD, DUIBA_SUPPLY_ORDER);

    public static boolean isUserWithdraw(WithdrawBizTypeEnum bizType){
        return userWithdraw.contains(bizType);
    }

    private String desc;

    WithdrawBizTypeEnum(String desc){
        this.desc = desc;
    }
}
