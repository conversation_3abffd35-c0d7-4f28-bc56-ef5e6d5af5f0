package cn.com.duiba.paycenter.service;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.params.DuibaPayOrdersExtraParams;
import cn.com.duiba.paycenter.result.PayOrdersResult;

/**
 * Created by hww on 2018/10/21 下午11:31.
 */
@AdvancedFeignClient(
        qualifier = "duibaPayAccountDrawService"
)
public interface DuibaPayAccountDrawService {


    /**
     * 为支持全局账户提现扣款可以走兑吧账户
     * 新增可以指定relation类型的扣款接口
     * 功能同payOrder
     * */
    PayOrdersResult pay(Long orderId, Long money, String sign, DuibaPayOrdersExtraParams p) ;

    /**
     * 为支持全局账户提现扣款可以走兑吧账户
     * 新增可以指定relation类型的扣款返还接口
     * 功能同backpayOrder
     *
     * */
    PayOrdersResult backPay(Long orderId, Long money, String sign) ;
}
