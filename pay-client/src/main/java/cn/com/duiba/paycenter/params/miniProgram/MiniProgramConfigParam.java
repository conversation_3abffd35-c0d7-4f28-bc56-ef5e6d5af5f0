package cn.com.duiba.paycenter.params.miniProgram;


import cn.com.duiba.paycenter.enums.ChannelModeEnum;

import java.io.Serializable;

public class MiniProgramConfigParam implements Serializable {

    /**
     * 小程序配置id
     */
    Long id;

    /**
     * 兑吧appid
     */
    Long duibaAppId;

    /**
     * 商户id
     */
    String murId;

    /**
     * 微信appid
     */
    String wxAppId;

    /**
     * 微信端的sercert
     */
    String wxAppSercert;

    /**
     * api_key  小程序接入，看该应用绑定的上那个主体 需要这边需要判断（自主模式允许配置）
     * 传入的apiKey 对于的主体，跟开发者的主体是否一致
     * 如果不一致，不允许配置
     *
     * 兑吧固定值E08C39D9A185C27E8563C115872AFDEE
     * 兑啊固定值vZHnu5znp5HmioDmnInpmZDlhazlj7gx
     */
    String apiKey;

    /**
     * 商户apiCert
     */
    private String apiCert;

    /**
     * 渠道模式
     *
     * @see ChannelModeEnum#getCode()
     */
    private Integer channelMode;

    /**
     * 提现费率
     */
    Integer rate = 60;



    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDuibaAppId() {
        return duibaAppId;
    }

    public void setDuibaAppId(Long duibaAppId) {
        this.duibaAppId = duibaAppId;
    }

    public String getMurId() {
        return murId;
    }

    public void setMurId(String murId) {
        this.murId = murId;
    }

    public String getWxAppId() {
        return wxAppId;
    }

    public void setWxAppId(String wxAppId) {
        this.wxAppId = wxAppId;
    }

    public String getWxAppSercert() {
        return wxAppSercert;
    }

    public void setWxAppSercert(String wxAppSercert) {
        this.wxAppSercert = wxAppSercert;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getApiCert() {
        return apiCert;
    }

    public void setApiCert(String apiCert) {
        this.apiCert = apiCert;
    }

    public Integer getChannelMode() {
        return channelMode;
    }

    public void setChannelMode(Integer channelMode) {
        this.channelMode = channelMode;
    }

    public Integer getRate() {
        return rate;
    }

    public void setRate(Integer rate) {
        this.rate = rate;
    }
}
