package cn.com.duiba.paycenter.dto.payment.charge;

import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * 支付收单回执
 *
 * <AUTHOR>
 * @date 2018/11/14
 */
public abstract class BaseChargeResponse implements Serializable {
    private static final long serialVersionUID = 5310381202979072350L;
    /**
     * 第三方支付下单是否成功
     */
    private boolean success;
    /**
     * 第三方支付错误信息描述
     */
    private String message;
    /**
     * 支付流水号
     */
    private String orderNo;

    /**
     * 三方支付单号，选填
     */
    private String transactionNo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }


    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 将账户信息等存入extra中
     * @return jsonString
     */
    public abstract String getExtra();

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }
}
