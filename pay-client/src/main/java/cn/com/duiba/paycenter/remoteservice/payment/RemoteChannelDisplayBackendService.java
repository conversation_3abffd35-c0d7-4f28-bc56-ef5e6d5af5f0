package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.ChannelDisplayDto;

import java.util.List;

/**
 * 支付渠道显示服务
 * <AUTHOR>
 * @date 2018/11/01
 */
@AdvancedFeignClient
public interface RemoteChannelDisplayBackendService {
    /**
     * 更新app纬度前端支付渠道显示
     * @param appId appId
     * @param displayStatus 显示状态 0 不显示 1 显示
     * @param channelId 渠道id 1 微信 2 支付宝
     * @return 是否更新成功
     * @throws BizException bizException
     */
    boolean updateDisplay(Long appId, Integer displayStatus, Integer channelId) throws BizException;

    /**
     * 手机端获取app纬度的支付显示配置列表
     * @param appId appId
     * @return List<ChannelDisplayDto>
     * @throws BizException bizException
     */
    List<ChannelDisplayDto> listByAppId(Long appId) throws BizException;
}
