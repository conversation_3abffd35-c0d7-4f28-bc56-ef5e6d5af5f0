package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbOneNetPayNotifyRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbOneNetPayNotifyResponse;

/**
 * @program: pay-center
 * @description: 招商银行一网通支付通知
 * @author: Simba
 * @create: 2019-09-26 14:25
 **/
@AdvancedFeignClient
public interface RemoteCmbOneNetPayNotifyService {
    /**
     * 招商银行一网通支付通知
     *
     * @param request
     * @return
     * @throws BizException
     */
    CmbOneNetPayNotifyResponse orderNotify(CmbOneNetPayNotifyRequest request) throws BizException;

}