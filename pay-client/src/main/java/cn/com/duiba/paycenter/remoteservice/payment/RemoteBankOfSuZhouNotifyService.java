package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.bankofsuzhou.BankOfSuZhouChargeNotifyResponse;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/06/12
 */
@AdvancedFeignClient
public interface RemoteBankOfSuZhouNotifyService {
    /**
     * 苏州银行支付通知
     * @param map 参数
     * @return response
     * @throws BizException bizException
     */
    BankOfSuZhouChargeNotifyResponse orderNotify(Map<String, String> map) throws BizException;
}
