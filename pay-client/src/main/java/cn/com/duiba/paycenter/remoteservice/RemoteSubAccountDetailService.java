package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.SubAccountDetailDto;

import java.util.List;

/**
 * @Description 开发者余额子账户详情
 * @Date 2023/2/20 15:08
 * <AUTHOR>
 */
@AdvancedFeignClient
public interface RemoteSubAccountDetailService {

    /**
     * 根据子账户id分页查询
     * @param subAccountId
     * @param pageNo
     * @param pageSize
     * @return
     */
    Page<SubAccountDetailDto> pageBySubAccountId(Long subAccountId, Integer pageNo, Integer pageSize);

    /**
     * 批量插入子账户详情
     * @param list
     * @return
     */
    Boolean batchInsert(List<SubAccountDetailDto> list);
}
