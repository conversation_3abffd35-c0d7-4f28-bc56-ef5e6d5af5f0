package cn.com.duiba.paycenter.remoteservice.duibaaccount;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.duibaaccount.SupplierAccountDetailPageDto;
import cn.com.duiba.paycenter.dto.duibaaccount.SupplierSettleAccountDetailDto;
import cn.com.duiba.paycenter.params.duibaaccount.SupplierAccountDetailQryParams;

import java.util.List;

/**
 * 供应商待结算账户明细remote
 * author zhanghuifeng
 * date 2018-12-13-14:24
 */
@AdvancedFeignClient
public interface RemoteSupplierSettleAccountDetailService {

    /**
     * 分页查询
     * @param params
     * @return
     */
    List<SupplierAccountDetailPageDto> find4Page(SupplierAccountDetailQryParams params);

    /**
     * 分页查询总数
     * @param params
     * @return
     */
    Integer count4page(SupplierAccountDetailQryParams params);

    /**
     * 根据主键和供应商ID查询
     * @param id
     * @return
     */
    SupplierSettleAccountDetailDto findByIdAndSupplierId(Long id, Long supplierId);

    /**
     * 根据业务ID和业务类型查询当前供应商待结算账户明细
     * @param relationId    业务ID(必填)
     * @param relationTypes 业务类型集合(必填)
     * @see cn.com.duiba.paycenter.enums.duibaaccount.SupplierAccountRelationTypeEnum
     * @param supplierId    供应商ID(必填)
     * @return
     */
    List<SupplierSettleAccountDetailDto> findByRelationAndSupplierId(String relationId, List<Integer> relationTypes, Long supplierId);
}
