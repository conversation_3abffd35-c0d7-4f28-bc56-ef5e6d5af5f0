package cn.com.duiba.paycenter.enums;

/**
 * 微信现金红包-红包状态枚举
 *
 * <AUTHOR>
 * @date 2020/04/15
 */
public enum RedPacketStatusTypeEnum {
    SENDING("SENDING", "发放中"),
    SENT("SENT", "已发放待领取"),
    FAILED("FAILED", "发放失败"),
    RECEIVED("RECEIVED", "已领取"),
    RFUND_ING("RFUND_ING", "退款中"),
    REFUND("REFUND", "已退款");

    /**
     * 状态编码
     */
    private String code;

    /**
     * 状态描述
     */
    private String desc;


    RedPacketStatusTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
