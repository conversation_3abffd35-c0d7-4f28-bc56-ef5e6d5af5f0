package cn.com.duiba.paycenter.dto.payment.charge;

import cn.com.duiba.paycenter.validator.BizTypeEnumCheck;
import cn.com.duiba.paycenter.validator.ChannelEnumCheck;
import org.apache.commons.lang.builder.ToStringBuilder;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 支付请求的基本类
 * <AUTHOR>
 * @date 2018/11/12
 */
public abstract class BaseChargeRequest implements Serializable {
    private static final long serialVersionUID = 9164182528682621687L;
    /**
     * 支付系统流水号
     * =6位随机字符+bizOrderNo
     */
    private String orderNo;
    /**
     * 支付金额，单位为分
     */
    @NotNull(message = "缺少支付金额")
    @Min(value = 1, message = "最少支付一分钱")
    private Integer amount;
    /**
     * 上游调用方的订单号
     */
    @NotBlank(message = "业务方订单号不能为空")
    @Size(max = 25, message = "业务方订单号不能超过25位")
    private String bizOrderNo;
    /**
     * 分配给业务方的类型和bizOrderNo配合使用
     * bizType + bizOrderNo 保证唯一
     * @see cn.com.duiba.paycenter.enums.BizTypeEnum
     */
    @NotNull(message = "bizType不能为空")
    @BizTypeEnumCheck
    private Integer bizType;
    /**
     * 支付渠道
     * @see cn.com.duiba.paycenter.enums.ChannelEnum
     */
    @NotNull(message = "channelType不能为空")
    @ChannelEnumCheck
    private String channelType;
    /**
     * 兑吧appId
     */
    @NotNull(message = "appId不能为空")
    private Long appId;
   /**
     * 业务方查询需要的信息
     * 创建订单时自行放入，格式不做要求，业务方自行解析
     */
    @Size(max = 255, message = "metadata长度超出255")
    private String metadata;

    /**
     * 业务主体类型 -- 必传参数
     * @see cn.com.duiba.api.enums.SubjectTypeEnum
     */
    private String subjectType;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public String getSubjectType() {
        return subjectType;
    }

    public void setSubjectType(String subjectType) {
        this.subjectType = subjectType;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
