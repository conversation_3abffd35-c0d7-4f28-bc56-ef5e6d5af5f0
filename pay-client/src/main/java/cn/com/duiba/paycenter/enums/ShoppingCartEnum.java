package cn.com.duiba.paycenter.enums;

public enum ShoppingCartEnum {

    OLD_VERSION(0,"老版本非定制"),
    NEW_VERSION_SHOPPINGCART(1,"兑吧商品加购物车版本购物车"),
    NEW_VERSION_MALL(2,"兑吧商品加购物车版本普兑")
    ;

    private Integer code;
    private String desc;

    ShoppingCartEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
