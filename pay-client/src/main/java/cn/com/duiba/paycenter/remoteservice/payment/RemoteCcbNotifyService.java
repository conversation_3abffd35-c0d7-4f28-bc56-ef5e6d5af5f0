package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.ccb.CcbChargeNotifyResponse;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/09/03
 */
@AdvancedFeignClient
public interface RemoteCcbNotifyService {
    /**
     * 建行支付通知
     * @param map params
     * @return response
     * @throws BizException exception
     */
    CcbChargeNotifyResponse orderNotify(Map<String, String> map) throws BizException;
}
