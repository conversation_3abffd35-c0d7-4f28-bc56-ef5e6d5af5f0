package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.shouxin.ShouxinPayChargeNotifyRequest;
import cn.com.duiba.paycenter.dto.payment.charge.shouxin.ShouxinPayChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.refund.shouxin.ShouxinPayRefundNotifyRequest;
import cn.com.duiba.paycenter.dto.payment.refund.shouxin.ShouxinPayRefundNotifyResponse;

/**
 *
 * <AUTHOR>
 * @date 2019/11/21
 */
@AdvancedFeignClient
public interface RemoteShouxinPayNotifyService {
    /**
     * 首信支付通知
     * @param shouxinPayChargeNotifyRequest
     * @return response
     * @throws BizException exception
     */
    ShouxinPayChargeNotifyResponse orderNotify(ShouxinPayChargeNotifyRequest shouxinPayChargeNotifyRequest) throws BizException;

    /**
     * 首信退款通知
     * @param shouxinPayRefundNotifyRequest
     * @return response
     * @throws BizException exception
     */
    ShouxinPayRefundNotifyResponse refundNotify(ShouxinPayRefundNotifyRequest shouxinPayRefundNotifyRequest);
}
