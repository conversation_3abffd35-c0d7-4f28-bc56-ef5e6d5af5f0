package cn.com.duiba.paycenter.enums.duibaaccount;

/**
 * 开发者账户金额变化类型枚举
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/7-11:20 AM
 */
public enum AppAccountChangeKindEnum {
    ADD(1, "ADD"),
    SUB(2, "SUB");

    private Integer code;

    private String desc;

    AppAccountChangeKindEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
