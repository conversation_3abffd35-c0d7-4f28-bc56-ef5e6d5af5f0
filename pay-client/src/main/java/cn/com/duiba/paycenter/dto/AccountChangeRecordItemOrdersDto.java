package cn.com.duiba.paycenter.dto;

import java.io.Serializable;
import java.util.Date;

public class AccountChangeRecordItemOrdersDto implements Serializable {

    private static final long serialVersionUID = 5174005463754521891L;
    private Long id;
    private Long appId;
    private Long orderId;
    private Long orderItemId;
    private Long money;
    private Integer itemRecordType;
    private Integer itemRecordStatus;
    private Date gmtCreate;
    private Date gmtModified;
    private String orderNum;
    //备注
    private String brief;

    public String getBrief() {
        return brief;
    }

    public void setBrief(String brief) {
        this.brief = brief;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Long getMoney() {
        return money;
    }

    public void setMoney(Long money) {
        this.money = money;
    }

    public Integer getItemRecordStatus() {
        return itemRecordStatus;
    }

    public void setItemRecordStatus(Integer itemRecordStatus) {
        this.itemRecordStatus = itemRecordStatus;
    }

    public Integer getItemRecordType() {
        return itemRecordType;
    }

    public void setItemRecordType(Integer itemRecordType) {
        this.itemRecordType = itemRecordType;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
}
