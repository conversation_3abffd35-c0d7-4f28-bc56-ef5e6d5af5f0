package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.params.AppAccountChangeParams;
import cn.com.duiba.paycenter.result.PayCenterResult;

/**
 * 开发应用余额账户
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/7-5:51 PM
 */
@AdvancedFeignClient
public interface RemoteAppAccountService {

    /**
     * 开发应用余额账户扣款
     * @param param
     * @return
     */
    PayCenterResult reduceMoney(AppAccountChangeParams param,String sign);

    /**
     * 开发应用余额账户回款
     * @param param
     * @return
     */
    PayCenterResult addMoney(AppAccountChangeParams param,String sign);
}
