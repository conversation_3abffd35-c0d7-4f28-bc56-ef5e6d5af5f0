package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.params.AppSettleAccountParams;
import cn.com.duiba.paycenter.result.PayCenterResult;

/**
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/14-7:58 PM
 */
@AdvancedFeignClient
public interface RemoteAppSettleAccountService {

    /**
     * 开发应用账户出账
     * @param settleParams
     * @param sign
     * @return
     */
    PayCenterResult reduceMoney(AppSettleAccountParams settleParams, String sign);

    /**
     * 开发应用账户入账
     * @param settleParams
     * @return
     */
    PayCenterResult addMoney(AppSettleAccountParams settleParams,String sign);
}
