package cn.com.duiba.paycenter.client;

import java.util.HashMap;
import java.util.Map;


import cn.com.duiba.paycenter.constant.ActionTypes.OrdersActionType;
import cn.com.duiba.paycenter.params.DuibaPayOrdersExtraParams;
import cn.com.duiba.paycenter.result.PayOrdersResult;
import cn.com.duiba.paycenter.service.DuibaPayOrdersService;
import cn.com.duiba.paycenter.util.SignUtil;

public class DuibaPayOrdersServiceClient {

	private DuibaPayOrdersService duibaPayOrdersService;
	
	
	/**
	 * 主订单下单时的付款接口
	 * 
	 * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
	 * 
	 * @param developerId
	 * @param orderId
	 * @param money
	 */
	public RpcResult<PayOrdersResult> payOrder(Long orderId,Long money,DuibaPayOrdersExtraParams p){
		try {
			Map<String, String> params=new HashMap<>();
			params.put("orderId", orderId+"");
			params.put("money", money+"");
			String sign=SignUtil.sign(params);
			
			PayOrdersResult ret=duibaPayOrdersService.payOrder(orderId, money,sign,p);

			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}
	/**
	 * 主订单失败还款的接口
	 * 
	 * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
	 * 
	 * @param developerId
	 * @param orderId
	 * @param money
	 * @return
	 */
	public RpcResult<PayOrdersResult> backpayOrder(Long orderId,Long money){
		try {
			Map<String, String> params=new HashMap<>();
			params.put("orderId", orderId+"");
			params.put("money", money+"");
			String sign=SignUtil.sign(params);
			
			PayOrdersResult ret=duibaPayOrdersService.backpayOrder( orderId, money,sign);
			
			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}
	/**
	 * 检查一个操作是否执行成功
	 * @param orderId
	 * @param actionType
	 * @return
	 */
	public RpcResult<Boolean> checkActionSuccess(Long orderId,OrdersActionType actionType){
		try {
			boolean ret=duibaPayOrdersService.checkActionSuccess(orderId, actionType.getKey());

			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}
	

	public void setDuibaPayOrdersService(DuibaPayOrdersService duibaPayOrdersService) {
		this.duibaPayOrdersService = duibaPayOrdersService;
	}
	
	
}
