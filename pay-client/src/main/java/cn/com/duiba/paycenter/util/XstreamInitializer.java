package cn.com.duiba.paycenter.util;

import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.converters.reflection.PureJavaReflectionProvider;
import com.thoughtworks.xstream.core.util.QuickWriter;
import com.thoughtworks.xstream.io.HierarchicalStreamWriter;
import com.thoughtworks.xstream.io.xml.PrettyPrintWriter;
import com.thoughtworks.xstream.io.xml.XppDriver;
import com.thoughtworks.xstream.security.NullPermission;
import com.thoughtworks.xstream.security.PrimitiveTypePermission;

import java.io.Writer;

/**
 * <AUTHOR>
 * @date 2018/11/15
 */
public class XstreamInitializer {
    private static final String PREFIX_CDATA = "<![CDATA[";
    private static final String SUFFIX_CDATA = "]]>";
    private static final String PREFIX_MEDIA_ID = "<MediaId>";
    private static final String SUFFIX_MEDIA_ID = "</MediaId>";

    private XstreamInitializer() {
    }

    public static XStream getInstance() {
        XStream xStream = new XStream(new PureJavaReflectionProvider(), new XppDriver() {
            @Override
            public HierarchicalStreamWriter createWriter(Writer out) {
                return new PrettyPrintWriter(out, getNameCoder()) {

                    @Override
                    protected void writeText(QuickWriter writer, String text) {
                        //CDATA标签用于说明数据不被XML解析器解析
                        if (text.startsWith(PREFIX_CDATA) && text.endsWith(SUFFIX_CDATA)) {
                            writer.write(text);
                        } else if (text.startsWith(PREFIX_MEDIA_ID) && text.endsWith(SUFFIX_MEDIA_ID)) {
                            writer.write(text);
                        } else {
                            super.writeText(writer, text);
                        }

                    }

                    @Override
                    public String encodeNode(String name) {
                        //防止将_转换成__
                        return name;
                    }
                };
            }
        });
        xStream.ignoreUnknownElements();
        xStream.setMode(XStream.NO_REFERENCES);
        xStream.addPermission(NullPermission.NULL);
        xStream.addPermission(PrimitiveTypePermission.PRIMITIVES);
        xStream.setClassLoader(Thread.currentThread().getContextClassLoader());
        return xStream;
    }
}
