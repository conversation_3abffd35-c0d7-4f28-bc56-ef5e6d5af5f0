package cn.com.duiba.paycenter.service;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.result.PayChargeResult;

/**
 * 实际上是RemoteService
 */
@AdvancedFeignClient(qualifier = "duibaPayChargeService")
public interface DuibaPayChargeService {
	/**
	 * 人工充值
	 * @param manualApplyId
	 * @param money
	 * @return
	 */
	PayChargeResult chargeMoneyByManual(Long manualApplyId,Long money,String sign);
	/**
	 * 当充值错误，需要减钱时，调用此方法
	 * 正常情况不会使用此方法
	 * @param developerId
	 * @param manualApplyId
	 * @param money
	 * @param memo
	 * @return
	 */
	PayChargeResult reduceMoneyByManual(Long manualApplyId,Long money,String sign);

}
