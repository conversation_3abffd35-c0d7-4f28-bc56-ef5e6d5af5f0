package cn.com.duiba.paycenter.dto.payment.refund;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/11/27
 */
public class RefundRequest implements Serializable {

    private static final long serialVersionUID = -1721185925672482225L;
    /**
     * 支付订单id 支付订单和第三方支付流水号二选一
     */
    private String chargeOrderNo;
    /**
     * 第三方支付流水号
     */
    private String transactionNo;
    /**
     * 需要退款的金额，必须小于支付金额
     * 单位为分
     */
    @NotNull(message = "退款金额不能为空")
    @Min(value = 1, message = "最少退款一分钱")
    private Integer amount;
    @Size(max = 255, message = "extra最长255")
    private String extra;
    @Size(max = 255, message = "description最长255")
    private String description;
    /**
     * 业务主体 - 必传参数
     * @see cn.com.duiba.api.enums.SubjectTypeEnum
     */
    private String subjectType;

    /**
     * 退款回调通知的url：不错走默认
     */
    private String callbackUrl;

    //主订单子订单号, 支持购物车按每种商品退
    private Long ordersItemId;

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getChargeOrderNo() {
        return chargeOrderNo;
    }

    public void setChargeOrderNo(String chargeOrderNo) {
        this.chargeOrderNo = chargeOrderNo;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getSubjectType() {
        return subjectType;
    }

    public void setSubjectType(String subjectType) {
        this.subjectType = subjectType;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public Long getOrdersItemId() {
        return ordersItemId;
    }

    public void setOrdersItemId(Long ordersItemId) {
        this.ordersItemId = ordersItemId;
    }
}
