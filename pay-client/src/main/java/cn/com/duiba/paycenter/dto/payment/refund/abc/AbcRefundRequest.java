package cn.com.duiba.paycenter.dto.payment.refund.abc;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/07/30
 */
public class AbcRefundRequest implements Serializable {
    private static final long serialVersionUID = -6359467012078035648L;
    /**
     * 退款金额
     */
    @NotNull(message = "金额不能为空")
    @Min(value = 1, message = "金额必须大于1分")
    private Integer amount;
    /**
     * 主订单号
     */
    @NotNull(message = "主订单号不能为空")
    private String bizOrderNo;

    /**
     * 用于区分 底层用兑吧在农行哪家支行开户的账户进行 交易
     * 如果不传 默认走 兑吧在农总行的账户！！！！
     * @see  AbcAccountTypeEnum
     */
    private Integer duibaAbcAccountType;

    /**
     * 业务类型：
     * @see cn.com.duiba.paycenter.enums.BizTypeEnum
     */
    private Integer bizType;

    //主订单子订单号, 支持购物车按每种商品退
    private Long ordersItemId;

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public Integer getDuibaAbcAccountType() {
        return duibaAbcAccountType;
    }

    public void setDuibaAbcAccountType(Integer duibaAbcAccountType) {
        this.duibaAbcAccountType = duibaAbcAccountType;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public Long getOrdersItemId() {
        return ordersItemId;
    }

    public void setOrdersItemId(Long ordersItemId) {
        this.ordersItemId = ordersItemId;
    }
}
