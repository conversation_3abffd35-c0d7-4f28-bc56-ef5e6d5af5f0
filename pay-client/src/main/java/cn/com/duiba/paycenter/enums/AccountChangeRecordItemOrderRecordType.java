package cn.com.duiba.paycenter.enums;

public enum AccountChangeRecordItemOrderRecordType {

    CANCEL_SHIP(0, "取消发货"),
    POSTSALE(1, "售后");

    private Integer code;

    private String desc;

    AccountChangeRecordItemOrderRecordType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
