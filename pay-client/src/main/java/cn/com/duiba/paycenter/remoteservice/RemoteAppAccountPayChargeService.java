package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.params.AppAccountPayChargeParams;
import cn.com.duiba.paycenter.result.PayCenterResult;

/**
 * 开发者账户充值服务
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/7-11:29 AM
 */
@AdvancedFeignClient
public interface RemoteAppAccountPayChargeService {

    /**
     * 人工充值
     * @param chargeParams
     * @param sign
     * @return
     */
    PayCenterResult chargeMoneyByManual(AppAccountPayChargeParams chargeParams, String sign);

    /**
     * 在线充值
     * @param chargeParams
     * @param sign
     * @return
     */
    PayCenterResult chargeMoneyByOnline(AppAccountPayChargeParams chargeParams, String sign);

    /**
     * 人工减款
     * @param chargeParams
     * @param sign
     * @return
     */
    PayCenterResult reduceMoneyByManual(AppAccountPayChargeParams chargeParams, String sign);

    /**
     * 在线充值手续费
     * @param chargeParams
     * @param sign
     * @return
     */
    PayCenterResult chargeMoneyFeeByOnline(AppAccountPayChargeParams chargeParams, String sign);

}
