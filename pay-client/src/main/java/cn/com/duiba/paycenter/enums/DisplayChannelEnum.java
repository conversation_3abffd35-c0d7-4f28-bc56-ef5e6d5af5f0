package cn.com.duiba.paycenter.enums;

/**
 * <AUTHOR>
 * @date 2018/11/07
 */
public enum DisplayChannelEnum {
    WX(1, "微信支付"),
    ALIPAY(2, "支付宝")
    ;
    private Integer code;
    private String desc;

    DisplayChannelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
