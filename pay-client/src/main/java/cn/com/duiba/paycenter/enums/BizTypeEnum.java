package cn.com.duiba.paycenter.enums;

/**
 * 支付中心分配的业务方类型
 * <AUTHOR>
 * @date 2018/11/12
 */
public enum BizTypeEnum {

    /**
     * 订单支付
     */
    ORD(1),

    /**
     * 充值支付
     */
    RCG(2),
    /**
     * 客集集
     */
    KJJ(3),
    /**
     * 客集集微信扫码支付
     */
    KJJ_QR(4),

    /**
     * 建行定制需求支付增加次数
     */
    CCB_TIMES(5),

    /**
     * 客集集红包
     */
    KJJ_RED_PACKET(6),

    /**
     * 星速台支付
     */
    XST(7),

    /**
     * 活动工具支付
     */
    ACTIVITY(8),

    /**
     * 平安小程序支付
     */
    PINGAN(9)
    ;

    private Integer code;

    BizTypeEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public static BizTypeEnum getBizTypeEnumByCode(Integer code) {
        for (BizTypeEnum bizTypeEnum : BizTypeEnum.values()) {
            if (bizTypeEnum.getCode().equals(code)) {
                return bizTypeEnum;
            }
        }
        return null;
    }
}

