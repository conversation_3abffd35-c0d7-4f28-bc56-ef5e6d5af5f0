package cn.com.duiba.paycenter.params;

import cn.com.duiba.paycenter.enums.duibaaccount.AccountTransferInAndOutEnum;
import cn.com.duiba.paycenter.enums.duibaaccount.AppAccountRelationTypeEnum;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/14-7:54 PM
 */
public class AppSettleAccountParams implements Serializable {

    private static final long serialVersionUID = -4879686125463929279L;
    /**
     * 开发者ID
     */
    @NotNull
    private Long developerId;

    /**
     * 应用ID
     */
    @NotNull
    private Long appId;

    /**
     * 业务ID
     */
    @NotBlank
    private String relationId;

    @NotNull
    private Long money;

    @NotNull
    private AppAccountRelationTypeEnum relationType;

    /**
     * 转入方
     */
    private AccountTransferInAndOutEnum transferIn;

    /**
     * 转出方
     */
    private AccountTransferInAndOutEnum transferOut;

    private String memo;

    public Long getDeveloperId() {
        return developerId;
    }

    public void setDeveloperId(Long developerId) {
        this.developerId = developerId;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public Long getMoney() {
        return money;
    }

    public void setMoney(Long money) {
        this.money = money;
    }

    public AppAccountRelationTypeEnum getRelationType() {
        return relationType;
    }

    public void setRelationType(AppAccountRelationTypeEnum relationType) {
        this.relationType = relationType;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public AccountTransferInAndOutEnum getTransferIn() {
        return transferIn;
    }

    public void setTransferIn(AccountTransferInAndOutEnum transferIn) {
        this.transferIn = transferIn;
    }

    public AccountTransferInAndOutEnum getTransferOut() {
        return transferOut;
    }

    public void setTransferOut(AccountTransferInAndOutEnum transferOut) {
        this.transferOut = transferOut;
    }
}
