package cn.com.duiba.paycenter.dto.payment;

import java.io.Serializable;
import java.util.Date;

/**
* 微信红包订单流水
*/
public class WxRedpacketOrderDto implements Serializable {

    private static final long serialVersionUID = -620403735283266784L;
    /**
    * 主键
    */
    private Long id;

    /**
    * 业务方关联活动类型
    */
    private Integer bizRelationType;

    /**
    * 业务方关联活动id
    */
    private String bizRelationId;

    /**
    * 业务方订单号
    */
    private String bizOrderNo;

    /**
    * 商户订单号/支付订单号
    */
    private String mchBillno;

    /**
    * 红包订单的微信单号
    */
    private String sendListid;

    /**
    * 微信支付分配的商户号
    */
    private String mchId;

    /**
    * 微信分配的公众账号ID（企业号corpid即为此appId）
    */
    private String wxappid;

    /**
    * 红包发送者名称
    */
    private String sendName;

    /**
    * 接受红包的用户openid
    */
    private String reOpenid;

    /**
    * 付款金额/红包总金额，单位分
    */
    private Integer totalAmount;

    /**
    * 红包发放总人数
    */
    private Integer totalNum;

    /**
    * 红包祝福语
    */
    private String wishing;

    /**
    * 调用接口的机器Ip地址
    */
    private String clientIp;

    /**
    * 活动名称
    */
    private String actName;

    /**
    * 备注
    */
    private String remark;

    /**
    * 场景id，发放红包使用场景，红包金额大于200或者小于1元时必传
    */
    private String sceneId;

    /**
    * 活动信息
    */
    private String riskInfo;

    /**
    * 红包状态：SENDING:发放中、SENT:已发放待领取、FAILED：发放失败、RECEIVED:已领取、RFUND_ING:退款中、REFUND:已退款 
    */
    private String status;

    /**
    * 红包类型
    */
    private String sendType;

    /**
    * 红包类型
    */
    private String hbType;

    /**
    * 发送失败原因
    */
    private String reason;

    /**
    * 红包发送时间 
    */
    private String sendTime;

    /**
    * 红包退款时间  
    */
    private String refundTime;

    /**
    * 红包退款金额   
    */
    private Integer refundAmount;

    /**
    * 领取红包的时间     
    */
    private String rcvTime;

    /**
    * 创建时间
    */
    private Date gmtCreate;

    /**
    * 修改时间
    */
    private Date gmtModified;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setBizRelationType(Integer bizRelationType) {
        this.bizRelationType = bizRelationType;
    }

    public Integer getBizRelationType() {
        return bizRelationType;
    }

    public void setBizRelationId(String bizRelationId) {
        this.bizRelationId = bizRelationId;
    }

    public String getBizRelationId() {
        return bizRelationId;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setMchBillno(String mchBillno) {
        this.mchBillno = mchBillno;
    }

    public String getMchBillno() {
        return mchBillno;
    }

    public void setSendListid(String sendListid) {
        this.sendListid = sendListid;
    }

    public String getSendListid() {
        return sendListid;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getMchId() {
        return mchId;
    }

    public void setWxappid(String wxappid) {
        this.wxappid = wxappid;
    }

    public String getWxappid() {
        return wxappid;
    }

    public void setSendName(String sendName) {
        this.sendName = sendName;
    }

    public String getSendName() {
        return sendName;
    }

    public void setReOpenid(String reOpenid) {
        this.reOpenid = reOpenid;
    }

    public String getReOpenid() {
        return reOpenid;
    }

    public void setTotalAmount(Integer totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getTotalAmount() {
        return totalAmount;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setWishing(String wishing) {
        this.wishing = wishing;
    }

    public String getWishing() {
        return wishing;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setActName(String actName) {
        this.actName = actName;
    }

    public String getActName() {
        return actName;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    public void setSceneId(String sceneId) {
        this.sceneId = sceneId;
    }

    public String getSceneId() {
        return sceneId;
    }

    public void setRiskInfo(String riskInfo) {
        this.riskInfo = riskInfo;
    }

    public String getRiskInfo() {
        return riskInfo;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setSendType(String sendType) {
        this.sendType = sendType;
    }

    public String getSendType() {
        return sendType;
    }

    public void setHbType(String hbType) {
        this.hbType = hbType;
    }

    public String getHbType() {
        return hbType;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getReason() {
        return reason;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public String getSendTime() {
        return sendTime;
    }

    public void setRefundTime(String refundTime) {
        this.refundTime = refundTime;
    }

    public String getRefundTime() {
        return refundTime;
    }

    public void setRefundAmount(Integer refundAmount) {
        this.refundAmount = refundAmount;
    }

    public Integer getRefundAmount() {
        return refundAmount;
    }

    public void setRcvTime(String rcvTime) {
        this.rcvTime = rcvTime;
    }

    public String getRcvTime() {
        return rcvTime;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

}

