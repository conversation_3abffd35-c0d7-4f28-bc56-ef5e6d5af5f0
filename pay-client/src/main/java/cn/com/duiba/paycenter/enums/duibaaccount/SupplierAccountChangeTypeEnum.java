package cn.com.duiba.paycenter.enums.duibaaccount;

/**
 * 供应商账户金额变更类型枚举
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/7-10:24 AM
 */
public enum SupplierAccountChangeTypeEnum {

    ADD(1, "入账，加"),
    SUB(2, "出账、减");

    private Integer code;

    private String desc;

    SupplierAccountChangeTypeEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
