package cn.com.duiba.paycenter.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付渠道手续费费率比例
 * Created by sunyan on 2020/3/17.
 */
public enum PayChannelFeeRateEnum {
    WEIXIN("weixin","微信支付",0.01d),
    ALIPAY("alipay","支付宝支付",0.006d)
    ;
    private String payChannelType;
    private String payChannelName;
    private double rate;

    PayChannelFeeRateEnum(String payChannelType, String payChannelName,double rate) {
        this.payChannelType = payChannelType;
        this.payChannelName = payChannelName;
        this.rate = rate;
    }

    public String getPayChannelType() {
        return payChannelType;
    }

    public double getRate() {
        return rate;
    }

    public String getPayChannelName() {
        return payChannelName;
    }

    private static final Map<String, PayChannelFeeRateEnum> ENUM_MAP = new HashMap<>();

    static{
        for(PayChannelFeeRateEnum tmp : values()){
            ENUM_MAP.put(tmp.getPayChannelType(), tmp);
        }
    }

    public static PayChannelFeeRateEnum getByType(String type) {
        return ENUM_MAP.get(type);
    }
}
