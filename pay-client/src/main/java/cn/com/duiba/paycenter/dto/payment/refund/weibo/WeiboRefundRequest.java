package cn.com.duiba.paycenter.dto.payment.refund.weibo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WeiboRefundRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 签名类型，目前仅支持 rsa
     */
    @JSONField(name = "sign_type")
    private String signType;

    /**
     * 签名内容
     */
    @JSONField(name = "sign")
    private String sign;

    /**
     * 服务商微博 UID，即接入该产品的业务方
     */
    @JSONField(name = "seller_id")
    private Long sellerId;

    /**
     * 外部退款单号，用于幂等判断
     */
    @JSONField(name = "out_refund_id")
    private String outRefundId;

    /**
     * 微博支付单号
     */
    @JSONField(name = "pay_id")
    private Long payId;

    /**
     * 退款金额，以分为单位。针对不同支付渠道有不同规则：
     * 1. 支付宝：
     *  - 仅退分账时传 0；
     *  - 传非 0，表示退款；
     * 2. 微信：
     *  - 退分账时，此字段不传；
     *  - 传非 0，表示退款；
     */
    @JSONField(name = "refund_amount")
    private Integer refundAmount;

    /**
     * 退款原因
     */
    @JSONField(name = "reason")
    private String reason;

    /**
     * 退分账信息（支付宝：支持同时退款和退分账；微信：不支持同时退款和退分账）
     */
    @JSONField(name = "refund_transfer")
    private List<RefundTransfer> refundTransfer;

    /**
     * 二级商户 id，仅退回平台服务费时不传
     */
    @JSONField(name = "sub_merchant_id")
    private Long subMerchantId;

    /**
     * 退分账金额，单位：分
     */
    @JSONField(name = "refund_transfer_amount")
    private Long refundTransferAmount;

    /**
     * 退分账描述
     */
    @JSONField(name = "refund_transfer_desc")
    private String desc;

    /**
     * 分账单号，微信退分账必传原分账单号，支付宝可不传此字段
     */
    @JSONField(name = "transfer_id")
    private Long transferId;

    /**
     * 是否退回平台服务费，true：是，false：否
     */
    @JSONField(name = "platform_account")
    private Boolean platformAccount;

    /**
     * 退分账信息对象
     */
    @Data
    public static class RefundTransfer implements Serializable {

        private static final long serialVersionUID = -2090736432060513809L;
        /**
         * 分账金额，单位：分
         */
        @JSONField(name = "transfer_amount")
        private Long transferAmount;

        /**
         * 分账描述
         */
        @JSONField(name = "transfer_desc")
        private String transferDesc;
    }
}
