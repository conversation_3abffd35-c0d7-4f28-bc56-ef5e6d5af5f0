package cn.com.duiba.paycenter.dto.payment.charge;

import com.alibaba.fastjson.annotation.JSONField;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * 支付收单回执
 *
 * <AUTHOR>
 * @date 2018/11/14
 */
public class WxCouponResponse implements Serializable {

    private static final long serialVersionUID = -6213293184094222165L;
    /**
     * 第三方错误信息描述
     */
    private String message;
    /**
     * 获得优惠券id
     */
    @JSONField(name = "coupon_id")
    private String couponId;

    /**
     * 对应错误code
     */
    private String code;

    private boolean success;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }


    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
