package cn.com.duiba.paycenter.validator.impl;

import cn.com.duiba.paycenter.validator.MinExpireTime;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018/11/13
 */
public class MinExpireTimeConstraintValidator implements ConstraintValidator<MinExpireTime, Date> {
    private static final int FIVE_MINUTES = 5;
    private String message;

    @Override
    public void initialize(MinExpireTime constraintAnnotation) {
        this.message = constraintAnnotation.message();
    }

    @Override
    public boolean isValid(Date  date, ConstraintValidatorContext context) {
        if (date == null) {
            return true;
        }
        //过期时间需要最少要比当前时间多5分钟
        //微信时间以东8区时间为准
        if (LocalDateTime.now().plus(FIVE_MINUTES, ChronoUnit.MINUTES)
                .isBefore(date.toInstant().atZone(ZoneId.of("GMT+08:00")).toLocalDateTime())) {
            return true;
        }
        context.disableDefaultConstraintViolation();
        ConstraintValidatorContext.ConstraintViolationBuilder builder =
                context.buildConstraintViolationWithTemplate(message);
        builder.addConstraintViolation();
        return false;
    }
}
