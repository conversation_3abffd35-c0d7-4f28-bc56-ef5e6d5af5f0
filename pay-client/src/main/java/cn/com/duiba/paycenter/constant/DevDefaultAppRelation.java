package cn.com.duiba.paycenter.constant;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 开发者默认应用关系
 * 只为存量开发者初始化开发者应用纬度账户使用
 * author z<PERSON><PERSON><PERSON>
 * date 2018/11/22-3:21 PM
 */
public class DevDefaultAppRelation {

    private static final Map<Long, Long> devIdToAppIdMap = new HashMap<>();

    static {
        devIdToAppIdMap.put(207L,59L);
        devIdToAppIdMap.put(542L,502L);
        devIdToAppIdMap.put(873L,25942L);
        devIdToAppIdMap.put(1183L,1538L);
        devIdToAppIdMap.put(1569L,2199L);
        devIdToAppIdMap.put(1801L,2781L);
        devIdToAppIdMap.put(2044L,2873L);
        devIdToAppIdMap.put(2113L,2997L);
        devIdToAppIdMap.put(2138L,3038L);
        devIdToAppIdMap.put(2179L,3103L);
        devIdToAppIdMap.put(3805L,5350L);
        devIdToAppIdMap.put(3831L,5387L);
        devIdToAppIdMap.put(5427L,7569L);
        devIdToAppIdMap.put(5527L,7693L);
        devIdToAppIdMap.put(8066L,11040L);
        devIdToAppIdMap.put(8460L,11565L);
        devIdToAppIdMap.put(9169L,12526L);
        devIdToAppIdMap.put(9396L,12827L);
        devIdToAppIdMap.put(10023L,14288L);
        devIdToAppIdMap.put(10683L,16298L);
        devIdToAppIdMap.put(11665L,15971L);
        devIdToAppIdMap.put(12842L,17844L);
        devIdToAppIdMap.put(14451L,19901L);
        devIdToAppIdMap.put(14721L,40177L);
        devIdToAppIdMap.put(16989L,23457L);
        devIdToAppIdMap.put(17000L,39884L);
        devIdToAppIdMap.put(17528L,24136L);
        devIdToAppIdMap.put(24897L,34081L);
        devIdToAppIdMap.put(26747L,43876L);
        devIdToAppIdMap.put(28246L,38731L);
        devIdToAppIdMap.put(28861L,45215L);
        devIdToAppIdMap.put(29034L,40001L);
        devIdToAppIdMap.put(36269L,49454L);
        devIdToAppIdMap.put(36722L,54988L);
        devIdToAppIdMap.put(38271L,53770L);

        devIdToAppIdMap.put(212L,61L);
        devIdToAppIdMap.put(255L,100L);
        devIdToAppIdMap.put(470L,2648L);
        devIdToAppIdMap.put(573L,641L);
        devIdToAppIdMap.put(628L,636L);
        devIdToAppIdMap.put(686L,731L);
        devIdToAppIdMap.put(802L,40656L);
        devIdToAppIdMap.put(858L,1009L);
        devIdToAppIdMap.put(875L,11105L);
        devIdToAppIdMap.put(1088L,1452L);
        devIdToAppIdMap.put(1131L,1448L);
        devIdToAppIdMap.put(1194L,1557L);
        devIdToAppIdMap.put(1197L,2428L);
        devIdToAppIdMap.put(1361L,1823L);
        devIdToAppIdMap.put(1554L,2145L);
        devIdToAppIdMap.put(1701L,5318L);
        devIdToAppIdMap.put(2163L,3106L);
        devIdToAppIdMap.put(2321L,3324L);
        devIdToAppIdMap.put(2341L,4388L);
        devIdToAppIdMap.put(2394L,3454L);
        devIdToAppIdMap.put(2397L,3427L);
        devIdToAppIdMap.put(2688L,3853L);
        devIdToAppIdMap.put(2909L,4176L);
        devIdToAppIdMap.put(3053L,4369L);
        devIdToAppIdMap.put(3151L,4511L);
        devIdToAppIdMap.put(3341L,4744L);
        devIdToAppIdMap.put(3481L,4920L);
        devIdToAppIdMap.put(3595L,9266L);
        devIdToAppIdMap.put(3612L,5090L);
        devIdToAppIdMap.put(3691L,5189L);
        devIdToAppIdMap.put(3762L,47158L);
        devIdToAppIdMap.put(3766L,5295L);
        devIdToAppIdMap.put(3781L,5320L);
        devIdToAppIdMap.put(3876L,24863L);
        devIdToAppIdMap.put(3915L,5965L);
        devIdToAppIdMap.put(3927L,5609L);
        devIdToAppIdMap.put(3954L,10439L);
        devIdToAppIdMap.put(4110L,10521L);
        devIdToAppIdMap.put(4170L,5825L);
        devIdToAppIdMap.put(4177L,5834L);
        devIdToAppIdMap.put(4179L,14112L);
        devIdToAppIdMap.put(4253L,5940L);
        devIdToAppIdMap.put(4302L,6002L);
        devIdToAppIdMap.put(4334L,6043L);
        devIdToAppIdMap.put(4511L,6288L);
        devIdToAppIdMap.put(4778L,6635L);
        devIdToAppIdMap.put(4915L,6810L);
        devIdToAppIdMap.put(5143L,14486L);
        devIdToAppIdMap.put(5189L,7466L);
        devIdToAppIdMap.put(5343L,7433L);
        devIdToAppIdMap.put(5419L,7560L);
        devIdToAppIdMap.put(5452L,39290L);
        devIdToAppIdMap.put(5606L,9791L);
        devIdToAppIdMap.put(5618L,39078L);
        devIdToAppIdMap.put(5710L,7951L);
        devIdToAppIdMap.put(5734L,49498L);
        devIdToAppIdMap.put(5992L,8331L);
        devIdToAppIdMap.put(6016L,11955L);
        devIdToAppIdMap.put(6029L,8380L);
        devIdToAppIdMap.put(6056L,8413L);
        devIdToAppIdMap.put(6199L,8588L);
        devIdToAppIdMap.put(6400L,8844L);
        devIdToAppIdMap.put(6608L,9112L);
        devIdToAppIdMap.put(6769L,15897L);
        devIdToAppIdMap.put(6850L,9438L);
        devIdToAppIdMap.put(6996L,9628L);
        devIdToAppIdMap.put(7063L,10380L);
        devIdToAppIdMap.put(7223L,9938L);
        devIdToAppIdMap.put(7989L,10935L);
        devIdToAppIdMap.put(8146L,11834L);
        devIdToAppIdMap.put(8181L,11184L);
        devIdToAppIdMap.put(8501L,11622L);
        devIdToAppIdMap.put(8636L,12091L);
        devIdToAppIdMap.put(8742L,12523L);
        devIdToAppIdMap.put(8828L,12057L);
        devIdToAppIdMap.put(9038L,13663L);
        devIdToAppIdMap.put(9445L,12896L);
        devIdToAppIdMap.put(9647L,13166L);
        devIdToAppIdMap.put(10105L,13912L);
        devIdToAppIdMap.put(10126L,13829L);
        devIdToAppIdMap.put(10191L,13920L);
        devIdToAppIdMap.put(10230L,48235L);
        devIdToAppIdMap.put(10367L,14149L);
        devIdToAppIdMap.put(10463L,40897L);
        devIdToAppIdMap.put(10570L,16449L);
        devIdToAppIdMap.put(10816L,40616L);
        devIdToAppIdMap.put(10890L,14832L);
        devIdToAppIdMap.put(10927L,14884L);
        devIdToAppIdMap.put(11129L,15168L);
        devIdToAppIdMap.put(11185L,15240L);
        devIdToAppIdMap.put(11613L,19872L);
        devIdToAppIdMap.put(11688L,16008L);
        devIdToAppIdMap.put(11809L,24296L);
        devIdToAppIdMap.put(12024L,16532L);
        devIdToAppIdMap.put(12446L,17152L);
        devIdToAppIdMap.put(12912L,17809L);
        devIdToAppIdMap.put(13017L,17959L);
        devIdToAppIdMap.put(13020L,17967L);
        devIdToAppIdMap.put(14071L,19349L);
        devIdToAppIdMap.put(14396L,19828L);
        devIdToAppIdMap.put(14568L,46565L);
        devIdToAppIdMap.put(15743L,22030L);
        devIdToAppIdMap.put(17004L,23474L);
        devIdToAppIdMap.put(18046L,24797L);
        devIdToAppIdMap.put(18806L,25780L);
        devIdToAppIdMap.put(18940L,25974L);
        devIdToAppIdMap.put(19309L,26451L);
        devIdToAppIdMap.put(20225L,28532L);
        devIdToAppIdMap.put(20228L,27663L);
        devIdToAppIdMap.put(20344L,27820L);
        devIdToAppIdMap.put(20844L,28523L);
        devIdToAppIdMap.put(22520L,42624L);
        devIdToAppIdMap.put(23359L,31974L);
        devIdToAppIdMap.put(25272L,34599L);
        devIdToAppIdMap.put(25597L,35035L);
        devIdToAppIdMap.put(25917L,35496L);
        devIdToAppIdMap.put(26021L,35627L);
        devIdToAppIdMap.put(26882L,36812L);
        devIdToAppIdMap.put(27046L,37040L);
        devIdToAppIdMap.put(27320L,37390L);
        devIdToAppIdMap.put(27759L,38010L);
        devIdToAppIdMap.put(27832L,38121L);
        devIdToAppIdMap.put(28152L,38589L);
        devIdToAppIdMap.put(28157L,38595L);
        devIdToAppIdMap.put(28478L,40701L);
        devIdToAppIdMap.put(29767L,40847L);
        devIdToAppIdMap.put(29897L,41638L);
        devIdToAppIdMap.put(31131L,42670L);
        devIdToAppIdMap.put(31366L,42973L);
        devIdToAppIdMap.put(31473L,43134L);
        devIdToAppIdMap.put(31542L,43238L);
        devIdToAppIdMap.put(31626L,43358L);
        devIdToAppIdMap.put(32889L,45952L);
        devIdToAppIdMap.put(32907L,44988L);
        devIdToAppIdMap.put(32951L,45053L);
        devIdToAppIdMap.put(32960L,45062L);
        devIdToAppIdMap.put(33470L,45737L);
        devIdToAppIdMap.put(33836L,46213L);
        devIdToAppIdMap.put(33972L,46404L);
        devIdToAppIdMap.put(34827L,47541L);
        devIdToAppIdMap.put(34925L,47664L);
        devIdToAppIdMap.put(35085L,47897L);
        devIdToAppIdMap.put(35165L,48003L);
        devIdToAppIdMap.put(35175L,48017L);
        devIdToAppIdMap.put(35443L,48354L);
        devIdToAppIdMap.put(35600L,48562L);
        devIdToAppIdMap.put(36368L,49592L);
        devIdToAppIdMap.put(36594L,49935L);
        devIdToAppIdMap.put(36695L,50077L);
        devIdToAppIdMap.put(37102L,50757L);
        devIdToAppIdMap.put(37139L,50708L);
        devIdToAppIdMap.put(37326L,51697L);
        devIdToAppIdMap.put(37506L,51923L);
        devIdToAppIdMap.put(37807L,51678L);
        devIdToAppIdMap.put(37843L,51734L);
        devIdToAppIdMap.put(38204L,52187L);
        devIdToAppIdMap.put(40192L,54171L);
    }

    private DevDefaultAppRelation(){
        // to do something
    }

    /**
     * 判断当前app是否是当前开发者的默认应用
     * @param developerId
     * @param appId
     * @return
     */
    public static boolean checkDefault(Long developerId, Long appId){
        Long defaultAppId = devIdToAppIdMap.get(developerId);
        return Objects.equals(appId,defaultAppId);
    }

    /**
     * 判断当前开发者是否有默认应用
     * @param developerId
     * @return
     */
    public static boolean checkExistDefault(Long developerId){
        Long defaultAppId = devIdToAppIdMap.get(developerId);
        return Objects.nonNull(defaultAppId);
    }

    public static Long getDefaultAppId(Long developerId){
        return devIdToAppIdMap.get(developerId);
    }

}
