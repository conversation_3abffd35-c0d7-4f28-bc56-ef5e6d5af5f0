package cn.com.duiba.paycenter.dto.payment.refund.alipay;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/12/11
 */
public class AlipayRefundRequest implements Serializable {
    private static final long serialVersionUID = -7596164774298980785L;
    /**
     * 退款金额
     */
    @NotNull(message = "金额不能为空")
    @Min(value = 1, message = "金额必须大于1分")
    private Integer amount;
    /**
     * 支付流水号和支付宝流水号二选一
     */
    private String chargeOrderNo;

    @NotNull(message = "兑吧appid不能为空")
    private Long appId;
    /**
     * 支付宝流水
     */
    private String tradeNo;
    /**
     * 业务主体 - 必传参数
     * @see cn.com.duiba.api.enums.SubjectTypeEnum
     */
    private String subjectType;

    //主订单子订单号, 支持购物车按每种商品退
    private Long ordersItemId;

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getChargeOrderNo() {
        return chargeOrderNo;
    }

    public void setChargeOrderNo(String chargeOrderNo) {
        this.chargeOrderNo = chargeOrderNo;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getSubjectType() {
        return subjectType;
    }

    public void setSubjectType(String subjectType) {
        this.subjectType = subjectType;
    }

    public Long getOrdersItemId() {
        return ordersItemId;
    }

    public void setOrdersItemId(Long ordersItemId) {
        this.ordersItemId = ordersItemId;
    }
}
