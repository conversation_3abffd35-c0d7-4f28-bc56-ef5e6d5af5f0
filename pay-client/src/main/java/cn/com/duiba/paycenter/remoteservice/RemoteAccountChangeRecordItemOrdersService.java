package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.AccountChangeRecordItemOrdersDto;
import cn.com.duiba.paycenter.dto.AccountPageInfo;

import java.util.List;

@AdvancedFeignClient
public interface RemoteAccountChangeRecordItemOrdersService {

    List<AccountChangeRecordItemOrdersDto> findByOrderItemIdAndType(Long orderId, Long orderItemId, Integer recordType);

    /**
     * 分页查询
     * @param pageInfo
     * @return
     */
    Page<AccountChangeRecordItemOrdersDto> findByOrderItemIdPage(AccountPageInfo pageInfo);
}
