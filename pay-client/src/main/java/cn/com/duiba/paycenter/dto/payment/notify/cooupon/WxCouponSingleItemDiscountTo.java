package cn.com.duiba.paycenter.dto.payment.notify.cooupon;


import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Description:
 * <p>
 * date: 2022/10/11 6:29 下午
 *
 * <AUTHOR>
 */
public class WxCouponSingleItemDiscountTo implements Serializable {
    private static final long serialVersionUID = -3815891425853700263L;
    /**
     * 减至后优惠单价
     */
    @JSONField(name = "cut_to_price")
    private int cutToPrice;

    /**
     * 最高价格
     */
    @JSONField(name = "max_price")
    private int maxPrice;

    public int getCutToPrice() {
        return cutToPrice;
    }

    public WxCouponSingleItemDiscountTo setCutToPrice(int cutToPrice) {
        this.cutToPrice = cutToPrice;
        return this;
    }

    public int getMaxPrice() {
        return maxPrice;
    }

    public WxCouponSingleItemDiscountTo setMaxPrice(int maxPrice) {
        this.maxPrice = maxPrice;
        return this;
    }
}
