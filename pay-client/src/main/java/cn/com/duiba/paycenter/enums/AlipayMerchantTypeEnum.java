package cn.com.duiba.paycenter.enums;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AccessTokenController.java
 * @Package cn.com.duiba.paycenter.enums
 * @Description:
 * @date 2023/2/10
 */
public enum AlipayMerchantTypeEnum {
    DEFAULT(0, "默认"),
    // 自定义
    CUSTOM(1, "自定义商户"),
    ;
    private int type;

    private String desc;

    AlipayMerchantTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public AlipayMerchantTypeEnum setType(int type) {
        this.type = type;
        return this;
    }

    public String getDesc() {
        return desc;
    }

    public AlipayMerchantTypeEnum setDesc(String desc) {
        this.desc = desc;
        return this;
    }
}
