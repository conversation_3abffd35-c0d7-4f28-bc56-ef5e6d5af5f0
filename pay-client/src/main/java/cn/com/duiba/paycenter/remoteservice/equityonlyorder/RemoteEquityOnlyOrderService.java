package cn.com.duiba.paycenter.remoteservice.equityonlyorder;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.EquityOnlyOrderDto;
import cn.com.duiba.paycenter.enums.EquityOnlyOrderBizTypeEnum;
import cn.com.duiba.paycenter.enums.PayOrderStatusEnum;

/**
 * 通用型：根据外部订单编号做幂登处理的 底层使用mysql的乐观锁
 * @Date 2023/4/18 2:43 PM （可以根据需要修改）
 * @Version 1.0 （版本号）
 */
@AdvancedFeignClient
public interface RemoteEquityOnlyOrderService {


    /**
     * 插入操作
     * @param dto 数据表对应的实体类
     * @return
     */
    Long insert(EquityOnlyOrderDto dto);
    /**
     * 根据主键更新对应数据条目，如果entity数据域为null则不更新对应数据域
     * @param dto
     * @return
     */
    Integer updateById(EquityOnlyOrderDto dto);

    /**
     * 修改状态
     * @return
     */
    Integer updateStatusByUniqueIndex(PayOrderStatusEnum orderStatusEnum, EquityOnlyOrderBizTypeEnum equityOnlyOrderBizTypeEnum, String bizNo);

    /**
     * 修改状态
     * 为成功时增加备注和三方返回的流水号
     * @return
     */
    Integer updateSuccess(EquityOnlyOrderDto equityOnlyOrderDto);

    /**
     * 根据主键查询订单号
     * @param bizType
     * @param bizNo
     * @return
     */
    EquityOnlyOrderDto findByUniqueIndex(EquityOnlyOrderBizTypeEnum bizType, String bizNo);



}
