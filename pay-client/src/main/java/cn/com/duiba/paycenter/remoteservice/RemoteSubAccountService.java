package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.SubAccountDto;

import java.util.List;

/**
 * @Description 开发者余额子账号
 * @Date 2023/2/20 14:49
 * <AUTHOR>
 */
@AdvancedFeignClient
public interface RemoteSubAccountService {

    SubAccountDto findById(Long id);

    /**
     * 根据developerId查询子账户
     * @param developerId
     * @return
     */
    List<SubAccountDto> findByDeveloperId(Long developerId);

    /**
     * 根据开发者id和账户标识查询子账户
     * @param developerId
     * @param accountIdentity
     * @return
     */
    SubAccountDto findByDeveloperIdAndAccountIdentity(Long developerId, String accountIdentity);

    /**
     * 根据developerIds查询子账户
     * @param developerIds
     * @return
     */
    List<SubAccountDto> findByDeveloperIds(List<Long> developerIds);

    /**
     * 批量插入子账户
     * @param subAccountDtoList
     */
    Boolean batchInsert(List<SubAccountDto> subAccountDtoList);
}
