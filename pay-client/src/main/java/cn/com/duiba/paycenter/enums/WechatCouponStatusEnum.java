package cn.com.duiba.paycenter.enums;

import java.util.HashMap;
import java.util.Map;

public enum WechatCouponStatusEnum {

    /**
     * 微信卡券状态
     *
     */
    UNACTIVATED("unactivated","未激活"),
    AUDIT("audit","审核中"),
    RUNNING("running","运行中"),

    STOPED("stoped","已停止"),

    PAUSED("paused","暂停发放"),

    ;

    private static final Map<String, WechatCouponStatusEnum> enumMap = new HashMap<>();
    static{
        for(WechatCouponStatusEnum wechatCouponStatusEnum : values()){
            enumMap.put(wechatCouponStatusEnum.getCode(), wechatCouponStatusEnum);
        }
    }

    public static WechatCouponStatusEnum getByCode(String code) {
        WechatCouponStatusEnum wechatCouponStatusEnum = enumMap.get(code);
        return wechatCouponStatusEnum;
    }
    private String code;
    private String desc;

    WechatCouponStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
