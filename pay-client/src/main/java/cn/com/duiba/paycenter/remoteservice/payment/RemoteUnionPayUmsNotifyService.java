package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay.UnionPayUmsChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay.UnionPayUmsRefundNotifyResponse;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/7/19 09:53
 */
@AdvancedFeignClient
public interface RemoteUnionPayUmsNotifyService {
    /**
     * 订单通知
     *
     * @param map 地图
     * @return {@link UnionPayUmsChargeNotifyResponse}
     */
    UnionPayUmsChargeNotifyResponse orderNotify(Map<String, String> map);


    /**
     * 退款通知
     *
     * @param map 地图
     * @return {@link UnionPayUmsRefundNotifyResponse}
     */
    UnionPayUmsRefundNotifyResponse refundNotify(Map<String, String> map);
}
