package cn.com.duiba.paycenter.params.duibaaccount;

import cn.com.duiba.paycenter.enums.duibaaccount.AccountTransferInAndOutEnum;
import cn.com.duiba.paycenter.enums.duibaaccount.SupplierAccountRelationTypeEnum;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 供应商账户资金变动入参
 * author z<PERSON><PERSON><PERSON>
 * date 2018-12-13-14:05
 */
public class SupplierAccountChangeParams implements Serializable {

    private static final long serialVersionUID = -5839281554977733588L;
    /**
     * 供应商ID
     */
    @NotNull
    private Long supplierId;

    /**
     * 业务ID
     */
    @NotBlank
    private String relationId;

    /**
     * 业务类型
     */
    @NotNull
    private SupplierAccountRelationTypeEnum relationType;

    /**
     * 变更金额
     */
    @NotNull
    private Long changeMoney;

    /**
     * 操作员ID
     */
    private Long operatorId;

    /**
     * 转出方
     * @see AccountTransferInAndOutEnum
     */
    private AccountTransferInAndOutEnum transferOut;

    /**
     * 转入方
     * @see AccountTransferInAndOutEnum
     */
    private AccountTransferInAndOutEnum transferIn;

    /**
     * 备注
     */
    private String memo;

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public SupplierAccountRelationTypeEnum getRelationType() {
        return relationType;
    }

    public void setRelationType(SupplierAccountRelationTypeEnum relationType) {
        this.relationType = relationType;
    }

    public Long getChangeMoney() {
        return changeMoney;
    }

    public void setChangeMoney(Long changeMoney) {
        this.changeMoney = changeMoney;
    }

    public AccountTransferInAndOutEnum getTransferOut() {
        return transferOut;
    }

    public void setTransferOut(AccountTransferInAndOutEnum transferOut) {
        this.transferOut = transferOut;
    }

    public AccountTransferInAndOutEnum getTransferIn() {
        return transferIn;
    }

    public void setTransferIn(AccountTransferInAndOutEnum transferIn) {
        this.transferIn = transferIn;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }
}
