package cn.com.duiba.paycenter.client;

import java.util.HashMap;
import java.util.Map;

import cn.com.duiba.paycenter.result.PayChargeResult;
import cn.com.duiba.paycenter.service.DuibaPayChargeService;
import cn.com.duiba.paycenter.util.SignUtil;

public class DuibaPayChargeServiceClient {

	private DuibaPayChargeService duibaPayChargeService;
	
	/**
	 * 人工充值
	 * @param manualApplyId
	 * @param money
	 * @return
	 */
	public RpcResult<PayChargeResult> chargeMoneyByManual(Long manualApplyId,Long money){
		try {
			Map<String, String> params=new HashMap<>();
			params.put("relationId", manualApplyId+"");
			params.put("money", money+"");
			String sign=SignUtil.sign(params);
			
			PayChargeResult ret= duibaPayChargeService.chargeMoneyByManual(manualApplyId, money,sign);

			return new RpcResult<>(ret);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}
	/**
	 * 当充值错误，需要减钱时，调用此方法
	 * 正常情况不会使用此方法
	 * @param developerId
	 * @param manualApplyId
	 * @param money
	 * @param memo
	 * @return
	 */
	public RpcResult<PayChargeResult> reduceMoneyByManual(Long manualApplyId,Long money){
		try {
			Map<String, String> params=new HashMap<>();
			params.put("relationId", manualApplyId+"");
			params.put("money", money+"");
			String sign=SignUtil.sign(params);
			
			PayChargeResult ret=duibaPayChargeService.reduceMoneyByManual(manualApplyId, money,sign);

			return new RpcResult<>(ret);
		} catch (Exception e){
			return new RpcResult<>(e);
		}
	}

	public void setDuibaPayChargeService(DuibaPayChargeService duibaPayChargeService) {
		this.duibaPayChargeService = duibaPayChargeService;
	}
}
