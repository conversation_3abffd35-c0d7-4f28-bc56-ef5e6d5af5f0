package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.cebXyk.CebXykChargeNotifyResponse;

/**
 * <AUTHOR>
 * @date 2021-01-30
 */
@AdvancedFeignClient
public interface RemoteCebXykNotifyService {

    CebXykChargeNotifyResponse orderNotify(String encryptData) throws BizException;

}
