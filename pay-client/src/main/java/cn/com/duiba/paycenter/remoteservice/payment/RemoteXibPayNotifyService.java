package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.xib.notify.XibPayNotifyRequest;
import cn.com.duiba.paycenter.dto.payment.charge.xib.notify.XibPayNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.xib.query.XibQueryResponseDTO;

/**
 * 厦门国际银行支付
 *
 * <AUTHOR>
 * @date 2022/10/25
 */
@AdvancedFeignClient
public interface RemoteXibPayNotifyService {

    /**
     * 订单通知
     *
     * @param xibPayNotifyRequest 支付通知请求
     * @return response
     * @throws BizException exception
     */
    XibPayNotifyResponse orderNotify(XibPayNotifyRequest xibPayNotifyRequest) throws BizException;

    XibQueryResponseDTO orderQuery(String orderCode);
}
