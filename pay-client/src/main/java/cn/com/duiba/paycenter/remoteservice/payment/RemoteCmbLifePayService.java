package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifePayNotifyDto;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifePayNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifePayQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifePayQueryResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifeRefundQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifeRefundQueryResponse;

/**
 * 招商银行-掌上生活-H5支付remote
 *
 * <AUTHOR>
 * @date 2024/2/18 8:46 下午
 */
@AdvancedFeignClient
public interface RemoteCmbLifePayService {

    /**
     * 根据参数生成签名
     *
     * @param reqParam 请求参数
     * @return 签名值
     * @throws BizException 异常
     */
    String doSign(String reqParam) throws BizException;

    /**
     * 解析支付通知参数
     *
     * @param notifyParam 通知参数
     * @return 解析后的支付通知参数
     * @throws BizException 异常
     */
    CmbLifePayNotifyResponse notify(String notifyParam) throws BizException;

    /**
     * 解析回调结果
     *
     * @param notifyParam 通知参数
     * @return 解析后的支付通知参数
     * @throws BizException 异常
     */
    CmbLifePayNotifyDto decryptNotify(String notifyParam) throws BizException;

    /**
     * 支付结果查询
     *
     * @param request 请求参数
     * @return 请求结果
     * @throws BizException 异常
     */
    CmbLifeRefundQueryResponse refundQuery(CmbLifeRefundQueryRequest request) throws BizException;

    /**
     * 退款结果查询
     *
     * @param request 请求参数
     * @return 请求结果
     * @throws BizException 异常
     */
    CmbLifePayQueryResponse payQuery(CmbLifePayQueryRequest request) throws BizException;
}
