package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.notify.NbcbNotifyRequestWrap;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.notify.NbcbNotifyRespWrap;

/**
 * <AUTHOR>
 */
@AdvancedFeignClient
public interface RemoteNingboNotifyService {

    /**
     * 支付退款通知
     *
     * @param notifyRequestWrap 请求包装类
     * @return 响应包装类
     * @throws Exception -
     */
    NbcbNotifyRespWrap orderNotify(NbcbNotifyRequestWrap notifyRequestWrap) throws Exception;
}
