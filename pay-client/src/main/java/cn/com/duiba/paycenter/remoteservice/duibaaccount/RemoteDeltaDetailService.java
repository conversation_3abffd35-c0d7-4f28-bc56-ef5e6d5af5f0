package cn.com.duiba.paycenter.remoteservice.duibaaccount;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.duibaaccount.DuibaAccountDeltaDetailDto;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/21
 */

/**
 * 兑吧余额
 */
@AdvancedFeignClient
public interface RemoteDeltaDetailService {
    public List<DuibaAccountDeltaDetailDto> findPageList(Date startTime, Date endTime, String operationType, int max, int offset);
    public Long findPageCount(Date startTime, Date endTime, String operationType);
}
