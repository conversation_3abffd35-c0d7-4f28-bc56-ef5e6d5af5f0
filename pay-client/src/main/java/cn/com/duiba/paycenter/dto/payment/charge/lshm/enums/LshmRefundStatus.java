package cn.com.duiba.paycenter.dto.payment.charge.lshm.enums;

/**
 * 001 退款中 002 退款成功 003 退款失败
 */
public enum LshmRefundStatus {
    REFUNDING("001", "退款中"),
    SUCCESS("002", "退款成功"),
    FAIL("003", "退款失败");
    private String status;
    private String desc;

    LshmRefundStatus(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
