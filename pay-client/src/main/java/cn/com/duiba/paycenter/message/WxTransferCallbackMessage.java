package cn.com.duiba.paycenter.message;

import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxTransferPaySendRequest;
import cn.com.duiba.paycenter.result.FundTransferResult;

import java.io.Serializable;

public class WxTransferCallbackMessage implements Serializable {
    private static final long serialVersionUID = -158540658712524775L;

    private WxTransferPaySendRequest requestParams;

    private FundTransferResult result;

    public WxTransferPaySendRequest getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(WxTransferPaySendRequest requestParams) {
        this.requestParams = requestParams;
    }

    public FundTransferResult getResult() {
        return result;
    }

    public void setResult(FundTransferResult result) {
        this.result = result;
    }
}
