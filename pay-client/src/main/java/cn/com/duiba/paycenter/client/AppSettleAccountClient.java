package cn.com.duiba.paycenter.client;

import cn.com.duiba.paycenter.params.AppSettleAccountParams;
import cn.com.duiba.paycenter.remoteservice.RemoteAppSettleAccountService;
import cn.com.duiba.paycenter.result.PayCenterResult;
import cn.com.duiba.paycenter.util.SignUtil;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * author z<PERSON><PERSON><PERSON>
 * date 2018/11/14-7:53 PM
 */
public class AppSettleAccountClient {

    private static final String DEVELOPER_ID = "developerId";
    private static final String RELATION_ID = "relationId";
    private static final String MONEY = "money";
    private static final String APP_ID = "appId";

    @Resource
    private RemoteAppSettleAccountService remoteAppSettleAccountService;

    /**
     * 待结算账户出账
     * @param settleParams
     * @return
     */
    public RpcResult<PayCenterResult> reduceMoney(AppSettleAccountParams settleParams){
        try {
            PayCenterResult ret = remoteAppSettleAccountService.reduceMoney(settleParams,getSign(settleParams));

            return new RpcResult<>(ret);
        } catch (Exception e) {
            return new RpcResult<>(e);
        }
    }

    /**
     * 待结算账户入账
     * @param settleParams
     * @return
     */
    public RpcResult<PayCenterResult> addMoney(AppSettleAccountParams settleParams){
        try {
            PayCenterResult ret = remoteAppSettleAccountService.addMoney(settleParams,getSign(settleParams));
            return new RpcResult<>(ret);
        } catch (Exception e) {
            return new RpcResult<>(e);
        }
    }

    private String getSign(AppSettleAccountParams settleParams){
        Map<String, String> params=new HashMap<>();
        params.put(DEVELOPER_ID, Objects.toString(settleParams.getDeveloperId()));
        params.put(MONEY, Objects.toString(settleParams.getMoney()));
        params.put(APP_ID,Objects.toString(settleParams.getAppId()));
        params.put(RELATION_ID, settleParams.getRelationId());
        return SignUtil.sign(params);
    }
}
