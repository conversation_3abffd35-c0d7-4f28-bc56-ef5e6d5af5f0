package cn.com.duiba.paycenter.service;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.result.AmbResult;

import java.io.Serializable;
import java.util.Map;

/**
 * 实际上是RemoteService
 */
@AdvancedFeignClient(qualifier = "ambPayCenterService")
public interface AmbPayCenterService {
	public static class AmbPayParams implements Serializable {
		private static final long serialVersionUID = 1L;
		private Long conusmerId;
		private Long appId;
		private String ordersSource; // duiba, dlp
		public Long getConusmerId() {
			return conusmerId;
		}
		public void setConusmerId(Long conusmerId) {
			this.conusmerId = conusmerId;
		}
		public Long getAppId() {
			return appId;
		}
		public void setAppId(Long appId) {
			this.appId = appId;
		}
		public String getOrdersSource() {
			return ordersSource;
		}
		public void setOrdersSource(String ordersSource) {
			this.ordersSource = ordersSource;
		}
	}
	public static class AmbPayChargeExtraParams implements Serializable{
		private static final long serialVersionUID = 1L;
		private Long appId;
		private Long conusmerId;
		private String desription;
		private String memo;
		private String subject = "1";
		private Long developerId;

		public Long getDeveloperId() {
			return developerId;
		}

		public void setDeveloperId(Long developerId) {
			this.developerId = developerId;
		}

		public Long getConusmerId() {
			return conusmerId;
		}

		public void setConusmerId(Long conusmerId) {
			this.conusmerId = conusmerId;
		}

		public Long getAppId() {
			return appId;
		}
		public void setAppId(Long appId) {
			this.appId = appId;
		}
		public String getDesription() {
			return desription;
		}
		public void setDesription(String desription) {
			this.desription = desription;
		}
		public String getMemo() {
			return memo;
		}
		public void setMemo(String memo) {
			this.memo = memo;
		}

		public String getSubject() {
			return subject;
		}

		public void setSubject(String subject) {
			this.subject = subject;
		}
	}
	/**
     * 用户 付款接口
     *
     * @param developerID
     * @param orderID
     * @param p
     * @return
     */
	public AmbResult consumerPay(Long developerId, Long orderId, Long consumerPrice, AmbPayParams p,String sign);
	/***
     * 开发者退款接口
     *
     * @param developerID
     * @param orderID
     * @param p
     * @return
     */
	public AmbResult consumerPayBack(Long developerId, Long orderId, Long paybackMoney, Long ordersItemId, AmbPayParams p,String sign);

	/***
     * 开发者批量退款接口
     *
     * @param developerID
     * @param orderID
     * @param p
     * @return
     */
	public AmbResult consumerPayBackList(Long developerId, Long orderId, Map<Long, Long> orderItemIdsAndMoney, AmbPayParams p, String sign);
	/***
     * 开发者提现到 余额帐户
     *
     * @param deveoperId
     *            开发者ID
     * @param manualChargeId
     *            手动ID
     * @param withdrawCashOrderId
     *            提现申请订单ID
     * @param re_money
     *            转帐金额
     * @param p
     *            relationType="withdraw_remaining",actionType="transform",
     *            change_kind="sub"
     *            备注：会同步操作account_detail,remaing_money，account_change_record
     *            ,类似开发者手动充值接口。
     * @return
     */
	public AmbResult dlpWithdrawToRemaining(Long developerId, Long withdrawCashOrderId, Long money, AmbPayChargeExtraParams p,String sign);
	/***
     * 定单结算接口
     *
     * @param order_id
     * @param developer_id
     * @param duiba_money
     *            结算给兑吧的钱。
     * @param dev_money
     *            结算给开发者钱。
     * @param p
     *            relationType="orders",actionType="settle",change_kind="add"
     * @return
     */
	public AmbResult orderSettle(Long orderId, Long duibaMoney, Long devMoney, AmbPayChargeExtraParams p,String sign);

	/***
     * 定单结算接口
     *
     * @param order_id
     * @param developer_id
     * @param duiba_money
     *            结算给兑吧的钱。
     * @param dev_money
     *            结算给开发者钱。
     * @param fee
     *            收开发手续费。
     * @param p
     *            relationType="orders",actionType="settle",change_kind="add"
     * @return
    */
	AmbResult orderSettleWithFee(Long orderId, Long duibaMoney, Long devMoney,Long fee, AmbPayChargeExtraParams p,String sign);

	/****
     * 开发者审请提现接口,提到支付宝或银行账户
     *
     * @param developer_id
     * @param amb_developer_withdraw_cash_order_ID
     *            提现审请ID
     * @param drawCach_money
     *            审请提现的金额
     * @param p
     *            relationType="withdraw_cash",actionType="withdraw",change_kind
     *            ="sub"
     * @return
     */
	public AmbResult dlpWithdrawCashApply(Long developerId, Long ambDeveloperWithdrawCashOrderId, Long drawCachMoney, AmbPayChargeExtraParams p,String sign);
	/****
     * 管理员拒绝提现 审请返还接口
     *
     * @param developerId
     *            开发者ID
     * @param amb_developer_withdraw_cash_order_ID
     *            开发者提醒审请ID。
     * @param drawCach_money
     *            提醒审请金额。
     * @param relationType
     *            ="withdraw_cash",actionType="withdraw_back",change_kind="add"
     * @return
     */
	public AmbResult dlpWithdrawCashRefuse(Long developerId, Long ambDeveloperWithdrawCashOrderId, Long drawCachMoney, AmbPayChargeExtraParams p,String sign);

	/****
	      * 管理员拒绝提现 审请返还接口 -- 根据业务主体，返还到相应的业务主体账户
	      *
	      * @param developerId
	      *            开发者ID
	      * @param amb_developer_withdraw_cash_order_ID
	      *            开发者提醒审请ID。
	      * @param drawCach_money
	      *            提醒审请金额。
	      * @param relationType
	      *            ="withdraw_cash",actionType="withdraw_back",change_kind="add"
	      * @return
	      */
	public AmbResult dlpWithdrawCashRefuseBySubject(Long developerId, Long ambDeveloperWithdrawCashOrderId, Long drawCachMoney, AmbPayChargeExtraParams p, String sign, String subjectType);


	/***
	      * 星速台一分扫  直接结算接口
	      * 总费用 = dev_money +  fee
	      * @param order_id
	      * @param dev_money
	      *            结算给开发者钱。
	      * @param fee
	      *            收开发手续费。
	      * @param p
	      *            relationType="orders",actionType="settle",change_kind="add"
	      * @return
	     */
	AmbResult xstOrderSettleWithFee(Long orderId, Long devMoney,Long fee, AmbPayChargeExtraParams p,String sign);
}
