package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.refund.RefundOrderDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/02/26
 */
@AdvancedFeignClient
public interface RemoteRefundBackendService {
    /**
     * 获取退款详情
     * @param transactionNo 第三方支付单号
     * @return RefundOrderDto
     */
    RefundOrderDto findByTransactionNo(String transactionNo);

    /**
     * 通过退款流水号查找退款订单流水
     * @param refundOrderNo 退款单号
     * @return  退款流水entity
     */
    RefundOrderDto findByRefundOrderNo(String refundOrderNo);

    /**
     * 通过业务订单号查找退款单
     * @param bizOrderNo 业务主订单号
     * @return  退款流水信息
     */
    List<RefundOrderDto> findByBizNo(String bizOrderNo);

    /**
     * 根据业务单号和业务类型查找退款成功的记录
     * @param bizOrderNos 业务单号列表
     * @param bizType 业务类型
     * @return 退款记录
     */
    List<RefundOrderDto> selectByBizOrderNosAndTypeForRefundSuccess(List<String> bizOrderNos,Integer bizType);
}
