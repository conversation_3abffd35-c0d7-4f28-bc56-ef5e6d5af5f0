package cn.com.duiba.paycenter.result;

import cn.com.duiba.paycenter.enums.PayOrderStatusEnum;

import java.io.Serializable;

/**
 * 转账结果
 * Created by xiaoxuda on 2017/11/1.
 */
public class FundTransferResult implements Serializable{
    private static final long serialVersionUID = -5554677886208162743L;
    /**
     * 转账状态
     */
    private PayOrderStatusEnum transferStatus;
    /**
     * 转账结果信息，失败或异常时会有值
     */
    private String message;

    /**
     * 构造函数
     * @param transferStatus
     * @param message
     */
    public FundTransferResult(PayOrderStatusEnum transferStatus, String message){
        this.transferStatus = transferStatus;
        int index;
        if(message != null && (index = ":".lastIndexOf(message)) > 0 && index < (message.length() - 1)){//支付宝提现信息中有效信息在最后的冒号的后面
            this.message = message.substring(index + 1);
        } else {
            this.message = message;
        }
    }

    /**
     * 默认构造函数
     */
    public FundTransferResult(){
        //默认构造函数
    }

    public PayOrderStatusEnum getTransferStatus() {
        return transferStatus;
    }

    public void setTransferStatus(PayOrderStatusEnum transferStatus) {
        this.transferStatus = transferStatus;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
