package cn.com.duiba.paycenter.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class AccountPageInfo implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 7409514138298977654L;
	private String rechargeType;
	private List<String> rechargeTypeList;
	private String devEmail;
	private Long developerId;
	private String timeType;
	private Date start;
	private Date now;
	private String mode;//检索方式
	private String orderId;
	private Long appId;
	private String orderType;
	private Integer max;
	private Integer offset;
	
	private Long maxId;
	
	public Date getStart() {
		return start;
	}
	public void setStart(Date start) {
		this.start = start;
	}
	public Date getNow() {
		return now;
	}
	public void setNow(Date now) {
		this.now = now;
	}
	public String getMode() {
		return mode;
	}
	public void setMode(String mode) {
		this.mode = mode;
	}
	public String getOrderId() {
		return orderId;
	}
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	public Long getAppId() {
		return appId;
	}
	public void setAppId(Long appId) {
		this.appId = appId;
	}
	public String getOrderType() {
		return orderType;
	}
	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
	public String getRechargeType() {
		return rechargeType;
	}
	public void setRechargeType(String rechargeType) {
		this.rechargeType = rechargeType;
	}
	public String getDevEmail() {
		return devEmail;
	}
	public void setDevEmail(String devEmail) {
		this.devEmail = devEmail;
	}
	public Long getDeveloperId() {
		return developerId;
	}
	public void setDeveloperId(Long developerId) {
		this.developerId = developerId;
	}
	public String getTimeType() {
		return timeType;
	}
	public void setTimeType(String timeType) {
		this.timeType = timeType;
	}
	public Integer getMax() {
		return max;
	}
	public void setMax(Integer max) {
		this.max = max;
	}
	public Integer getOffset() {
		return offset;
	}
	public void setOffset(Integer offset) {
		this.offset = offset;
	}
	public Long getMaxId() {
		return maxId;
	}
	public void setMaxId(Long maxId) {
		this.maxId = maxId;
	}

	public List<String> getRechargeTypeList() {
		return rechargeTypeList;
	}

	public void setRechargeTypeList(List<String> rechargeTypeList) {
		this.rechargeTypeList = rechargeTypeList;
	}

	@Override
	public String toString() {
		return "AccountPageInfo [rechargeType=" + rechargeType + ", devEmail=" + devEmail + ", developerId=" + developerId + ", timeType=" + timeType + ", start=" + start + ", now=" + now + ", max=" + max + ", offset=" + offset + "]";
	}
	
}
