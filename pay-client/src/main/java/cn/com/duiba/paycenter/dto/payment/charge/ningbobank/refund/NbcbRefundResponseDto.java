package cn.com.duiba.paycenter.dto.payment.charge.ningbobank.refund;

import java.io.Serializable;

/**
 * 退款请求
 *
 * <AUTHOR>
 * @date 2022-03-17
 */
public class NbcbRefundResponseDto implements Serializable{
    private static final String REFUND_APPLY_SUCCESS_CODE = "00";

    private String merSeqNo;

    private String transSeqNo;

    private String clearDate;

    private String transStatus;

    private String transDateTime;

    private String errorCode;

    private String errorMsg;


    public boolean isSuccess() {
        return REFUND_APPLY_SUCCESS_CODE.equals(this.transStatus);
    }


    public String getMerSeqNo() {
        return merSeqNo;
    }

    public void setMerSeqNo(String merSeqNo) {
        this.merSeqNo = merSeqNo;
    }

    public String getTransSeqNo() {
        return transSeqNo;
    }

    public void setTransSeqNo(String transSeqNo) {
        this.transSeqNo = transSeqNo;
    }

    public String getClearDate() {
        return clearDate;
    }

    public void setClearDate(String clearDate) {
        this.clearDate = clearDate;
    }

    public String getTransStatus() {
        return transStatus;
    }

    public void setTransStatus(String transStatus) {
        this.transStatus = transStatus;
    }

    public String getTransDateTime() {
        return transDateTime;
    }

    public void setTransDateTime(String transDateTime) {
        this.transDateTime = transDateTime;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
