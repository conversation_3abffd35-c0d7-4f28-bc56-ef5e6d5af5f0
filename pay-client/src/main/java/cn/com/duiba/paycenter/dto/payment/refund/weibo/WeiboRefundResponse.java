package cn.com.duiba.paycenter.dto.payment.refund.weibo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
@Data
public class WeiboRefundResponse implements Serializable {


    /**
     * 微博退款单号
     */
    @JSONField(name = "refund_id")
    private Long refundId;

    /**
     * 微博支付单号
     */
    @JSONField(name = "pay_id")
    private Long payId;

    /**
     * 退款金额，以分为单位
     */
    @JSONField(name = "refund_amount")
    private Integer refundAmount;

    /**
     * 退款状态
     */
    @JSONField(name = "status")
    private String status;


    @Getter
    public enum RefundStatus implements Serializable {

        /** 待处理 */
        STATUS_PENDING("STATUS_PENDING","待处理"),

        /** 处理中 */
        STATUS_PROCESSING("STATUS_PROCESSING","处理中"),

        /** 处理成功 */
        STATUS_SUCCESS("STATUS_SUCCESS","处理成功"),

        /** 处理失败 */
        STATUS_FAIL("STATUS_FAIL","处理失败"),

        /** 处理结果待确认 */
        STATUS_TBC("STATUS_TBC","处理结果待确认");

        private final String code;

        /** 状态描述 */
        private final String description;

        RefundStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

    }
}
