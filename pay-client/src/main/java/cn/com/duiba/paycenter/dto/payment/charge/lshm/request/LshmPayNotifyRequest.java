package cn.com.duiba.paycenter.dto.payment.charge.lshm.request;

import java.io.Serializable;

public class LshmPayNotifyRequest implements Serializable {
    /**
     * 支付中心订单号
     */
    private String tradeNo;
    /**
     * 商户号
     */
    private String merchantNo;
    /**
     * 回调内容(需要用下单时的Aeskey解密才能得到回调明文)
     */
    private String body;

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }
}
