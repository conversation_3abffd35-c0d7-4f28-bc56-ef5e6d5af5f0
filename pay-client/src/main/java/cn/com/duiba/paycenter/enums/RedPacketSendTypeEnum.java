package cn.com.duiba.paycenter.enums;

/**
 * 微信现金红包-发放类型枚举
 *
 * <AUTHOR>
 * @date 2020/04/15
 */
public enum RedPacketSendTypeEnum {
    API("API", "通过API接口发放"),
    UPLOAD("UPLOAD", "通过上传文件方式发放"),
    ACTIVITY("ACTIVITY", "通过活动方式发放");

    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    private String desc;


    RedPacketSendTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
