package cn.com.duiba.paycenter.dto.payment.charge.lshm;

import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeRequest;

import java.math.BigDecimal;

public class LshmChargeRequest extends BaseChargeRequest {

    /**
     * 店铺编码
     */
    private String storeCode;

    /**
     * 关单时间（如果不填写默认5分钟关单）
     * 非必填项
     */
    private Long closeOrderTime;

    /**
     * 微信用户openId，微信支付的时候必传
     * 非必填项
     */
    private String wxOpenId;

    /**
     * 回调地址
     * 非必填项
     */
    private String notifyUrl;

    /**
     * 支付标题
     */
    private String title;


    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public Long getCloseOrderTime() {
        return closeOrderTime;
    }

    public void setCloseOrderTime(Long closeOrderTime) {
        this.closeOrderTime = closeOrderTime;
    }

    public String getWxOpenId() {
        return wxOpenId;
    }

    public void setWxOpenId(String wxOpenId) {
        this.wxOpenId = wxOpenId;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
