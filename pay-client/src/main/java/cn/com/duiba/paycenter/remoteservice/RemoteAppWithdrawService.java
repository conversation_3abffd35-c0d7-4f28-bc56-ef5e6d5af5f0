package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.client.RpcResult;
import cn.com.duiba.paycenter.params.AppWithdrawParams;
import cn.com.duiba.paycenter.result.WithdrawResult;

/**
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/8-2:02 PM
 */
@AdvancedFeignClient
public interface RemoteAppWithdrawService {

    /**
     * 提现
     * @param requestParams
     * @param sign
     * @return
     */
    RpcResult<WithdrawResult> withdrawCashApply(AppWithdrawParams requestParams, String sign);

    /**
     * 提现失败退回
     * @param requestParams
     * @param sign
     * @return
     */
    RpcResult<WithdrawResult> withdrawCashApplyPaybackApply(AppWithdrawParams requestParams, String sign);
}
