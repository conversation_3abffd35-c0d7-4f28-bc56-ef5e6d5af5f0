package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.DeveloperMoneyInfoDto;

import java.util.List;

/**
 * 开发者收入账户remote
 * author z<PERSON><PERSON><PERSON>
 * date 2018-12-12-14:39
 */
@AdvancedFeignClient
public interface RemoteTpcDlpIncomeMoneyService {

    /**
     * 批量获取开发者收入金额
     * @param developerIds 开发者ID集合
     * @return
     */
    List<DeveloperMoneyInfoDto> batchFindIncomeMoney(List<Long> developerIds);
}
