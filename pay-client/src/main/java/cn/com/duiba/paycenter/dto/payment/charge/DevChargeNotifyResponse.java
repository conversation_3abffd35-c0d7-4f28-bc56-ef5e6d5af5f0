package cn.com.duiba.paycenter.dto.payment.charge;

/**
 * <AUTHOR>
 * @Desc 开发者响应体信息
 * @date 2020/03/13
 */
public class DevChargeNotifyResponse extends BaseChargeNotifyResponse {

    private static final long serialVersionUID = -2177504876750542579L;

    private Long appId;

    private String orderNo;

    /**
     * 订单类型
     * @see cn.com.duiba.paycenter.dto.payment.WaitConfirmChargeOrderDto
     */
    private int orderType = 0;

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public int getOrderType() {
        return orderType;
    }

    public void setOrderType(int orderType) {
        this.orderType = orderType;
    }
}