package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.mock.MockChargeNotifyResponse;

/**
 * Created by xutao on 2020/4/24.
 */
@AdvancedFeignClient
public interface RemoteMockNotifyService {
    /**
     * 中信支付通知
     * @param bizOrderNo
     * @return response
     * @throws BizException exception
     */
    MockChargeNotifyResponse orderNotify(String bizOrderNo) throws BizException;
}
