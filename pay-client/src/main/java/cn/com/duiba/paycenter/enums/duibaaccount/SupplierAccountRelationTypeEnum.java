package cn.com.duiba.paycenter.enums.duibaaccount;

import java.util.Arrays;
import java.util.Objects;

/**
 * 供应商账户业务类型枚举
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018-12-13-14:09
 * 1:结算,2:分销收入,3:分销收入退回,4:供货平台佣金
 */
public enum SupplierAccountRelationTypeEnum {

    SETTLE(1, "结算"),
    DISTRIBUTE_INCOME(2,"分销收入"),
    DISTRIBUTE_PAYBACK(3,"分销收入退回"),
    COMMISSION(4,"供货平台佣金"),
    ;
    private Integer code;

    private String desc;

    SupplierAccountRelationTypeEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }


    public static String getDescByCode(Integer code){
        if (Objects.isNull(code)){
            return "";
        }
        SupplierAccountRelationTypeEnum typeEnum = getByCode(code);
        if (Objects.nonNull(typeEnum)){
            return typeEnum.getDesc();
        }
        return "";
    }

    public static SupplierAccountRelationTypeEnum getByCode(Integer code){
        return Arrays.stream(SupplierAccountRelationTypeEnum.values()).filter(a -> a.code.equals(code)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
