package cn.com.duiba.paycenter.params;

import java.io.Serializable;
import java.util.Map;

public class PayOrdersExtraParams implements Serializable{

	private static final long serialVersionUID = -9136297229489028835L;
	private Long appId;
	private Long credits;
	private String memo;
	private Integer newShoppingCart;
	private Integer recordType;
	private Long subAccountId;
	private Map<Long, Integer> orderItemIdAndDlpActualPrice;
	private String rechargeType;

	public Long getSubAccountId() {
		return subAccountId;
	}

	public void setSubAccountId(Long subAccountId) {
		this.subAccountId = subAccountId;
	}

	public Map<Long, Integer> getOrderItemIdAndDlpActualPrice() {
		return orderItemIdAndDlpActualPrice;
	}
	public void setOrderItemIdAndDlpActualPrice(Map<Long, Integer> orderItemIdAndDlpActualPrice) {
		this.orderItemIdAndDlpActualPrice = orderItemIdAndDlpActualPrice;
	}
	public Integer getNewShoppingCart() {
		return newShoppingCart;
	}
	public void setNewShoppingCart(Integer newShoppingCart) {
		this.newShoppingCart = newShoppingCart;
	}
	public Integer getRecordType() {
		return recordType;
	}
	public void setRecordType(Integer recordType) {
		this.recordType = recordType;
	}
	public Long getAppId() {
		return appId;
	}
	public void setAppId(Long appId) {
		this.appId = appId;
	}
	public Long getCredits() {
		return credits;
	}
	public void setCredits(Long credits) {
		this.credits = credits;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getRechargeType() {
		return rechargeType;
	}

	public void setRechargeType(String rechargeType) {
		this.rechargeType = rechargeType;
	}
}
