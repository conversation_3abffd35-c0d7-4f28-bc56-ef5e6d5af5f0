package cn.com.duiba.paycenter.remoteservice.equity;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.equity.request.BaseEquityRequest;
import cn.com.duiba.paycenter.dto.equity.request.wx.favor.EquityWxFavorUserCouponsRequest;
import cn.com.duiba.paycenter.dto.equity.response.BaseEquityResultResponse;
import cn.com.duiba.paycenter.dto.equity.response.EquityResponse;
import cn.com.duiba.paycenter.dto.equity.response.wx.favor.EquityWxFavorUserCouponsResponse;
import cn.com.duiba.paycenter.dto.equity.response.wx.favor.WxFavorStocksDto;

/**
 * 微信立减金
 * <AUTHOR>
 * @date 2023/5/21 4:01 PM
 */
@AdvancedFeignClient
public interface RemoteEquityWxFavorService {

    /**
     * 发放
     * 注意：若结果以mq回调，业务方需要根据业务类型+业务单号幂等<br/>
     * 长时间收不到回调调用查询接口查询<br/>
     *
     * @param request 发放权益请求参数
     * @return 本次请求是否成功
     */
    EquityResponse distribute(BaseEquityRequest<EquityWxFavorUserCouponsRequest> request);

    /**
     * 查询发放结果
     *
     * @param bizType 业务类型
     *        {@link cn.com.duiba.paycenter.enums.equity.EquityBizTypeEnum}
     * @param bizNo 业务方唯一单号
     * @return 发放权益结果
     */
    BaseEquityResultResponse<EquityWxFavorUserCouponsResponse> distributeResult(Integer bizType, String bizNo);

    /**
     * 获取最新请求参数
     * @param bizType 业务类型
     * @param bizNo 订单号
     * @return 最新请求参数
     */
    EquityWxFavorUserCouponsRequest getReqParam(Integer bizType, String bizNo);

    /**
     * 查询批次详情
     * @param mchId 商户号
     * @param stockId 批次号
     * @param stockCreatorMchId 创建批次的商户号
     * @return 批次详情
     */
    WxFavorStocksDto getStocksInfo(String mchId, String stockId, String stockCreatorMchId);

}
