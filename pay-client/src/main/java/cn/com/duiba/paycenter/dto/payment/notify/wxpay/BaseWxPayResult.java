package cn.com.duiba.paycenter.dto.payment.notify.wxpay;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.util.XstreamInitializer;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import org.w3c.dom.Document;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/11/19
 */
public class BaseWxPayResult implements Serializable {
    private static final long serialVersionUID = -150910352129619718L;
    public static final String SUCCESS = "SUCCESS";
    private static final String SIGN = "sign";
    /**
     * 返回状态码.
     * 表示通信是否成功
     */
    @XStreamAlias("return_code")
    private String returnCode;
    /**
     * 返回信息.
     */
    @XStreamAlias("return_msg")
    private String returnMsg;

    //当return_code为SUCCESS的时候，还会包括以下字段：
    /**
     * 业务结果.
     */
    @XStreamAlias("result_code")
    private String resultCode;
    /**
     * 错误代码.
     */
    @XStreamAlias("err_code")
    private String errCode;
    /**
     * 错误代码描述.
     */
    @XStreamAlias("err_code_des")
    private String errCodeDes;
    /**
     * 公众账号ID.
     */
    @XStreamAlias("appid")
    private String appid;
    /**
     * 商户号.
     */
    @XStreamAlias("mch_id")
    private String mchId;

    /**
     * 服务商模式下的子商户公众账号ID.
     */
    @XStreamAlias("sub_appid")
    private String subAppId;

    /**
     * 服务商模式下的子商户号.
     */
    @XStreamAlias("sub_mch_id")
    private String subMchId;
    /**
     * 随机字符串.
     */
    @XStreamAlias("nonce_str")
    private String nonceStr;
    /**
     * 签名.
     */
    @XStreamAlias("sign")
    private String sign;

    //以下为辅助属性
    /**
     * xml字符串.
     */
    private String xmlString;

    /**
     * xml的Document对象，用于解析xml文本.
     */
    private transient Document xmlDoc;

    /**
     * 从xml字符串创建bean对象.
     *
     * @param <T>       需要转欢的类型
     * @param xmlString the xml string
     * @param clazz     需要转的类型
     * @return T
     * @throws BizException bizException
     */
    @SuppressWarnings("unchecked")
    public static <T extends BaseWxPayResult> T fromXML(String xmlString, Class<T> clazz) throws BizException {
        try {
            XStream xstream = XstreamInitializer.getInstance();
            xstream.processAnnotations(clazz);
            T result = (T) xstream.fromXML(xmlString);
            result.setXmlString(xmlString);
            return result;
        } catch (Exception e) {
            throw new BizException(xmlString);
        }
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMsg() {
        return returnMsg;
    }

    public void setReturnMsg(String returnMsg) {
        this.returnMsg = returnMsg;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public String getErrCodeDes() {
        return errCodeDes;
    }

    public void setErrCodeDes(String errCodeDes) {
        this.errCodeDes = errCodeDes;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getSubAppId() {
        return subAppId;
    }

    public void setSubAppId(String subAppId) {
        this.subAppId = subAppId;
    }

    public String getSubMchId() {
        return subMchId;
    }

    public void setSubMchId(String subMchId) {
        this.subMchId = subMchId;
    }

    public String getXmlString() {
        return xmlString;
    }

    public void setXmlString(String xmlString) {
        this.xmlString = xmlString;
    }
}
