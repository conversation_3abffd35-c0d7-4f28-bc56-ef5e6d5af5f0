package cn.com.duiba.paycenter.enums;

/**
 * 微信现金红包-场景类型枚举
 * 发放红包使用场景，红包金额大于200或者小于1元时必传
 *
 * <AUTHOR>
 * @date 2020/04/15
 */
public enum RedPacketSceneTypeEnum {
    PRODUCT_1("PRODUCT_1", "商品促销"),
    PRODUCT_2("PRODUCT_2", "抽奖"),
    PRODUCT_3("PRODUCT_3", "虚拟物品兑奖"),
    PRODUCT_4("PRODUCT_4", "企业内部福利"),
    PRODUCT_5("PRODUCT_5", "渠道分润"),
    PRODUCT_6("PRODUCT_6", "保险回馈"),
    PRODUCT_7("PRODUCT_7", "彩票派奖"),
    PRODUCT_8("PRODUCT_8", "税务刮奖");

    /**
     * 场景编码
     */
    private String code;

    /**
     * 场景描述
     */
    private String desc;


    RedPacketSceneTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
