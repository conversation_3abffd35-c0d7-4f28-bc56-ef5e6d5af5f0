package cn.com.duiba.paycenter.enums.duibaaccount;

import java.util.Arrays;
import java.util.Objects;

/**
 * 账户转入方与转入方类型枚举
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/13-2:45 PM
 */
public enum AccountTransferInAndOutEnum {
    USER(1, "用户"),
    SETTLE_ACCOUNT(2, "待结算账户"),
    BALANCE_ACCOUNT(3, "余额账户"),
    DISTRIBUTOR(4, "分销商"),
    SUPPLIER(5, "供应商"),
    ALIPAY_CHANNEL(6, "支付宝渠道"),
    DUIBA_PLATFORM(7, "兑吧平台"),
    COMMISSION_ACCOUNT(8, "佣金账户"),
    MARK_ACCOUNT(9, "标记账户");

    private Integer code;

    private String desc;

    AccountTransferInAndOutEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code){
        if (Objects.isNull(code)){
            return "";
        }
        AccountTransferInAndOutEnum inAndOutEnum = getByCode(code);
        if (Objects.nonNull(inAndOutEnum)){
            return inAndOutEnum.getDesc();
        }
        return "";
    }

    public static AccountTransferInAndOutEnum getByCode(Integer code){
        return Arrays.stream(AccountTransferInAndOutEnum.values()).filter(a -> a.code.equals(code)).findFirst().orElse(null);
    }

    public static boolean isExist(Integer code){
        return Objects.nonNull(code) &&
                Arrays.stream(AccountTransferInAndOutEnum.values()).anyMatch(a -> a.code.equals(code));
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
