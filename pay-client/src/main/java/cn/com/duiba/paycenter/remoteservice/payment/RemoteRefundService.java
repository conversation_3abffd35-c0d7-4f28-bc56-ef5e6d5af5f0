package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.boot.netflix.feign.hystrix.FeignHystrixProperty;
import cn.com.duiba.boot.netflix.feign.hystrix.conf.HystrixPropertiesManager;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifeCreateRefundResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifeRefundRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifeRefundResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cus.YbsRefundOrderReq;
import cn.com.duiba.paycenter.dto.payment.charge.cus.YbsRefundOrderResp;
import cn.com.duiba.paycenter.dto.payment.charge.elife.IcbcELifeCreateRefundResponse;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.refund.NbcbRefundRequestDto;
import cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay.UnionPayUmsRefundRequest;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayRefundRequest;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayRefundResponse;
import cn.com.duiba.paycenter.dto.payment.charge.xib.refund.XibRefundRequestDTO;
import cn.com.duiba.paycenter.dto.payment.refund.RefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.RefundResponse;
import cn.com.duiba.paycenter.dto.payment.refund.abc.AbcRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.alipay.AlipayRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.boc.BocRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.citic.CiticRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.cmbonenet.CmbOneNetRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.duibaLive.bf.DuibaLiveBFRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.duibaLive.installment.DuibaLiveInstallmentRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.duibaLive.mp.DuibaLiveMpRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.icbc.IcbcRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.icbc.IcbcRefundResponse;
import cn.com.duiba.paycenter.dto.payment.refund.icbc.elife.IcbcELifeRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.mock.MockRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.shouxin.ShouxinPayRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.shouxin.ShouxinPayRefundResponse;
import cn.com.duiba.paycenter.dto.payment.refund.wjrcb.WjrcbRefundRequest;

/**
 * <AUTHOR>
 * @date 2018/11/26
 */
@AdvancedFeignClient
public interface RemoteRefundService {
    /**
     * 微信退款异步接口
     * 返回成功只是提交成功
     *
     * @param refundRequest 退款请求
     * @return 微信退款请求响应
     * @throws BizException bizException
     */
    RefundResponse refund(RefundRequest refundRequest) throws BizException;


    /**
     * 通用退款异步接口
     * 返回成功只是提交成功
     *
     * @param refundRequest 退款请求
     * @return 通用退款请求响应
     * @throws BizException bizException
     */
    RefundResponse commonRefund(RefundRequest refundRequest) throws BizException;

    /**
     * 支付宝同步退款请求
     * 返回成功即退款成功
     *
     * @param alipayRefundRequest 支付宝退款请求
     * @return RefundResponse
     * @throws BizException bizException
     */
    RefundResponse alipayRefund(AlipayRefundRequest alipayRefundRequest) throws BizException;

    /**
     * 苏州农商行退款请求
     * @param refundRequest refund request
     * @return refund response
     * @throws BizException bizException
     */
    RefundResponse wjrcbRefund(WjrcbRefundRequest refundRequest) throws BizException;

    /**
     * 获取退款详情
     * @param request 退款请求类
     * @return RefundOrderDto
     */
    RefundResponse abcRefund(AbcRefundRequest request);

    /**
     * 中国银行 退款
     * @param request
     * @return
     */
    RefundResponse bocRefund(BocRefundRequest request);

    /**
     * 万达 退款
     * @param request
     * @return
     */
    RefundResponse wandaRefund(RefundRequest request);

    /**
     * 中信银行 退款
     * @param request
     * @return
     */
    RefundResponse citicRefund(CiticRefundRequest request);

    /**
     * 招行一网通支付退款请求
     * @param refundRequest refund request
     * @return refund response
     * @throws BizException bizException
     */
    RefundResponse cmbOneNetPayRefund(CmbOneNetRefundRequest refundRequest) throws BizException;

    /**
     * 首信支付退款请求
     * @param refundRequest refund request
     * @return refund response
     * @throws BizException bizException
     */
    ShouxinPayRefundResponse shouxinPayRefund(ShouxinPayRefundRequest refundRequest) throws BizException;

    /**
     * mock退款
     * @param request
     * @return
     */
    RefundResponse mockRefund(MockRefundRequest request) throws BizException;


    UnionPayRefundResponse unionPayRefund(UnionPayRefundRequest refundRequest) throws BizException;

    /**
     * 工行退款
     * @param refundRequest
     * @return
     * @throws BizException
     */
    RefundResponse icbcPayRefund(IcbcRefundRequest refundRequest) throws BizException;


    /**
     * 兑吧直播小程序支付退款
     * @param refundRequest
     * @return
     * @throws BizException
     */
    RefundResponse duibaLiveMpPayPayRefund(DuibaLiveMpRefundRequest refundRequest) throws BizException;

    /**
     * 兑吧直播小程序支付退款
     * @param refundRequest
     * @return
     * @throws BizException
     */
    RefundResponse duibaLiveInstallmentPayPayRefund(DuibaLiveInstallmentRefundRequest refundRequest) throws BizException;

    /**
     * 兑吧直播宝付支付退款
     * @param refundRequest
     * @return
     * @throws BizException
     */
    RefundResponse duibaLiveBFPayPayRefund(DuibaLiveBFRefundRequest refundRequest) throws BizException;

    /**
     * 宁波银行退款
     *
     * @param nbcbRefundRequestDto 请求
     * @return 响应
     */
    @FeignHystrixProperty(name= HystrixPropertiesManager.EXECUTION_ISOLATION_THREAD_TIMEOUT_IN_MILLISECONDS, value="60000")
    RefundResponse ningboBankPayRefund(NbcbRefundRequestDto nbcbRefundRequestDto);

    /**
     * 深圳银联62vip 退款
     * @param refundOrderReq 请求
     * @return 响应
     */
    YbsRefundOrderResp activity62VipRefund(YbsRefundOrderReq refundOrderReq);

    /**
     * 厦门国际银行支付退款
     *
     * @param xibRefundRequestDTO xib dto退款请求
     * @return {@link RefundResponse}
     */
    RefundResponse xibPayRefund(XibRefundRequestDTO xibRefundRequestDTO);

    /**
     * 深圳银联天满服务平台退款
     *
     * @param refundRequest 退款请求
     * @return {@link RefundResponse}
     */
    RefundResponse unionPayUmsRefund(UnionPayUmsRefundRequest refundRequest);

    /**
     * 掌上生活-H5支付-发起退款
     * @param refundRequest 退款请求
     * @return response
     */
    CmbLifeCreateRefundResponse cmbLifeRefund(CmbLifeRefundRequest refundRequest);


    /**
     * 工行e生活-退款
     * @param refundRequest 退款请求
     * @return response
     */
    IcbcELifeCreateRefundResponse icbcELifePayRefund(IcbcELifeRefundRequest refundRequest);

}
