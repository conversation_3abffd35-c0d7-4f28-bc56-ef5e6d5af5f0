package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.PayOrderDto;
import cn.com.duiba.paycenter.enums.PayOrderBizTypeEnum;

import java.util.List;

/**
 * 支付订单remote
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018-12-29-13:59
 */
@AdvancedFeignClient
public interface RemotePayOrderService {

    /**
     * 根据支付流水号查询
     * @param executorBizId
     * @return
     */
    PayOrderDto findByExecutorBizId(String executorBizId);

    /**
     * 根据业务类型和业务ID集合批量查询
     * @param bizType
     * @param bizNoList
     * @return
     */
    List<PayOrderDto> findByBizTypeAndBizNoList(PayOrderBizTypeEnum bizType, List<String> bizNoList);


    Integer updateById(PayOrderDto dto);
}
