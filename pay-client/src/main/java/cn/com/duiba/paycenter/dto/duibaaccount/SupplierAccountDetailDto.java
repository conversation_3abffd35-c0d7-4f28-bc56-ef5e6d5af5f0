package cn.com.duiba.paycenter.dto.duibaaccount;

import cn.com.duiba.paycenter.enums.duibaaccount.AccountTransferInAndOutEnum;
import cn.com.duiba.paycenter.enums.duibaaccount.SupplierAccountChangeTypeEnum;
import cn.com.duiba.paycenter.enums.duibaaccount.SupplierAccountRelationTypeEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * 供应商待账户明细实体
 * author zhang<PERSON><PERSON>
 * date 2018/12/13-10:05 AM
 */
public class SupplierAccountDetailDto implements Serializable {

    private static final long serialVersionUID = 242017545333620158L;
    private Long id;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 变更前余额
     */
    private Long beforeBalance;

    /**
     * 变更后余额
     */
    private Long afterBalance;
    /**
     * 备注
     */
    private String memo;

    /**
     * 变更金额
     */
    private Long changeMoney;

    /**
     * 来源类型 1:结算,2:分销收入,3:分销收入退回,4:供货平台佣金
     * @see SupplierAccountRelationTypeEnum
     */
    private Integer relationType;

    /**
     * 关联的类型ID
     */
    private String relationId;

    /**
     * 类型 1:入账，加 2：出账、减
     * @see SupplierAccountChangeTypeEnum
     */
    private Integer changeType;

    /**
     * 转出方
     * @see AccountTransferInAndOutEnum
     */
    private Integer transferOut;

    /**
     * 转入方
     * @see AccountTransferInAndOutEnum
     */
    private Integer transferIn;

    private Date gmtCreate;

    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getRelationType() {
        return relationType;
    }

    public void setRelationType(Integer relationType) {
        this.relationType = relationType;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getChangeMoney() {
        return changeMoney;
    }

    public void setChangeMoney(Long changeMoney) {
        this.changeMoney = changeMoney;
    }

    public Integer getTransferOut() {
        return transferOut;
    }

    public void setTransferOut(Integer transferOut) {
        this.transferOut = transferOut;
    }

    public Integer getTransferIn() {
        return transferIn;
    }

    public void setTransferIn(Integer transferIn) {
        this.transferIn = transferIn;
    }

    public Long getBeforeBalance() {
        return beforeBalance;
    }

    public void setBeforeBalance(Long beforeBalance) {
        this.beforeBalance = beforeBalance;
    }

    public Long getAfterBalance() {
        return afterBalance;
    }

    public void setAfterBalance(Long afterBalance) {
        this.afterBalance = afterBalance;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }
}
