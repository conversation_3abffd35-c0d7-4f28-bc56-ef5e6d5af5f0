package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;

/**
 * <AUTHOR>
 * @date 2018/12/12
 */
@AdvancedFeignClient
public interface RemoteAlipayConfigService {
    /**
     * 获取app的配置类型
     *
     * @param appId       兑吧appId
     * @param channelType 渠道类型
     * @return channel mode
     * @see cn.com.duiba.paycenter.enums.ChannelModeEnum
     */
    Integer getChannelMode(Long appId, String channelType);
}
