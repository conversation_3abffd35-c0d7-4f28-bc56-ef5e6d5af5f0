package cn.com.duiba.paycenter.enums;

/**
 * 渠道配置之模式
 * 兑吧代收，使用兑吧的渠道账户
 * 自主模式，使用开发者自己的渠道账户
 * <AUTHOR>
 * @date 2018/11/06
 */
public enum ChannelModeEnum {
    /**
     * 开发者委托兑吧收钱，钱在兑吧
     */
    DUIBA(1, "兑吧代收"),
    /**
     * 开发者拥有支付能力，提供配置
     * 钱不过兑吧
     */
    INDEPENDENT(2, "自主模式"),
    /**
     * 服务商模式
     * 兑吧帮开发者申请支付，钱不过兑吧
     */
    SERVICE_PROVIDER(3, "服务商模式")
    ;
    private Integer code;
    private String desc;

    ChannelModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
