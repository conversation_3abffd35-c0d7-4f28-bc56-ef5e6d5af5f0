package cn.com.duiba.paycenter.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * 兑吧扣费明细DO
 * 
 * @create 2015年11月30日 下午4:22:44
 * <AUTHOR>
 * @version
 */
public class DuibaDeduceDetailDto implements Serializable{

    /**
	 * 
	 */
	private static final long serialVersionUID = 3102161463960143283L;

	public static final String DUIBA_OPERATION_STATUS_CREATE="create";
	public static final String DUIBA_OPERATION_STATUS_SUCCESS="success";
	public static final String DUIBA_OPERATION_STATUS_PAYBACK=" payback";
	public static final String DUIBA_OPERATION_STATUS_UNKNOW="unknow";
	public static final String DUIBA_OPERATION_STATUS_FAILED="failed";  //支付中心告知 失败

    /**
     * 付款
     */
    public static final String DUIBA_OPERATION_TYPE_PAY = "pay";
    /**
     * 还款
     */
    public static final String DUIBA_OPERATION_TYPE_PAYBACK = "payback";

    private Long               id;

    private String             orderNo;                // 订单号

    private Long               orderId;                // 订单ID

    private String             appName;                // 应用名称

    private Long               appId;                  // 应用ID

    private Long               developId;              // 开发者ID

    private String             activityType;           // 活动类型

    private String             activityTitle;          // 活动标题

    private Long               activityId;             // 活动ID

    private String             operationType;          // 操作类型 pay：付款。payback：还款

    private Double             operationMoney;         // 操作金额

    private String             operationStatus;        // 最终处理状态create success payback,paybackfailed

    private String             memo;

    private Date               gmtCreate;              // 记录创建时间

    private Date               gmtModified;            // 记录最后修改时间

    public DuibaDeduceDetailDto() {
        // Do nothing
    }

    public DuibaDeduceDetailDto(Long id) {
        this.id = id;
        this.gmtModified = new Date();
    }

    public DuibaDeduceDetailDto(boolean init4insert) {
        if (init4insert) {
            gmtCreate = new Date();
            gmtModified = new Date();
        }
    }

    public void beforeUpdate() {
        gmtModified = new Date();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public long getOrderId() {
        return orderId;
    }

    public void setOrderId(long orderId) {
        this.orderId = orderId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Long getDevelopId() {
        return developId;
    }

    public void setDevelopId(Long developId) {
        this.developId = developId;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public String getActivityTitle() {
        return activityTitle;
    }

    public void setActivityTitle(String activityTitle) {
        this.activityTitle = activityTitle;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public Double getOperationMoney() {
        return operationMoney;
    }

    public void setOperationMoney(Double operationMoney) {
        this.operationMoney = operationMoney;
    }

    public String getOperationStatus() {
        return operationStatus;
    }

    public void setOperationStatus(String operationStatus) {
        this.operationStatus = operationStatus;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

}
