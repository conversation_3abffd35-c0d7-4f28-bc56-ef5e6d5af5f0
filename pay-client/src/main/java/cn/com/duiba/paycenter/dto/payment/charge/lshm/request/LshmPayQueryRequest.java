package cn.com.duiba.paycenter.dto.payment.charge.lshm.request;

import lombok.Data;

import java.io.Serializable;

@Data
public class LshmPayQueryRequest implements Serializable {
    private static final long serialVersionUID = -287277620869590256L;
    /**
     * 门店编码
     * 必填项
     */
    private String merchantNo;

    /**
     * 支付订单号
     */
    private String tradeNo;

    /**
     * 商户订单号
     * 支付订单号和商户订单号两者必须传一个
     */
    private String merchantOrderNo;
}
