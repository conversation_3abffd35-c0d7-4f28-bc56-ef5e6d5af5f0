package cn.com.duiba.paycenter.remoteservice.equity;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.equity.request.BaseEquityRequest;
import cn.com.duiba.paycenter.dto.equity.request.EquityAlipayCouponCardRequest;
import cn.com.duiba.paycenter.dto.equity.response.BaseEquityResultResponse;
import cn.com.duiba.paycenter.dto.equity.response.EquityResponse;
import cn.com.duiba.paycenter.dto.equity.response.alipay.EquityAlipayCouponCardResponse;

/**
 * 支付宝立减金
 * <AUTHOR>
 * @date 2023/4/20 1:44 PM
 */
@AdvancedFeignClient
public interface RemoteEquityAlipayCouponService {

    /**
     * 支付宝获取用户ID
     * @param appId 支付宝应用ID
     * @param authCode 授权码
     * @return 支付宝user_id
     */
    String queryAlipayUserIdByAuthCode(String appId, String authCode);

    /**
     * 发放
     * 注意：若结果以mq回调，业务方需要根据业务类型+业务单号幂等<br/>
     * 长时间收不到回调调用查询接口查询<br/>
     *
     * @param request 发放权益请求参数
     * @return 本次请求是否成功
     */
    EquityResponse distribute(BaseEquityRequest<EquityAlipayCouponCardRequest> request);

    /**
     * 查询支付宝立减金权益发放结果
     *
     * @param bizType 业务类型
     *        {@link cn.com.duiba.paycenter.enums.equity.EquityBizTypeEnum}
     * @param bizNo 业务方唯一单号
     * @return 发放权益结果
     */
    BaseEquityResultResponse<EquityAlipayCouponCardResponse> alipayCouponDistributeResult(Integer bizType, String bizNo);
}
