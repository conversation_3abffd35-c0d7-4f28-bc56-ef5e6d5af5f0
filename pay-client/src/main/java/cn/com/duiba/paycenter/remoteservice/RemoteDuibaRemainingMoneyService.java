package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.DuibaRemainingMoneyDto;
import cn.com.duiba.paycenter.exception.CodeException;
import cn.com.duiba.service.exception.BusinessException;

@AdvancedFeignClient
public interface RemoteDuibaRemainingMoneyService {
	/**
	 * 查询兑吧余额账户
	 * @return
	 */
	public DuibaRemainingMoneyDto findRecord();
	
	/**
	 * @return
	 */
	public DuibaRemainingMoneyDto findRecord4update();
	
	/**
	 * 查询兑吧收入结算账户
	 * @return
	 */
	public DuibaRemainingMoneyDto findDuibaIncomeRecord();
	/**
	 * 悲观锁兑吧收入结算账户
	 * @return
	 */
	public DuibaRemainingMoneyDto findDuibaIncomeRecord4update();
	
	/**
	 * 扣钱
	 * @param money
	 * @param rm
	 * @return
	 * @throws Exception
	 */
	public boolean reduceMoney(DuibaRemainingMoneyDto rm,Long money) throws CodeException, BusinessException;
	
	/**
	 * 加钱
	 * @param money 金额
	 * @return 
	 */
	public boolean addMoney(DuibaRemainingMoneyDto rm,Long money) throws CodeException, BusinessException;
}
