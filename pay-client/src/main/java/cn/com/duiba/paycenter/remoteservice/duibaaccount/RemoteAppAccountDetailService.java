package cn.com.duiba.paycenter.remoteservice.duibaaccount;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.duibaaccount.AppAccountDetailDto;
import cn.com.duiba.paycenter.dto.duibaaccount.AppAccountDetailPageDto;
import cn.com.duiba.paycenter.dto.duibaaccount.RechargeDetailPageDto;
import cn.com.duiba.paycenter.params.duibaaccount.AppAccountDetailQryParams;
import cn.com.duiba.paycenter.params.duibaaccount.RechargeDetailQryParams;

import java.util.List;

/**
 * 开发者余额账户明细服务
 * author z<PERSON><PERSON><PERSON>
 * date 2018-11-27-13:52
 */
@AdvancedFeignClient
public interface RemoteAppAccountDetailService {

    /**
     * 分页查询
     * @param params
     * @return
     */
    List<AppAccountDetailPageDto> find4page(AppAccountDetailQryParams params);

    /**
     * 分页查询总数
     * @param params
     * @return
     */
    Long count4page(AppAccountDetailQryParams params);

    /**
     * 根据主键和appId查询
     * @param id
     * @return
     */
    AppAccountDetailDto findByIdAndAppId(Long id, Long appId);

    /**
     * 根据业务单号获取
     * @param relationId
     * @param appId
     * @return
     */
    List<AppAccountDetailDto> findByRelationAndAppId(String relationId, List<Integer> relationTypes, Long appId);

    List<AppAccountDetailPageDto> find4Export(AppAccountDetailQryParams params);

    /**
     * 兑吧后台充值管理分页查询
     * @param params
     * @return
     */
    List<RechargeDetailPageDto> findPage4recharge(RechargeDetailQryParams params);

    /**
     * 兑吧后台充值管理分页查询
     * @param params
     * @return
     */
    Integer findCount4recharge(RechargeDetailQryParams params);
}
