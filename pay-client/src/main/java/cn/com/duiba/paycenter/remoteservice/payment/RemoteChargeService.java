package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.WxCouponDetailDto;
import cn.com.duiba.paycenter.dto.payment.WxRedpacketOrderDto;
import cn.com.duiba.paycenter.dto.payment.charge.ChargeOrderDto;
import cn.com.duiba.paycenter.dto.payment.charge.WxCouponResponse;
import cn.com.duiba.paycenter.dto.payment.charge.abc.AbcWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.abc.AbcWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayNativeChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayNativeChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.bankofsuzhou.BankOfSuZhouWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.bankofsuzhou.BankOfSuZhouWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.boc.BocWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.boc.BocWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.ccb.CcbWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.ccb.CcbWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cebXyk.CebXykWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cebXyk.CebXykWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cib.CibPayWxChargeRequestDTO;
import cn.com.duiba.paycenter.dto.payment.charge.cib.CibPayWxChargeResponseDTO;
import cn.com.duiba.paycenter.dto.payment.charge.citic.CiticWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.citic.CiticWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifeChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifePayRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbOneNetPayRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbOneNetPayResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cus.Activity62VipPayRequestDto;
import cn.com.duiba.paycenter.dto.payment.charge.cus.Activity62VipPayRespDto;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.bf.DuibaLiveBFChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.bf.DuibaLiveBFChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.installment.DuibaLiveInstallmentChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.installment.DuibaLiveInstallmentChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.mp.DuibaLiveMpChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.mp.DuibaLiveMpChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.hello.HelloPayChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.hello.HelloPayChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.IcbcH5ChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.IcbcH5ChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeResp;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.elife.IcbcELifeChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.elife.IcbcELifeChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4AppChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4AppChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4WxChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4WxChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.LshmChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.LshmChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.mock.MockWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.mock.MockWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.charge.NbcbChargeRequestDto;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.charge.NbcbChargeResponseDto;
import cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay.UnionPayUmsChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay.UnionPayUmsChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.shouxin.ShouxinPayChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.shouxin.ShouxinPayChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wjrcb.WjrcbPayWxPubChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wjrcb.WjrcbPayWxPubChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.BuildAuthorizationUrlRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxCloseOrderRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxCouponSendRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayLiteChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayLiteChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayMpChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayMpChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayNativeChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayNativeChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayRedPacketQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayRedPacketSendRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.xib.charge.XibChargeRequestDTO;
import cn.com.duiba.paycenter.dto.payment.charge.xib.charge.XibChargeResponseDTO;
import cn.com.duiba.paycenter.result.FundTransferResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/14
 */
@AdvancedFeignClient
public interface RemoteChargeService {
    /**
     * 微信H5支付收单接口
     *
     * @param chargeRequest 支付请求
     * @return WxPayWapChargeResponse
     * @throws BizException bizException
     */
    WxPayWapChargeResponse createWxPayWapCharge(WxPayWapChargeRequest chargeRequest) throws BizException;

    /**
     * 微信公众号支付收单接口
     *
     * @param chargeRequest 支付请求
     * @return WxPayMpChargeResponse
     * @throws BizException bizException
     */
    WxPayMpChargeResponse createWxPayMpCharge(WxPayMpChargeRequest chargeRequest) throws BizException;

    /**
     * 微信小程序收单接口
     * @param chargeRequest 支付请求
     * @return WxPayLiteChargeResponse
     * @throws BizException bizException
     */
    WxPayLiteChargeResponse createWxPayLiteCharge(WxPayLiteChargeRequest chargeRequest) throws BizException;

    /**
     * 微信扫一扫收单接口
     * 返回扫一扫二维码
     * @param chargeRequest wxpay native charge
     * @return wxpay native response
     * @throws BizException bizException
     */
    WxPayNativeChargeResponse createWxPayNativeCharge(WxPayNativeChargeRequest chargeRequest) throws BizException;

    /**
     * 发起支付宝手机网站支付请求
     *
     * @param chargeRequest 支付请求参数
     * @return response
     * @throws BizException bizException
     */
    AlipayWapChargeResponse createAlipayWapCharge(AlipayWapChargeRequest chargeRequest) throws BizException;

    /**
     * 发起支付宝扫一扫支付请求
     * @param chargeRequest alipay native request
     * @return alipay response
     * @throws BizException bizException
     */
    AlipayNativeChargeResponse createAlipayNativeCharge(AlipayNativeChargeRequest chargeRequest) throws BizException;

    /**
     * 苏州银行支付
     * @param chargeRequest suzhou pay request
     * @return suzhou pay response
     * @throws BizException bizException
     */
    BankOfSuZhouWapChargeResponse createSuZhouCharge(BankOfSuZhouWapChargeRequest chargeRequest) throws BizException;


    /**
     * 建设银行支付
     * @param chargeRequest ccb pay request
     * @return ccb pay response
     * @throws BizException bizException
     */
    CcbWapChargeResponse createCcbCharge(CcbWapChargeRequest chargeRequest) throws BizException;
    /**
     * 苏州农商行微信公众号支付
     * @param chargeRequest wjrcb pay request
     * @return wjrcb pay response
     * @throws BizException bizException
     */
    WjrcbPayWxPubChargeResponse createWjrcbWxPubCharge(WjrcbPayWxPubChargeRequest chargeRequest) throws BizException;

    /**
     * 招商银行一网通支付
     * @param chargeRequest
     * @return
     * @throws BizException
     */
    CmbOneNetPayResponse createCmbOneNetCharge(CmbOneNetPayRequest chargeRequest) throws BizException;

    /**
     * 查询支付订单详情
     *
     * @param orderNo 支付订单号
     * @return chargeOrderDto
     * @throws BizException bizException
     */
    ChargeOrderDto findByOrderNo(String orderNo) throws BizException;

    /**
     * 根据业务订单号查询支付信息
     *
     * @param bizOrderNo 业务主订单号
     * @return chargeOrderDto
     * @throws BizException bizException
     */
    List<ChargeOrderDto> findByBizOrderNo(String bizOrderNo) throws BizException;

    /**
     * 查询支付订单详情
     *
     * @param orderNos 支付订单号
     * @return List<ChargeOrderDto>
     * @throws BizException bizException
     */
    List<ChargeOrderDto> batchFindByOrderNo(List<String> orderNos) throws BizException;

    /**
     * 查询支付订单详情
     *
     * @param transactionNos 交易流水号
     * @return List<ChargeOrderDto>
     * @throws BizException bizException
     */
    List<ChargeOrderDto> batchFindByTransactionNo(List<String> transactionNos) throws BizException;

    /**
     * 构造oauth2授权的url连接
     *
     * @param appId       兑吧appId
     * @param redirectURI 跳转的url
     * @return url
     * @throws BizException bizException
     *
     * the latest version：oauth2buildAuthorizationUrlBySubject
     */
    @Deprecated
    String oauth2buildAuthorizationUrl(Long appId, String redirectURI) throws BizException;

    /**
     * 获取支付订单详情
     * @param bizNo 业务方流水号
     * @param bizType 业务方类型
     * @return ChargeOrderDto
     */
    ChargeOrderDto findByBizNoAndBizType(String bizNo, Integer bizType);

    /**
     * 同一个biztype 多个bizOrderNo批量查询
     * @param bizNos
     * @param bizType
     * @return
     */
    public List<ChargeOrderDto> selectByBizNosAndBizType(List<String> bizNos, Integer bizType);

    /**
     * 同一个biztype 多个bizOrderNo批量查询
     * @param bizNos
     * @param bizType
     * @return
     */
     List<ChargeOrderDto> listByBizNosAndBizType(List<String> bizNos, Integer bizType);

    /**
     * 农行支付
     * @param chargeRequest abc pay request
     * @return abc pay response
     * @throws BizException bizException
     */
    AbcWapChargeResponse createAbcCharge(AbcWapChargeRequest chargeRequest) throws BizException;

    /**
     * 中国银行支付
     * @param chargeRequest boc pay request
     * @return boc pay response
     * @throws BizException bizException
     */
    BocWapChargeResponse createBocCharge(BocWapChargeRequest chargeRequest) throws BizException;

    /**
     * 银联云闪付
     *
     * @param chargeRequest union pay request
     * @return union pay response
     * @throws BizException bizException
     */
    UnionPayWapChargeResponse createUnionPayCharge(UnionPayWapChargeRequest chargeRequest) throws BizException;

    /**
     * 中信银行支付
     * @param chargeRequest citic pay request
     * @return citic pay response
     * @throws BizException bizException
     */
    CiticWapChargeResponse createCiticCharge(CiticWapChargeRequest chargeRequest) throws BizException;


    /**
     * 构造oauth2授权的url连接  --  区分业务主体
     *
     * @return url
     * @throws BizException
     */
    String oauth2buildAuthorizationUrlBySubject(BuildAuthorizationUrlRequest request) throws BizException;

    /**
     * 哈啰零钱支付请求
     *
     * @param chargeRequest 支付请求参数
     * @return response
     * @throws BizException bizException
     */
    HelloPayChargeResponse createHelloPayCharge(HelloPayChargeRequest chargeRequest) throws BizException;

    /**
     * 首信支付请求
     *
     * @param chargeRequest 支付请求参数
     * @return response
     * @throws BizException bizException
     */
    ShouxinPayChargeResponse createShouxinPayCharge(ShouxinPayChargeRequest chargeRequest) throws BizException;

    /**
     * 微信小程序收单接口 - 客集集(临时)
     * @param chargeRequest 支付请求
     * @return WxPayLiteChargeResponse
     * @throws BizException bizException
     */
    WxPayLiteChargeResponse createWxPayLiteChargeForKejiji(WxPayLiteChargeRequest chargeRequest) throws BizException;

    /**
     * 微信支付关单
     * @param request
     * @throws BizException
     */
    void closeOrder(WxCloseOrderRequest request) throws BizException;

    /**
     * 发送微信红包
     *
     * @param request 请求
     * @return {@link WxRedpacketOrderDto}
     * @throws BizException 业务异常
     */
    WxRedpacketOrderDto sendWxRedPacket(WxPayRedPacketSendRequest request) throws BizException;


    /**
     * 异步发送微信红包（只负责交给线程池处理）
     *
     * @param request 请求
     * @return {@link WxRedpacketOrderDto}
     * @throws BizException 业务异常
     */
    FundTransferResult asynSendWxRedPacket(WxPayRedPacketSendRequest request);

    /**
     * 微信红包订单信息查询
     *
     * @param request 请求
     * @return {@link WxRedpacketOrderDto}
     * @throws BizException 业务异常
     */
    WxRedpacketOrderDto queryWxRedPacketInfo(WxPayRedPacketQueryRequest request) throws BizException;

    /**
     * mock订单支付
     * @param chargeRequest
     * @return
     * @throws BizException
     */
    MockWapChargeResponse createMockCharge(MockWapChargeRequest chargeRequest) throws BizException;

    /**
     * 工行e支付
     * @param chargeRequest
     * @return
     * @throws BizException
     */
    IcbcH5ChargeResponse createIcbcCharge(IcbcH5ChargeRequest chargeRequest) throws BizException;


    /**
     * 工行e生活支付，app端
     *
     * @param chargeRequest
     * @return
     * @throws BizException
     */
    IcbcElife4AppChargeResponse createIcbcElifeCharge4App(IcbcElife4AppChargeRequest chargeRequest) throws BizException;


    /**
     * 工行e生活支付，微信端
     *
     * @param chargeRequest
     * @return
     * @throws BizException
     */
    IcbcElife4WxChargeResponse createIcbcElifeCharge4Wx(IcbcElife4WxChargeRequest chargeRequest) throws BizException;


    /**
     * 发放微信代金券（目前仅支持兑吧业务主体）
     *
     * @param request 请求
     * @return {@link WxRedpacketOrderDto}
     * @throws BizException 业务异常
     */
    WxCouponResponse sendWxCoupon(WxCouponSendRequest request) throws BizException;

    /**
     * 发放微信代金券（福建兑吧）
     *
     * @param request 请求
     * @return {@link WxRedpacketOrderDto}
     * @throws BizException 业务异常
     */
    WxCouponResponse sendFujianDuibaWxCoupon(WxCouponSendRequest request) throws BizException;

    /**
     * 发送微信立减金，根据微信立减金商户主体
     * @param request
     * @return
     * @throws BizException
     */
    WxCouponResponse sendWxCouponByWxSubject(WxCouponSendRequest request) throws BizException;

    /**
     * 中国光大银行信用卡中信支付
     *
     * @param chargeRequest
     * @return
     * @throws BizException
     */
    CebXykWapChargeResponse createCebXykCharge(CebXykWapChargeRequest chargeRequest) throws BizException;

    /**
     * 兑吧直播小程序支付
     *
     * @param chargeRequest
     * @return
     * @throws BizException
     */
    DuibaLiveMpChargeResponse createDuibaLiveMpCharge(DuibaLiveMpChargeRequest chargeRequest) throws BizException;

    /**
     * 兑吧直播分期支付
     *
     * @param chargeRequest
     * @return
     * @throws BizException
     */
    DuibaLiveInstallmentChargeResponse createDuibaLiveInstallmentCharge(DuibaLiveInstallmentChargeRequest chargeRequest) throws BizException;

    /**
     * 兑吧直播宝付支付
     *
     * @param chargeRequest
     * @return
     * @throws BizException
     */
    DuibaLiveBFChargeResponse createDuibaLiveBFCharge(DuibaLiveBFChargeRequest chargeRequest) throws BizException;


    /**
     * 宁波银行预下单
     * @param chargeRequestDto
     * @return
     * @throws BizException
     */
    NbcbChargeResponseDto createNingboBankPayCharge(NbcbChargeRequestDto chargeRequestDto) throws BizException;


    /**
     * 62vip支付预下单
     *
     * @param activity62VipPayRequestDto 62vip活动支付请求类
     * @return 预下单响应
     * @throws BizException -
     */
    Activity62VipPayRespDto createActivity62VipCharge(Activity62VipPayRequestDto activity62VipPayRequestDto) throws BizException;

    /**
     * 工商银行预下单
     *
     * @param icbcCreditsChargeRequest 预下单请求
     * @return 响应
     * @throws BizException
     */
    IcbcCreditsChargeResp createIcbcCreditsPayCharge(IcbcCreditsChargeRequest icbcCreditsChargeRequest) throws BizException;

    /**
     * 获取微信立减金详情
     * @param couponId
     * @param openId
     * @return ChargeOrderDto
     */
    WxCouponDetailDto findWxCouponDetail(String wxAppId, String couponId, String openId) throws BizException;

    /**
     * 根据主体获取微信立减金详情
     * @param couponId
     * @param openId
     * @return ChargeOrderDto
     */
    WxCouponDetailDto findWxCouponDetailBySubject(String wxAppId, String couponId, String openId, String subjectCode) throws BizException;

    /**
     * 厦门国际银行预下单
     *
     * @param xibChargeRequestDTO xib费用请求dto
     * @return {@link XibChargeResponseDTO}
     * @throws BizException 业务异常
     */
    XibChargeResponseDTO createXibPayCharge(XibChargeRequestDTO xibChargeRequestDTO) throws BizException;

    /**
     * 兴业银行支付预下单
     *
     * @param wxChargeRequestDTO 预下单请求
     * @return {@link CibPayWxChargeResponseDTO}
     * @throws BizException 业务异常
     */
    CibPayWxChargeResponseDTO createCibWxPayCharge(CibPayWxChargeRequestDTO wxChargeRequestDTO) throws BizException;

    /**
     * 微信立减金补发 (建总行使用，其他的别用)
     * @param bizId
     * @param openId
     * @param sotckId
     * @param wxappid
     * @return
     * @throws BizException
     */
    WxCouponResponse reissueWxCoupon(String bizId, String openId, String sotckId, String wxappid, String subject) throws BizException;


    /**
     * 深圳银联ums平台发起支付
     *
     * @param request 请求
     * @return {@link UnionPayUmsChargeResponse}
     */
    UnionPayUmsChargeResponse createUnionPayUmsCharge(UnionPayUmsChargeRequest request)  throws BizException;

    /**
     * 掌上生活-H5支付
     *
     * @param request 请求
     * @return resp
     * @throws BizException 业务异常
     */
    CmbLifeChargeResponse createCmbLifeCharge(CmbLifePayRequest request) throws BizException;


    /**
     * 工行e生活支付
     * @param chargeRequest 请求
     * @return resp
     * @throws BizException 业务异常
     */
    IcbcELifeChargeResponse createIcbcELifeCharge(IcbcELifeChargeRequest chargeRequest) throws BizException;

    /**
     * 微博支付
     * @param request 请求
     * @return resp
     * @throws BizException 业务异常
     */
    WeiboChargeResponse createWeiboCharge(WeiboChargeRequest request) throws BizException;

    /**
     * 零食很忙支付
     * @param request 请求
     * @return resp
     * @throws BizException 业务异常
     */
    LshmChargeResponse createLshmCharge(LshmChargeRequest request) throws BizException;

}
