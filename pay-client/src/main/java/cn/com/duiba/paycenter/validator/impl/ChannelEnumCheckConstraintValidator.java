package cn.com.duiba.paycenter.validator.impl;

import cn.com.duiba.paycenter.enums.ChannelEnum;
import cn.com.duiba.paycenter.validator.ChannelEnumCheck;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 * @date 2018/11/13
 */
public class ChannelEnumCheckConstraintValidator implements ConstraintValidator<ChannelEnumCheck, String> {
    private String message;
    @Override
    public void initialize(ChannelEnumCheck constraintAnnotation) {
        this.message = constraintAnnotation.message();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        if (ChannelEnum.isActive(value)) {
            return true;
        }
        context.disableDefaultConstraintViolation();
        ConstraintValidatorContext.ConstraintViolationBuilder builder =
                context.buildConstraintViolationWithTemplate(message);
        builder.addConstraintViolation();
        return false;
    }
}
