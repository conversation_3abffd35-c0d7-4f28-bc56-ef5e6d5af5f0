package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayActivityInfoResponse;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayCouponCardRequest;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayCouponCardResponse;

import java.util.Map;


@AdvancedFeignClient
public interface RemoteAlipayCouponService {

    /**
     * 根据活动id查询支付宝立减金活动
     * @param subject
     * @param actId
     * @return
     */
    AlipayActivityInfoResponse getAlipayActivityInfo(String subject, String actId);

    /**
     * 发放支付宝立减金
     * @return
     */
    AlipayCouponCardResponse grantAlipayCoupon(AlipayCouponCardRequest alipayCouponCardRequest) throws BizException;


    /**
     * 根据主体，获取授权链接URL
     * @param subject
     * @return appid
     */
    String getAccessUrl(String subject, String redirectUrl);

    /**
     * 根据authcode查询支付宝用户id
     * @param subject
     * @param authCode
     * @return
     */
    String queryAlipayUserId(String subject, String authCode);

    /**
     * 支付宝核销通知处理 -- 根据业务主体区分
     * @param params 支付宝异步通知参数
     * @return AlipayChargeNotifyResponse
     * @throws BizException bizException
     */
    boolean activityNotifyBySubjectType(Map<String, String> params, String subjectType) throws BizException;

}
