package cn.com.duiba.paycenter.dto.payment.notify.cooupon;


import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

/**
 * Description:
 * <p>
 * date: 2022/10/11 7:29 下午
 *
 * <AUTHOR>
 */
public class ConsumeInformation implements Serializable {

    private static final long serialVersionUID = -2754029171270389535L;
    /**
     * 核销时间
     */
    @JSONField(name = "consume_time")
    private String consumeTime;
    /**
     * 核销商户号
     */
    @JSONField(name = "consume_mchid")
    private String consumeMchid;
    /**
     * 核销订单号
     */
    @JSONField(name = "transaction_id")
    private String transactionId;

    /**
     *单品信息
     */
    @JSONField(name = "goods_detail")
    private List<GoodDetail> goodsDetail;

    public String getConsumeTime() {
        return consumeTime;
    }

    public ConsumeInformation setConsumeTime(String consumeTime) {
        this.consumeTime = consumeTime;
        return this;
    }

    public String getConsumeMchid() {
        return consumeMchid;
    }

    public ConsumeInformation setConsumeMchid(String consumeMchid) {
        this.consumeMchid = consumeMchid;
        return this;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public ConsumeInformation setTransactionId(String transactionId) {
        this.transactionId = transactionId;
        return this;
    }

    public List<GoodDetail> getGoodsDetail() {
        return goodsDetail;
    }

    public ConsumeInformation setGoodsDetail(List<GoodDetail> goodsDetail) {
        this.goodsDetail = goodsDetail;
        return this;
    }
}
