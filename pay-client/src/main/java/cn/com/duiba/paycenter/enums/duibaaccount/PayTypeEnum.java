package cn.com.duiba.paycenter.enums.duibaaccount;

import java.util.Arrays;
import java.util.Objects;

/**
 * 支付类型枚举
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018-11-27-16:16
 */
public enum PayTypeEnum {
    ALIPAY(1, "支付宝"),
    WECHAT(2, "微信"),
    BANK(3, "银行卡");

    private Integer code;

    private String desc;

    PayTypeEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code){
        if (Objects.isNull(code)){
            return "";
        }
        PayTypeEnum payTypeEnum = getByCode(code);
        if (Objects.nonNull(payTypeEnum)){
            return payTypeEnum.getDesc();
        }
       return "";
    }

    public static PayTypeEnum getByCode(Integer code){
        return Arrays.stream(PayTypeEnum.values()).filter(a -> a.code.equals(code)).findFirst().orElse(null);
    }

    public static boolean isExist(Integer code){
        return Objects.nonNull(code) &&
                Arrays.stream(PayTypeEnum.values()).anyMatch(a -> a.code.equals(code));
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
