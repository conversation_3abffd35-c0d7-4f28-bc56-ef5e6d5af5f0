package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayChargeNotifyResponse;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/12/11
 */
@AdvancedFeignClient
public interface RemoteAlipayNotifyService {
    /**
     * 支付宝支付通知处理
     * @param params 支付宝异步通知参数
     * @link https://docs.open.alipay.com/203/105286/   具体详见文档
     * @return AlipayChargeNotifyResponse
     * @throws BizException bizException
     *
     * the latest version：orderNotifyBySubjectType
     */
    @Deprecated
    AlipayChargeNotifyResponse orderNotify(Map<String, String> params) throws BizException;

    /**
     * 支付宝支付通知处理 -- 根据业务主体区分
     * @param params 支付宝异步通知参数
     * @link https://docs.open.alipay.com/203/105286/   具体详见文档
     * @return AlipayChargeNotifyResponse
     * @throws BizException bizException
     */
    AlipayChargeNotifyResponse orderNotifyBySubjectType(Map<String, String> params, String subjectType) throws BizException;
}
