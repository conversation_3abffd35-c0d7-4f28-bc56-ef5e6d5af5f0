package cn.com.duiba.paycenter.exception;

public class CodeException extends Exception{

	private static final long serialVersionUID = 1L;

	private final int code;
	
	public CodeException(int code){
		this.code=code;
	}
	
	public CodeException(int code, Exception e){
		super(e);
		this.code=code;
	}

	public CodeException(int code, String msg){
		super(msg);
		this.code=code;
	}

	public int getCode() {
		return code;
	}
}
