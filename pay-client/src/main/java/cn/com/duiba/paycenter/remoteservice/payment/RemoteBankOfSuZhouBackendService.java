package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.bankofsuzhou.BankOfSuZhouWapChargeResponse;

/**
 * <AUTHOR>
 * @date 2019/6/26 20:38
 */
@AdvancedFeignClient
public interface RemoteBankOfSuZhouBackendService {

    /**
     * 苏州银行对账单请求接口
     * @param acDate 会计时间 YYYYMMDD格式
     *               会计时间可以为null,默认取当前日期前一天的数据
     * @return
     */
    BankOfSuZhouWapChargeResponse getBillCheckFileUrl(String acDate);
}
