package cn.com.duiba.paycenter.model;
/**
 * DO????????????
 * ??????insert???update???????????????
 * <AUTHOR>
 *
 */
public abstract class BaseDO {

	protected transient Boolean toBeInsert;
	
	protected transient Boolean toBeUpdate;

	public Boolean getToBeInsert() {
		if(toBeInsert==null){
			toBeInsert=false;
		}
		return toBeInsert;
	}

	public Boolean getToBeUpdate() {
		if(toBeUpdate==null){
			toBeUpdate=false;
		}
		return toBeUpdate;
	}
}
