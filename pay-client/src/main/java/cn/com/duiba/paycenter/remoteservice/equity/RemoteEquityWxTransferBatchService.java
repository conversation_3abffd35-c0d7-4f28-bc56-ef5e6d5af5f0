package cn.com.duiba.paycenter.remoteservice.equity;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.equity.request.BaseEquityRequest;
import cn.com.duiba.paycenter.dto.equity.request.wx.transfer.EquityWxInitiateBatchTransferRequest;
import cn.com.duiba.paycenter.dto.equity.response.BaseEquityResultResponse;
import cn.com.duiba.paycenter.dto.equity.response.EquityResponse;
import cn.com.duiba.paycenter.dto.equity.response.wx.transfer.BaseEquityDetailResultResponse;
import cn.com.duiba.paycenter.dto.equity.response.wx.transfer.EquityWxInitiateBatchTransferResponse;

/**
 * 微信商家转账到零钱
 * <AUTHOR>
 * @date 2023/4/25 11:56 AM
 */
@AdvancedFeignClient
public interface RemoteEquityWxTransferBatchService {

    /**
     * 发放
     * 注意：若结果以mq回调，业务方需要根据业务类型+业务单号幂等<br/>
     * 长时间收不到回调调用查询接口查询<br/>
     *
     * @param request 发放权益请求参数
     * @return 本次请求是否成功
     */
    EquityResponse distribute(BaseEquityRequest<EquityWxInitiateBatchTransferRequest> request);

    /**
     * 查询发放结果
     *
     * @param bizType 业务类型
     *        {@link cn.com.duiba.paycenter.enums.equity.EquityBizTypeEnum}
     * @param bizNo 业务方唯一单号
     * @return 发放权益结果
     */
    BaseEquityResultResponse<EquityWxInitiateBatchTransferResponse> distributeResult(Integer bizType, String bizNo);

    /**
     * 查询发放结果
     *
     * @param bizType 业务类型
     *        {@link cn.com.duiba.paycenter.enums.equity.EquityBizTypeEnum}
     * @param bizNo 业务方唯一单号
     * @param outDetailNo 明细单号
     * @return 发放权益结果
     */
    BaseEquityDetailResultResponse distributeDetailResult(Integer bizType, String bizNo, String outDetailNo);
}
