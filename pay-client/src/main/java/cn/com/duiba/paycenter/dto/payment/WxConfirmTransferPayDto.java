package cn.com.duiba.paycenter.dto.payment;

import java.io.Serializable;
import java.util.Date;

public class WxConfirmTransferPayDto implements Serializable {
    private static final long serialVersionUID = 8653731002085170783L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 兑吧appId
     */
    private Long appId;

    /**
     * 微信支付分配的商户号
     */
    private String mchId;

    /**
     * 微信分配的公众账号ID（企业号corpid即为此appId）
     */
    private String wxappid;

    /**
     * 商户订单号
     */
    private String mchTradeNo;

    /**
     * 业务方关联活动类型
     */
    private Integer bizRelationType;

    /**
     * 业务方关联活动id
     */
    private String bizRelationId;

    /**
     * 业务订单号
     */
    private String bizOrderNo;

    /**
     * 支付渠道
     */
    private String channelType;

    /**
     * 下次需要执行的时间
     */
    private Date nextTime;

    /**
     * 重试查询次数
     */
    private Integer retry;

    /**
     * 渠道发起交易时需要的数据和额外的数据，JSON格式
     */
    private String extra;

    private Integer subjectType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getWxappid() {
        return wxappid;
    }

    public void setWxappid(String wxappid) {
        this.wxappid = wxappid;
    }

    public String getMchTradeNo() {
        return mchTradeNo;
    }

    public void setMchTradeNo(String mchTradeNo) {
        this.mchTradeNo = mchTradeNo;
    }

    public Integer getBizRelationType() {
        return bizRelationType;
    }

    public void setBizRelationType(Integer bizRelationType) {
        this.bizRelationType = bizRelationType;
    }

    public String getBizRelationId() {
        return bizRelationId;
    }

    public void setBizRelationId(String bizRelationId) {
        this.bizRelationId = bizRelationId;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public Date getNextTime() {
        return nextTime;
    }

    public void setNextTime(Date nextTime) {
        this.nextTime = nextTime;
    }

    public Integer getRetry() {
        return retry;
    }

    public void setRetry(Integer retry) {
        this.retry = retry;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public Integer getSubjectType() {
        return subjectType;
    }

    public void setSubjectType(Integer subjectType) {
        this.subjectType = subjectType;
    }
}
