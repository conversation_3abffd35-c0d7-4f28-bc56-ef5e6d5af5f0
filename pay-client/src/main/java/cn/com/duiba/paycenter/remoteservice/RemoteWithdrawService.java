package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.client.RpcResult;
import cn.com.duiba.paycenter.params.WithdrawParams;
import cn.com.duiba.paycenter.result.WithdrawResult;

/**
 * Created by xiaoxuda on 2018/1/30.
 */
@AdvancedFeignClient
public interface RemoteWithdrawService {
    /**
     * 用户余额提现，对开发者扣费
     * @param requestParams
     * @param sign
     * @return
     */
    RpcResult<WithdrawResult> userWithdrawCashApply(WithdrawParams requestParams, String sign);

    /**
     * 用户余额提现，对开发者扣费
     * @param requestParams
     * @param sign
     * @return
     */
    RpcResult<WithdrawResult> userWithdrawCashPaybackApply(WithdrawParams requestParams, String sign);
}
