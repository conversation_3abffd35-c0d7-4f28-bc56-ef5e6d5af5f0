package cn.com.duiba.paycenter.remoteservice.duibaaccount;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.duibaaccount.SupplierRemainingMoneyDto;
import cn.com.duiba.paycenter.params.duibaaccount.SupplierAccountChangeParams;
import cn.com.duiba.paycenter.result.PayCenterResult;

/**
 * 供应商待结算账户remote
 * author zhang<PERSON><PERSON>
 * date 2018-12-13-14:38
 */
@AdvancedFeignClient
public interface RemoteSupplierSettleAccountService {

    /**
     * 供应商待结算账户扣款
     * @param param
     * @return
     */
    PayCenterResult reduceMoney(SupplierAccountChangeParams param, String sign);

    /**
     * 供应商待结算账户回款
     * @param param
     * @return
     */
    PayCenterResult addMoney(SupplierAccountChangeParams param,String sign);

    /**
     * 获取待结算账户金额
     * @param supplierId
     * @return
     */
    Long findMoney(Long supplierId);

    /**
     * 创建供应商待结算账户
     * @param supplierId 供应商ID
     * @return
     */
    SupplierRemainingMoneyDto create(Long supplierId);
}
