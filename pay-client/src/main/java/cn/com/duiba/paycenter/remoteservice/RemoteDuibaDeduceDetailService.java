package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.DuibaDeduceDetailDto;

import java.util.List;
import java.util.Map;

@AdvancedFeignClient
public interface RemoteDuibaDeduceDetailService {
	
	/**
     * @Title: findPage
     * @Description: 分页列表
     * @param queryMap
     * @return
     */
    public List<DuibaDeduceDetailDto> findPageList(Map<String, Object> queryMap);

    public Long findPageCount(Map<String, Object> queryMap);

    public DuibaDeduceDetailDto insert(DuibaDeduceDetailDto duibaDeduceDetailDO);

    public int update(DuibaDeduceDetailDto duibaDeduceDetailDO);
    
    public int updateDuibaDeduceDetail(Long id,String operationStatus);

}
