package cn.com.duiba.paycenter.dto.payment.notify.cooupon;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * @author: pengyi
 * @description:
 * @date: 2022/12/13 下午2:40
 */
public class WxCouponUserFlowResponse implements Serializable {
    private static final long serialVersionUID = -3268156179287867839L;

    @JSONField(name = "url")
    private String url;

    @JSONField(name = "hash_value")
    private String hashValue;

    @JSONField(name = "hash_type")
    private String hashType;

    @JSONField(name = "code")
    private String code;

    @JSONField(name = "message")
    private String message;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getHashValue() {
        return hashValue;
    }

    public void setHashValue(String hashValue) {
        this.hashValue = hashValue;
    }

    public String getHashType() {
        return hashType;
    }

    public void setHashType(String hashType) {
        this.hashType = hashType;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
