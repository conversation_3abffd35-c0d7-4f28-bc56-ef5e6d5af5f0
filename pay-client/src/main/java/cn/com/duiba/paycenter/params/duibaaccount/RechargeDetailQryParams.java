package cn.com.duiba.paycenter.params.duibaaccount;

import cn.com.duiba.paycenter.params.PageQueryParams;

import java.util.Date;
import java.util.List;

/**
 * 兑吧后台充值管理查询参数
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2019-01-29-11:25
 */
public class RechargeDetailQryParams extends PageQueryParams {

    private static final long serialVersionUID = 6065463520230667401L;

    /**
     * 开发者ID集合
     */
    private List<Long> appIdList;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 业务类型
     * @see cn.com.duiba.paycenter.enums.duibaaccount.AppAccountRelationTypeEnum
     */
    private Integer relationType;

    public List<Long> getAppIdList() {
        return appIdList;
    }

    public void setAppIdList(List<Long> appIdList) {
        this.appIdList = appIdList;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getRelationType() {
        return relationType;
    }

    public void setRelationType(Integer relationType) {
        this.relationType = relationType;
    }
}
