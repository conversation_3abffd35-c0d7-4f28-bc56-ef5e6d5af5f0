package cn.com.duiba.paycenter.dto.payment.notify.cooupon;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Description:
 * <p>
 * date: 2022/10/17 4:17 下午
 *
 * <AUTHOR>
 */
public class WxCouponNotifyUrlSetResponse implements Serializable {
    private static final long serialVersionUID = -2418726184047872536L;

    /**
     * 修改时间
     */
    @JSONField(name = "update_time")
    private String updateTime;

    /**
     * 通知地址
     */
    @JSONField(name = "notify_url")
    private String notifyUrl;

    public String getUpdateTime() {
        return updateTime;
    }

    public WxCouponNotifyUrlSetResponse setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public WxCouponNotifyUrlSetResponse setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
        return this;
    }
}
