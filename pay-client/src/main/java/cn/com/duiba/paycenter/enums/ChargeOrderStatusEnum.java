package cn.com.duiba.paycenter.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/11/15
 */
public enum ChargeOrderStatusEnum {
    INIT(0, "初始化状态"),
    ORDER_SUCCESS(1,"第三方支付收单成功"),
    ORDER_FAIL(2, "第三方支付收单失败"),
    PAY_SUCCESS(4, "支付成功"),
    PAY_FAIL(3, "支付失败"),
    PAY_REVERSED(5, "支付取消"),
    REFUNDING(6, "退款中"),
    REFUND_SUCCESS(7, "退款成功"),
    REFUND_FAIL(8, "退款失败")
    ;
    private Integer code;
    private String desc;

    ChargeOrderStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getCode() {
        return code;
    }

    private static final Map<Integer, ChargeOrderStatusEnum> ENUM_MAP = new HashMap<>();

    static{
        for(ChargeOrderStatusEnum tmp : values()){
            ENUM_MAP.put(tmp.getCode(), tmp);
        }
    }

    public static ChargeOrderStatusEnum getByCode(Integer code) {
        return ENUM_MAP.get(code);
    }
}
