package cn.com.duiba.paycenter.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018/11/27
 */
public enum RefundOrderStatusEnum {
    INIT(1, "初始化状态"),
    ORDER_FAILED(2, "第三方退款申请失败"),
    ORDER_SUCCEEDED(3, "第三方退款申请成功"),
    SUCCEEDED(4, "退款成功"),
    FAILED(5, "退款失败")
    ;
    private Integer code;
    private String desc;

    RefundOrderStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据类型，反查枚举类<br/>
     * 若是没找到，返回null
     *
     * @param code 枚举类型
     * @return 枚举类
     */
    public static RefundOrderStatusEnum getRefundOrderStatusEnum(Integer code) {
        if(code == null){
            return null;
        }
        RefundOrderStatusEnum[] values = RefundOrderStatusEnum.values();
        for (RefundOrderStatusEnum value : values) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }

        return null;
    }
}
