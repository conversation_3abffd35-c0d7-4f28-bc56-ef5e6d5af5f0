package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayLiteNotifyV3Request;
import cn.com.duiba.paycenter.dto.payment.refund.wxpay.WxPayRefundNotifyResponse;

/**
 * <AUTHOR>
 * @date 2018/11/21
 */
@AdvancedFeignClient
public interface RemoteWxPayNotifyService {
    /**
     * 支付订单异步通知接口
     *
     * @param xmlData 微信返回的xml
     * @return WxPayChargeNotifyResponse
     * @throws BizException bizException
     *
     * the latest version：orderNotifyBySubjectType
     */
    @Deprecated
    WxPayChargeNotifyResponse orderNotify(String xmlData) throws BizException;

    /**
     * 退款结果异步通知处理接口
     * @param xmlData 微信返回的xml
     * @return 支付中心的处理结果
     * @throws BizException 业务异常
     *
     * the latest version：refundNotifyBySubjectType
     */
    @Deprecated
    WxPayRefundNotifyResponse refundNotify(String xmlData) throws BizException;

    /**
     * 微信小程序支付V3退款结果通知
     *
     * @param request 微信结果加签前数据
     * @return 支付中心的处理结果
     */
    WxPayRefundNotifyResponse refundNotifyV3(WxPayLiteNotifyV3Request request);

    /**
     * 支付订单异步通知接口
     *
     * @param xmlData 微信返回的xml
     * @param subjectType 业务主体标识
     * @return WxPayChargeNotifyResponse
     * @throws BizException bizException
     */
    WxPayChargeNotifyResponse orderNotifyBySubjectType(String xmlData, String subjectType) throws BizException;

    /**
     * 退款结果异步通知处理接口
     * @param xmlData 微信返回的xml
     * @param subjectType 业务主体标识
     * @return 支付中心的处理结果
     * @throws BizException 业务异常
     */
    WxPayRefundNotifyResponse refundNotifyBySubjectType(String xmlData, String subjectType) throws BizException;
}
