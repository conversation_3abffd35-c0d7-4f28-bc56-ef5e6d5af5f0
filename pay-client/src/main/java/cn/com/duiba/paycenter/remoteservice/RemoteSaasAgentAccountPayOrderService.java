package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.params.PayOrdersExtraParams;
import cn.com.duiba.paycenter.result.PayOrdersResult;

import java.util.Map;

/**
 * 代理商订单付款相关接口服务
 *
 * <AUTHOR>
 */
@AdvancedFeignClient(qualifier = "remoteSaasAgentAccountPayService")
public interface RemoteSaasAgentAccountPayOrderService {


    /**
     * 订单下单时的付款接口
     * <p>
     * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
     * <p>
     * 参数签名生成方式
     * Map&lt;String, String&gt; params = new HashMap<>();
     * params.put("agentId", agentId + "");
     * params.put("orderId", orderId + "");
     * params.put("money", money + "");
     * String checksign = cn.com.duiba.paycenter.util.SignUtil.sign(params);
     *
     * @param agentId 代理商ID
     * @param orderId 订单ID
     * @param money   充值金额
     * @param sign    参数签名
     * @param p       额外信息
     * @return 下单结果
     * @see cn.com.duiba.paycenter.util.SignUtil#sign
     */
    PayOrdersResult payOrder(Long agentId, Long orderId, Long money, String sign, PayOrdersExtraParams p);

    /**
     * 订单失败还款的接口
     * <p>
     * 可重复执行，在执行之前会查询之前是否已经执行成功，如果之前已经执行成功，本次也返回成功的结果
     * <p>
     * 参数签名生成方式
     * Map&lt;String, String&gt; params = new HashMap<>();
     * params.put("agentId", agentId + "");
     * params.put("orderId", orderId + "");
     * params.put("money", money + "");
     * String checksign = cn.com.duiba.paycenter.util.SignUtil.sign(params);
     *
     * @param agentId 代理商ID
     * @param orderId 订单ID
     * @param money   充值金额
     * @param sign    数字签名
     * @param p       额外信息
     * @return 还款结果
     * @see cn.com.duiba.paycenter.util.SignUtil#sign
     */
    PayOrdersResult backPayOrder(Long agentId, Long orderId, Long money, String sign, PayOrdersExtraParams p);

    /**
     * 检查一个操作是否执行成功
     *
     * @param orderId 订单ID
     * @return 操作结果
     */
    boolean checkActionSuccess(Long orderId, String actionType);
}
