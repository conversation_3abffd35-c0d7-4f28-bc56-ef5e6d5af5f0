package cn.com.duiba.paycenter.dto.payment;

import java.io.Serializable;
import java.util.Date;

/**
 * @program: pay-center
 * @description: 待核实支付订单实体类
 * @author: Simba
 * @create: 2020-01-14 16:56
 **/
public class WaitConfirmChargeOrderDto implements Serializable {

    /**
     * 订单类型 - 支付
     */
    public static final Integer ORDER_TYPE_CHARGE = 0;
    /**
     * 订单类型 - 退款
     */
    public static final Integer ORDER_TYPE_REFUND = 1;

    private static final long serialVersionUID = -6746539958580366713L;
    private Long id;

    private Long appId;

    private String orderNo;

    private String bizOrderNo;

    private Integer bizType;

    private String channelType;

    private Integer orderType = ORDER_TYPE_CHARGE;

    /**
     * 2048
     */
    private String extra;

    /**
     * 已重试次数
     */
    private Integer retry;

    private Date orderDate;

    private Date nextTime;

    private Date gmtCreate;

    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public Integer getRetry() {
        return retry;
    }

    public void setRetry(Integer retry) {
        this.retry = retry;
    }

    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    public Date getNextTime() {
        return nextTime;
    }

    public void setNextTime(Date nextTime) {
        this.nextTime = nextTime;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
}

