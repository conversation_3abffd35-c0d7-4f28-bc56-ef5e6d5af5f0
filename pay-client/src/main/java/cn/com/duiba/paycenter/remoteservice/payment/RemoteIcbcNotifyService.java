package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.abc.AbcChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.IcbcChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.elife.IcbcELifeNotifyResponse;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/12
 */
@AdvancedFeignClient
public interface RemoteIcbcNotifyService {
    /**
     * 工行支付通知
     * @param notifyUrl 要对通知接口路径进行签名校验，如果上游业务充值时指定了通知接口，则必传，否则走默认配置
     * @param map params
     * @return response
     * @throws BizException exception
     */
    IcbcChargeNotifyResponse orderNotify(String notifyUrl, Map<String, String> map) throws BizException;


    /**
     * 支付通知
     *
     * @param notifyParam 通知参数
     * @return 解析后的支付通知参数
     * @throws BizException 异常
     */
    IcbcELifeNotifyResponse payNotify(String notifyParam) throws BizException;
}
