package cn.com.duiba.paycenter.model;

import java.io.Serializable;
import java.util.Date;

public class AccountChangeLogDO extends BaseDO implements Serializable {

	private static final long serialVersionUID = 44212072389923L;

	public static final String ChangeKindAdd="add";
	public static final String ChangeKindSub="sub";
	
	public static final int StatusSuccess=1;
	public static final int StatusFail=0;
	
	private Long id;
	private Long developerId;
	private String relationType;
	private Long relationId;
	private String actionType;
	private Long changeMoney;
	private String changeKind;
	private Long beforeBalance;
	private Long afterBalance;
	private Date gmtCreate;
	private Date gmtModified;
	
	private Integer status;
	private String failReason;

	private String orderItemIds;
	
	public AccountChangeLogDO() {
		// Do nothing
	}

	public AccountChangeLogDO(boolean init4insert) {
		if (init4insert) {
			toBeInsert = true;
			gmtCreate = new Date();
			gmtModified = new Date();
		}
	}

	public String getOrderItemIds() {
		return orderItemIds;
	}
	public void setOrderItemIds(String orderItemIds) {
		this.orderItemIds = orderItemIds;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getDeveloperId() {
		return developerId;
	}
	public void setDeveloperId(Long developerId) {
		this.developerId = developerId;
	}
	public String getRelationType() {
		return relationType;
	}
	public void setRelationType(String relationType) {
		this.relationType = relationType;
	}
	public Long getRelationId() {
		return relationId;
	}
	public void setRelationId(Long relationId) {
		this.relationId = relationId;
	}
	public String getActionType() {
		return actionType;
	}
	public void setActionType(String actionType) {
		this.actionType = actionType;
	}
	public Long getChangeMoney() {
		return changeMoney;
	}
	public void setChangeMoney(Long changeMoney) {
		this.changeMoney = changeMoney;
	}
	public String getChangeKind() {
		return changeKind;
	}
	public void setChangeKind(String changeKind) {
		this.changeKind = changeKind;
	}
	public Long getBeforeBalance() {
		return beforeBalance;
	}
	public void setBeforeBalance(Long beforeBalance) {
		this.beforeBalance = beforeBalance;
	}
	public Long getAfterBalance() {
		return afterBalance;
	}
	public void setAfterBalance(Long afterBalance) {
		this.afterBalance = afterBalance;
	}
	public Date getGmtCreate() {
		return gmtCreate;
	}
	public void setGmtCreate(Date gmtCreate) {
		this.gmtCreate = gmtCreate;
	}
	public Date getGmtModified() {
		return gmtModified;
	}
	public void setGmtModified(Date gmtModified) {
		this.gmtModified = gmtModified;
	}
	public String getFailReason() {
		return failReason;
	}
	public void setFailReason(String failReason) {
		this.failReason = failReason;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}
}
