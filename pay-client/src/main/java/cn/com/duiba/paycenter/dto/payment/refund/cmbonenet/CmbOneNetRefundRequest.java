package cn.com.duiba.paycenter.dto.payment.refund.cmbonenet;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @program: pay-center
 * @description: 招商银行一网通支付退款请求体
 * @author: <PERSON>mba
 * @create: 2019-09-27 10:13
 **/
public class CmbOneNetRefundRequest implements Serializable {

    private static final long serialVersionUID = 6650835624038124808L;
    /**
     * 退款金额
     */
    @NotNull(message = "金额不能为空")
    @Min(value = 1, message = "金额必须大于1分")
    private Integer amount;
    /**
     * 主订单号
     */
    @NotNull(message = "主订单号不能为空")
    private String bizOrderNo;

    @NotNull(message = "订单日期不能为空")
    private Date orderDate;

    //主订单子订单号, 支持购物车按每种商品退
    private Long ordersItemId;

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    public Long getOrdersItemId() {
        return ordersItemId;
    }

    public void setOrdersItemId(Long ordersItemId) {
        this.ordersItemId = ordersItemId;
    }
}
