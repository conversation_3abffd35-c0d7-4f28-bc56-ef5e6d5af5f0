package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.bf.DuibaLiveBfSubAccountRequest;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.citic.DuibaLiveCiticNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.installment.DuibaLiveInstallmentNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.mp.DuibaLiveMpNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.refund.BaseRefundNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.refund.wxpay.WxPayRefundNotifyResponse;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/5/18 3:55 下午
 */
@AdvancedFeignClient
public interface RemoteDuibaLiveNotifyService {
    /**
     * 兑吧直播小程序支付通知
     * @param params
     * @return
     * @throws BizException
     */
    DuibaLiveMpNotifyResponse orderNotify(Map<String,String> params) throws BizException;

    /**
     * 兑吧直播小程序支付通知(直连)
     * @param xmlData
     * @return
     * @throws BizException
     */
    DuibaLiveMpNotifyResponse wxOrderNotify(String xmlData) throws BizException;

    /**
     * 退款通知接口
     * @param xmlData
     * @return
     * @throws BizException
     */
    WxPayRefundNotifyResponse refundNotify(String xmlData) throws BizException;


    /**
     * 兑吧直播分期支付通知
     * @param params
     * @return
     * @throws BizException
     */
    DuibaLiveInstallmentNotifyResponse installmentOrderNotify(Map<String,String> params) throws BizException;

    /**
     * 银联分期支付退款通知
     * @param params
     * @return
     * @throws BizException
     */
    BaseRefundNotifyResponse installmentOrderRefundNotify(Map<String,String> params) throws BizException;


    /**
     * 兑吧直播中信支付回调通知
     *
     * @param content 开发者回调通知信息
     * @return
     */
    DuibaLiveCiticNotifyResponse duibaLiveCiticCallbackPayNotify(String content);

    /**
     * 兑吧直播宝付支付回调通知
     *
     * @param params
     * @return
     */
    BaseChargeNotifyResponse duibaLiveBFPayNotify(Map<String, String> params);

    /**
     * 兑吧直播宝付退款回调通知
     *
     * @param params
     * @return
     */
    BaseRefundNotifyResponse duibaLiveBFRefundNotify(Map<String, String> params);

    /**
     * 宝付确认分账
     */
    void confirmSubAccount(DuibaLiveBfSubAccountRequest request);

    /**
     * 宝付确认分账通知
     */
    void confirmSubAccountNodify(Map<String, String> params);
}
