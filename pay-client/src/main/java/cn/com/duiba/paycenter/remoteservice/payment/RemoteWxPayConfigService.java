package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.config.WxPayConfigDto;
import cn.com.duiba.paycenter.params.miniProgram.MiniProgramConfigParam;

/**
 * <AUTHOR>
 * @date 2018/12/05
 */
@AdvancedFeignClient
public interface RemoteWxPayConfigService {
    /**
     * 获取app的配置类型
     *
     * @param appId       兑吧appId
     * @param channelType 渠道类型
     * @return channel mode
     * @see cn.com.duiba.paycenter.enums.ChannelModeEnum
     */
    Integer getChannelMode(Long appId, String channelType);

    /**
     * 获取微信配置
     * ⚠️如果是兑吧的配置不带app信息和渠道信息
     *      查询不到默认返回"兑吧"微信支付配置
     *
     * @param wxAppId 微信公众号id
     * @param mchId   微信商户id
     * @return 微信配置
     * @throws BizException bizException
     */
    WxPayConfigDto findByWxAppIdAndMchId(String wxAppId, String mchId) throws BizException;

    /**
     * 根据渠道查询配置，过滤无效的记录
     * @param appId
     * @param channelType
     * @return
     * @throws BizException
     */
    WxPayConfigDto findByWxAppIdAndChannelType(Long appId, String channelType) ;


    Integer updateMiniProgramConfigById(MiniProgramConfigParam miniProgramConfigParam) throws BizException;


    Integer insertMiniProgram(MiniProgramConfigParam miniProgramConfigParam) throws BizException;

    /**
     * 失效
     * @param id
     */
    void inValideCacheKey(Long id);

}
