package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.abc.AbcChargeNotifyResponse;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/09/03
 */
@AdvancedFeignClient
public interface RemoteAbcNotifyService {
    /**
     * 农行支付通知
     * @param map params
     * @return response
     * @throws BizException exception
     */
    AbcChargeNotifyResponse orderNotify(Map<String, String> map) throws BizException;
}
