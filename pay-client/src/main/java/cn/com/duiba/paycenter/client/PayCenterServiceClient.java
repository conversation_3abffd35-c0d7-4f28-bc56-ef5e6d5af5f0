package cn.com.duiba.paycenter.client;

import cn.com.duiba.paycenter.model.AccountChangeRecordDO;
import cn.com.duiba.paycenter.model.DuibaAccountChangeRecordDO;
import cn.com.duiba.paycenter.service.PayCenterService;

import java.util.List;

public class PayCenterServiceClient {

	private PayCenterService payCenterService;
	
	public RpcResult<Long> getBalance(Long developerId){
		try {
			Long balance=payCenterService.getBalance(developerId);
			
			return new RpcResult<>(balance);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}
	
	public RpcResult<Long> getDuibaBalance(){
		try {
			Long balance=payCenterService.getDuibaBalance();

			return new RpcResult<>(balance);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}
	
	public RpcResult<List<AccountChangeRecordDO>> find(String relationType,Long relationId){
		
		try {
			List<AccountChangeRecordDO> list=payCenterService.find(relationType, relationId);
			return new RpcResult<>(list);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}
	
	public RpcResult<List<DuibaAccountChangeRecordDO>> findDuibaRecords(String relationType,Long relationId){
		
		try {
			List<DuibaAccountChangeRecordDO> list=payCenterService.findDuibaRecords(relationType, relationId);
			return new RpcResult<>(list);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}
	
	public RpcResult<List<AccountChangeRecordDO>>findAllGreaterId(Long lastId,int batchSize){
		
		try {
			List<AccountChangeRecordDO> list=payCenterService.findAllGreaterId(lastId,batchSize);
			return new RpcResult<>(list);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}
	
	public RpcResult<List<DuibaAccountChangeRecordDO>> findAllDuibaGreaterId(Long lastId,int batchSize){
		try {
			List<DuibaAccountChangeRecordDO> list=payCenterService.findAllDuibaGreaterId(lastId, batchSize);
			
			return new RpcResult<>(list);
		} catch (Exception e) {
			return new RpcResult<>(e);
		}
	}

	public boolean successPayRecord(Long orderId){
		return payCenterService.successPayRecord(orderId);
	}

	public void setPayCenterService(PayCenterService payCenterService) {
		this.payCenterService = payCenterService;
	}


}
