package cn.com.duiba.paycenter.remoteservice.duibaaccount;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.duibaaccount.SupplierAccountDetailDto;
import cn.com.duiba.paycenter.dto.duibaaccount.SupplierAccountDetailPageDto;
import cn.com.duiba.paycenter.params.duibaaccount.SupplierAccountDetailQryParams;

import java.util.List;

/**
 * 供应商余额账户明细服务
 * author z<PERSON><PERSON><PERSON>
 * date 2018-11-27-13:52
 */
@AdvancedFeignClient
public interface RemoteSupplierAccountDetailService {

    /**
     * 分页查询
     * @param params
     * @return
     */
    List<SupplierAccountDetailPageDto> find4page(SupplierAccountDetailQryParams params);

    /**
     * 分页查询总数
     * @param params
     * @return
     */
    Integer count4page(SupplierAccountDetailQryParams params);

    /**
     * 根据主键和供应商Id查询
     * @param id
     * @return
     */
    SupplierAccountDetailDto findByIdAndSupplierId(Long id, Long supplierId);

    /**
     * 根据业务ID和业务类型查询当前供应商余额账户明细
     * @param relationId    业务ID(必填)
     * @param relationTypes 业务类型集合(必填)
     * @see cn.com.duiba.paycenter.enums.duibaaccount.SupplierAccountRelationTypeEnum
     * @param supplierId    供应商ID(必填)
     * @return
     */
    List<SupplierAccountDetailDto> findByRelationAndSupplierId(String relationId, List<Integer> relationTypes, Long supplierId);
}
