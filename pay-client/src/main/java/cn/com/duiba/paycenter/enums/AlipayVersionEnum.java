package cn.com.duiba.paycenter.enums;

/**
 * <AUTHOR>
 * @date 2018/12/02
 */
public enum AlipayVersionEnum {
    MAPI(1, "mapi"),
    OPENAPI(2, "openapi")
    ;
    private Integer code;
    private String desc;

    AlipayVersionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
