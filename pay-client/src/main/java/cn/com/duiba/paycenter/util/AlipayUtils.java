package cn.com.duiba.paycenter.util;

import java.net.URLEncoder;

/**
 * 支付宝相关工具类
 * <AUTHOR>
 * @date 2023/4/21 11:43 AM
 */
public class AlipayUtils {
    
    private AlipayUtils() {}

    /**
     * 拼接授权链接
     * @param appId 支付宝应用ID
     * @param redirectUrl 授权后重定向地址
     * @return 授权链接
     */
    public static String getAccessUrl(String appId, String redirectUrl) {
        return "https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id=" + appId + "&scope=auth_user&redirect_uri="+ URLEncoder.encode(redirectUrl) + "&state=init";
    }
}
