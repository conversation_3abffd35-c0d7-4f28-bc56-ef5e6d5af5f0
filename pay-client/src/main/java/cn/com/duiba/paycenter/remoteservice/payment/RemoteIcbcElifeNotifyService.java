package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4AppChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4WxChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4WxChargeQueryResponse;

import java.util.Map;

/**
 * 工行e生活支付，回调结果通知
 *
 * <AUTHOR>
 * @date 2022/06/10
 */
@AdvancedFeignClient
public interface RemoteIcbcElifeNotifyService {

    /**
     * 工行e生活支付，回调通知，app端
     *
     * @param notifyUrl
     * @param params
     * @return response
     * @throws BizException exception
     */
    IcbcElife4AppChargeNotifyResponse orderNotify4AppWithBizType(String notifyUrl, Map<String, String> params, Integer bizType) throws BizException;


    /**
     * 工行e生活支付，回调通知，app端
     *
     * @param notifyUrl
     * @param params
     * @return response
     * @throws BizException exception
     */
    IcbcElife4AppChargeNotifyResponse orderNotify4App(String notifyUrl, Map<String, String> params) throws BizException;

    /**
     * 工行e生活支付，回调通知，微信端
     *
     * @param notifyUrl
     * @param params
     * @return response
     * @throws BizException exception
     */
    IcbcElife4WxChargeNotifyResponse orderNotify4Wx(String notifyUrl, Map<String, String> params) throws BizException;

    /**
     * 工行e生活支付，查询订单状态，微信端
     *
     * @param bizOrderNo  业务方订单号
     * @param bizType     业务方业务类型
     * @return
     */
    IcbcElife4WxChargeQueryResponse queryOrderStatus4Wx(String bizOrderNo, Integer bizType);

}
