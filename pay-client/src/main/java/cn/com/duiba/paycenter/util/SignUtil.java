package cn.com.duiba.paycenter.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class SignUtil {
	private static final Logger logger = LoggerFactory.getLogger(SignUtil.class);
	private SignUtil(){}

	public static String sign(Map<String,String> params){
		List<String> keys=new ArrayList<>(params.keySet());
		Collections.sort(keys);
		StringBuilder string=new StringBuilder("");
		for(String s:keys){
			string.append(params.get(s));
		}
		return toHexValue(encryptMD5(string.toString().getBytes(Charset.forName("utf-8"))));
	}

	private static byte[] encryptMD5(byte[] data)  {
		try {
			MessageDigest md5 = MessageDigest.getInstance("MD5");
			md5.update(data);
			return md5.digest();
		} catch (NoSuchAlgorithmException e) {
			logger.error("", e);
			return new byte[0];
		}
	}
	
	private static String toHexValue(byte[] messageDigest) {
		if (messageDigest == null)
			return "";
		StringBuilder hexValue = new StringBuilder();
		for (byte aMessageDigest : messageDigest) {
			int val = 0xFF & aMessageDigest;
			if (val < 16) {
				hexValue.append("0");
			}
			hexValue.append(Integer.toHexString(val));
		}
		return hexValue.toString();
	}
}
