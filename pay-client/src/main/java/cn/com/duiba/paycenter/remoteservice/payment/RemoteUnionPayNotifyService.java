package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayRefundNotifyResponse;

import java.util.Map;

@AdvancedFeignClient
public interface RemoteUnionPayNotifyService {
    /**
     * 消费接口后台通知
     *
     * @param map
     * @return
     * @throws BizException
     */
    UnionPayChargeNotifyResponse orderNotify(Map<String, String> map) throws BizException;

    /**
     * 退货接口后台通知
     *
     * @param map
     * @return
     * @throws BizException
     */
    UnionPayRefundNotifyResponse refundNotify(Map<String, String> map) throws BizException;
}
