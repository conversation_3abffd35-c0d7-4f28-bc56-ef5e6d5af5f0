package cn.com.duiba.paycenter.remoteservice.duibaaccount;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.duibaaccount.AppAccountDetailPageDto;
import cn.com.duiba.paycenter.dto.duibaaccount.AppSettleAccountDetailDto;
import cn.com.duiba.paycenter.params.duibaaccount.AppAccountDetailQryParams;

import java.util.List;

/**
 * 开发者应用待结算账户明细remote
 * author zhanghuifeng
 * date 2018-11-27-14:24
 */
@AdvancedFeignClient
public interface RemoteAppSettleAccountDetailService {

    /**
     * 分页查询
     * @param params
     * @return
     */
    List<AppAccountDetailPageDto> find4Page(AppAccountDetailQryParams params);

    /**
     * 分页查询总数
     * @param params
     * @return
     */
    Long count4page(AppAccountDetailQryParams params);

    /**
     * 根据主键和appId查询
     * @param id
     * @return
     */
    AppSettleAccountDetailDto findByIdAndAppId(Long id, Long appId);

    /**
     * 根据业务单号获取
     * @param relationId
     * @param appId
     * @return
     */
    List<AppSettleAccountDetailDto> findByRelationAndAppId(String relationId, List<Integer> relationTypes, Long appId);

    List<AppAccountDetailPageDto> find4Export(AppAccountDetailQryParams params);
}
