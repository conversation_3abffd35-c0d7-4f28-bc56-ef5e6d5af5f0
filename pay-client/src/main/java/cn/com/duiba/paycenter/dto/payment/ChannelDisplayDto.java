package cn.com.duiba.paycenter.dto.payment;

import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/11/01
 */
public class ChannelDisplayDto implements Serializable {
    private static final long serialVersionUID = 3271427933592412528L;

    /**
     * id
     */
    private Long id;
    /**
     * appId
     */
    private Long appId;
    /**
     * 显示状态 0 不显示 1 显示
     */
    private Integer displayStatus;
    /**
     * 1 微信 2 支付宝
     */
    private Integer channelId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public Integer getDisplayStatus() {
        return displayStatus;
    }

    public void setDisplayStatus(Integer displayStatus) {
        this.displayStatus = displayStatus;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
