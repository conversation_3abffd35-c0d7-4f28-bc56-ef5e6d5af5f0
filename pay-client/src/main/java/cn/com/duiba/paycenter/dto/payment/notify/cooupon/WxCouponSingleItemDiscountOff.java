package cn.com.duiba.paycenter.dto.payment.notify.cooupon;


import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * Description:
 * <p>
 * date: 2022/10/11 6:29 下午
 *
 * <AUTHOR>
 */
public class WxCouponSingleItemDiscountOff implements Serializable {
    private static final long serialVersionUID = 6347947518351421546L;
    /**
     * 单品最高优惠价格
     */
    @JSONField(name = "single_price_max")
    private int singlePriceMax;

    public int getSinglePriceMax() {
        return singlePriceMax;
    }

    public WxCouponSingleItemDiscountOff setSinglePriceMax(int singlePriceMax) {
        this.singlePriceMax = singlePriceMax;
        return this;
    }
}
