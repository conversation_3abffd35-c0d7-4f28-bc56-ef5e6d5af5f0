package cn.com.duiba.paycenter.enums;

import org.apache.commons.lang.StringUtils;

import java.util.Arrays;

/**
 * 支付渠道枚举
 * <AUTHOR>
 * @date 2018/11/05
 */
public enum ChannelEnum {
    /**
     * 支付宝APP支付
     */
    ALIPAY("alipay", "支付宝APP支付"),
    /**
     * 支付宝手机网站支付
     */
    ALIPAY_WAP("alipay_wap", "支付宝手机网站支付"),
    /**
     * 支付宝扫码支付
     */
    ALIPAY_QR("alipay_qr", "支付宝扫码支付"),
    /**
     * 支付宝网站支付
     */
    ALIPAY_PC("alipay_pc", "支付宝电脑网站支付"),
    /**
     * 微信APP支付
     */
    WX("wx", "微信APP支付"),
    /**
     * 微信公众号支付
     */
    WX_PUB("wx_pub", "微信公众号支付"),
    /**
     * 微信扫码支付
     */
    WX_PUB_QR("wx_pub_qr", "微信扫码支付"),
    /**
     * 微信H5支付
     */
    WX_WAP("wx_wap", "微信H5支付"),
    /**
     * 微信小程序支付
     */
    WX_LITE("wx_lite", "微信小程序支付"),
    /**
     * 微信现金红包
     */
    WX_RED_PACKET("wx_red_packet", "微信现金红包"),

    /**
     * 微信零钱
     */
    WX_CHANGE("wx_change", "微信零钱"),

    /**
     * 连连个人支付
     */
    LIANLIAN_PERSONAL("lianlian_personal", "连连个人支付"),
    /**
     * 连连企业支付
     */
    LIANLIAN_ENTERPRISE("lianlian_enterprise", "连连企业支付"),

    BANK_OF_SUZHOU_WAP("bank_of_suzhou_wap", "苏州银行"),

    WJRCB_WX_PUB("wjrcb_wx_pub", "苏州农商户微信公众号支付"),

    BANK_OF_CCB_PAY("bank_of_ccb_wap", "建设银行"),

    BANK_OF_ABC_PAY("bank_of_abc_wap", "农业银行"),

    BANK_OF_CITIC_PAY("bank_of_citic_wap", "中信银行"),

    BANK_OF_BOC_PAY("bank_of_boc_wap", "中国银行"),

    BANK_OF_ICBC_PAY_H5("bank_of_icbc_h5", "工行e支付H5"),

    HELLO_PAY("hellopay", "哈啰支付"),
    SHOUXIN_PAY("shouxinpay", "首信支付"),

    MOCK_PAY("mockpay", "Mock支付"),


    /**
     * 招商银行一网通支付（OneNetPay）
     */
    ONE_NET_PAY_OF_CMB("one_net_pay_of_cmb", "招商银行一网通支付"),

    /**
     * 客集集微信小程序支付
     */
    WX_LITE_KEJIJI("wx_lite_kejiji", "客集集微信小程序支付 - 客集集"),

    UNION_PAY("unionpay", "银联云闪付"),

    WX_COUPON("wxcoupon", "微信优惠券"),

    BANK_OF_CEB_XYK_PAY("bank_of_ceb_xyk_pay", "中国光大银行信用卡中信支付"),

    DUIBA_LIVE_MP_PAY("dbl_mp_pay", "兑吧直播小程序支付"),

    DUIBA_LIVE_INSTALLMENT_PAY("dbl_installment_pay", "兑吧直播分期支付"),

    DUIBA_LIVE_BF_PAY("dbl_bf_pay", "宝付支付"),


    NINGBO_BANK_PAY("nbcb_pay", "宁波银行支付"),

    UNION_PAY_OF_SHENZHEN_YBS("unionpay_shenzhen", "深圳银联易办事"),

    //工商银行积分兑换
    BANK_OF_ICBC_PAY_CREDITS("icbcCredits", "工商银行积分兑换"),
    ICBC_ELIFE_PAY_APP("icbc_elife_pay_app", "工行e生活支付-app端"),

    ICBC_ELIFE_PAY_WX("icbc_elife_pay_wx", "工行e生活支付-微信端"),

    ICBC_AP_WX_LITE("icbc_ap_wx_lite", "工行聚合支付-微信小程序支付"),
    ICBC_AP_WX_PUB("icbc_ap_wx_pub", "工行聚合支付-微信公众号支付"),

    XIB_PAY("xib_pay","厦门国际银行支付"),

    CIB_WX_PAY("cib_wx_pay","兴业银行微信预支付下单"),

    UNION_PAY_UMS("union_pay_ums","深圳银联天满服务平台支付"),

    CMB_LIFE_PAY("cmb_life_pay","招行-掌上生活-H5支付"),

    BANK_OF_ICBC_ELIFE_PAY("bank_of_icbc_elife", "工行e生活支付(江苏工行)"),
    WEIBO_PAY("weibo_pay", "微博支付"),
    LSHM_PAY("lshm_pay", "零食很忙支付"),
    ;

    private String channelType;
    private String channelName;

    ChannelEnum(String channelType, String channelName) {
        this.channelType = channelType;
        this.channelName = channelName;
    }

    public String getChannelType() {
        return channelType;
    }

    public String getChannelName() {
        return channelName;
    }

    public static String getChannelName(String channelType) {
        if (StringUtils.isBlank(channelType)) {
            return null;
        }
        if (isActive(channelType)) {
            return ChannelEnum.valueOf(channelType).channelName;
        }
        return null;
    }

    /**
     * 返回channelId是否有效
     * @param channelType 渠道
     * @return 是否有效
     */
    public static boolean isActive(String channelType) {
        if (StringUtils.isBlank(channelType)) {
            return false;
        }
        return Arrays.stream(ChannelEnum.values())
                .anyMatch(channelEnum -> channelEnum.channelType.equals(channelType));
    }
}
