package cn.com.duiba.paycenter.client;

import cn.com.duiba.paycenter.params.AppWithdrawParams;
import cn.com.duiba.paycenter.remoteservice.RemoteAppWithdrawService;
import cn.com.duiba.paycenter.result.WithdrawResult;
import cn.com.duiba.paycenter.util.SignUtil;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 开发者应用账户提现
 * author z<PERSON><PERSON><PERSON>
 * date 2018/11/8-10:49 AM
 */
public class AppWithdrawServiceClient {

    @Resource
    private RemoteAppWithdrawService remoteAppWithdrawService;

    /**
     * 提现处理
     *
     */
    public RpcResult<WithdrawResult> withdrawCashApply(AppWithdrawParams requestParams) {
        try {
            return remoteAppWithdrawService.withdrawCashApply(requestParams, getSign(requestParams));
        } catch (Exception e) {
            return new RpcResult<>(e);
        }
    }

    /**
     * 提现回退
     *
     */
    public RpcResult<WithdrawResult> withdrawCashPaybackApply(AppWithdrawParams requestParams) {
        try {
            return remoteAppWithdrawService.withdrawCashApplyPaybackApply(requestParams, getSign(requestParams));

        } catch (Exception e) {
            return new RpcResult<>(e);
        }
    }

    private String getSign(AppWithdrawParams requestParams){
        Map<String, String> params = new HashMap<>();
        params.put("developerId", Objects.toString(requestParams.getDeveloperId()));
        params.put("withdrawId", requestParams.getBizId());
        params.put("money", Objects.toString(requestParams.getMoney()));
        params.put("appId",Objects.toString(requestParams.getAppId()));
        return SignUtil.sign(params);
    }

}
