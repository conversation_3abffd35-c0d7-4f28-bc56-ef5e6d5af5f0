package cn.com.duiba.paycenter.client;

import cn.com.duiba.paycenter.remoteservice.RemoteAppPayOrderService;
import cn.com.duiba.paycenter.result.PayCenterResult;
import cn.com.duiba.paycenter.util.SignUtil;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 处理订单资金流client
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018/11/13-8:03 PM
 */
public class AppPayOrderServiceClient {

    private static final String DEVELOPER_ID = "developerId";
    private static final String RELATION_ID = "relationId";
    private static final String MONEY = "money";
    private static final String APP_ID = "appId";

    @Resource
    private RemoteAppPayOrderService remoteAppPayOrderService;

    /**
     * 订单结算
     * @param orderId
     * @param duibaMoney
     * @param devMoney
     * @param memo
     * @return
     */
    public RpcResult<PayCenterResult> orderSettle(Long orderId, Long duibaMoney, Long devMoney, String memo) {
        try {
            Map<String, String> params=new HashMap<>();
            params.put("orderId", Objects.toString(orderId));
            params.put("devMoney", Objects.toString(devMoney));
            params.put("duibaMoney", Objects.toString(duibaMoney));
            String sign= SignUtil.sign(params);

            PayCenterResult ret= remoteAppPayOrderService.orderSettle(orderId, duibaMoney, devMoney,memo, sign);

            return new RpcResult<>(ret);
        } catch (Exception e) {
            return new RpcResult<>(e);
        }
    }

    /**
     * 采购退款
     * @param developerId
     * @param appId
     * @param money
     * @param relationId
     * @param memo
     * @return
     */
    public RpcResult<PayCenterResult> purchasePayback(Long developerId, Long appId, Long money, String relationId, String memo) {
        try {
            Map<String, String> params=new HashMap<>();
            params.put(DEVELOPER_ID, Objects.toString(developerId));
            params.put(APP_ID,Objects.toString(appId));
            params.put(RELATION_ID, relationId);
            params.put(MONEY, Objects.toString(money));
            String sign= SignUtil.sign(params);

            PayCenterResult ret= remoteAppPayOrderService.purchasePayback(developerId,appId,money,relationId,memo,sign);

            return new RpcResult<>(ret);
        } catch (Exception e) {
            return new RpcResult<>(e);
        }
    }


}
