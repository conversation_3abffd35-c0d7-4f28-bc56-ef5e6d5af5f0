package cn.com.duiba.paycenter.remoteservice;

import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.SaasAgentAccountDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/5/22 16:50
 */
@AdvancedFeignClient
public interface RemoteSaasAgentAccountService {

    /**
     * 查询代理商账户信息
     *
     * @param agentId 代理商ID
     * @return 代理商账户信息
     */
    SaasAgentAccountDto findByAgentId(Long agentId);

    /**
     * 批量查询代理商账户信息
     *
     * @param agentIds 代理商ID
     * @return 代理商账户信息
     */
    List<SaasAgentAccountDto> findByAgentIds(List<Long> agentIds);

    /**
     * 查询代理商账户信息(加更新锁)
     *
     * @param agentId 代理商ID
     * @return 代理商账户信息
     */
    SaasAgentAccountDto findByAgentId4update(Long agentId);

    /**
     * 新建代理商账户
     *
     * @param agentId 代理商ID
     * @param adminId 操作人
     * @return 代理商账户信息
     */
    SaasAgentAccountDto insert(Long agentId, Long adminId);
}
