package cn.com.duiba.paycenter.dto;

import cn.com.duiba.paycenter.enums.PayAccountTypeEnum;
import cn.com.duiba.paycenter.enums.PayOrderBizTypeEnum;
import cn.com.duiba.paycenter.enums.PayOrderStatusEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by xiaoxuda on 2017/10/31.
 */
public class PayOrderDto implements Serializable {
    private static final long serialVersionUID = -6513586971762924282L;

    /**
     * 自增主键
     **/
    private Long id;
    /**
     * 业务标识
     **/
    private PayOrderBizTypeEnum bizType;
    /**
     * 业务方的业务唯一编号
     **/
    private String bizNo;
    /**
     * 支付金额，单位分
     **/
    private Long amount;
    /**
     * 付款账号类型
     **/
    private PayAccountTypeEnum payerType;
    /**
     * 付款账号编码（支付宝开放平台应用ID/银行卡号）
     **/
    private String payerNo;
    /**
     * 付款账户姓名
     **/
    private String payerName;
    /**
     * 收款账号类型
     **/
    private PayAccountTypeEnum payeeType;
    /**
     * 收款账号
     **/
    private String payeeNo;
    /**
     * 收款账户姓名
     **/
    private String payeeName;
    /**
     * 支付状态
     **/
    private PayOrderStatusEnum payStatus;
    /**
     * 已重试次数
     **/
    private Integer retryCount;
    /**
     * 支付执行人业务唯一标识（支付宝/银行支付流水号）
     **/
    private String executorBizId;
    /**
     * 业务方的备注信息，会传递给支付宝
     */
    private String bizRemark;
    /**
     * 支付订单信息
     */
    private String remark;
    /**
     * 创建时间
     **/
    private Date gmtCreate;
    /**
     * 修改时间
     **/
    private Date gmtModified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public PayOrderBizTypeEnum getBizType() {
        return bizType;
    }

    public void setBizType(PayOrderBizTypeEnum bizType) {
        this.bizType = bizType;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public PayAccountTypeEnum getPayerType() {
        return payerType;
    }

    public void setPayerType(PayAccountTypeEnum payerType) {
        this.payerType = payerType;
    }

    public String getPayerNo() {
        return payerNo;
    }

    public void setPayerNo(String payerNo) {
        this.payerNo = payerNo;
    }

    public String getPayerName() {
        return payerName;
    }

    public void setPayerName(String payerName) {
        this.payerName = payerName;
    }

    public PayAccountTypeEnum getPayeeType() {
        return payeeType;
    }

    public void setPayeeType(PayAccountTypeEnum payeeType) {
        this.payeeType = payeeType;
    }

    public String getPayeeNo() {
        return payeeNo;
    }

    public void setPayeeNo(String payeeNo) {
        this.payeeNo = payeeNo;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public PayOrderStatusEnum getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(PayOrderStatusEnum payStatus) {
        this.payStatus = payStatus;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public String getExecutorBizId() {
        return executorBizId;
    }

    public void setExecutorBizId(String executorBizId) {
        this.executorBizId = executorBizId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getBizRemark() {
        return bizRemark;
    }

    public void setBizRemark(String bizRemark) {
        this.bizRemark = bizRemark;
    }
}
