package cn.com.duiba.paycenter.dto.payment.config;

import cn.com.duiba.paycenter.validator.ChannelEnumCheck;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.security.PrivateKey;

/**
 * <AUTHOR>
 * @date 2018/12/01
 */
public class WxPayConfigDto implements Serializable {
    private static final long serialVersionUID = -1720409075432360927L;
    @NotNull(message = "id不能为空")
    private Long id;
    /**
     * 兑吧appid
     */
    private Long appId;
    /**
     * wechat app id
     */
    @Size(max = 32, message = "wxAppId不能超过32位")
    private String wxAppId;
    /**
     * 商户id
     */
    @Size(max = 32, message = "mchId不能超过32位")
    private String mchId;
    /**
     * api 证书
     */
    private String apiCert;

    /**
     * api证书序列号
     */
    private String serialNo;
    /**
     * api 密钥
     */
    @Size(max = 64, message = "apiKey不能超过64位")
    private String apiKey;
    /**
     * AppSecret是APPID对应的接口密码
     */
    @Size(max = 64, message = "appSecret不能超过64")
    private String appSecret;
    /**
     * 渠道类型
     *
     * @see cn.com.duiba.paycenter.enums.ChannelEnum
     */
    @ChannelEnumCheck
    private String channelType;
    /**
     * 渠道名称
     */
    @Size(max = 64, message = "渠道名称不能超过64")
    private String channelName;
    /**
     * 渠道状态
     *
     * @see cn.com.duiba.paycenter.enums.ChannelStatusEnum
     */
    private Integer channelStatus;
    /**
     * 渠道模式
     *
     * @see cn.com.duiba.paycenter.enums.ChannelModeEnum
     */
    private Integer channelMode;
    /**
     * 渠道描述
     */
    @Size(max = 255, message = "描述不能超过255")
    private String description;
    /**
     * 渠道费率
     * 100 表示 1% 精确到万一
     */
    private Integer rate;

    /**
     * 签名所需的privateKey
     */
    private PrivateKey privateKey;

    /**
     * 请求微信优惠券的url
     */
    private String couponUrl;

    /**
     * 微信apiV3密钥
     */
    private String apiV3Key;

    public String getApiV3Key() {
        return apiV3Key;
    }

    public void setApiV3Key(String apiV3Key) {
        this.apiV3Key = apiV3Key;
    }

    public String getCouponUrl() {
        return couponUrl;
    }

    public void setCouponUrl(String couponUrl) {
        this.couponUrl = couponUrl;
    }

    public PrivateKey getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(PrivateKey privateKey) {
        this.privateKey = privateKey;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getWxAppId() {
        return wxAppId;
    }

    public void setWxAppId(String wxAppId) {
        this.wxAppId = wxAppId;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getApiCert() {
        return apiCert;
    }

    public void setApiCert(String apiCert) {
        this.apiCert = apiCert;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public Integer getChannelStatus() {
        return channelStatus;
    }

    public void setChannelStatus(Integer channelStatus) {
        this.channelStatus = channelStatus;
    }

    public Integer getChannelMode() {
        return channelMode;
    }

    public void setChannelMode(Integer channelMode) {
        this.channelMode = channelMode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getRate() {
        return rate;
    }

    public void setRate(Integer rate) {
        this.rate = rate;
    }
}
