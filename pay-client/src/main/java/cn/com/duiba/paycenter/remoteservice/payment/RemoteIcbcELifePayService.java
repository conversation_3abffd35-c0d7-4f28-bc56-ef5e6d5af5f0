package cn.com.duiba.paycenter.remoteservice.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.elife.IcbcELifePayQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.elife.IcbcELifePayQueryResponse;
import cn.com.duiba.paycenter.dto.payment.charge.elife.IcbcELifeRefundQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.elife.IcbcELifeRefundQueryResponse;

/**
 * 江苏工行-e生活-remote
 *
 * <AUTHOR>
 * @date 2024/5/11 5:15 下午
 */
@AdvancedFeignClient
public interface RemoteIcbcELifePayService {

    /**
     * 支付结果查询
     *
     * @param request 请求参数
     * @return 请求结果
     * @throws BizException 异常
     */
    IcbcELifePayQueryResponse payQuery(IcbcELifePayQueryRequest request) throws BizException;

    /**
     * 退款结果查询
     *
     * @param request 请求参数
     * @return 请求结果
     * @throws BizException 异常
     */
    IcbcELifeRefundQueryResponse refundQuery(IcbcELifeRefundQueryRequest request) throws BizException;

}
