package cn.com.duiba.paycenter.params;

import cn.com.duiba.paycenter.enums.AlipayMerchantTypeEnum;
import cn.com.duiba.paycenter.enums.PayAccountTypeEnum;
import cn.com.duiba.paycenter.enums.PayOrderBizTypeEnum;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 资金转账请求
 * Created by xiaoxuda on 2017/11/1.
 */
public class FundTransferRequestParams implements Serializable {
    private static final long serialVersionUID = -8286934799512512845L;

    /**
     * 业务标识
     **/
    @NotNull(message = "业务标识不能为null")
    private PayOrderBizTypeEnum bizType;
    /**
     * 业务方的业务唯一编号
     **/
    @NotBlank(message = "业务编号不能为空")
    private String bizNo;
    /**
     * 支付金额，单位分
     **/
    @NotNull(message = "金额不能为null")
    private Long amount;
    /**
     * 收款账号类型
     **/
    @NotNull(message = "收款账号类型不能为null")
    private PayAccountTypeEnum payeeType;
    /**
     * 收款账号
     **/
    @NotBlank(message = "收款账号不能为空")
    private String payeeNo;
    /**
     * 收款账户姓名
     **/
    @NotBlank(message = "收款账户姓名不能为空")
    private String payeeName;
    /**
     * 转账备注
     */
    private String remark;
    /**
     * 业务主体 - 必传参数
     * @see cn.com.duiba.api.enums.SubjectTypeEnum
     */
    private String subjectType;

    /**
     * 提现成功回调消息主题- 非必传
     */
    private String callbackTopic;
    /**
     * 提现成功回调消息标签- 非必传
     */
    private String callbackTag;

    /**
     * 付款方商户类型
     */
    private AlipayMerchantTypeEnum merchantType;

    /**
     * 付款方自定义商户号(支付宝id)，付款方商户类型是自定义时参数不能为空
     */
    private String merchantNo;

    public AlipayMerchantTypeEnum getMerchantType() {
        return merchantType;
    }

    public FundTransferRequestParams setMerchantType(AlipayMerchantTypeEnum merchantType) {
        this.merchantType = merchantType;
        return this;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public FundTransferRequestParams setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
        return this;
    }

    public PayOrderBizTypeEnum getBizType() {
        return bizType;
    }

    public void setBizType(PayOrderBizTypeEnum bizType) {
        this.bizType = bizType;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public PayAccountTypeEnum getPayeeType() {
        return payeeType;
    }

    public void setPayeeType(PayAccountTypeEnum payeeType) {
        this.payeeType = payeeType;
    }

    public String getPayeeNo() {
        return payeeNo;
    }

    public void setPayeeNo(String payeeNo) {
        this.payeeNo = payeeNo;
    }

    public String getPayeeName() {
        return payeeName;
    }

    public void setPayeeName(String payeeName) {
        this.payeeName = payeeName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSubjectType() {
        return subjectType;
    }

    public void setSubjectType(String subjectType) {
        this.subjectType = subjectType;
    }

    public String getCallbackTopic() {
        return callbackTopic;
    }

    public void setCallbackTopic(String callbackTopic) {
        this.callbackTopic = callbackTopic;
    }

    public String getCallbackTag() {
        return callbackTag;
    }

    public void setCallbackTag(String callbackTag) {
        this.callbackTag = callbackTag;
    }
}
