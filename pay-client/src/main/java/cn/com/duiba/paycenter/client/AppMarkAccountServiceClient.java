package cn.com.duiba.paycenter.client;

import cn.com.duiba.paycenter.params.AppAccountChangeParams;
import cn.com.duiba.paycenter.remoteservice.RemoteAppMarkAccountService;
import cn.com.duiba.paycenter.result.PayCenterResult;
import cn.com.duiba.paycenter.util.SignUtil;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 开发者应用标记账户
 * author zhang<PERSON><PERSON>
 * date 2018/11/20-11:00 AM
 */
public class AppMarkAccountServiceClient {

    @Resource
    private RemoteAppMarkAccountService remoteAppMarkAccountService;

    /**
     * 账户扣款接口
     * @param param
     * @return
     */
    public RpcResult<PayCenterResult> reduceMoney(AppAccountChangeParams param){
        try {
            PayCenterResult ret = remoteAppMarkAccountService.reduceMoney(param,getSign(param));

            return new RpcResult<>(ret);
        } catch (Exception e) {
            return new RpcResult<>(e);
        }
    }

    /**
     * 账户发生回款接口
     * @param param
     * @return
     */
    public RpcResult<PayCenterResult> addMoney(AppAccountChangeParams param){
        try {
            PayCenterResult ret = remoteAppMarkAccountService.addMoney(param,getSign(param));
            return new RpcResult<>(ret);
        } catch (Exception e) {
            return new RpcResult<>(e);
        }
    }

    private String getSign(AppAccountChangeParams param){
        Map<String, String> params=new HashMap<>();
        params.put("developerId", Objects.toString(param.getDeveloperId()));
        params.put("relationId", param.getRelationId());
        params.put("money", Objects.toString(param.getMoney()));
        params.put("appId",Objects.toString(param.getAppId()));
        return SignUtil.sign(params);
    }
}
