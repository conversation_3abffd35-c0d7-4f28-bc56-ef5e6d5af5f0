<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

    <bean id="paymentSqlSessionTemplate" class="org.mybatis.spring.SqlSessionTemplate">
        <constructor-arg>
            <bean class="org.mybatis.spring.SqlSessionFactoryBean">
                <property name="dataSource" ref="paymentDataSource"></property>
                <property name="configLocation" value="classpath:mybatis/sqlMapConfig.xml"></property>
                <property name="mapperLocations" value="classpath:mybatis/payment/*.xml"></property>
            </bean>
        </constructor-arg>
    </bean>

    <!-- 配置Spring的事务管理器 -->
    <bean id="paymentTransactionManager" class="cn.com.duiba.wolf.spring.datasource.AutoRoutingDataSourceTransactionManager">
        <property name="dataSource" ref="paymentDataSource" />
        <qualifier value="payment"/>
    </bean>
</beans>