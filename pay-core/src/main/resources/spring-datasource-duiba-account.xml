<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd"
	   default-autowire="byName">

	<bean id="duibaAccountSqlSessionTemplate" class="org.mybatis.spring.SqlSessionTemplate">
    	<constructor-arg>
  			<bean class="org.mybatis.spring.SqlSessionFactoryBean">
		  		<property name="dataSource" ref="duibaAccountDataSource"></property>
		        <property name="configLocation" value="classpath:mybatis/sqlMapConfig.xml"></property>
		        <property name="mapperLocations" value="classpath:mybatis/duibaaccount/*.xml"></property>
  			</bean>
  		</constructor-arg>
    </bean>

    <!-- 配置Spring的事务管理器 -->
	<bean id="duibaAccountTransactionManager" class="cn.com.duiba.wolf.spring.datasource.AutoRoutingDataSourceTransactionManager">
		<property name="dataSource" ref="duibaAccountDataSource" />
		<qualifier value="duibaAccount"/>
	</bean>
	
</beans>
