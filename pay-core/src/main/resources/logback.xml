<?xml version="1.0" encoding="UTF-8"?>
<configuration>

	<property name="logging.path" value="${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}/}"/>

	<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%d{HH:mm:ss.SSS} %-5level [%thread] %logger{32}[%file:%line] -&gt; %msg%n</pattern>
			<charset class="java.nio.charset.Charset">UTF-8</charset>
		</encoder>
	</appender>

	<!-- 文件输出日志 (文件大小策略进行文件输出，超过指定大小对文件备份)-->
	<appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${logging.path}/application.log</file>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%d{HH:mm:ss.SSS} %-5level [%thread] %logger{32}[%file:%line] -&gt; %msg%n</pattern>
			<charset class="java.nio.charset.Charset">UTF-8</charset>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>${logging.path}/application.log.%d{yyyy-MM-dd}</FileNamePattern>
			<maxHistory>7</maxHistory>
		</rollingPolicy>
	</appender>

	<appender name="httpRequestLogAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${logging.path}/http_request.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${logging.path}/http_request_%d{yyyy-MM-dd}.log</fileNamePattern>
		</rollingPolicy>
		<encoder>
			<pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] -&gt; %msg%n</pattern>
		</encoder>
	</appender>


	<!-- 统一内部日志 -->
	<appender name="INNER-LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>${logging.path}/inner/inner.log</File>
		<layout class="ch.qos.logback.classic.PatternLayout">
			<pattern>%msg%n</pattern>
		</layout>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>${logging.path}/inner/inner.log.%d{yyyy-MM-dd}</FileNamePattern>
			<maxHistory>7</maxHistory>
		</rollingPolicy>
	</appender>
	<!-- 统一内部日志 -->
	<logger name="innerLog"  level="INFO" additivity="false">
		<appender-ref ref="INNER-LOG" />
	</logger>

	<!-- 异步输出 -->
	<appender name="HTTP_REQUEST_ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志. 默认地,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>512</queueSize>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref ="httpRequestLogAppender"/>
	</appender>

	<logger name="cn.com.duiba.tool.HttpRequestLog" level="INFO" additivity="false">
		<appender-ref ref="HTTP_REQUEST_ASYNC_FILE" />
	</logger>

	<!-- 需要记录日志的包 -->
	<logger name="org.springframework" level="WARN"/>
	<logger name="org.quartz" level="WARN"/>
	<logger name="com.mchange" level="WARN"/>
	<logger name="org.apache" level="WARN"/>
	<logger name="net.sf" level="WARN"/>
	<logger name="net.spy" level="WARN"/>
	<logger name="org.mybatis" level="ERROR" />

	<logger name="cn.com.duiba" level="INFO"/>

	<root>
		<level value="WARN" />
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="FILE" />
	</root>

</configuration>
