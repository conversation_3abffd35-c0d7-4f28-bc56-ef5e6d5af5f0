#tomcat
server.port=8787
server.tomcat.access-log-enabled=false
server.tomcat.uri-encoding=UTF-8

#spring
spring.main.show-banner=false
spring.jmx.enabled=false
spring.http.encoding.enabled=true
spring.http.encoding.charset=UTF-8
spring.http.encoding.force=true
spring.velocity.checkTemplateLocation=false

duiba.server.internal-mode=true

#å¿ééç½®
##å¯ç¨çº¿ç¨æ± (ExecutorService)
duiba.threadpool.enabled=true
#å¯ééç½®
##çº¿ç¨æ± æ ¸å¿å¤§å°.é»è®¤2ï¼å»ºè®®æåºç¨ä½¿ç¨æåµæ¹ä¸ºåéçå¼
duiba.threadpool.core-size=20
##çº¿ç¨æ± æå¤§çº¿ç¨æ°ï¼é»è®¤20ï¼å»ºè®®æåºç¨ä½¿ç¨æåµæ¹ä¸ºåéçå¼(å®éä¸maxSizeè·åçç®æ³ä¸ºmaxSize=Math.max(maxSize,coreSize))
duiba.threadpool.max-size=50
##Queueçæå¤§å®¹éï¼é»è®¤500ï¼å¦æä¸º0åä½¿ç¨SynchronousQueueï¼å¦åä½¿ç¨ArrayBlockingQueue
duiba.threadpool.queue-size=2000
##ä¼éå³é­è¶æ¶æ¶é´ï¼åä½msï¼é»è®¤3000msï¼è¶æ¶åå¼ºå¶å³é­çº¿ç¨æ± 
duiba.threadpool.shutdown-timeout=6000

duiba.profile.threshold=2000

#å®¹å¨çº¿ç¨æ± 
server.tomcat.max-threads=700
duiba.feign.serialization=hessian2


## åéå¾®ä¿¡é¶é±
##çº¿ç¨æ± æ ¸å¿å¤§å°.8ï¼å»ºè®®æåºç¨ä½¿ç¨æåµæ¹ä¸ºåéçå¼
duiba.threadpool.extra.wxTransferExecutorService.core-size=8
##çº¿ç¨æ± æå¤§çº¿ç¨æ° 10ï¼å»ºè®®æåºç¨ä½¿ç¨æåµæ¹ä¸ºåéçå¼(å®éä¸maxSizeè·åçç®æ³ä¸ºmaxSize=Math.max(maxSize,coreSize))
duiba.threadpool.extra.wxTransferExecutorService.max-size=10
##Queueçæå¤§å®¹éï¼5000ï¼å¦æä¸º0åä½¿ç¨SynchronousQueueï¼å¦åä½¿ç¨ArrayBlockingQueue
duiba.threadpool.extra.wxTransferExecutorService.queue-size=5000

## åéå¾®ä¿¡çº¢å
##çº¿ç¨æ± æ ¸å¿å¤§å°.é»è®¤8ï¼å»ºè®®æåºç¨ä½¿ç¨æåµæ¹ä¸ºåéçå¼
duiba.threadpool.extra.asynSendWxRedPacketExecutorService.core-size=8
##çº¿ç¨æ± æå¤§çº¿ç¨æ°ï¼10ï¼å»ºè®®æåºç¨ä½¿ç¨æåµæ¹ä¸ºåéçå¼(å®éä¸maxSizeè·åçç®æ³ä¸ºmaxSize=Math.max(maxSize,coreSize))
duiba.threadpool.extra.asynSendWxRedPacketExecutorService.max-size=10
##Queueçæå¤§å®¹éï¼5000ï¼å¦æä¸º0åä½¿ç¨SynchronousQueueï¼å¦åä½¿ç¨ArrayBlockingQueue
duiba.threadpool.extra.asynSendWxRedPacketExecutorService.queue-size=5000