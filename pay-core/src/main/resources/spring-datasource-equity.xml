<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd"
	   default-autowire="byName">

	<bean id="equitySqlSessionTemplate" class="org.mybatis.spring.SqlSessionTemplate">
		<constructor-arg name="sqlSessionFactory" ref="equitySqlSessionFactory" />
	</bean>

	<!-- 配置sqlSessionFactory -->
	<bean id="equitySqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
		<property name="dataSource" ref="equityDataSource"></property>
		<property name="mapperLocations" value="classpath:mybatis/equity/*.xml"></property>
		<property name="configLocation" value="classpath:mybatis/sqlMapConfig.xml"/>
		<property name="failFast" value="true"/>
	</bean>

	<!-- 配置扫描器 -->
	<bean id=" equityMapperScannerConfigurer" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
		<property name="basePackage" value="cn.com.duiba.paycenter.dao.equity"></property>
		<property name="sqlSessionFactoryBeanName" value="equitySqlSessionFactory"></property>
	</bean>
	
	<!-- 配置Spring的事务管理器 -->
	<bean id="equityTransactionManager" class="cn.com.duiba.wolf.spring.datasource.AutoRoutingDataSourceTransactionManager">
		<property name="dataSource" ref="equityDataSource" />
		<qualifier value="equity"/>
	</bean>
	
	

	
	
</beans>
