<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.TpcDlpIncomeChangeDAOImpl">

	<select id="find" resultType="cn.com.duiba.paycenter.model.TpcDlpIncomeChangeDO">
		select 
		
		<include refid="fields"></include>
		
		from tpc_dlp_income_change where id=#{id}
	</select>
	
	<select id="findCountExist" resultType="int">
		select count(*)
		
		from tpc_dlp_income_change where relation_type=#{relationType} and relation_id=#{relationId} and action_type=#{actionType}
	</select>
	
	<select id="findExistRecord" resultType="cn.com.duiba.paycenter.model.TpcDlpIncomeChangeDO">
		select
		
		<include refid="fields"></include>
		
		from tpc_dlp_income_change where relation_type=#{relationType} and relation_id=#{relationId} and action_type=#{actionType}
	</select>
	
	<select id="findAllByRelation" resultType="cn.com.duiba.paycenter.model.TpcDlpIncomeChangeDO">
		select
		
		<include refid="fields"></include>
		
		from tpc_dlp_income_change where relation_type=#{relationType} and relation_id=#{relationId} 
	</select>
	
	<select id="findAllGreaterId" resultType="cn.com.duiba.paycenter.model.AccountChangeRecordDO">
		select 
		
		<include refid="fields"></include>
		
		from tpc_dlp_income_change where
		id>#{lastId} and gmt_create <![CDATA[ < adddate(sysdate(),interval -1 minute)  ]]>  order by id asc limit #{limit}
	</select>

	
	
	<insert id="insert" parameterType="cn.com.duiba.paycenter.model.TpcDlpIncomeChangeDO" useGeneratedKeys="true" keyProperty="id">
		insert into tpc_dlp_income_change(developer_id,relation_type,relation_id,action_type,change_money,change_kind,before_balance,
		after_balance,gmt_create,gmt_modified)
		values(#{developerId},#{relationType},#{relationId},#{actionType},#{changeMoney},#{changeKind},#{beforeBalance},#{afterBalance},
		#{gmtCreate},#{gmtModified})
		
	</insert>

	<sql id="fields">
		id,
		developer_id as developerId,
		relation_type as relationType,
		relation_id as relationId,
		action_type as actionType,
		change_money as changeMoney,
		change_kind as changeKind,
		before_balance as beforeBalance,
		after_balance as afterBalance,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified

	</sql>

</mapper>