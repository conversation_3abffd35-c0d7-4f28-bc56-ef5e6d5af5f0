<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.AccountDetailDaoImpl">

	<!-- ======================= common queries =============================== -->
	<sql id="fields">
		id as id,
		developer_id as developerId,
		credits as credits,
		app_id as appId,
		type as type,
		money_change as moneyChange,
		description as description,
		memo as memo,
		operator_id as operatorId,
		balance as balance,
		order_id as orderId,
		recharge_type as rechargeType,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
	</sql>
	
	<select id="find" resultType="AccountDetailEntity" parameterType="Long">
		select
		<include refid="fields" />
		from account_detail
		where id = #{id}
	</select>
	
	<insert id="insert" parameterType="AccountDetailEntity" useGeneratedKeys="true" keyProperty="id">
		insert into account_detail
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="developerId != null">developer_id,</if>
			<if test="credits != null">credits,</if>
			<if test="appId != null">app_id,</if>
			<if test="type != null">type,</if>
			<if test="moneyChange != null">money_change,</if>
			<if test="description != null">description,</if>
			<if test="memo != null">memo,</if>
			<if test="operatorId != null">operator_id,</if>
			<if test="balance != null">balance,</if>
			<if test="orderId != null">order_id,</if>
			<if test="rechargeType != null">recharge_type,</if>
			<if test="gmtCreate != null">gmt_create,</if>
			<if test="gmtModified != null">gmt_modified,</if>
			
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="developerId != null">#{developerId},</if>
			<if test="credits != null">#{credits},</if>
			<if test="appId != null">#{appId},</if>
			<if test="type != null">#{type},</if>
			<if test="moneyChange != null">#{moneyChange},</if>
			<if test="description != null">#{description},</if>
			<if test="memo != null">#{memo},</if>
			<if test="operatorId != null">#{operatorId},</if>
			<if test="balance != null">#{balance},</if>
			<if test="orderId != null">#{orderId},</if>
			<if test="rechargeType != null">#{rechargeType},</if>
			<if test="gmtCreate != null">#{gmtCreate},</if>
			<if test="gmtModified != null">#{gmtModified},</if>
			
		</trim>
	</insert>
	
	<select id="findPageByDeveloperId" resultType="AccountDetailEntity" parameterType="ADeveloperFlowQuery">
		select
		<include refid="fields"/>
		from account_detail
		<if test="developerId != null">
			where developer_id = #{developerId}
		</if>
		order by gmt_create desc
		limit #{offset}, #{max}
	</select>
	
	<select id="findPageByDeveloperIdCount" resultType="Long" parameterType="ADeveloperFlowQuery">
		select
		count(id)
		from account_detail
		<if test="developerId != null">
			where developer_id = #{developerId}
		</if>
	</select>
	
	<select id="findAllByPageInfo" resultType="AccountDetailEntity" parameterType="AccountPageInfo">
		select
		<include refid="fields"/>
		from account_detail
		<where>			
			recharge_type in ('ebatong','manual')
			<if test="pageInfo.rechargeType!=null and pageInfo.rechargeType != ''">
				and recharge_type = #{pageInfo.rechargeType}
			</if>
			<if test="pageInfo.devEmail!=null and pageInfo.devEmail!=''">
				and developer_id = #{pageInfo.developerId}
			</if>
			<if test="pageInfo.timeType !=null and pageInfo.timeType != ''">
				and gmt_create between #{pageInfo.start} and #{pageInfo.now}
			</if>
		</where>
		order by gmt_create desc
		limit #{pageInfo.offset},#{pageInfo.max}
	</select>
	
	<select id="getCountByPageInfo" resultType="Long" parameterType="AccountPageInfo">
		select
		count(*)
		from account_detail
		<where>			
			recharge_type in ('ebatong','manual')
			<if test="pageInfo.rechargeType!=null and pageInfo.rechargeType != ''">
				and recharge_type = #{pageInfo.rechargeType}
			</if>
			<if test="pageInfo.devEmail!=null and pageInfo.devEmail!=''">
				and developer_id = #{pageInfo.developerId}
			</if>
			<if test="pageInfo.timeType !=null and pageInfo.timeType != ''">
				and gmt_create between #{pageInfo.start} and #{pageInfo.now}
			</if>
		</where>
	</select>
	 <!-- 开发者资金明细专用 -->
	<select id="findAccountDetailByPageInfo" resultType="AccountDetailEntity">
		select
		<include refid="fields"/>
		from account_detail
		<include refid="pageWhereSql" />
		order by gmt_create desc
		limit #{pageInfo.offset},#{pageInfo.max}	
	</select>

	<sql id="pageWhereSql">
		<where>
			developer_id = #{pageInfo.developerId}
			and gmt_create between #{pageInfo.start} and #{pageInfo.now}
			<if test="pageInfo.appId !=null and pageInfo.appId !=''">
				and app_id = #{pageInfo.appId}
			</if>
			<if test="pageInfo.orderType !=null">
				and type = #{pageInfo.orderType}
				<if test="pageInfo.orderType =='income' and pageInfo.rechargeTypeList==null and pageInfo.rechargeType==null">
					and recharge_type in('manual','ebatong','alipay')
				</if>
			</if>
			<if test="pageInfo.rechargeTypeList!=null">
				and recharge_type in
				<foreach collection="pageInfo.rechargeTypeList" item="rechargeType" index="index" open="(" close=")" separator="," >
					#{rechargeType}
				</foreach>
			</if>
			<if test="pageInfo.rechargeType !=null and pageInfo.rechargeType =='orders'">
				and recharge_type is null
			</if>
		</where>
	</sql>
	
	<select id="countAccountDetailByPageInfo" resultType="Long">
		select
		count(*)
		from account_detail
		<include refid="pageWhereSql" />
	</select>
	
	<select id="findAllByOrder" resultType="AccountDetailEntity" >
		select
		<include refid="fields" />
		from account_detail
		where order_id = #{orderId} order by id desc
	</select>
	
	<select id="findLastDetails" resultType="AccountDetailEntity" parameterType="Integer">
		select
		<include refid="fields" />
		from account_detail
		order by id desc limit #{count}
	</select>

	<select id="findLastDetailsByAppId" resultType="AccountDetailEntity" parameterType="map">
		select
		<include refid="fields" />
		from account_detail
		where developer_id = #{developerId}
		order by id desc limit #{count}
	</select>
	
	<select id="getRowAndMax" parameterType="AccountPageInfo" resultMap="rowAndMaxMap">
    	select
    	count(id) as countInt,
    	max(id) as maxInt 
		from account_detail
		<include refid="pageWhereSql" />
	</select>
	
	<select id="findAccountDetailExport" resultType="AccountDetailEntity">
		select
		<include refid="fields" />
		from account_detail
		<include refid="pageWhereSql" />
		and id &lt;= #{pageInfo.maxId}
		order by gmt_create desc
		limit #{pageInfo.offset},#{pageInfo.max}
	</select>

	<select id="getDetailByOrderIds" resultType="AccountDetailEntity">
		select
		<include refid="fields" />
		from account_detail
		where app_Id = #{appId}
		and order_id in
		<foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>
	
	<update id="update" parameterType="AccountDetailEntity">
		update account_detail
		<set>
			<if test="developerId != null">developer_id =#{developerId},</if>
			<if test="credits != null">credits =#{credits},</if>
			<if test="appId != null">app_id =#{appId},</if>
			<if test="type != null">type =#{type},</if>
			<if test="moneyChange != null">money_change =#{moneyChange},</if>
			<if test="description != null">description =#{description},</if>
			<if test="memo != null">memo =#{memo},</if>
			<if test="operatorId != null">operator_id =#{operatorId},</if>
			<if test="balance != null">balance =#{balance},</if>
			<if test="orderId != null">order_id =#{orderId},</if>
			<if test="rechargeType != null">recharge_type =#{rechargeType},</if>
			<if test="gmtCreate != null">gmt_create =#{gmtCreate},</if>
			<if test="gmtModified != null">gmt_modified =#{gmtModified}</if>
		</set>
		where id = #{id}
	</update>
	
	<resultMap type="java.util.HashMap" id="rowAndMaxMap">
		<result column="countInt" property="count" />
		<result column="maxInt" property="max" />
	</resultMap>

	<select id="getSumByTypeAndDay" resultType="Long" parameterType="cn.com.duiba.paycenter.params.AccountChangeParam">
		select count(money_change)
		from account_detail
		WHERE developer_id = #{developerId} and app_id = #{appId} and `type` = #{type}
				and gmt_create between #{startDate} and #{endDate}
	</select>

	<select id="getSumByIdsAndDate" resultType="cn.com.duiba.paycenter.dto.AccountDetailTotalDto">
		SELECT SUM(money_change) as rechargeCapital,developer_id as developId
		FROM account_detail
		WHERE
		<![CDATA[ gmt_create >= #{startDate} ]]>
		and  <![CDATA[ gmt_create <= #{endDate} ]]>
		and developer_id in
		<foreach collection="developIds" item="developId" index="index" open="(" close=")" separator="," >
			#{developId}
		</foreach>
		and type = 'income'
		and order_id IS NULL
		GROUP BY developer_id ;
	</select>

</mapper>