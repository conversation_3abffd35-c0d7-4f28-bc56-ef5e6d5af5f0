<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.SubAccountDaoImpl">

    <sql id="fields">
        id,
        version,
        developer_id,
        account_name,
        account_identity,
        balance,
        gmt_create,
        gmt_modified
    </sql>

    <select id="findByIdentity" parameterType="map" resultType="cn.com.duiba.paycenter.entity.credits.SubAccountEntity">
        select <include refid="fields"/> from tb_sub_account
        where developer_id = #{developerId} and account_identity = #{accountIdentity}
    </select>

    <select id="findByDeveloperId" resultType="cn.com.duiba.paycenter.entity.credits.SubAccountEntity">
        select <include refid="fields"/> from tb_sub_account
        where developer_id = #{developerId}
    </select>


    <update id="updateByAppId" parameterType="map">
        update tb_sub_account set balance = #{newBalance}
        where developer_id = #{developerId}
        and balance = #{oldBalance}
        and account_identity = #{accountIdentity}
    </update>

    <select id="findById4update" resultType="cn.com.duiba.paycenter.entity.credits.SubAccountEntity">
        select
        <include refid="fields" />
        from tb_sub_account
        where id = #{id} order by id desc limit 1 for update
    </select>

    <select id="findById" resultType="cn.com.duiba.paycenter.entity.credits.SubAccountEntity">
        select
        <include refid="fields" />
        from tb_sub_account
        where id = #{id} order by id desc limit 1
    </select>

    <update id="reduceMoney">
        update tb_sub_account set balance=(balance-#{money}) where id=#{id} and balance>=#{money}
    </update>

    <update id="addMoney">
        update tb_sub_account set balance=(balance+#{money}) where id=#{id}
    </update>

    <select id="findByDeveloperIds" resultType="cn.com.duiba.paycenter.entity.credits.SubAccountEntity">
        select <include refid="fields"/> from tb_sub_account
        where developer_id in
        <foreach collection="list" item="developerId" open="(" separator="," close=")">
            #{developerId}
        </foreach>

    </select>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into tb_sub_account (developer_id, account_name, account_identity, balance)
        values
        <foreach collection="list" item="subAccount" separator=",">
            ( #{subAccount.developerId}, #{subAccount.accountName}, #{subAccount.accountIdentity}, #{subAccount.balance})
        </foreach>
    </insert>

</mapper>
