<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.DuibaRemainingMoneyDaoImpl">
    
    <!-- ======================= special queries ============================== -->
    <insert id="insert" parameterType="DuibaRemainingMoneyEntity" useGeneratedKeys="true" keyProperty="id">
		insert into duiba_remaining_money
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="money != null">money,</if>
			<if test="type != null">type,</if>
			<if test="version != null">version,</if>
			<if test="sign != null">sign,</if>
			<if test="gmtCreate != null">gmt_create,</if>
			<if test="gmtModified != null">gmt_modified,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="money != null">#{money},</if>
			<if test="type != null">#{type},</if>
			<if test="version != null">#{version},</if>
			<if test="sign != null">#{sign},</if>
			<if test="gmtCreate != null">#{gmtCreate},</if>
			<if test="gmtModified != null">#{gmtModified},</if>
		</trim>
	</insert>
	
	<select id="find" resultType="DuibaRemainingMoneyEntity" parameterType="Long">
		select
		<include refid="fields" />
		from duiba_remaining_money
		where id = #{id}
	</select>
    
    <update id="reduceMoney">
        update duiba_remaining_money set money=(money-#{money}) , version=(version+1) , sign=#{sign}, gmt_modified=#{modified} where id=#{id} and version=#{version} and money>=#{money}
    </update>
    
    <update id="addMoney">
        update duiba_remaining_money set money=(money+#{money}) , version=(version+1) , sign=#{sign}, gmt_modified=#{modified} where id=#{id} and version=#{version}
    </update>
    
    <select id="findRecord" resultType="DuibaRemainingMoneyEntity" >
		select
		<include refid="fields" />
		from duiba_remaining_money
		where type='duiba'
	</select>
	
    <select id="findRecord4update" resultType="DuibaRemainingMoneyEntity">
		select
		<include refid="fields" />
		from duiba_remaining_money
		where type='duiba' for update
	</select>
	
	<select id="findDuibaIncomeRecord" resultType="DuibaRemainingMoneyEntity" >
		select
		<include refid="fields" />
		from duiba_remaining_money
		where type='settle_duiba'
	</select>
	
	<select id="findDuibaIncomeRecord4update" resultType="DuibaRemainingMoneyEntity">
		select
		<include refid="fields" />
		from duiba_remaining_money
		where type='settle_duiba' for update
	</select>

	<sql id="fields">
		id as id,
		money as money, 
		type as type,
		version as version,
		sign as sign,
		gmt_create	as gmtCreate,
		gmt_modified as gmtModified
	</sql>


</mapper>