<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 数据库基本操作 -->
<mapper namespace="cn.com.duiba.paycenter.dao.impl.EquityOnlyOrderDaoImpl">
    <sql id="columnMap">
            id as id,
            biz_type as bizType,
            biz_no as bizNo,
            order_status as orderStatus,
            retry_count as retryCount,
            executor_biz_id as executorBizId,
            biz_remark as bizRemark,
            remark as remark,
            gmt_create as gmtCreate,
            gmt_modified as gmtModified
    </sql>
	<!--base operation start-->
    <insert id="insert" parameterType="cn.com.duiba.paycenter.entity.EquityOnlyOrderEntity" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO `tb_equity_only_order`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bizType!=null">biz_type ,</if>
            <if test="bizNo!=null"> biz_no ,</if>
            <if test="orderStatus!=null"> order_status ,</if>
            <if test="executorBizId!=null"> executor_biz_id ,</if>
            <if test="remark!=null">remark,</if>
            <if test="bizRemark!=null">biz_remark,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bizType!=null">#{bizType} ,</if>
            <if test="bizNo!=null"> #{bizNo} ,</if>
            <if test="orderStatus!=null"> #{orderStatus} ,</if>
            <if test="executorBizId!=null"> #{executorBizId} ,</if>
            <if test="remark!=null">#{remark},</if>
            <if test="bizRemark!=null">#{bizRemark},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="cn.com.duiba.paycenter.entity.EquityOnlyOrderEntity">
        UPDATE `tb_equity_only_order`
        SET gmt_modified = now()
            <if test="orderStatus!=null" >,order_status = #{orderStatus}</if>
            <if test="retryCount!=null" >,retry_count = #{retryCount}</if>
            <if test="executorBizId!=null" >,executor_biz_id = #{executorBizId}</if>
            <if test="remark!=null">,remark = #{remark}</if>
            <if test="bizRemark!=null">,biz_remark = #{bizRemark}</if>
        WHERE
            id = #{id} 
    </update>

    <update id="updateStatusByUniqueIndex" parameterType="map">
        UPDATE `tb_equity_only_order`
        SET gmt_modified = now()
        <if test="orderStatus!=null" >,order_status = #{orderStatus}</if>
        <if test="retryCount!=null" >,retry_count = #{retryCount}</if>
        <if test="executorBizId!=null" >,executor_biz_id = #{executorBizId}</if>
        <if test="remark!=null">,remark = #{remark}</if>
        <if test="bizRemark!=null">,biz_remark = #{bizRemark}</if>
        WHERE biz_type = #{bizType} AND biz_no = #{bizNo}
    </update>

    <select id="findByUniqueIndex" resultType="cn.com.duiba.paycenter.entity.EquityOnlyOrderEntity" parameterType="java.util.Map">
        SELECT <include refid="columnMap"></include>
        FROM `tb_equity_only_order`
        WHERE
        biz_type = #{bizType} AND biz_no = #{bizNo}
    </select>

</mapper>

