<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.RemainingMoneyDaoImpl">
    
    <!-- ======================= special queries ============================== -->
    <update id="reduceMoney">
        update remaining_money set money=(money-#{money}) , version=(version+1) , sign=#{sign}, gmt_modified=#{modified} where id=#{id} and version=#{version} and money>=#{money}
    </update>
    
    <update id="addMoney">
        update remaining_money set money=(money+#{money}) , version=(version+1) , sign=#{sign}, gmt_modified=#{modified} where id=#{id} and version=#{version}
    </update>

    <select id="findByDeveloperId" resultType="RemainingMoneyEntity" >
		select
		<include refid="fields" />
		from remaining_money
		where developer_id=#{developerId} order by sort desc limit 1
	</select>

	<select id="findByDeveloperIdList" resultType="RemainingMoneyEntity">
		select
		<include refid="fields" />
		from remaining_money
		where developer_id in
		<foreach collection="developerIds" close=")" open="(" separator="," item="id" index="index">
			#{id}
		</foreach>
	</select>
	
    <select id="findByDeveloperId4update" resultType="RemainingMoneyEntity">
		select
		<include refid="fields" />
		from remaining_money
		where developer_id=#{developerId} order by sort desc limit 1 for update
	</select>

	<select id="findByDeveloperId4updateWithSubject" resultType="RemainingMoneyEntity">
		select
		<include refid="fields" />
		from remaining_money
		where developer_id=#{developerId} AND subject = #{subject} limit 1 for update
	</select>

	<!-- ======================= common queries =============================== -->
	<select id="find" resultType="RemainingMoneyEntity" parameterType="Long">
		select
		<include refid="fields" />
		from remaining_money
		where id = #{id}
	</select>
	
	<insert id="insert" parameterType="RemainingMoneyEntity" useGeneratedKeys="true" keyProperty="id">
		insert into remaining_money
		<trim prefix="(" suffix=")" suffixOverrides=",">
			money,
			<if test="id != null">id,</if>
			<if test="developerId != null">developer_id,</if>
			<if test="version != null">version,</if>
			<if test="sign != null">sign,</if>
			<if test="gmtCreate != null">gmt_create,</if>
			<if test="gmtModified != null">gmt_modified,</if>
			<if test="subject != null">subject,</if>
			<if test="sort != null">sort,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			0,
			<if test="id != null">#{id},</if>
			<if test="developerId != null">#{developerId},</if>
			<if test="version != null">#{version},</if>
			<if test="sign != null">#{sign},</if>
			<if test="gmtCreate != null">#{gmtCreate},</if>
			<if test="gmtModified != null">#{gmtModified},</if>
			<if test="subject != null">#{subject},</if>
			<if test="sort != null">#{sort},</if>
		</trim>
	</insert>

	<select id="findBalance" resultType="Long" >
		select
		money
		from remaining_money
		where developer_id = #{developerId}
		AND subject = #{subject}
	</select>

	<select id="batchFindBalance" resultType="cn.com.duiba.paycenter.dto.DeveloperMoneyInfoDto">
		select money as money, developer_id as developerId
		from remaining_money
		where developer_id in
		<foreach collection="developerIds" item="item" index="index" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- ======================= query settings =============================== -->
	<sql id="fields">
		id as id,
		developer_id as developerId,
		money as money,
		version as version,
		sign as sign,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified,
		subject AS subject,
        sort AS sort
		
	</sql>

</mapper>