<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.AppDaoImpl">

    <select id="findFirstAppByDevId" resultType="long">
        select id from app
        where developer_id = #{developerId}
        order by gmt_create
        limit 1
    </select>

</mapper>
