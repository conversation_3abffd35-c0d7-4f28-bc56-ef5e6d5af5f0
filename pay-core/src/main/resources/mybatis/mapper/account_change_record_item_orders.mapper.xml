<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.AccountChangeRecordItemOrdersDAOImpl">

	<sql id="fields">
		id,gmt_create,gmt_modified,order_id,order_item_id,money,item_record_type,item_record_status,app_id,developer_id
	</sql>
	
	<insert id="insert" parameterType="AccountChangeRecordItemOrdersEntity" useGeneratedKeys="true" keyProperty="id">
		insert into account_change_record_item_orders(order_id,order_item_id,money,item_record_type,item_record_status,app_id,developer_id,gmt_create,gmt_modified)
		values(#{orderId},#{orderItemId},#{money},#{itemRecordType},0,#{appId},#{developerId},now(),now())
	</insert>

	<insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
		insert into account_change_record_item_orders(order_id,order_item_id,money,item_record_type,item_record_status,app_id,developer_id,gmt_create,gmt_modified)
		values
		<foreach collection="list" index="index" item="it" separator=",">
			(#{it.orderId},#{it.orderItemId},#{it.money},#{it.itemRecordType},0,#{it.appId},#{it.developerId},now(),now())
		</foreach>
	</insert>

	<select id="find" parameterType="long" resultType="AccountChangeRecordItemOrdersEntity">
		select <include refid="fields"/> from account_change_record_item_orders where id = #{id}
	</select>

	<select id="findByOrderItemIdAndType" parameterType="map" resultType="java.util.List">
		select <include refid="fields"/> from account_change_record_item_orders
		<where>
			<if test="orderId != null">
				and order_id = #{orderId}
			</if>
			<if test="orderItemId != null">
				and order_item_id = #{orderItemId}
			</if>
			<if test="itemRecordType != null">
				and item_record_type = #{itemRecordType}
			</if>
			<if test="itemRecordStatus != null">
				and item_record_status = #{itemRecordStatus}
			</if>
		</where>
	</select>

	<select id="findByOrderItemIds" parameterType="map" resultType="java.util.List">
		select <include refid="fields"/> from account_change_record_item_orders
		<where>
			<if test="orderId != null">
				and order_id = #{orderId}
			</if>
			<if test="orderItemIds != null">
				and order_item_id in
				<foreach collection="orderItemIds" index="index" open="(" close=")" item="it" separator=",">
					#{it}
				</foreach>
			</if>
		</where>
	</select>


	<update id="updateSettleByOrderId">
		update account_change_record_item_orders set item_record_status = 1
		where order_id = #{orderId}
	</update>



	<select id="findByOrderItemIdPage" parameterType="cn.com.duiba.paycenter.dto.AccountPageInfo" resultType="AccountChangeRecordItemOrdersEntity">
		select <include refid="fields"/> from account_change_record_item_orders
		<include refid="pageSelect"/> order by gmt_create desc
		limit #{offset},#{max}
	</select>

	<select id="findCountByOrderItemIdPage" parameterType="cn.com.duiba.paycenter.dto.AccountPageInfo" resultType="int">
		select count(1) from account_change_record_item_orders
		<include refid="pageSelect"/>
	</select>

	<sql id="pageSelect">
		<where>
			developer_id = #{developerId}
			<choose>
				<when test="orderId != null">
					and order_id = #{orderId}
				</when>
				<otherwise>
					<if test="start !=null and now !=null">
						and gmt_create between #{start} and #{now}
					</if>
					<if test="appId != null">
						and app_id = #{appId}
					</if>
				</otherwise>
			</choose>
			and item_record_status = 0
		</where>
	</sql>

</mapper>