<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.DuibaDeduceDetailDaoImpl">

	<select id="findPageList" resultType="DuibaDeduceDetailEntity" parameterType="java.util.Map">
		select
		<include refid="BaseColumn"/>
		from
		duiba_deduce_detail 
		<include refid="pageWhereSql"/>
		ORDER BY id DESC
		LIMIT #{offset}, #{max}
	</select>
	
	<select id="find" resultType="DuibaDeduceDetailEntity" parameterType="Long">
		select
		<include refid="BaseColumn" />
		from duiba_deduce_detail
		where id = #{id}
	</select>
	
	<select id="findPageCount" resultType="Long" parameterType="java.util.Map">
		select
		count(id)
		from
		duiba_deduce_detail <include refid="pageWhereSql"/>
	</select>
	
	<insert id="insert" parameterType="DuibaDeduceDetailEntity" useGeneratedKeys="true" keyProperty="id">
		insert into duiba_deduce_detail
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="orderNo != null">order_num,</if>
			<if test="orderId != null">order_id,</if>
			<if test="appName != null">app_name,</if>
			<if test="appId != null">app_id,</if>
			<if test="developId != null">develop_id,</if>
			<if test="activityType != null">activity_type,</if>
			<if test="activityTitle != null">activity_title,</if>
			<if test="activityId != null">activity_id,</if>
			<if test="operationType != null">operation_type,</if>
			<if test="operationMoney != null">operatoin_money,</if>
			<if test="operationStatus != null">operation_status,</if>
			<if test="memo != null">memo,</if>
			<if test="gmtCreate != null">gmt_create,</if>
			<if test="gmtModified != null">gmt_modified,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="orderNo != null">#{orderNo},</if>
			<if test="orderId != null">#{orderId},</if>
			<if test="appName != null">#{appName},</if>
			<if test="appId != null">#{appId},</if>
			<if test="developId != null">#{developId},</if>
			<if test="activityType != null">#{activityType},</if>
			<if test="activityTitle != null">#{activityTitle},</if>
			<if test="activityId != null">#{activityId},</if>
			<if test="operationType != null">#{operationType},</if>
			<if test="operationMoney != null">#{operationMoney},</if>
			<if test="operationStatus != null">#{operationStatus},</if>
			<if test="memo != null">#{memo},</if>
			<if test="gmtCreate != null">#{gmtCreate},</if>
			<if test="gmtModified != null">#{gmtModified},</if>
		</trim>
	</insert>
	
	<update id="update" parameterType="DuibaDeduceDetailEntity">
		update duiba_deduce_detail set operation_status=#{operationStatus},
		gmt_modified=now()
		where id = #{id}
	</update>
	
	<update id="updateDuibaDeduceDetail" parameterType="Map">
	  update duiba_deduce_detail set operation_status=#{operationStatus},gmt_modified=now() where id=#{id}
	</update>
	
	<sql id="BaseColumn">
		id as id,
		order_num as orderNo, 
		order_id as orderId,
		app_name as appName,
		app_id	as appId,
		develop_id	as developId,
		activity_type as activityType,
		activity_title as activityTitle,
		activity_id	 as activityId,
		operation_type	as operationType,
		operatoin_money	as operationMoney,
		operation_status as operationStatus,
		memo as memo,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
	</sql>
	
	<sql id="pageWhereSql">
		<trim prefix="where" prefixOverrides="and">
			<if test="startDate != null">
				<![CDATA[  
				and gmt_create >= #{startDate}
				]]>
			</if>
			<if test="endDate != null">
				<![CDATA[  
				and gmt_create <= #{endDate}
				]]>
			</if>
			<if test="operationType != null and operationType!=''">
				and operation_type= #{operationType}
			</if>
		</trim>
	</sql>
	
</mapper>