<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.SubAccountDeatilDaoImpl">
    <sql id="fields">
        id,
        sub_account_id,
        version,
        app_id,
        description,
        balance,
        gmt_create,
        gmt_modified,
        memo,
        money_change,
        operator_id,
        order_id,
        type,
        recharge_type,
        developer_id
    </sql>


    <insert id="insert" parameterType="cn.com.duiba.paycenter.entity.credits.SubAccountDetailEntity" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO `tb_sub_account_detail`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="subAccountId!=null">sub_account_id ,</if>
            <if test="appId!=null"> app_id ,</if>
            <if test="description!=null"> description ,</if>
            <if test="balance!=null"> balance ,</if>
            <if test="developerId!=null"> developer_id ,</if>
            <if test="memo!=null"> memo ,</if>
            <if test="moneyChange!=null"> money_change ,</if>
            <if test="operatorId!=null"> operator_id ,</if>
            <if test="orderId">  order_id,</if>
            <if test="type!=null"> type ,</if>
            <if test="rechargeType!=null"> recharge_type ,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="subAccountId!=null">#{subAccountId} ,</if>
            <if test="appId!=null"> #{appId} ,</if>
            <if test="description!=null"> #{description} ,</if>
            <if test="balance!=null"> #{balance} ,</if>
            <if test="developerId!=null"> #{developerId} ,</if>
            <if test="memo!=null"> #{memo} ,</if>
            <if test="moneyChange!=null"> #{moneyChange} ,</if>
            <if test="operatorId!=null"> #{operatorId} ,</if>
            <if test="orderId"> #{orderId} ,</if>
            <if test="type!=null"> #{type} ,</if>
            <if test="rechargeType!=null"> #{rechargeType} ,</if>
        </trim>
    </insert>

    <select id="countBySubAccountId" resultType="java.lang.Integer">
        select count(*)
        from `tb_sub_account_detail`
        where sub_account_id = #{subAccountId} and recharge_type is not null
    </select>

    <select id="listBySubAccountId" resultType="cn.com.duiba.paycenter.entity.credits.SubAccountDetailEntity">
        select <include refid="fields"/>
        from `tb_sub_account_detail`
        where sub_account_id = #{subAccountId} and recharge_type is not null
        order by id desc
        limit #{offset}, #{pageSize}
    </select>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `tb_sub_account_detail`
        ( sub_account_id, app_id, description, balance, memo, money_change, operator_id,
        order_id, type, recharge_type, developer_id)
        values
        <foreach collection="list" item="subAccountDetail" separator=",">
            (#{subAccountDetail.subAccountId}, #{subAccountDetail.appId}, #{subAccountDetail.description},
            #{subAccountDetail.balance}, #{subAccountDetail.memo},
            #{subAccountDetail.moneyChange}, #{subAccountDetail.operatorId}, #{subAccountDetail.orderId}, #{subAccountDetail.type},
            #{subAccountDetail.rechargeType}, #{subAccountDetail.developerId})
        </foreach>
    </insert>
</mapper>
