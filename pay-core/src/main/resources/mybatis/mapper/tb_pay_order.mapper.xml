<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 数据库基本操作 -->
<mapper namespace="cn.com.duiba.paycenter.dao.impl.PayOrderDaoImpl">
    <sql id="columnMap">
            id as id,
            biz_type as bizType,
            biz_no as bizNo,
            amount as amount,
            payer_type as payerType,
            payer_no as payerNo,
            payer_name as payerName,
            payee_type as payeeType,
            payee_no as payeeNo,
            payee_name as payeeName,
            pay_status as payStatus,
            retry_count as retryCount,
            executor_biz_id as executorBizId,
            biz_remark as bizRemark,
            remark as remark,
            gmt_create as gmtCreate,
            gmt_modified as gmtModified
    </sql>
	<!--base operation start-->
    <insert id="insert" parameterType="cn.com.duiba.paycenter.entity.PayOrderEntity" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO `tb_pay_order`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bizType!=null">biz_type ,</if>
            <if test="bizNo!=null"> biz_no ,</if>
            <if test="amount!=null"> amount ,</if>
            <if test="payerType!=null"> payer_type ,</if>
            <if test="payerNo!=null"> payer_no ,</if>
            <if test="payerName!=null"> payer_name ,</if>
            <if test="payeeType!=null"> payee_type ,</if>
            <if test="payeeNo!=null"> payee_no ,</if>
            <if test="payeeName"> payee_name ,</if>
            <if test="payStatus!=null"> pay_status ,</if>
            <if test="retryCount!=null"> retry_count ,</if>
            <if test="executorBizId!=null"> executor_biz_id ,</if>
            <if test="remark!=null">remark,</if>
            <if test="bizRemark!=null">biz_remark,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bizType!=null">#{bizType} ,</if>
            <if test="bizNo!=null"> #{bizNo} ,</if>
            <if test="amount!=null"> #{amount} ,</if>
            <if test="payerType!=null"> #{payerType} ,</if>
            <if test="payerNo!=null"> #{payerNo} ,</if>
            <if test="payerName!=null"> #{payerName} ,</if>
            <if test="payeeType!=null"> #{payeeType} ,</if>
            <if test="payeeNo!=null"> #{payeeNo} ,</if>
            <if test="payeeName"> #{payeeName} ,</if>
            <if test="payStatus!=null"> #{payStatus} ,</if>
            <if test="retryCount!=null"> #{retryCount} ,</if>
            <if test="executorBizId!=null"> #{executorBizId} ,</if>
            <if test="remark!=null">#{remark},</if>
            <if test="bizRemark!=null">#{bizRemark},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="cn.com.duiba.paycenter.entity.PayOrderEntity">
        UPDATE `tb_pay_order`
        SET
            id = #{id}
            <if test="amount!=null">,amount = #{amount}</if>
            <if test="payerType!=null">,payer_type = #{payerType}</if>
            <if test="payerNo!=null">,payer_no = #{payerNo}</if>
            <if test="payerName!=null">,payer_name = #{payerName}</if>
            <if test="payeeType!=null">,payee_type = #{payeeType}</if>
            <if test="payeeNo!=null">,payee_no = #{payeeNo}</if>
            <if test="payeeName">,payee_name = #{payeeName}</if>
            <if test="payStatus!=null" >,pay_status = #{payStatus}</if>
            <if test="retryCount!=null" >,retry_count = #{retryCount}</if>
            <if test="executorBizId!=null" >,executor_biz_id = #{executorBizId}</if>
            <if test="remark!=null">,remark = #{remark}</if>
            <if test="bizRemark!=null">,biz_remark = #{bizRemark}</if>
        WHERE
            id = #{id} 
    </update>
    
    <select id="findById" resultType="cn.com.duiba.paycenter.entity.PayOrderEntity">
        SELECT <include refid="columnMap"></include>
        FROM `tb_pay_order`
        WHERE 
            id = #{id} 
    </select>

    <select id="findByUniqueIndex" resultType="cn.com.duiba.paycenter.entity.PayOrderEntity" parameterType="java.util.Map">
        SELECT <include refid="columnMap"></include>
        FROM `tb_pay_order`
        WHERE
        biz_type = #{bizType} AND biz_no = #{bizNo}
    </select>
	<!--base operation end-->

    <select id="findAlipayExceptionOrderInFiveDay" resultType="cn.com.duiba.paycenter.entity.PayOrderEntity" parameterType="java.util.Map">
        SELECT <include refid="columnMap"/>
        FROM `tb_pay_order`
        WHERE <![CDATA[ gmt_create > #{startDate} AND gmt_create < #{endDate} AND pay_status = 'EXCEPTION' AND retry_count <= 5 ]]>
        limit 100;
    </select>
    
    <select id="findByExecutorBizId" resultType="cn.com.duiba.paycenter.entity.PayOrderEntity">
        select <include refid="columnMap"/>
        from tb_pay_order
        where executor_biz_id = #{executorBizId}
        limit 1
    </select>
    
    <select id="findByBizTypeAndBizNoList" resultType="cn.com.duiba.paycenter.entity.PayOrderEntity">
        select <include refid="columnMap"/>
        from tb_pay_order
        where biz_type = #{bizType} AND biz_no in
        <foreach collection="bizNoList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

	<!--customize operation start-->
</mapper>

