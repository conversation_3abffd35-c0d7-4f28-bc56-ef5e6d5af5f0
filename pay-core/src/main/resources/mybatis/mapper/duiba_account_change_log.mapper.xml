<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.DuibaAccountChangeLogDAOImpl">
	
	<select id="findAllByTypeIdAction" resultType="cn.com.duiba.paycenter.model.DuibaAccountChangeLogDO">
		select 
		
		id,
		developer_id as developerId,
		app_id as appId,
		relation_type as relationType,
		relation_id as relationId,
		action_type as actionType,
		change_money as changeMoney,
		change_kind as changeKind,
		before_balance as beforeBalance,
		after_balance as afterBalance,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified,
		status ,
		fail_reason as failReason
		
		 from duiba_account_change_log where relation_type=#{relationType}
		 and relation_id=#{relationId} and action_type=#{actionType}
	</select>
	
	<select id="find" resultType="cn.com.duiba.paycenter.model.DuibaAccountChangeLogDO">
		select 
		
		id,
		developer_id as developerId,
		app_id as appId,
		relation_type as relationType,
		relation_id as relationId,
		action_type as actionType,
		change_money as changeMoney,
		change_kind as changeKind,
		before_balance as beforeBalance,
		after_balance as afterBalance,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified,
		status ,
		fail_reason as failReason
		
		 from duiba_account_change_log where id =#{id}
	</select>
	
	<insert id="insert" parameterType="cn.com.duiba.paycenter.model.DuibaAccountChangeLogDO" useGeneratedKeys="true" keyProperty="id">
		insert into duiba_account_change_log
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="developerId != null">developer_id,</if>
			<if test="appId != null">app_id,</if>
			<if test="relationType != null">relation_type,</if>
			<if test="relationId != null">relation_id,</if>
			<if test="actionType != null">action_type,</if>
			<if test="changeMoney != null">change_money,</if>
			<if test="changeKind != null">change_kind,</if>
			<if test="beforeBalance != null">before_balance,</if>
			<if test="afterBalance != null">after_balance,</if>
			<if test="gmtCreate != null">gmt_create,</if>
			<if test="gmtModified != null">gmt_modified,</if>
			<if test="status != null">status,</if>
			<if test="failReason != null">fail_reason,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="developerId != null">#{developerId},</if>
			<if test="appId != null">#{appId},</if>
			<if test="relationType != null">#{relationType},</if>
			<if test="relationId != null">#{relationId},</if>
			<if test="actionType != null">#{actionType},</if>
			<if test="changeMoney != null">#{changeMoney},</if>
			<if test="changeKind != null">#{changeKind},</if>
			<if test="beforeBalance != null">#{beforeBalance},</if>
			<if test="afterBalance != null">#{afterBalance},</if>
			<if test="gmtCreate != null">#{gmtCreate},</if>
			<if test="gmtModified != null">#{gmtModified},</if>
			<if test="status != null">#{status},</if>
			<if test="failReason != null">#{failReason},</if>
			
		</trim>
	</insert>



</mapper>