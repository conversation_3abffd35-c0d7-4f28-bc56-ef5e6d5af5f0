<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.AmbPaychannelOrdersDaoImpl">

    <select id="findDevCashierAmbSubOrdersIds" resultType="long">
        select amb_sub_orders_id
        from amb_paychannel_orders
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and paychannel_type = 'developer_cashier'
    </select>


    <sql id="fields">
		id as id ,
		developer_id as developerId ,
		app_id as appId ,
		orders_id as ordersId ,
		amb_sub_orders_id as amb_sub_orders_id ,
		status
		as status ,
		paychannel_type as paychannelType ,
		sub_paychannel_type as subPaychannelType ,
		consumer_money as
		consumerMoney ,
		order_num as orderNum ,
		trade_num as tradeNum ,
		refund_price as refundPrice ,
		refund_status as refundStatus ,
		orders_source as ordersSource ,
		gmt_create as gmtCreate ,
		gmt_modified
		as gmtModified


	</sql>

</mapper>