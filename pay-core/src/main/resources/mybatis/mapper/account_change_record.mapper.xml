<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.AccountChangeRecordDAOImpl">


	<select id="findAllGreaterId" resultType="cn.com.duiba.paycenter.model.AccountChangeRecordDO">
		select 
		
		id,
		developer_id as developerId,
		relation_type as relationType,
		relation_id as relationId,
		action_type as actionType,
		change_money as changeMoney,
		change_kind as changeKind,
		before_balance as beforeBalance,
		after_balance as afterBalance,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
		
		from account_change_record where
		id>#{lastId} and gmt_create <![CDATA[ < adddate(sysdate(),interval -1 minute)  ]]>  order by id asc limit #{limit}
	</select>

	<select id="findAllByRelation" resultType="cn.com.duiba.paycenter.model.AccountChangeRecordDO">
		select 
		
		id,
		developer_id as developerId,
		relation_type as relationType,
		relation_id as relationId,
		action_type as actionType,
		change_money as changeMoney,
		change_kind as changeKind,
		before_balance as beforeBalance,
		after_balance as afterBalance,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
		
		from account_change_record where
		relation_type=#{relationType} and relation_id=#{relationId}
	</select>

	<select id="find" resultType="cn.com.duiba.paycenter.model.AccountChangeRecordDO">
		select 
		id,
		developer_id as developerId,
		relation_type as relationType,
		relation_id as relationId,
		action_type as actionType,
		change_money as changeMoney,
		change_kind as changeKind,
		before_balance as beforeBalance,
		after_balance as afterBalance,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
		from account_change_record where id =#{id}
	</select>

	<select id="findLastRecord" resultType="cn.com.duiba.paycenter.model.AccountChangeRecordDO">
		select
		id,
		developer_id as developerId,
		relation_type as relationType,
		relation_id as relationId,
		action_type as actionType,
		change_money as changeMoney,
		change_kind as changeKind,
		before_balance as beforeBalance,
		after_balance as afterBalance,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
		from account_change_record where developer_id =#{developerId}
		order by id desc limit 1
	</select>

	<update id="update" parameterType="cn.com.duiba.paycenter.model.AccountChangeRecordDO">
		update account_change_record
		<set>
			<if test="developerId != null">developer_id =#{developerId},</if>
			<if test="relationType != null">relation_type =#{relationType},</if>
			<if test="relationId != null">relation_id =#{relationId},</if>
			<if test="actionType != null">action_type =#{actionType},</if>
			<if test="changeMoney != null">change_money =#{changeMoney},</if>
			<if test="changeKind != null">change_kind =#{changeKind},</if>
			<if test="beforeBalance != null">before_balance =#{beforeBalance},</if>
			<if test="afterBalance != null">after_balance =#{afterBalance},</if>
			<if test="gmtModified != null">gmt_modified = now()</if>
		</set>
		where id = #{id}
	</update>

	<select id="findCountExist" resultType="int">
		select count(*) from account_change_record where
		relation_type=#{relationType} and relation_id=#{relationId} and action_type=#{actionType}
	</select>
	
	<select id="findExistRecord" resultType="cn.com.duiba.paycenter.model.AccountChangeRecordDO">
		select 
		
		id,
		developer_id as developerId,
		relation_type as relationType,
		relation_id as relationId,
		action_type as actionType,
		change_money as changeMoney,
		change_kind as changeKind,
		before_balance as beforeBalance,
		after_balance as afterBalance,
		settle_status as settleStatus,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
		
		from account_change_record where
		relation_type=#{relationType} and relation_id=#{relationId} and action_type=#{actionType}
	</select>
	
	<insert id="insert" parameterType="cn.com.duiba.paycenter.model.AccountChangeRecordDO" useGeneratedKeys="true" keyProperty="id">
		<if test="!toBeInsert"> not allow to execute insert </if>
		insert into account_change_record
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="developerId != null">developer_id,</if>
			<if test="relationType != null">relation_type,</if>
			<if test="relationId != null">relation_id,</if>
			<if test="actionType != null">action_type,</if>
			<if test="changeMoney != null">change_money,</if>
			<if test="changeKind != null">change_kind,</if>
			<if test="beforeBalance != null">before_balance,</if>
			<if test="afterBalance != null">after_balance,</if>
			<if test="settleStatus != null">settle_status,</if>
			<if test="gmtCreate != null">gmt_create,</if>
			<if test="gmtModified != null">gmt_modified,</if>
			
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="developerId != null">#{developerId},</if>
			<if test="relationType != null">#{relationType},</if>
			<if test="relationId != null">#{relationId},</if>
			<if test="actionType != null">#{actionType},</if>
			<if test="changeMoney != null">#{changeMoney},</if>
			<if test="changeKind != null">#{changeKind},</if>
			<if test="beforeBalance != null">#{beforeBalance},</if>
			<if test="afterBalance != null">#{afterBalance},</if>
			<if test="settleStatus != null">#{settleStatus},</if>
			<if test="gmtCreate != null">#{gmtCreate},</if>
			<if test="gmtModified != null">#{gmtModified},</if>
			
		</trim>
	</insert>

	<update id="updateSettleStatus" >
		update account_change_record set settle_status = #{settleStatus} where id = #{id}
	</update>

	<update id="batchUpdateStatus" parameterType="Map">

		update account_change_record
		<set>
			settle_status = #{settleStatus}
		</set>

		where id in
		<foreach collection="ids" item="id" index="index" open="(" close=")" separator="," >
			#{id}
		</foreach>
	</update>

	<select id="findByStatusLimit" resultType="cn.com.duiba.paycenter.model.AccountChangeRecordDO">
		select

		id,
		developer_id as developerId,
		relation_type as relationType,
		relation_id as relationId,
		action_type as actionType,
		change_money as changeMoney,
		change_kind as changeKind,
		before_balance as beforeBalance,
		after_balance as afterBalance,
		settle_status as settleStatus,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified

		from account_change_record where settle_status = #{settleStatus}  limit #{limit}
	</select>

	<select id="batchQueryFrozenCapital" resultType="cn.com.duiba.paycenter.dto.AcountChangeTotalDto">
		SELECT SUM(change_money) AS totalCapital,developer_id as developId
		FROM account_change_record
		WHERE
		 <![CDATA[ gmt_create >= #{startDate} ]]>
		and  <![CDATA[ gmt_create <= #{endDate} ]]>
		AND settle_status = #{settleStatus}
		AND developer_id IN
		<foreach collection="developIds" item="developId" index="index" open="(" close=")" separator="," >
			#{developId}
		</foreach>
		AND relation_type = 'orders'
		AND action_type='pay'
		GROUP BY developer_id
	</select>

	<select id="batchQueryConsumeCapital" resultType="cn.com.duiba.paycenter.dto.AcountChangeTotalDto">
		SELECT SUM(change_money) AS totalCapital,developer_id as developId
		FROM account_change_record
		WHERE
		<![CDATA[ gmt_create >= #{startDate} ]]>
		and  <![CDATA[ gmt_create <= #{endDate} ]]>
		and change_kind = 'sub'
		AND settle_status = #{settleStatus}
		AND developer_id IN
		<foreach collection="developIds" item="developId" index="index" open="(" close=")" separator="," >
			#{developId}
		</foreach>
		GROUP BY developer_id
	</select>



	<select id="findPageListByStartId" resultType="cn.com.duiba.paycenter.model.AccountChangeRecordDO">
		select

		id,
		developer_id as developerId,
		relation_type as relationType,
		relation_id as relationId,
		action_type as actionType,
		change_money as changeMoney,
		change_kind as changeKind,
		before_balance as beforeBalance,
		after_balance as afterBalance,
		settle_status as settleStatus,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified

		from account_change_record
		where
		 id >= #{id}
		 and settle_status = #{settleStatus}
		limit #{limit}
	</select>



</mapper>