<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.TpcDlpIncomeMoneyDAOImpl">
    
    <!-- ======================= special queries ============================== -->
    
    <update id="reduceMoney">
        update tpc_dlp_income_money set money=(money-#{money}) , version=(version+1) , sign=#{sign}, gmt_modified=#{modified} where id=#{id} and version=#{version} and money>=#{money}
    </update>
    <update id="addMoney">
        update tpc_dlp_income_money set money=(money+#{money}) , version=(version+1) , sign=#{sign}, gmt_modified=#{modified} where id=#{id} and version=#{version}
    </update>
    <select id="findByDeveloperId" resultType="cn.com.duiba.paycenter.model.TpcDlpIncomeMoneyDO" >
		select
		<include refid="fields" />
		from tpc_dlp_income_money
		where developer_id=#{developerId}
	</select>
	
    <select id="findByDeveloperId4update" resultType="cn.com.duiba.paycenter.model.TpcDlpIncomeMoneyDO">
		select
		<include refid="fields" />
		from tpc_dlp_income_money
		where id=#{id}
		for update
	</select>
	
	<insert id="insert" parameterType="cn.com.duiba.paycenter.model.TpcDlpIncomeChangeDO" useGeneratedKeys="true" keyProperty="id">
		insert into tpc_dlp_income_money (developer_id,money,sign,version,gmt_create,gmt_modified, subject, sort)
		values(#{developerId},#{money},#{sign},#{version},#{gmtCreate},#{gmtModified}, #{subject}, #{sort})
	</insert>

	<select id="findIncomeMoney" resultType="Long"
			parameterType="Long">
		select
		money
		from tpc_dlp_income_money
		where developer_id = #{developerId}
		AND subject = #{subject}
	</select>

	<select id="batchFindIncomeMoney" resultType="cn.com.duiba.paycenter.dto.DeveloperMoneyInfoDto">
		select money as money, developer_id as developerId
		from tpc_dlp_income_money
		where developer_id in
		<foreach collection="developerIds" item="item" index="index" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>
	
	<sql id="fields">
		id,
		developer_id as developerId,
		money,
		sign,
		version,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified,
		subject AS subject,
		sort AS sort
	</sql>


</mapper>