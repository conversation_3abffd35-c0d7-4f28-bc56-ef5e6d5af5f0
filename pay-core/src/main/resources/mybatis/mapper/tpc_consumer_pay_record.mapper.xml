<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.TpcConsumerPayRecordDAOImpl">

	<select id="find" resultType="cn.com.duiba.paycenter.model.TpcConsumerPayRecordDO">
		select 
		
		<include refid="fields"></include>
		
		from tpc_consumer_pay_record where id=#{id}
	</select>
	
	<select id="findByOrderId" resultType="cn.com.duiba.paycenter.model.TpcConsumerPayRecordDO">
		select
		
		<include refid="fields"></include>
		
		from tpc_consumer_pay_record where order_id =#{orderId}
	</select>

	<select id="findByOrderIdKind" resultType="cn.com.duiba.paycenter.model.TpcConsumerPayRecordDO">
		select

		<include refid="fields"></include>

		from tpc_consumer_pay_record where order_id =#{orderId} and kind = #{kind}
	</select>

	
	
	<insert id="insert" parameterType="cn.com.duiba.paycenter.model.TpcConsumerPayRecordDO" useGeneratedKeys="true" keyProperty="id">
		insert into tpc_consumer_pay_record(consumer_id,order_id,app_id,developer_id,money,kind,orders_source,
		<if test="ordersItemId != null"> orders_item_id, </if>
		gmt_create,gmt_modified)
		values(#{consumerId},#{orderId},#{appId},#{developerId},#{money},#{kind},#{ordersSource},
		<if test="ordersItemId != null"> #{ordersItemId}, </if>
		#{gmtCreate},#{gmtModified})
		
	</insert>

	<insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
		insert into tpc_consumer_pay_record(consumer_id,order_id,app_id,developer_id,money,kind,orders_source,orders_item_id,
		gmt_create,gmt_modified) values
		<foreach collection="list" index="index" item="it" separator=",">
			(#{it.consumerId},#{it.orderId},#{it.appId},#{it.developerId},#{it.money},#{it.kind},#{it.ordersSource},
			#{it.ordersItemId},now(),now())
		</foreach>
	</insert>

	<sql id="fields">
		id,
		consumer_id as consumerId,
		order_id as orderId,
		app_id as appId,
		developer_id as developerId,
		money ,
		kind,
		orders_source as ordersSource,
		orders_item_id as ordersItemId,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified

	</sql>

</mapper>