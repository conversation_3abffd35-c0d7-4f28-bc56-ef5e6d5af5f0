<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper
	namespace="cn.com.duiba.paycenter.dao.duibaaccount.impl.DeltaDetailDaoImpl">

	<select id="findPageList" resultType="cn.com.duiba.paycenter.dto.duibaaccount.DuibaAccountDeltaDetailDto"
		parameterType="java.util.Map">
		select
		<include refid="BaseColumn" />
		from
		duiba_account_delta_detail
		<include refid="pageWhereSql" />
		ORDER BY id DESC
		LIMIT #{offset}, #{max}
	</select>

	<select id="findPageCount" resultType="Long" parameterType="java.util.Map">
		select
		count(id)
		from
		duiba_account_delta_detail
		<include refid="pageWhereSql" />
	</select>

	<sql id="BaseColumn">
		id as id,
		admin_id as adminid,
		operation_type as operationType,
		operatoin_money as
		operationMoney,
		operation_status as operationStatus,
		remarks as remarks,
		gmt_create as gmtCreate,
		gmt_modified as mgtModified
	</sql>

	<sql id="pageWhereSql">
		<trim prefix="where" prefixOverrides="and">
			and operation_status in ('success','payback')
			<if test="startDate != null">
				<![CDATA[  
				and gmt_create >= #{startDate}
				]]>
			</if>
			<if test="endDate != null">
				<![CDATA[  
				and gmt_create <= #{endDate}
				]]>
			</if>
			<if test="operationType != null and operationType!=''">
				and operation_type= #{operationType}
			</if>
		</trim>
	</sql>

	<insert id="insert" parameterType="cn.com.duiba.paycenter.dto.duibaaccount.DuibaAccountDeltaDetailDto"
		useGeneratedKeys="true" keyProperty="id">
		<if test="!toBeInsert"> not allow to execute insert </if>
		insert into duiba_account_delta_detail
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="operationType != null">operation_type,</if>
			<if test="operationMoney != null">operatoin_money,</if>
			<if test="operationStatus != null">operation_status,</if>
			<if test="remarks != null">remarks,</if>
			<if test="gmtCreate != null">gmt_create,</if>
			<if test="gmtModified != null">gmt_modified,</if>
			<if test="adminid != null">admin_id,</if>
			<if test="applyId != null">apply_id,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="operationType != null">#{operationType},</if>
			<if test="operationMoney != null">#{operationMoney},</if>
			<if test="operationStatus != null">#{operationStatus},</if>
			<if test="remarks != null">#{remarks},</if>
			<if test="gmtCreate != null">#{gmtCreate},</if>
			<if test="gmtModified != null">#{gmtModified},</if>
			<if test="adminid != null">#{adminid},</if>
			<if test="applyId != null">#{applyId},</if>
		</trim>
	</insert>

	<update id="update" parameterType="cn.com.duiba.paycenter.dto.duibaaccount.DuibaAccountDeltaDetailDto">
		update
		duiba_account_delta_detail set operation_status=#{operationStatus},
		gmt_modified=now(),memo=#{memo}
		where id = #{id}
	</update>


	<update id="updateDuibaAccountDetail" parameterType="Map">

     update duiba_account_delta_detail set operation_status=#{operationStatus} where id=#{id}
	</update>
</mapper>