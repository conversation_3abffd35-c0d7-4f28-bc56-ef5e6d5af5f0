<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.AmbSubOrdersDaoImpl">


    <select id="findWaitSettleMoney" resultType="Long">
        select sum(money) as money from (select
        case when orders_source='dlp'
        then (dlp_sale_price + express_price - consumer_pay_back_price)
        when  orders_source='duiba' and (duiba_sale_price - dlp_sale_price - consumer_pay_back_price) > 0
        then  (duiba_sale_price - dlp_sale_price - consumer_pay_back_price)
        else 0 end AS money
        from amb_sub_orders
        where app_id = #{appId} and settle_status = 'wait_settle')temp
    </select>

    <select id="findWaitSettleAmbPaychannelOrdersIds" parameterType="long" resultType="cn.com.duiba.paycenter.bean.AmbSubOrderIdAndPaychannelIdBean" >
        select id,amb_paychannel_orders_id as ambPaychannelId
        from amb_sub_orders where app_id = #{appId} and settle_status = 'wait_settle'
    </select>

    <select id="findWaitSettleMoneyByIds" resultType="long">
        select sum(money) as money from (
        select
        case when orders_source='dlp'
        then (dlp_sale_price + express_price - consumer_pay_back_price)
        when  orders_source='duiba' and (duiba_sale_price - dlp_sale_price - consumer_pay_back_price) > 0
        then  (duiba_sale_price - dlp_sale_price - consumer_pay_back_price)
        else 0 end AS money
        from amb_sub_orders
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        )temp
    </select>

</mapper>