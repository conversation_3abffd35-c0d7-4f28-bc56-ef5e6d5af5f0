<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.TpcDuibaIncomeDetailDAOImpl">
	<select id="find" resultType="cn.com.duiba.paycenter.model.TpcDuibaIncomeDetailDO">
		select 
		<include refid="fields"></include>
		from tpc_duiba_income_detail where id=#{id}
	</select>
	
	<insert id="insert" parameterType="cn.com.duiba.paycenter.model.TpcDuibaIncomeDetailDO" useGeneratedKeys="true" keyProperty="id">
		insert into tpc_duiba_income_detail(
		relation_type,relation_id,action_type,
		change_money,change_kind,
		before_balance,after_balance,remark,gmt_create,gmt_modified)
		
		values(
		#{relationType},#{relationId},#{actionType},
		#{changeMoney},#{changeKind},
		#{beforeBalance},#{afterBalance},#{remark},#{gmtCreate},#{gmtModified})
		
	</insert>

	<select id="findPageList" resultType="cn.com.duiba.paycenter.model.TpcDuibaIncomeDetailDO" parameterType="java.util.Map">
		select
		<include refid="fields"/>
		from
		tpc_duiba_income_detail where
		gmt_create between #{startDate} and #{endDate}
		<if test="relationId != null and relationId != ''">
			and relation_id =#{relationId}
		</if>
		ORDER BY gmt_create DESC
		LIMIT #{offset}, #{max}
	</select>

	<select id="findCountIncome" resultType="Long">
		select
		sum(change_money)
		from
		tpc_duiba_income_detail
	</select>

	<select id="findPageCount" resultType="Long" parameterType="java.util.Map">
		select
		count(id)
		from
		tpc_duiba_income_detail where
		gmt_create between #{startDate} and #{endDate}
		<if test="relationId != null and relationId != ''">
			and relation_id =#{relationId}
		</if>
	</select>
	<sql id="fields">
		id,
		relation_type as relationType,
		relation_id as relationId,
		action_type as actionType,
		change_money as changeMoney,
		change_kind as changeKind,
		before_balance as beforeBalance,
		after_balance as afterBalance,
		remark,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified

	</sql>
</mapper>