<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.SaasAgentAccountLogDaoImpl">

    <resultMap id="saasAgentAccountLogResult" type="SaasAgentAccountLogEntity">
        <result column="id" property="id"/>
        <result column="agent_id" property="agentId"/>
        <result column="action_type" property="actionType"/>
        <result column="relation_id" property="relationId"/>
        <result column="relation_type" property="relationType"/>
        <result column="change_money" property="changeMoney"/>
        <result column="change_kind" property="changeKind"/>
        <result column="before_balance" property="beforeBalance"/>
        <result column="after_balance" property="afterBalance"/>
        <result column="memo" property="memo"/>
        <result column="operation_id" property="operationId"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <sql id="field">
        id, agent_id, action_type, relation_id, relation_type, change_money,
        change_kind, before_balance, after_balance, memo, operation_id,
        gmt_create, gmt_modified
    </sql>

    <select id="find" resultMap="saasAgentAccountLogResult">
        select
        <include refid="field"/>
        from tb_saas_agent_account_log
        where id = #{id}
    </select>

    <select id="findExistRecord" resultMap="saasAgentAccountLogResult">
        select
        <include refid="field"/>
        from tb_saas_agent_account_log
        where
        relation_id = #{relationId} and action_type = #{actionType} and relation_type = #{relationType}
    </select>

    <insert id="insert" parameterType="SaasAgentAccountLogEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_saas_agent_account_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentId != null">agent_id,</if>
            <if test="relationType != null">relation_type,</if>
            <if test="relationId != null">relation_id,</if>
            <if test="actionType != null">action_type,</if>
            <if test="changeMoney != null">change_money,</if>
            <if test="changeKind != null">change_kind,</if>
            <if test="beforeBalance != null">before_balance,</if>
            <if test="afterBalance != null">after_balance,</if>
            <if test="memo != null">memo,</if>
            <if test="operationId != null">operation_id,</if>
            <if test="gmtCreate != null">gmt_create,</if>
            <if test="gmtModified != null">gmt_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="agentId != null">#{agentId},</if>
            <if test="relationType != null">#{relationType},</if>
            <if test="relationId != null">#{relationId},</if>
            <if test="actionType != null">#{actionType},</if>
            <if test="changeMoney != null">#{changeMoney},</if>
            <if test="changeKind != null">#{changeKind},</if>
            <if test="beforeBalance != null">#{beforeBalance},</if>
            <if test="afterBalance != null">#{afterBalance},</if>
            <if test="memo != null">#{memo},</if>
            <if test="operationId != null">#{operationId},</if>
            <if test="gmtCreate != null">#{gmtCreate},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
        </trim>
    </insert>

</mapper>