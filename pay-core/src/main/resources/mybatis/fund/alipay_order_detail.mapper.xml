<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.AlipayOrderDetailDAOImpl">

	<!-- ======================= common queries =============================== -->
	<sql id="fields">
		id as id,
		alipay_order_id as alipayOrderId,
		order_biz_type as orderBizType,
		amount as amount,
		self_order_id as selfOrderId,
		trade_status as tradeStaus,
		sub_msg as subMsg,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
	</sql>

	
	<insert id="insert" parameterType="AlipayOrderDetailEntity" useGeneratedKeys="true" keyProperty="id">
		insert into tb_alipay_order_detail
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="alipayOrderId != null">alipay_order_id,</if>
			<if test="orderBizType != null">order_biz_type,</if>
			<if test="amount != null">amount,</if>
			<if test="selfOrderId != null">self_order_id,</if>
			<if test="tradeStaus != null">trade_status,</if>
			<if test="subMsg != null">sub_msg,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="alipayOrderId != null">#{alipayOrderId},</if>
			<if test="orderBizType != null">#{orderBizType},</if>
			<if test="amount != null">#{amount},</if>
			<if test="selfOrderId != null">#{selfOrderId},</if>
			<if test="tradeStaus != null">#{tradeStaus},</if>
			<if test="subMsg != null">#{subMsg},</if>
		</trim>
	</insert>

	<select id="findBySelfOrderId" resultType="AlipayOrderDetailEntity" parameterType="Long">
		select
		<include refid="fields" />
		from tb_alipay_order_detail
		where self_order_id = #{selfOrderId}
	</select>
	<update id="update" parameterType="AlipayOrderDetailEntity">
		update tb_alipay_order_detail set alipay_order_id=#{alipayOrderId},
		trade_status=#{tradeStaus},sub_msg=#{subMsg}
		where id = #{id}
	</update>

</mapper>