<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.impl.SaasAgentAccountDaoImpl">
    <resultMap id="agentAccountResult" type="SaasAgentAccountEntity">
        <result column="id" property="id"/>
        <result column="version" property="version"/>
        <result column="agent_id" property="agentId"/>
        <result column="money" property="money"/>
        <result column="sign" property="sign"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>
    <update id="reduceMoney" parameterType="map">
        update tb_saas_agent_account
        set money = (money - #{money}), version = (version + 1), sign = #{sign}, gmt_modified = #{modified}
        where agent_id = #{agentId} and version = #{version} and money >= #{money}
    </update>

    <update id="addMoney" parameterType="map">
        update tb_saas_agent_account
        set money = (money + #{money}), version = (version + 1), sign = #{sign}, gmt_modified = #{modified}
        where agent_id = #{agentId} and version = #{version}
    </update>

    <select id="findByAgentId" resultMap="agentAccountResult">
        select
            id,
            version,
            agent_id,
            money,
            sign,
            gmt_create,
            gmt_modified
        from tb_saas_agent_account
        where agent_id = #{agentId}
        limit 1
    </select>

    <select id="findByAgentIds" resultMap="agentAccountResult">
        select
        id,
        version,
        agent_id,
        money,
        sign,
        gmt_create,
        gmt_modified
        from tb_saas_agent_account where agent_id in
        <foreach collection="list" index="index" open="(" close=")" item="it" separator=",">
            #{it}
        </foreach>
    </select>

    <select id="findByAgentId4update" resultMap="agentAccountResult">
        select
            id,
            version,
            agent_id,
            money,
            sign,
            gmt_create,
            gmt_modified
        from tb_saas_agent_account
        where agent_id = #{agentId}
        limit 1 for update
    </select>

    <insert id="insert" parameterType="SaasAgentAccountEntity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_saas_agent_account (version, agent_id, money, sign, gmt_create, gmt_modified)
        values (#{version}, #{agentId}, #{money}, #{sign}, #{gmtCreate}, #{gmtModified})
    </insert>
</mapper>