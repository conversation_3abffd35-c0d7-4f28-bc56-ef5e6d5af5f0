<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.paycenter.dao.payment.impl.RefundOrderDaoImpl">
    <sql id="fields">
        id,
        order_no,
        transaction_no,
        charge_order_no,
        refund_no,
        refund_status,
        biz_type,
        biz_order_no,
        charge_amount,
        refund_amount,
        apply_refund_amount,
        refund_time,
        app_id,
        extra,
        description,
        failure_code,
        failure_msg,
        orders_item_id
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_refund_order
        (app_id, order_no, transaction_no, charge_order_no, refund_status, charge_amount,
         apply_refund_amount, extra, description, biz_type, biz_order_no, refund_time, refund_amount,
         failure_code, failure_msg, refund_no
        <if test="ordersItemId != null">
            ,orders_item_id
        </if>
         )
        VALUES
        (#{appId}, #{orderNo}, #{transactionNo}, #{chargeOrderNo}, #{refundStatus}, #{chargeAmount},
         #{applyRefundAmount}, #{extra}, #{description}, #{bizType}, #{bizOrderNo}, #{refundTime}, #{refundAmount},
         #{failureCode}, #{failureMsg}, #{refundNo}
        <if test="ordersItemId != null">
            ,#{ordersItemId}
        </if>
         )
    </insert>

    <select id="findByChargeOrderNo" resultType="cn.com.duiba.paycenter.entity.payment.RefundOrderEntity">
        SELECT
        <include refid="fields"/>
        FROM tb_refund_order
        WHERE charge_order_no = #{chargeOrderNo}
        <choose>
            <when test="ordersItemId != null">
                and orders_item_id = #{ordersItemId}
            </when>
            <otherwise>
                and orders_item_id is null
            </otherwise>
        </choose>
        LIMIT 1
    </select>

    <update id="update">
        UPDATE tb_refund_order
        <set>
            <if test="refundStatus != null">
                refund_status = #{refundStatus},
            </if>
            <if test="refundAmount != null">
                refund_amount = #{refundAmount},
            </if>
            <if test="refundTime != null">
                refund_time = #{refundTime},
            </if>
            <if test="failureCode != null">
                failure_code = #{failureCode},
            </if>
            <if test="failureMsg != null">
                failure_msg = #{failureMsg},
            </if>
            <if test="extra != null">
                extra = #{extra},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateRefundNo">
        UPDATE tb_refund_order
        SET refund_status = #{refundStatus},
            refund_no     = #{refundNo}
        WHERE order_no = #{refundOrderNo}
    </update>

    <select id="findByRefundOrderNo" resultType="cn.com.duiba.paycenter.entity.payment.RefundOrderEntity">
        SELECT
        <include refid="fields"/>
        FROM tb_refund_order
        WHERE order_no = #{refundOrderNo}
    </select>

    <select id="selectByBizNo" resultType="cn.com.duiba.paycenter.entity.payment.RefundOrderEntity">
        SELECT
        <include refid="fields"/>
        FROM tb_refund_order
        WHERE biz_order_no = #{bizOrderNo}
    </select>

    <select id="findByTransactionNo" resultType="cn.com.duiba.paycenter.entity.payment.RefundOrderEntity">
        SELECT
        <include refid="fields"/>
        FROM tb_refund_order
        WHERE transaction_no = #{transactionNo}
    </select>

    <select id="selectByBizOrderNosAndTypeForRefundSuccess" resultType="cn.com.duiba.paycenter.entity.payment.RefundOrderEntity">
        SELECT
        <include refid="fields"/>
        FROM tb_refund_order
        WHERE biz_order_no  in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and biz_type = #{bizType}
        and refund_status = 4
    </select>

    <update id="updateByOrderNo">
        UPDATE tb_refund_order
        <set>
            <if test="refundStatus != null">
                refund_status = #{refundStatus},
            </if>
            <if test="refundAmount != null">
                refund_amount = #{refundAmount},
            </if>
            <if test="refundTime != null">
                refund_time = #{refundTime},
            </if>
            <if test="failureCode != null">
                failure_code = #{failureCode},
            </if>
            <if test="failureMsg != null">
                failure_msg = #{failureMsg},
            </if>
            <if test="refundNo != null">
                refund_no = #{refund_No},
            </if>
        </set>
        WHERE order_no = #{orderNo}
    </update>
</mapper>