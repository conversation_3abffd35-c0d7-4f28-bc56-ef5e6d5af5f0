<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.paycenter.dao.payment.impl.WxTransferPayDaoImpl">

    <resultMap type="cn.com.duiba.paycenter.entity.payment.WxTransferPayEntity" id="wxTransferPayMap">
        <result property="id" column="id"/>
        <result property="appId" column="app_id"/>
        <result property="mchId" column="mch_id"/>
        <result property="wxappid" column="wx_appid"/>
        <result property="bizRelationType" column="biz_relation_type"/>
        <result property="bizRelationId" column="biz_relation_id"/>
        <result property="bizOrderNo" column="biz_order_no"/>
        <result property="mchTradeNo" column="mch_trade_no"/>
        <result property="channelType" column="channel_type"/>
        <result property="transactionNo" column="transaction_no"/>
        <result property="checkName" column="check_name"/>
        <result property="openId" column="open_id"/>
        <result property="amount" column="pay_amount"/>
        <result property="body" column="apply_desc"/>
        <result property="clientIp" column="client_ip"/>
        <result property="chargeStatus" column="charge_status"/>
        <result property="failureCode" column="failure_code"/>
        <result property="failureMsg" column="failure_msg"/>
        <result property="paidTime" column="paid_time"/>
        <result property="extra" column="apply_extra"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="fields">
        id,
        app_id,
        mch_id,
        wx_appid,
        biz_relation_type,
        biz_relation_id,
        biz_order_no,
        mch_trade_no,
        channel_type,
        transaction_no,
        check_name,
        open_id,
        pay_amount,
        apply_desc,
        client_ip,
        charge_status,
        failure_code,
        failure_msg,
        paid_time,
        apply_extra,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="save" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.paycenter.entity.payment.WxTransferPayEntity">
        INSERT INTO tb_wx_transfer_pay(app_id,mch_id,wx_appid,biz_relation_type,biz_relation_id,biz_order_no,mch_trade_no,channel_type,transaction_no,check_name,open_id,pay_amount,apply_desc,client_ip,failure_code,failure_msg,paid_time,apply_extra)
        VALUES(#{appId},#{mchId},#{wxappid},#{bizRelationType},#{bizRelationId},#{bizOrderNo},#{mchTradeNo},#{channelType},#{transactionNo},#{checkName},#{openId},#{amount},#{body},#{clientIp},#{failureCode},#{failureMsg},#{paidTime},#{extra})
    </insert>

    <update id="updateById" parameterType="cn.com.duiba.paycenter.entity.payment.WxTransferPayEntity">
        UPDATE tb_wx_transfer_pay
        <set>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="mchId != null">
                mch_id = #{mchId},
            </if>
            <if test="wxappid != null">
                wx_appid = #{wxappid},
            </if>
            <if test="bizRelationType != null">
                biz_relation_type = #{bizRelationType},
            </if>
            <if test="bizRelationId != null">
                biz_relation_id = #{bizRelationId},
            </if>
            <if test="bizOrderNo != null">
                biz_order_no = #{bizOrderNo},
            </if>
            <if test="mchTradeNo != null">
                mch_trade_no = #{mchTradeNo},
            </if>
            <if test="channelType != null">
                channel_type = #{channelType},
            </if>
            <if test="transactionNo != null">
                transaction_no = #{transactionNo},
            </if>
            <if test="checkName != null">
                check_name = #{checkName},
            </if>
            <if test="openId != null">
                open_id = #{openId},
            </if>
            <if test="amount != null">
                pay_amount = #{amount},
            </if>
            <if test="body != null">
                apply_desc = #{body},
            </if>
            <if test="clientIp != null">
                client_ip = #{clientIp},
            </if>
            <if test="chargeStatus != null">
                charge_status = #{chargeStatus},
            </if>
            <if test="failureCode != null">
                failure_code = #{failureCode},
            </if>
            <if test="failureMsg != null">
                failure_msg = #{failureMsg},
            </if>
            <if test="paidTime != null">
                paid_time = #{paidTime},
            </if>
            <if test="extra != null">
                apply_extra = #{extra},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="getById" resultMap="wxTransferPayMap">
        SELECT <include refid="fields"/>
        FROM tb_wx_transfer_pay
        WHERE id = #{id}
    </select>

    <select id="findByIds" resultMap="wxTransferPayMap">
        SELECT <include refid="fields"/>
        FROM tb_wx_transfer_pay
        WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getByMchTradeNo" resultMap="wxTransferPayMap">
        SELECT
        <include refid="fields"/>
        FROM tb_wx_transfer_pay
        WHERE mch_trade_no = #{mchTradeNo}
    </select>

    <select id="getByBizInfo" resultMap="wxTransferPayMap">
        SELECT
        <include refid="fields"/>
        FROM tb_wx_transfer_pay
        WHERE biz_order_no = #{bizOrderNo}
        AND biz_relation_type = #{bizRelationType}
        AND biz_relation_id = #{bizRelationId}
    </select>

</mapper>