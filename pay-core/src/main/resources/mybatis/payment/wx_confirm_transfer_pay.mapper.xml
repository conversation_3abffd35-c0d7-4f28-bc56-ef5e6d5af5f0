<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.paycenter.dao.payment.impl.WxConfirmTransferPayDaoImpl">

    <resultMap type="cn.com.duiba.paycenter.entity.payment.WxConfirmTransferPayEntity" id="wxConfirmTransferPayMap">
        <result property="id" column="id"/>
        <result property="appId" column="app_id"/>
        <result property="mchId" column="mch_id"/>
        <result property="wxappid" column="wx_appid"/>
        <result property="mchTradeNo" column="mch_trade_no"/>
        <result property="bizOrderNo" column="biz_order_no"/>
        <result property="bizRelationType" column="biz_relation_type"/>
        <result property="bizRelationId" column="biz_relation_id"/>
        <result property="channelType" column="channel_type"/>
        <result property="nextTime" column="next_time"/>
        <result property="retry" column="retry_times"/>
        <result property="extra" column="apply_extra"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="subjectType" column="subject_type"/>
    </resultMap>

    <sql id="fields">
        id,
        app_id,
        mch_id,
        wx_appid,
        mch_trade_no,
        biz_relation_id,
        biz_relation_type,
        biz_order_no,
        channel_type,
        subject_type,
        next_time,
        retry_times,
        apply_extra,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.paycenter.entity.payment.WxConfirmTransferPayEntity">
        INSERT INTO tb_wx_confirm_transfer_pay(app_id,mch_id,wx_appid,mch_trade_no,biz_relation_type,biz_relation_id,biz_order_no,channel_type,next_time,apply_extra,subject_type)
        VALUES(#{appId},#{mchId},#{wxappid},#{mchTradeNo},#{bizRelationType},#{bizRelationId},#{bizOrderNo},#{channelType},#{nextTime},#{extra},#{subjectType})
        on duplicate key update
        app_id = #{appId},
        mch_trade_no = #{mchTradeNo},
        biz_relation_type = #{bizRelationType},
        biz_relation_id = #{bizRelationId},
        biz_order_no = #{bizOrderNo},
        channel_type = #{channelType},
        retry_times = 0,
        apply_extra = #{extra},
        next_time = #{nextTime}
    </insert>

    <select id="listByAppId" resultMap="wxConfirmTransferPayMap">
        SELECT
        <include refid="fields"/>
        FROM tb_wx_confirm_transfer_pay
        WHERE app_id = #{appId}
        and next_time <![CDATA[ <= ]]> now()
    </select>

    <select id="listByAppIds" resultMap="wxConfirmTransferPayMap">
        SELECT
        <include refid="fields"/>
        FROM tb_wx_confirm_transfer_pay
        where app_id in
        <foreach collection="appIds" index="index" item="it" open="(" separator="," close=")">
            #{it}
        </foreach>
        and next_time <![CDATA[ <= ]]> now()
    </select>

    <update id="increRetry">
        UPDATE tb_wx_confirm_transfer_pay
        <set>
            retry_times = retry_times + 1
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM tb_wx_confirm_transfer_pay WHERE id=#{id}
    </delete>

    <select id="findByBizInfo" resultMap="wxConfirmTransferPayMap">
        SELECT
        <include refid="fields"/>
        FROM tb_wx_confirm_transfer_pay
        WHERE biz_order_no = #{bizOrderNo}
        AND biz_relation_type = #{bizRelationType}
        AND biz_relation_id = #{bizRelationId}
    </select>

    <update id="updateNextTime">
        UPDATE tb_wx_confirm_transfer_pay
        <set>
            next_time = #{nextTime}
        </set>
        WHERE id = #{id}
    </update>
</mapper>