<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.paycenter.dao.payment.impl.WaitConfirmChargeOrderDaoImpl">
    <sql id="fields">
        id,
        app_id,
        order_no,
        biz_order_no,
        biz_type,
        channel_type,
        order_type,
        retry,
        extra,
        order_date,
        next_time,
        gmt_create,
        gmt_modified
    </sql>
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_wait_confirm_charge_order
        (app_id, order_no, biz_order_no, biz_type, channel_type, order_type, extra, order_date, next_time)
        VALUES
        (#{appId}, #{orderNo}, #{bizOrderNo}, #{bizType}, #{channelType}, #{orderType}, #{extra}, #{orderDate}, #{nextTime})
        on duplicate key update
        app_id = #{appId},
        order_no = #{orderNo},
        channel_type = #{channelType},
        retry = 0,
        extra = #{extra},
        order_date = #{orderDate},
        next_time = #{nextTime}
    </insert>

    <select id="listByAppId" resultType="cn.com.duiba.paycenter.entity.payment.WaitConfirmChargeOrderEntity">
        SELECT
        <include refid="fields"/>
        FROM tb_wait_confirm_charge_order
        WHERE app_id = #{appId}
        and next_time <![CDATA[ <= ]]> now()
    </select>

    <select id="listByAppIds" resultType="cn.com.duiba.paycenter.entity.payment.WaitConfirmChargeOrderEntity">
        SELECT
        <include refid="fields"/>
        FROM tb_wait_confirm_charge_order
        where app_id in
        <foreach collection="appIds" index="index" item="it" open="(" separator="," close=")">
            #{it}
        </foreach>
        and next_time <![CDATA[ <= ]]> now()
    </select>

    <update id="increRetry">
        UPDATE tb_wait_confirm_charge_order
        <set>
        retry = retry + 1
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        Delete from tb_wait_confirm_charge_order
        WHERE id = #{id}
    </delete>

    <select id="findAllByBizOrderNoAndBizTypeAndChannel" resultType="cn.com.duiba.paycenter.entity.payment.WaitConfirmChargeOrderEntity">
        SELECT
        <include refid="fields"/>
        FROM tb_wait_confirm_charge_order
        WHERE biz_order_no = #{bizOrderNo} AND biz_type = #{bizType} AND channel_type = #{channelType}
    </select>

    <select id="findByBizOrderNoAndBizTypeAndChannelAndOrderType" resultType="cn.com.duiba.paycenter.entity.payment.WaitConfirmChargeOrderEntity">
        SELECT
        <include refid="fields"/>
        FROM tb_wait_confirm_charge_order
        WHERE biz_order_no = #{bizOrderNo} AND biz_type = #{bizType} AND channel_type = #{channelType} AND order_type = #{orderType}
    </select>

    <update id="updateNextTime">
        UPDATE tb_wait_confirm_charge_order
        <set>
            next_time = #{nextTime}
        </set>
        WHERE id = #{id}
    </update>


    <update id="updateExtra">
        UPDATE tb_wait_confirm_charge_order
        <set>
            extra = #{extra}
        </set>
        WHERE id = #{id}
    </update>
</mapper>
