<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.paycenter.dao.payment.impl.ChargeOrderDaoImpl">
    <sql id="fields">
        order_no,
        biz_order_no,
        biz_type,
        transaction_no,
        charge_status,
        channel_type,
        amount,
        client_ip,
        device_info,
        app_id,
        title,
        body,
        paid_time,
        expire_time,
        extra,
        meta_data,
        failure_code,
        failure_msg,
        gmt_create,
        gmt_modified
    </sql>
    <select id="findByOrderNo" resultType="cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity">
        SELECT id,
        <include refid="fields"/>
        FROM tb_charge_order
        WHERE order_no = #{orderNo}
    </select>

    <select id="batchFindByOrderNo" resultType="cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity">
        SELECT id,
        <include refid="fields"/>
        FROM tb_charge_order
        WHERE order_no in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="batchFindByTransactionNo" resultType="cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity">
        SELECT id,
        <include refid="fields"/>
        FROM tb_charge_order
        WHERE transaction_no in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by gmt_create desc
    </select>

    <select id="selectByBizNosAndBizType" resultType="cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity">
        SELECT id,
        <include refid="fields"/>
        FROM tb_charge_order
        WHERE biz_order_no  in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and biz_type = #{bizType}
    </select>

    <select id="findByTransactionNo" resultType="cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity">
        SELECT id,
        <include refid="fields"/>
        FROM tb_charge_order
        WHERE transaction_no = #{transactionNo}
    </select>

    <select id="findByBizOrderNoAndBizType" resultType="cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity">
        SELECT id,
        <include refid="fields"/>
        FROM tb_charge_order
        WHERE biz_order_no = #{bizOrderNo} AND biz_type = #{bizType}
        LIMIT 1
    </select>

    <select id="selectByBizNo" resultType="cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity">
        SELECT id,
        <include refid="fields"/>
        FROM tb_charge_order
        WHERE biz_order_no = #{bizOrderNo}
    </select>

    <select id="findByBizOrderNoAndBizTypeAndChannel" resultType="cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity">
        SELECT id,
        <include refid="fields"/>
        FROM tb_charge_order
        WHERE biz_order_no = #{bizOrderNo} AND biz_type = #{bizType} AND channel_type = #{channelType} order by gmt_modified desc
        LIMIT 1
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_charge_order(order_no,
                                    biz_order_no,
                                    biz_type,
                                    channel_type,
                                    amount,
                                    client_ip,
                                    device_info,
                                    app_id,
                                    title,
                                    body,
                                    expire_time,
                                    extra,
                                    meta_data)
        VALUES
        (#{orderNo},
         #{bizOrderNo},
         #{bizType},
         #{channelType},
         #{amount},
         #{clientIp},
         #{deviceInfo},
         #{appId},
         #{title},
         #{body},
         #{expireTime},
         #{extra},
         #{metadata})
    </insert>

    <update id="updateTransactionNo">
        UPDATE tb_charge_order
        SET
            transaction_no = #{transactionNo},
            charge_status  = #{chargeStatus}
        WHERE order_no = #{orderNo}
    </update>

    <update id="update">
        UPDATE tb_charge_order
        <set>
            <if test="chargeStatus != null">
                charge_status = #{chargeStatus},
            </if>
            <if test="paidTime != null">
                paid_time = #{paidTime},
            </if>
            <if test="transactionNo != null">
                transaction_no = #{transactionNo},
            </if>
            <if test="extra != null">
                extra = #{extra},
            </if>
            <if test="failureCode != null">
                failure_code = #{failureCode},
            </if>
            <if test="failureMsg != null">
                failure_msg = #{failureMsg},
            </if>
        </set>
        WHERE order_no = #{orderNo}
    </update>

    <update id="updateExtra">
        UPDATE tb_charge_order SET
        extra = #{extra}
        <if test="chargeStatus != null">
            , charge_status = #{chargeStatus}
        </if>
        WHERE order_no = #{orderNo}
    </update>

</mapper>