<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.payment.impl.ChannelDisplayDaoImpl">
    <select id="findByAppIdAndChannelId" resultType="cn.com.duiba.paycenter.entity.payment.ChannelDisplayEntity">
        SELECT id, channel_id, app_id, display_status
        FROM tb_channel_display
        WHERE app_id = #{appId} AND channel_id = #{channelId}
        LIMIT 1;
    </select>

    <update id="update">
        UPDATE tb_channel_display
        SET display_status = #{displayStatus}
        WHERE app_id = #{appId} AND channel_id = #{channelId}
    </update>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_channel_display(app_id, channel_id, display_status)
        VALUES (#{appId}, #{channelId}, #{displayStatus})
    </insert>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_channel_display(app_id, channel_id, display_status)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.appId}, #{item.channelId}, #{item.displayStatus})
        </foreach>
    </insert>

    <select id="listByAppId" resultType="cn.com.duiba.paycenter.entity.payment.ChannelDisplayEntity">
        SELECT id, channel_id, app_id, display_status
        FROM tb_channel_display
        WHERE app_id = #{appId}
    </select>
</mapper>