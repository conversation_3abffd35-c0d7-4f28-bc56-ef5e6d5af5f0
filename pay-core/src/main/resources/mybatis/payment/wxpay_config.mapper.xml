<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.paycenter.dao.payment.impl.WxPayConfigDaoImpl">
    <sql id="fields">
        id,
        channel_type,
        channel_name,
        channel_mode,
        channel_status,
        description,
        app_id,
        wx_app_id,
        mch_id,
        api_key,
        api_cert,
        app_secret,
        rate
    </sql>
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tb_wxpay_config
        (channel_type, channel_name, channel_mode, channel_status, description, wx_app_id, mch_id,
         api_key, api_cert, app_secret, rate, app_id)
        VALUES
        (#{channelType}, #{channelName}, #{channelMode}, #{channelStatus}, #{description}, #{wxAppId}, #{mchId},
         #{apiKey}, #{apiCert}, #{appSecret}, #{rate}, #{appId})
    </insert>

    <select id="listByAppId" resultType="cn.com.duiba.paycenter.entity.payment.WxPayConfigEntity">
        SELECT
        <include refid="fields"/>
        FROM tb_wxpay_config
        WHERE app_id = #{appId} AND channel_status > 0
    </select>

    <update id="update">
        UPDATE tb_wxpay_config
        <set>
            <if test="channelMode != null">
                channel_mode = #{channelMode},
            </if>
            <if test="channelStatus != null">
                channel_status = #{channelStatus},
            </if>
            <if test="wxAppId != null">
                wx_app_id = #{wxAppId},
            </if>
            <if test="mchId != null">
                mch_id = #{mchId},
            </if>
            <if test="apiKey != null">
                api_key = #{apiKey},
            </if>
            <if test="appSecret != null">
                app_secret = #{appSecret},
            </if>
            <if test="rate != null">
                rate = #{rate},
            </if>
            <if test="apiCert != null">
                api_cert = #{apiCert},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="findById" resultType="cn.com.duiba.paycenter.entity.payment.WxPayConfigEntity">
        SELECT
        <include refid="fields"/>
        FROM tb_wxpay_config
        WHERE id = #{id}
    </select>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_wxpay_config
        (channel_type, channel_name, channel_mode, channel_status, description
        , wx_app_id, mch_id, api_key, api_cert, app_secret, rate, app_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.channelType}, #{item.channelName}, #{item.channelMode}, #{item.channelStatus}, #{item.description},
            #{item.wxAppId}, #{item.mchId}, #{item.apiKey}, #{item.apiCert}, #{item.appSecret}, #{item.rate}, #{item.appId})
        </foreach>
    </insert>

    <select id="findByAppIdAndChannelType" resultType="cn.com.duiba.paycenter.entity.payment.WxPayConfigEntity">
        SELECT
        <include refid="fields" />
        FROM tb_wxpay_config
        WHERE app_id = #{appId} AND channel_type = #{channelType} AND channel_status = 2
        LIMIT 1
    </select>

    <select id="findByAppIdAndChannelTypeAndWxAppId" resultType="cn.com.duiba.paycenter.entity.payment.WxPayConfigEntity">
        SELECT
        <include refid="fields" />
        FROM tb_wxpay_config
        WHERE channel_type = #{channelType} AND channel_status = 2 and wx_app_id = #{wxAppId}
        <if test="appId != null">
            AND app_id = #{appId}
        </if>
        LIMIT 1
    </select>

    <select id="findByWxAppIdAndMchId" resultType="cn.com.duiba.paycenter.entity.payment.WxPayConfigEntity">
        SELECT
        <include refid="fields" />
        FROM tb_wxpay_config
        WHERE wx_app_id = #{wxAppId} AND mch_id = #{mchId} AND channel_status = 2
        LIMIT 1
    </select>
</mapper>
