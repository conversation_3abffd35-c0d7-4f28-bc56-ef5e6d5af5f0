<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.payment.impl.ChannelDaoImpl">
    <sql id="fields">
        channel_type
        ,
        channel_name,
        app_id,
        channel_mode,
        channel_status,
        scheme,
        description,
        fee_rate,
        param
    </sql>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true"
            parameterType="cn.com.duiba.paycenter.entity.payment.ChannelEntity">
        INSERT INTO tb_channel
        (<include refid="fields"/>)
        VALUES
        (#{channelType}, #{channelName}, #{appId}, #{channelMode}, #{channelStatus}, #{scheme}, #{description},
        #{feeRate}, #{param})
    </insert>

    <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO tb_channel
        (<include refid="fields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.channelType},
            #{item.channelName},
            #{item.appId},
            #{item.channelMode},
            #{item.channelStatus},
            #{item.scheme},
            #{item.description},
            #{item.feeRate},
            #{item.param})
        </foreach>
    </insert>

    <update id="update">
        UPDATE tb_channel
        <set>
            <if test="channelMode != null">
                channel_mode = #{channelMode},
            </if>
            <if test="channelStatus != null">
                channel_status = #{channelStatus},
            </if>
            <if test="scheme != null">
                scheme = #{scheme},
            </if>
            <if test="feeRate != null">
                fee_rate = #{feeRate},
            </if>
            <if test="param != null">
                param = #{param},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="findByAppIdAndChannelType" resultType="cn.com.duiba.paycenter.entity.payment.ChannelEntity">
        SELECT id,
        <include refid="fields"/>
        FROM tb_channel
        WHERE app_id = #{appId} AND channel_type = #{channelType}
    </select>

    <select id="listByAppId" resultType="cn.com.duiba.paycenter.entity.payment.ChannelEntity">
        SELECT id,
        <include refid="fields"/>
        FROM tb_channel
        WHERE app_id = #{appId}
    </select>

    <select id="findById" resultType="cn.com.duiba.paycenter.entity.payment.ChannelEntity">
        SELECT id,
        <include refid="fields"/>
        FROM tb_channel
        WHERE id = #{id}
    </select>
</mapper>