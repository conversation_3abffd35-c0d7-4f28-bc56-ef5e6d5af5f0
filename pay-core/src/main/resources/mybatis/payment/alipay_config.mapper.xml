<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.duiba.paycenter.dao.payment.impl.AlipayConfigDaoImpl">
    <sql id="fields">
        id,
        version,
        sign_type,
        partner_id,
        ali_app_id,
        app_id,
        public_key,
        private_key,
        md5_key,
        channel_type,
        channel_name,
        channel_mode,
        channel_status,
        rate,
        description
    </sql>
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_alipay_config
        (version, sign_type, partner_id, ali_app_id, app_id, public_key, private_key, md5_key, channel_type,
         channel_name, channel_mode, channel_status, rate, description)
        VALUES
        (#{version}, #{signType}, #{partnerId}, #{aliAppId}, #{appId}, #{publicKey}, #{privateKey}, #{md5Key},
         #{channelType}, #{channelName}, #{channelMode}, #{channelStatus}, #{rate}, #{description})
    </insert>

    <select id="listByAppId" resultType="cn.com.duiba.paycenter.entity.payment.AlipayConfigEntity">
        SELECT
        <include refid="fields"/>
        FROM tb_alipay_config
        WHERE app_id = #{appId} AND channel_status > 0
    </select>

    <update id="update">
        UPDATE tb_alipay_config
        <set>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="partnerId != null">
                partner_id = #{partnerid},
            </if>
            <if test="aliAppId != null">
                ali_app_id = #{aliAppId},
            </if>
            <if test="publicKey != null">
                public_key = #{publicKey},
            </if>
            <if test="privateKey != null">
                private_key = #{privateKey},
            </if>
            <if test="signType != null">
                sign_type = #{signType},
            </if>
            <if test="md5Key != null">
                md5_key = #{md5Key},
            </if>
            <if test="channelMode != null">
                channel_mode = #{channelMode},
            </if>
            <if test="channelStatus != null">
                channel_status = #{channelStatus},
            </if>
            <if test="rate != null">
                rate = #{rate},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="findById" resultType="cn.com.duiba.paycenter.entity.payment.AlipayConfigEntity">
        SELECT <include refid="fields" />
        FROM tb_alipay_config
        WHERE id = #{id}
    </select>

    <select id="findByAppIdAndChannelType" resultType="cn.com.duiba.paycenter.entity.payment.AlipayConfigEntity">
        SELECT <include refid="fields" />
        FROM tb_alipay_config
        WHERE app_id = #{appId} AND channel_type = #{channelType} AND channel_status = 2
        LIMIT 1
    </select>

    <select id="findByAliAppId" resultType="cn.com.duiba.paycenter.entity.payment.AlipayConfigEntity">
        SELECT
        <include refid="fields"/>
        FROM tb_alipay_config
        WHERE ali_app_id = #{aliAppId}
        LIMIT 1
    </select>
</mapper>
