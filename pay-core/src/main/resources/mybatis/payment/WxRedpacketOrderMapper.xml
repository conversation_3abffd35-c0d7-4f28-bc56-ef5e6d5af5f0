<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.com.duiba.paycenter.dao.payment.impl.WxRedpacketOrderDaoImpl">

    <resultMap type="cn.com.duiba.paycenter.entity.payment.WxRedpacketOrderEntity" id="wxRedpacketOrderMap">
        <result property="id" column="id"/>
        <result property="bizRelationType" column="biz_relation_type"/>
        <result property="bizRelationId" column="biz_relation_id"/>
        <result property="bizOrderNo" column="biz_order_no"/>
        <result property="mchBillno" column="mch_billno"/>
        <result property="sendListid" column="send_listid"/>
        <result property="mchId" column="mch_id"/>
        <result property="wxappid" column="wxappid"/>
        <result property="sendName" column="send_name"/>
        <result property="reOpenid" column="re_openid"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="totalNum" column="total_num"/>
        <result property="wishing" column="wishing"/>
        <result property="clientIp" column="client_ip"/>
        <result property="actName" column="act_name"/>
        <result property="remark" column="remark"/>
        <result property="sceneId" column="scene_id"/>
        <result property="riskInfo" column="risk_info"/>
        <result property="status" column="status"/>
        <result property="sendType" column="send_type"/>
        <result property="hbType" column="hb_type"/>
        <result property="reason" column="reason"/>
        <result property="sendTime" column="send_time"/>
        <result property="refundTime" column="refund_time"/>
        <result property="refundAmount" column="refund_amount"/>
        <result property="rcvTime" column="rcv_time"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="columns">
        id,
        biz_relation_type,
        biz_relation_id,
        biz_order_no,
        mch_billno,
        send_listid,
        mch_id,
        wxappid,
        send_name,
        re_openid,
        total_amount,
        total_num,
        wishing,
        client_ip,
        act_name,
        remark,
        scene_id,
        risk_info,
        status,
        send_type,
        hb_type,
        reason,
        send_time,
        refund_time,
        refund_amount,
        rcv_time,
        gmt_create,
        gmt_modified
    </sql>

    <insert id="save" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.duiba.paycenter.entity.payment.WxRedpacketOrderEntity">
        INSERT INTO payment.tb_wx_redpacket_order(biz_relation_type,biz_relation_id,biz_order_no,mch_billno,send_listid,mch_id,wxappid,send_name,re_openid,total_amount,total_num,wishing,client_ip,act_name,remark,scene_id,risk_info,status,send_type,hb_type,reason,send_time,refund_time,refund_amount,rcv_time)
        VALUES(#{bizRelationType},#{bizRelationId},#{bizOrderNo},#{mchBillno},#{sendListid},#{mchId},#{wxappid},#{sendName},#{reOpenid},#{totalAmount},#{totalNum},#{wishing},#{clientIp},#{actName},#{remark},#{sceneId},#{riskInfo},#{status},#{sendType},#{hbType},#{reason},#{sendTime},#{refundTime},#{refundAmount},#{rcvTime})
    </insert>

    <update id="updateById" parameterType="cn.com.duiba.paycenter.entity.payment.WxRedpacketOrderEntity">
        UPDATE payment.tb_wx_redpacket_order
        <set>
            <if test="bizRelationType != null">
                biz_relation_type = #{bizRelationType},
            </if>
            <if test="bizRelationId != null">
                biz_relation_id = #{bizRelationId},
            </if>
            <if test="bizOrderNo != null">
                biz_order_no = #{bizOrderNo},
            </if>
            <if test="mchBillno != null">
                mch_billno = #{mchBillno},
            </if>
            <if test="sendListid != null">
                send_listid = #{sendListid},
            </if>
            <if test="mchId != null">
                mch_id = #{mchId},
            </if>
            <if test="wxappid != null">
                wxappid = #{wxappid},
            </if>
            <if test="sendName != null">
                send_name = #{sendName},
            </if>
            <if test="reOpenid != null">
                re_openid = #{reOpenid},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount},
            </if>
            <if test="totalNum != null">
                total_num = #{totalNum},
            </if>
            <if test="wishing != null">
                wishing = #{wishing},
            </if>
            <if test="clientIp != null">
                client_ip = #{clientIp},
            </if>
            <if test="actName != null">
                act_name = #{actName},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="sceneId != null">
                scene_id = #{sceneId},
            </if>
            <if test="riskInfo != null">
                risk_info = #{riskInfo},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="sendType != null">
                send_type = #{sendType},
            </if>
            <if test="hbType != null">
                hb_type = #{hbType},
            </if>
            <if test="reason != null">
                reason = #{reason},
            </if>
            <if test="sendTime != null">
                send_time = #{sendTime},
            </if>
            <if test="refundTime != null">
                refund_time = #{refundTime},
            </if>
            <if test="refundAmount != null">
                refund_amount = #{refundAmount},
            </if>
            <if test="rcvTime != null">
                rcv_time = #{rcvTime},
            </if>
        </set>
        WHERE id=#{id}
    </update>

    <select id="getById" resultMap="wxRedpacketOrderMap">
        SELECT
        <include refid="columns"/>
        FROM payment.tb_wx_redpacket_order
        WHERE id = #{id}
    </select>

    <select id="getByMchBillno" resultMap="wxRedpacketOrderMap">
        SELECT
        <include refid="columns"/>
        FROM payment.tb_wx_redpacket_order
        WHERE mch_billno = #{mchBillno}
    </select>

    <select id="getByBizInfo" resultMap="wxRedpacketOrderMap">
        SELECT
        <include refid="columns"/>
        FROM payment.tb_wx_redpacket_order
        WHERE biz_relation_type = #{bizRelationType}
        AND biz_relation_id = #{bizRelationId}
        AND biz_order_no = #{bizOrderNo}
    </select>

    <select id="findByIds" resultMap="wxRedpacketOrderMap">
        SELECT
        <include refid="columns"/>
        FROM payment.tb_wx_redpacket_order
        WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>