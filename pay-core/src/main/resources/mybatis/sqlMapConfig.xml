<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
	<settings>
		<setting name="cacheEnabled" value="false" />
		<setting name="mapUnderscoreToCamelCase" value="true" />
	</settings>
	
	<typeAliases>
	
		<typeAlias alias="AccountDetailEntity" type="cn.com.duiba.paycenter.entity.AccountDetailEntity"/>
		
		<typeAlias alias="AccountPageInfo" type="cn.com.duiba.paycenter.dto.AccountPageInfo"/>
		
		<typeAlias alias="ADeveloperFlowQuery" type="cn.com.duiba.paycenter.dto.ADeveloperFlowQuery"/>
		
		<typeAlias alias="RemainingMoneyEntity" type="cn.com.duiba.paycenter.entity.RemainingMoneyEntity"/>
		
		<typeAlias alias="DuibaRemainingMoneyEntity" type="cn.com.duiba.paycenter.entity.DuibaRemainingMoneyEntity"/>
		
		<typeAlias alias="DuibaDeduceDetailEntity" type="cn.com.duiba.paycenter.entity.DuibaDeduceDetailEntity"/>

		<typeAlias alias="AlipayOrderDetailEntity" type="cn.com.duiba.paycenter.entity.AlipayOrderDetailEntity"/>

		<typeAlias alias="SaasAgentAccountEntity" type="cn.com.duiba.paycenter.entity.SaasAgentAccountEntity"/>

		<typeAlias alias="SaasAgentAccountLogEntity" type="cn.com.duiba.paycenter.entity.SaasAgentAccountLogEntity"/>

		<typeAlias alias="AppRemainingMoneyEntity" type="cn.com.duiba.paycenter.entity.duibaaccount.AppRemainingMoneyEntity"/>

		<typeAlias alias="AppAccountChangeRecordEntity" type="cn.com.duiba.paycenter.entity.duibaaccount.AppAccountChangeRecordEntity"/>

		<typeAlias alias="AppAccountDetailEntity" type="cn.com.duiba.paycenter.entity.duibaaccount.AppAccountDetailEntity"/>

		<typeAlias alias="AppAccountChangeLogEntity" type="cn.com.duiba.paycenter.entity.duibaaccount.AppAccountChangeLogEntity"/>

		<typeAlias alias="AppMarkAccountDetailEntity" type="cn.com.duiba.paycenter.entity.duibaaccount.AppMarkAccountDetailEntity"/>

		<typeAlias alias="AppSettleAccountDetailEntity" type="cn.com.duiba.paycenter.entity.duibaaccount.AppSettleAccountDetailEntity"/>

		<typeAlias alias="SupplierRemainingMoneyEntity" type="cn.com.duiba.paycenter.entity.duibaaccount.SupplierRemainingMoneyEntity"/>

		<typeAlias alias="SupplierAccountDetailEntity" type="cn.com.duiba.paycenter.entity.duibaaccount.SupplierAccountDetailEntity"/>

		<typeAlias alias="SupplierAccountChangeLogEntity" type="cn.com.duiba.paycenter.entity.duibaaccount.SupplierAccountChangeLogEntity"/>

		<typeAlias alias="SupplierAccountChangeRecordEntity" type="cn.com.duiba.paycenter.entity.duibaaccount.SupplierAccountChangeRecordEntity"/>

		<typeAlias alias="SupplierSettleAccountDetailEntity" type="cn.com.duiba.paycenter.entity.duibaaccount.SupplierSettleAccountDetailEntity"/>

		<typeAlias alias="AccountChangeRecordItemOrdersEntity" type="cn.com.duiba.paycenter.entity.credits.AccountChangeRecordItemOrdersEntity"/>

	</typeAliases>
</configuration>