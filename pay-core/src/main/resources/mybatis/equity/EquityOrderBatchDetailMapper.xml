<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.duiba.paycenter.dao.equity.EquityOrderBatchDetailMapper">
  <resultMap id="BaseResultMap" type="cn.com.duiba.paycenter.entity.equity.EquityOrderBatchDetailEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="channel_type" jdbcType="TINYINT" property="channelType" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="out_biz_no" jdbcType="VARCHAR" property="outBizNo" />
    <result column="out_detail_no" jdbcType="VARCHAR" property="outDetailNo" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="resp_serial_no" jdbcType="VARCHAR" property="respSerialNo" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="cn.com.duiba.paycenter.entity.equity.EquityOrderBatchDetailBlobsEntity">
    <result column="req_params" jdbcType="LONGVARCHAR" property="reqParams" />
    <result column="resp_result" jdbcType="LONGVARCHAR" property="respResult" />
    <result column="third_params" jdbcType="LONGVARCHAR" property="thirdParams" />
    <result column="third_result" jdbcType="LONGVARCHAR" property="thirdResult" />
  </resultMap>
  <sql id="Base_Column_List">
    id, channel_type, biz_type, biz_no, out_biz_no, out_detail_no, order_status, 
    resp_serial_no, error_msg, gmt_create, gmt_modified
  </sql>
  <sql id="Blobs_Column_List">
    ,req_params, resp_result, third_params, third_result
  </sql>
  <select id="selectByBiz" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    <include refid="Blobs_Column_List" />
    from tb_equity_order_batch_detail
    where biz_type = #{bizType} and biz_no = #{bizNo} and out_detail_no = #{outDetailNo}
  </select>
  <select id="selectPageByBiz" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_equity_order_batch_detail
    where biz_type = #{bizType} and biz_no = #{bizNo}
    limit #{offset}, #{pageSize}
  </select>
  <select id="selectOutDetailNoListByBiz" resultType="java.lang.String">
    select out_detail_no
    from tb_equity_order_batch_detail
    where biz_type = #{bizType} and biz_no = #{bizNo}
  </select>
  <select id="selectPageByTimeAndStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_equity_order_batch_detail
    where gmt_create >= #{startTime} and gmt_create &lt;= #{endTime}
    <if test="orderStatusList != null and orderStatusList.size > 0">
      and order_status in
      <foreach collection="orderStatusList" item="orderStatus" open="(" separator="," close=")">
        #{orderStatus}
      </foreach>
    </if>
    <if test="lastId != null and lastId > 0">
      and id > #{lastId}
    </if>
    order by id asc
    limit #{pageSize}
  </select>
  <insert id="batchInsert" parameterType="cn.com.duiba.paycenter.entity.equity.EquityOrderBatchDetailBlobsEntity">
    insert into tb_equity_order_batch_detail(channel_type, biz_type, biz_no, out_biz_no, out_detail_no, order_status,
    resp_serial_no, req_params, third_result, error_msg)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.channelType},#{item.bizType},#{item.bizNo},#{item.outBizNo},#{item.outDetailNo},#{item.orderStatus},
      #{item.respSerialNo},#{item.reqParams},#{item.thirdResult},#{item.errorMsg})
    </foreach>
  </insert>
  <update id="update" parameterType="cn.com.duiba.paycenter.entity.equity.EquityOrderBatchDetailBlobsEntity">
    update tb_equity_order_batch_detail
    <set>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=TINYINT},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=TINYINT},
      </if>
      <if test="bizNo != null">
        biz_no = #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="outBizNo != null">
        out_biz_no = #{outBizNo,jdbcType=VARCHAR},
      </if>
      <if test="outDetailNo != null">
        out_detail_no = #{outDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="respSerialNo != null">
        resp_serial_no = #{respSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="reqParams != null">
        req_params,
      </if>
      <if test="respResult != null">
        resp_result,
      </if>
      <if test="thirdParams != null">
        third_params,
      </if>
      <if test="thirdResult != null">
        third_result,
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByUk" parameterType="cn.com.duiba.paycenter.entity.equity.EquityOrderBatchDetailBlobsEntity">
    update tb_equity_order_batch_detail
    <set>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="respSerialNo != null">
        resp_serial_no = #{respSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="reqParams != null">
        req_params,
      </if>
      <if test="respResult != null">
        resp_result,
      </if>
      <if test="thirdParams != null">
        third_params,
      </if>
      <if test="thirdResult != null">
        third_result,
      </if>
    </set>
    where biz_type = #{bizType} and biz_no = #{bizNo} and out_detail_no = #{outDetailNo}
  </update>
</mapper>