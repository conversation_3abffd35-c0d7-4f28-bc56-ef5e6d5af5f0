<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.duiba.paycenter.dao.equity.EquityOrderRecordMapper">
  <resultMap id="BaseResultMap" type="cn.com.duiba.paycenter.entity.equity.EquityOrderRecordEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="record_no" jdbcType="VARCHAR" property="recordNo" />
    <result column="channel_type" jdbcType="TINYINT" property="channelType" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="out_biz_no" jdbcType="VARCHAR" property="outBizNo" />
    <result column="log_code" jdbcType="VARCHAR" property="logCode" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="resp_serial_no" jdbcType="VARCHAR" property="respSerialNo" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="cn.com.duiba.paycenter.entity.equity.EquityOrderRecordDetailEntity">
    <result column="req_params" jdbcType="LONGVARCHAR" property="reqParams" />
    <result column="resp_result" jdbcType="LONGVARCHAR" property="respResult" />
    <result column="third_params" jdbcType="LONGVARCHAR" property="thirdParams" />
    <result column="third_result" jdbcType="LONGVARCHAR" property="thirdResult" />
  </resultMap>
  <sql id="Base_Column_List">
    id, record_no, channel_type, biz_type, biz_no, out_biz_no, log_code, order_status, resp_serial_no, 
    error_msg, gmt_create, gmt_modified
  </sql>
  <sql id="Blobs_Column_List">
    ,req_params, resp_result, third_params, third_result
  </sql>
  <select id="selectByOutBizNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_equity_order_record
    where out_biz_no = #{outBizNo}
    limit 1
  </select>
  <select id="selectByRecordNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_equity_order_record
    where record_no = #{recordNo}
  </select>
  <select id="selectDetailByRecordNo" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    <include refid="Blobs_Column_List" />
    from tb_equity_order_record
    where record_no = #{recordNo}
  </select>
  <select id="selectLastByBiz" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_equity_order_record
    where biz_type=#{bizType} and biz_no=#{bizNo}
    order by id desc
    limit 1
  </select>
  <select id="selectReqParamsByNo" resultType="java.lang.String">
    select req_params
    from tb_equity_order_record
    where record_no = #{recordNo}
  </select>
  <insert id="insert" parameterType="cn.com.duiba.paycenter.entity.equity.EquityOrderRecordDetailEntity">
    insert into tb_equity_order_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recordNo != null">
        record_no,
      </if>
      <if test="channelType != null">
        channel_type,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="bizNo != null">
        biz_no,
      </if>
      <if test="outBizNo != null">
        out_biz_no,
      </if>
      <if test="logCode != null">
        log_code,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="respSerialNo != null">
        resp_serial_no,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
      <if test="reqParams != null">
        req_params,
      </if>
      <if test="respResult != null">
        resp_result,
      </if>
      <if test="thirdParams != null">
        third_params,
      </if>
      <if test="thirdResult != null">
        third_result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recordNo != null">
        #{recordNo,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        #{channelType,jdbcType=TINYINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=TINYINT},
      </if>
      <if test="bizNo != null">
        #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="outBizNo != null">
        #{outBizNo,jdbcType=VARCHAR},
      </if>
      <if test="logCode != null">
        #{logCode,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="respSerialNo != null">
        #{respSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="reqParams != null">
        #{reqParams,jdbcType=LONGVARCHAR},
      </if>
      <if test="respResult != null">
        #{respResult,jdbcType=LONGVARCHAR},
      </if>
      <if test="thirdParams != null">
        #{thirdParams,jdbcType=LONGVARCHAR},
      </if>
      <if test="thirdResult != null">
        #{thirdResult,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.com.duiba.paycenter.entity.equity.EquityOrderRecordDetailEntity">
    update tb_equity_order_record
    <set>
      <if test="recordNo != null">
        record_no = #{recordNo,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=TINYINT},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=TINYINT},
      </if>
      <if test="bizNo != null">
        biz_no = #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="outBizNo != null">
        out_biz_no = #{outBizNo,jdbcType=VARCHAR},
      </if>
      <if test="logCode != null">
        log_code = #{logCode,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="respSerialNo != null">
        resp_serial_no = #{respSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="reqParams != null">
        req_params = #{reqParams,jdbcType=LONGVARCHAR},
      </if>
      <if test="respResult != null">
        resp_result = #{respResult,jdbcType=LONGVARCHAR},
      </if>
      <if test="thirdParams != null">
        third_params = #{thirdParams,jdbcType=LONGVARCHAR},
      </if>
      <if test="thirdResult != null">
        third_result = #{thirdResult,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateProcessingStatus">
    update tb_equity_order_record
    set order_status = 2
    where record_no = #{recordNo} and order_status=#{beforeStatus}
  </update>
  <update id="updateResult">
    update tb_equity_order_record
    <set>
      order_status = #{entity.orderStatus},
      <if test="entity.respSerialNo != null">
        resp_serial_no = #{entity.respSerialNo},
      </if>
      <if test="entity.respResult != null">
        resp_result = #{entity.respResult},
      </if>
      <if test="entity.thirdParams != null">
        third_params = #{entity.thirdParams},
      </if>
      <if test="entity.thirdResult != null">
        third_result = #{entity.thirdResult},
      </if>
      <if test="entity.errorMsg != null">
        error_msg = #{entity.errorMsg},
      </if>
    </set>
    where record_no=#{entity.recordNo} and order_status=#{beforeStatus}
  </update>
</mapper>