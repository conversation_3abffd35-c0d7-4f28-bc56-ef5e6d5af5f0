<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.duiba.paycenter.dao.equity.EquityOrderMapper">
  <resultMap id="BaseResultMap" type="cn.com.duiba.paycenter.entity.equity.EquityOrderEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="channel_type" jdbcType="TINYINT" property="channelType" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="out_biz_no" jdbcType="VARCHAR" property="outBizNo" />
    <result column="log_code" jdbcType="VARCHAR" property="logCode" />
    <result column="notify_type" jdbcType="TINYINT" property="notifyType" />
    <result column="mq_topic" jdbcType="VARCHAR" property="mqTopic" />
    <result column="mq_tag" jdbcType="VARCHAR" property="mqTag" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="retry_count" jdbcType="TINYINT" property="retryCount" />
    <result column="record_no" jdbcType="VARCHAR" property="recordNo" />
    <result column="resp_serial_no" jdbcType="VARCHAR" property="respSerialNo" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="cn.com.duiba.paycenter.entity.equity.EquityOrderDetailEntity">
    <result column="req_params" jdbcType="LONGVARCHAR" property="reqParams" />
    <result column="resp_result" jdbcType="LONGVARCHAR" property="respResult" />
    <result column="third_params" jdbcType="LONGVARCHAR" property="thirdParams" />
    <result column="third_result" jdbcType="LONGVARCHAR" property="thirdResult" />
  </resultMap>
  <sql id="Base_Column_List">
    id, channel_type, biz_type, biz_no, out_biz_no, log_code, notify_type, mq_topic, mq_tag, order_status, retry_count, 
    record_no, resp_serial_no, error_msg, gmt_create, gmt_modified
  </sql>
  <sql id="Blobs_Column_List">
    ,req_params, resp_result, third_params, third_result
  </sql>
  <select id="selectByBiz" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_equity_order
    where biz_type=#{bizType} and biz_no=#{bizNo}
  </select>
  <select id="selectDetailByBiz" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    <include refid="Blobs_Column_List" />
    from tb_equity_order
    where biz_type=#{bizType} and biz_no=#{bizNo}
  </select>
  <select id="selectReqParamsByBiz" resultType="java.lang.String">
    select req_params
    from tb_equity_order
    where biz_type=#{bizType} and biz_no=#{bizNo}
  </select>
  <select id="selectRespResultByBiz" resultType="java.lang.String">
    select resp_result
    from tb_equity_order
    where biz_type=#{bizType} and biz_no=#{bizNo}
  </select>
  <select id="selectPageByTimeAndStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_equity_order
    where gmt_create >= #{startTime} and gmt_create &lt;= #{endTime}
    <if test="orderStatusList != null and orderStatusList.size > 0">
      and order_status in
      <foreach collection="orderStatusList" item="orderStatus" open="(" separator="," close=")">
        #{orderStatus}
      </foreach>
    </if>
    <if test="lastId != null and lastId > 0">
      and id > #{lastId}
    </if>
    order by id asc
    limit #{pageSize}
  </select>
  <select id="selectByBizList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_equity_order
    where biz_type in 
    <foreach collection="bizTypeList" item="bizType" open="(" separator="," close=")">
      #{bizType}
    </foreach>
    and biz_no in 
    <foreach collection="bizNoList" item="bizNo" open="(" separator="," close=")">
      #{bizNo}
    </foreach>
  </select>
  <select id="selectById" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_equity_order
    where id = #{id}
  </select>
  <insert id="insert" parameterType="cn.com.duiba.paycenter.entity.equity.EquityOrderDetailEntity">
    insert into tb_equity_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="channelType != null">
        channel_type,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="bizNo != null">
        biz_no,
      </if>
      <if test="outBizNo != null">
        out_biz_no,
      </if>
      <if test="logCode != null">
        log_code,
      </if>
      <if test="notifyType != null">
        notify_type,
      </if>
      <if test="mqTopic != null">
        mq_topic,
      </if>
      <if test="mqTag != null">
        mq_tag,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="retryCount != null">
        retry_count,
      </if>
      <if test="recordNo != null">
        record_no,
      </if>
      <if test="respSerialNo != null">
        resp_serial_no,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
      <if test="reqParams != null">
        req_params,
      </if>
      <if test="respResult != null">
        resp_result,
      </if>
      <if test="thirdParams != null">
        third_params,
      </if>
      <if test="thirdResult != null">
        third_result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="channelType != null">
        #{channelType,jdbcType=TINYINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=TINYINT},
      </if>
      <if test="bizNo != null">
        #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="outBizNo != null">
        #{outBizNo,jdbcType=VARCHAR},
      </if>
      <if test="logCode != null">
        #{logCode,jdbcType=VARCHAR},
      </if>
      <if test="notifyType != null">
        #{notifyType,jdbcType=TINYINT},
      </if>
      <if test="mqTopic != null">
        #{mqTopic,jdbcType=VARCHAR},
      </if>
      <if test="mqTag != null">
        #{mqTag,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="retryCount != null">
        #{retryCount,jdbcType=TINYINT},
      </if>
      <if test="recordNo != null">
        #{recordNo,jdbcType=VARCHAR},
      </if>
      <if test="respSerialNo != null">
        #{respSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="reqParams != null">
        #{reqParams,jdbcType=LONGVARCHAR},
      </if>
      <if test="respResult != null">
        #{respResult,jdbcType=LONGVARCHAR},
      </if>
      <if test="thirdParams != null">
        #{thirdParams,jdbcType=LONGVARCHAR},
      </if>
      <if test="thirdResult != null">
        #{thirdResult,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.com.duiba.paycenter.entity.equity.EquityOrderDetailEntity">
    update tb_equity_order
    <set>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=TINYINT},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=TINYINT},
      </if>
      <if test="bizNo != null">
        biz_no = #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="outBizNo != null">
        out_biz_no = #{outBizNo,jdbcType=VARCHAR},
      </if>
      <if test="logCode != null">
        log_code = #{logCode,jdbcType=VARCHAR},
      </if>
      <if test="notifyType != null">
        notify_type = #{notifyType,jdbcType=TINYINT},
      </if>
      <if test="mqTopic != null">
        mq_topic = #{mqTopic,jdbcType=VARCHAR},
      </if>
      <if test="mqTag != null">
        mq_tag = #{mqTag,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="retryCount != null">
        retry_count = #{retryCount,jdbcType=TINYINT},
      </if>
      <if test="recordNo != null">
        record_no = #{recordNo,jdbcType=VARCHAR},
      </if>
      <if test="respSerialNo != null">
        resp_serial_no = #{respSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="reqParams != null">
        req_params = #{reqParams,jdbcType=LONGVARCHAR},
      </if>
      <if test="respResult != null">
        resp_result = #{respResult,jdbcType=LONGVARCHAR},
      </if>
      <if test="thirdParams != null">
        third_params = #{thirdParams,jdbcType=LONGVARCHAR},
      </if>
      <if test="thirdResult != null">
        third_result = #{thirdResult,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateProcessingStatus">
    update tb_equity_order
    <set>
      order_status = 2,
      out_biz_no = #{entity.outBizNo},
      record_no = #{entity.recordNo},
      <if test="entity.reqParams != null">
        req_params = #{entity.reqParams},
      </if>
      <if test="entity.retryCount != null">
        retry_count = #{entity.retryCount},
      </if>
      <if test="entity.notifyType != null">
        notify_type = #{entity.notifyType,jdbcType=TINYINT},
      </if>
      <if test="entity.mqTopic != null">
        mq_topic = #{entity.mqTopic,jdbcType=VARCHAR},
      </if>
      <if test="entity.mqTag != null">
        mq_tag = #{entity.mqTag,jdbcType=VARCHAR},
      </if>
    </set>
    where biz_type=#{entity.bizType} and biz_no=#{entity.bizNo} and order_status=#{beforeStatus}
  </update>
  <update id="updateResult">
    update tb_equity_order
    <set>
      order_status = #{entity.orderStatus},
      <if test="entity.respSerialNo != null">
        resp_serial_no = #{entity.respSerialNo},
      </if>
      <if test="entity.respResult != null">
        resp_result = #{entity.respResult},
      </if>
      <if test="entity.thirdParams != null">
        third_params = #{entity.thirdParams},
      </if>
      <if test="entity.thirdResult != null">
        third_result = #{entity.thirdResult},
      </if>
      <if test="entity.errorMsg != null">
        error_msg = #{entity.errorMsg},
      </if>
    </set>
    where biz_type=#{entity.bizType} and biz_no=#{entity.bizNo} and order_status=#{beforeStatus}
  </update>
</mapper>