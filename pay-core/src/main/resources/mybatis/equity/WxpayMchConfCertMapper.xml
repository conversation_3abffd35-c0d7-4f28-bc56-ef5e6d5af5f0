<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.duiba.paycenter.dao.equity.WxpayMchConfCertMapper">
  <resultMap id="BaseResultMap" type="cn.com.duiba.paycenter.entity.equity.WxpayMchConfCertEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mch_id" jdbcType="VARCHAR" property="mchId" />
    <result column="cert_type" jdbcType="TINYINT" property="certType" />
    <result column="cert_serial_number" jdbcType="VARCHAR" property="certSerialNumber" />
    <result column="private_key" jdbcType="VARCHAR" property="privateKey" />
    <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="cn.com.duiba.paycenter.entity.equity.WxpayMchConfCertEntity">
    <result column="pem_cert" jdbcType="LONGVARCHAR" property="pemCert" />
  </resultMap>
  <sql id="Base_Column_List">
    id, mch_id, cert_type, cert_serial_number, private_key, effective_time, expire_time, 
    gmt_create, gmt_modified
  </sql>
  <sql id="Blobs_Column_List">
    , pem_cert
  </sql>
  <select id="selectById" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_wxpay_mch_conf_cert
    where id = #{id}
  </select>
  <select id="selectByMchType" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    <include refid="Blobs_Column_List" />
    from tb_wxpay_mch_conf_cert
    where mch_id = #{mchId} and cert_type = #{certType}
    <if test="certType != 1">
      and effective_time &lt; now()
      and expire_time > now()
    </if>
  </select>
  <select id="selectByUk" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    <include refid="Blobs_Column_List" />
    from tb_wxpay_mch_conf_cert
    where cert_serial_number = #{certSerialNumber}
  </select>
  <insert id="insert" parameterType="cn.com.duiba.paycenter.entity.equity.WxpayMchConfCertEntity">
    insert into tb_wxpay_mch_conf_cert
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mchId != null">
        mch_id,
      </if>
      <if test="certType != null">
        cert_type,
      </if>
      <if test="certSerialNumber != null">
        cert_serial_number,
      </if>
      <if test="privateKey != null">
        private_key,
      </if>
      <if test="effectiveTime != null">
        effective_time,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="pemCert != null">
        pem_cert,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mchId != null">
        #{mchId,jdbcType=VARCHAR},
      </if>
      <if test="certType != null">
        #{certType,jdbcType=TINYINT},
      </if>
      <if test="certSerialNumber != null">
        #{certSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="privateKey != null">
        #{privateKey,jdbcType=VARCHAR},
      </if>
      <if test="effectiveTime != null">
        #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pemCert != null">
        #{pemCert,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.com.duiba.paycenter.entity.equity.WxpayMchConfCertEntity">
    update tb_wxpay_mch_conf_cert
    <set>
      <if test="mchId != null">
        mch_id = #{mchId,jdbcType=VARCHAR},
      </if>
      <if test="certType != null">
        cert_type = #{certType,jdbcType=TINYINT},
      </if>
      <if test="certSerialNumber != null">
        cert_serial_number = #{certSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="privateKey != null">
        private_key = #{privateKey,jdbcType=VARCHAR},
      </if>
      <if test="effectiveTime != null">
        effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pemCert != null">
        pem_cert = #{pemCert,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>