<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.duiba.paycenter.dao.equity.WxpayMchConfMapper">
  <resultMap id="BaseResultMap" type="cn.com.duiba.paycenter.entity.equity.WxpayMchConfEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mch_id" jdbcType="VARCHAR" property="mchId" />
    <result column="mch_name" jdbcType="VARCHAR" property="mchName" />
    <result column="api_key" jdbcType="VARCHAR" property="apiKey" />
    <result column="api_v3_key" jdbcType="VARCHAR" property="apiV3Key" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="cn.com.duiba.paycenter.entity.equity.WxpayMchConfEntity">
    <result column="api_cert" jdbcType="LONGVARCHAR" property="apiCert" />
  </resultMap>
  <sql id="Base_Column_List">
    id, mch_id, mch_name, api_key, api_v3_key, gmt_create, 
    gmt_modified
  </sql>
  <sql id="Blobs_Column_List">
    , api_cert
  </sql>
  <select id="selectById" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_wxpay_mch_conf
    where id = #{id}
  </select>
  <select id="selectByMchId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    <include refid="Blobs_Column_List" />
    from tb_wxpay_mch_conf
    where mch_id = #{mchId}
  </select>
  <select id="selectApiKeyByMchId" resultType="java.lang.String">
    select api_key
    from tb_wxpay_mch_conf
    where mch_id = #{mchId}
  </select>
  <select id="selectApiCertByMchId" resultType="java.lang.String">
    select api_cert
    from tb_wxpay_mch_conf
    where mch_id = #{mchId}
  </select>
  <select id="selectAllMchName" resultType="cn.com.duiba.paycenter.bean.equity.WxpayMchNameBean">
    select mch_id as mchId, mch_name as mchName
    from tb_wxpay_mch_conf
  </select>

  <insert id="insert" parameterType="cn.com.duiba.paycenter.entity.equity.WxpayMchConfEntity"
          keyProperty="id"
          useGeneratedKeys="true">
    insert into tb_wxpay_mch_conf
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mchId != null">
        mch_id,
      </if>
      <if test="mchName != null">
        mch_name,
      </if>
      <if test="apiKey != null">
        api_key,
      </if>
      <if test="apiV3Key != null">
        api_v3_key,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mchId != null">
        #{mchId},
      </if>
      <if test="mchName != null">
        #{mchName},
      </if>
      <if test="apiKey != null">
        #{apiKey},
      </if>
      <if test="apiV3Key != null">
        #{apiV3Key},
      </if>

    </trim>
  </insert>
</mapper>