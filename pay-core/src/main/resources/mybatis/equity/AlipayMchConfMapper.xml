<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.duiba.paycenter.dao.equity.AlipayMchConfMapper">
  <resultMap id="BaseResultMap" type="cn.com.duiba.paycenter.entity.equity.AlipayMchConfEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="public_key" jdbcType="VARCHAR" property="publicKey" />
    <result column="alipay_public_key" jdbcType="VARCHAR" property="alipayPublicKey" />
    <result column="private_key" jdbcType="VARCHAR" property="privateKey" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="cn.com.duiba.paycenter.entity.equity.AlipayMchConfDetailEntity">
    <result column="app_cert" jdbcType="LONGVARCHAR" property="appCert" />
    <result column="alipay_cert" jdbcType="LONGVARCHAR" property="alipayCert" />
    <result column="alipay_root_cert" jdbcType="LONGVARCHAR" property="alipayRootCert" />
  </resultMap>
  <sql id="Base_Column_List">
    id, app_id, app_name, public_key, alipay_public_key, private_key, gmt_create, gmt_modified
  </sql>
  <sql id="Base_Column_Blobs_List">
    , app_cert, alipay_cert, alipay_root_cert
  </sql>
  <select id="selectByAppId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_alipay_mch_conf
    where app_id = #{appId}
  </select>
  <select id="selectDetailByAppId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    <include refid="Base_Column_Blobs_List" />
    from tb_alipay_mch_conf
    where app_id = #{appId}
  </select>
  <insert id="insert" parameterType="cn.com.duiba.paycenter.entity.equity.AlipayMchConfEntity">
    insert into tb_alipay_mch_conf
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        app_id,
      </if>
      <if test="appName != null">
        app_name,
      </if>
      <if test="publicKey != null">
        public_key,
      </if>
      <if test="alipayPublicKey != null">
        alipay_public_key,
      </if>
      <if test="privateKey != null">
        private_key,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="appName != null">
        #{appName,jdbcType=VARCHAR},
      </if>
      <if test="publicKey != null">
        #{publicKey,jdbcType=VARCHAR},
      </if>
      <if test="alipayPublicKey != null">
        #{alipayPublicKey,jdbcType=VARCHAR},
      </if>
      <if test="privateKey != null">
        #{privateKey,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.com.duiba.paycenter.entity.equity.AlipayMchConfEntity">
    update tb_alipay_mch_conf
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="appName != null">
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="publicKey != null">
        public_key = #{publicKey,jdbcType=VARCHAR},
      </if>
      <if test="alipayPublicKey != null">
        alipay_public_key = #{alipayPublicKey,jdbcType=VARCHAR},
      </if>
      <if test="privateKey != null">
        private_key = #{privateKey,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <delete id="delete">
    delete from tb_alipay_mch_conf where id = #{id}
  </delete>
</mapper>