<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.duibaaccount.impl.SupplierRemainingMoneyDaoImpl">
    
    <!-- ======================= special queries ============================== -->
    <insert id="insert" parameterType="SupplierRemainingMoneyEntity" useGeneratedKeys="true" keyProperty="id">
		insert into tb_supplier_remaining_money
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="money != null">money,</if>
			<if test="version != null">version,</if>
			<if test="supplierId != null">supplier_id,</if>
			<if test="accountType != null">account_type,</if>
			<if test="sign != null">sign,</if>
            <if test="gmtModified != null">gmt_modified,</if>
            <if test="gmtCreate != null">gmt_create,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="money != null">#{money},</if>
			<if test="version != null">#{version},</if>
			<if test="supplierId != null">#{supplierId},</if>
			<if test="accountType != null">#{accountType},</if>
			<if test="sign != null">#{sign},</if>
            <if test="gmtModified != null">#{gmtModified},</if>
			<if test="gmtCreate != null">#{gmtCreate},</if>
		</trim>
	</insert>
	
	<select id="findExist" resultType="int">
		select
		count(id)
		from tb_supplier_remaining_money
		where supplier_id = #{supplierId} and account_type = #{accountType}
	</select>

    <update id="reduceMoney">
        update tb_supplier_remaining_money set money=(money-#{money}) , version=(version+1) , sign=#{sign}, gmt_modified=#{modified} where id=#{id} and version=#{version}
    </update>
    
    <update id="addMoney">
        update tb_supplier_remaining_money set money=(money+#{money}) , version=(version+1) , sign=#{sign}, gmt_modified=#{modified} where id=#{id} and version=#{version}
    </update>
    
	<select id="findAccount4update" resultType="SupplierRemainingMoneyEntity">
		select
		<include refid="fields" />
		from tb_supplier_remaining_money
		where supplier_id = #{supplierId} and account_type = #{accountType} for update
	</select>

    <select id="findMoney" resultType="long">
        select money from tb_supplier_remaining_money
        where supplier_id = #{supplierId} and account_type = #{accountType}
    </select>

	<select id="find" resultType="SupplierRemainingMoneyEntity">
        select <include refid="fields"/> from tb_supplier_remaining_money
        where supplier_id = #{supplierId} and account_type = #{accountType}
    </select>

	<sql id="fields">
		id as id,
		money as money,
		supplier_id as supplierId,
		account_type as accountType,
		version as version,
		sign as sign,
		gmt_create	as gmtCreate,
		gmt_modified as gmtModified
	</sql>

</mapper>