<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.duibaaccount.impl.SupplierAccountChangeLogDaoImpl">
	
	<insert id="insert" parameterType="SupplierAccountChangeLogEntity" useGeneratedKeys="true" keyProperty="id">
		insert into tb_supplier_account_change_log
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="supplierId != null">supplier_id,</if>
			<if test="relationType != null">relation_type,</if>
			<if test="relationId != null">relation_id,</if>
			<if test="changeMoney != null">change_money,</if>
			<if test="changeType != null">change_type,</if>
			<if test="beforeBalance != null">before_balance,</if>
			<if test="afterBalance != null">after_balance,</if>
			<if test="changeStatus != null">change_status,</if>
			<if test="failReason != null">fail_reason,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="supplierId != null">#{supplierId},</if>
			<if test="relationType != null">#{relationType},</if>
			<if test="relationId != null">#{relationId},</if>
			<if test="changeMoney != null">#{changeMoney},</if>
			<if test="changeType != null">#{changeType},</if>
			<if test="beforeBalance != null">#{beforeBalance},</if>
			<if test="afterBalance != null">#{afterBalance},</if>
			<if test="changeStatus != null">#{changeStatus},</if>
			<if test="failReason != null">#{failReason},</if>
		</trim>
	</insert>

</mapper>