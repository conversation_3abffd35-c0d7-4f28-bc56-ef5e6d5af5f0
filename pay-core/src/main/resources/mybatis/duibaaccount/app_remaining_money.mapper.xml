<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.duibaaccount.impl.AppRemainingMoneyDaoImpl">
    
    <!-- ======================= special queries ============================== -->
    <insert id="insert" parameterType="AppRemainingMoneyEntity" useGeneratedKeys="true" keyProperty="id">
		insert into tb_app_remaining_money
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="money != null">money,</if>
			<if test="version != null">version,</if>
			<if test="developerId != null">developer_id,</if>
			<if test="appId != null">app_id,</if>
			<if test="accountType != null">account_type,</if>
			<if test="sign != null">sign,</if>
            <if test="dbmodifiedStr != null and dbmodifiedStr !=''">gmt_modified,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="money != null">#{money},</if>
			<if test="version != null">#{version},</if>
			<if test="developerId != null">#{developerId},</if>
			<if test="appId != null">#{appId},</if>
			<if test="accountType != null">#{accountType},</if>
			<if test="sign != null">#{sign},</if>
            <if test="dbmodifiedStr != null and dbmodifiedStr !=''">#{dbmodifiedStr},</if>
		</trim>
	</insert>
	
	<select id="findExist" resultType="int">
		select
		count(id)
		from tb_app_remaining_money
		where app_id = #{appId} and account_type = #{accountType}
	</select>

    <select id="findExistDeveloperAccount" resultType="int">
		select
		count(id)
		from tb_app_remaining_money
		where developer_id = #{developerId} and account_type = #{accountType}
	</select>
    
    <update id="reduceMoney">
        update tb_app_remaining_money set money=(money-#{money}) , version=(version+1) , sign=#{sign}, gmt_modified=#{modified} where id=#{id} and version=#{version}
    </update>
    
    <update id="addMoney">
        update tb_app_remaining_money set money=(money+#{money}) , version=(version+1) , sign=#{sign}, gmt_modified=#{modified} where id=#{id} and version=#{version}
    </update>
    
	<select id="findAccount4update" resultType="AppRemainingMoneyEntity">
		select
		<include refid="fields" />
		from tb_app_remaining_money
		where app_id = #{appId} and account_type = #{accountType} for update
	</select>

    <select id="findMoney" resultType="long">
        select money from tb_app_remaining_money
        where app_id = #{appId} and account_type = #{accountType}
    </select>

	<select id="findByAppIdsAndType" resultType="AppRemainingMoneyEntity">
		select <include refid="fields"/>
		from tb_app_remaining_money
		where app_id in
			<foreach collection="appIds" item="item" index="index" open="(" separator="," close=")">
				#{item}
			</foreach>
			and account_type = #{accountType}
	</select>

    <select id="findAllDeveloperId" resultType="long">
        select distinct(developer_id) from tb_app_remaining_money
    </select>
    
    <select id="findDeveloperMoney" resultType="cn.com.duiba.paycenter.dto.DeveloperMoneyInfoDto">
        select sum(money) as money,developer_id as developerId
        from tb_app_remaining_money
        where developer_id in
        <foreach collection="developerIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and account_type in
        <foreach collection="accountTypes" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY developer_id
    </select>

	<select id="findDevMoney" resultType="long">
		select sum(money) from tb_app_remaining_money
		where developer_id = #{developerId} and account_type = #{accountType}
	</select>
	
	<sql id="fields">
		id as id,
		money as money,
		developer_id as developerId,
		app_id as appId,
		account_type as accountType,
		version as version,
		sign as sign,
		gmt_create	as gmtCreate,
		gmt_modified as gmtModified
	</sql>

</mapper>