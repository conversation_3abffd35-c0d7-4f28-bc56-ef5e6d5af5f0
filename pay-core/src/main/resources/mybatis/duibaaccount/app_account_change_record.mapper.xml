<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.duibaaccount.impl.AppAccountChangeRecordDaoImpl">

	<select id="findCountExist" resultType="int">
		select count(id) from tb_app_account_change_record where
		relation_id = #{relationId} and relation_type = #{relationType} and change_type = #{changeType}
	</select>
	
	<insert id="insert" parameterType="AppAccountChangeRecordEntity" useGeneratedKeys="true" keyProperty="id">
		insert into tb_app_account_change_record
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="developerId != null">developer_id,</if>
			<if test="appId != null">app_id,</if>
			<if test="relationType != null">relation_type,</if>
			<if test="relationId != null">relation_id,</if>
			<if test="changeMoney != null">change_money,</if>
			<if test="changeType != null">change_type,</if>
			<if test="beforeBalance != null">before_balance,</if>
			<if test="afterBalance != null">after_balance,</if>
			<if test="gmtCreate != null">gmt_create,</if>
			<if test="gmtModified != null">gmt_modified,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="developerId != null">#{developerId},</if>
			<if test="appId != null">#{appId},</if>
			<if test="relationType != null">#{relationType},</if>
			<if test="relationId != null">#{relationId},</if>
			<if test="changeMoney != null">#{changeMoney},</if>
			<if test="changeType != null">#{changeType},</if>
			<if test="beforeBalance != null">#{beforeBalance},</if>
			<if test="afterBalance != null">#{afterBalance},</if>
			<if test="gmtCreate != null">#{gmtCreate},</if>
			<if test="gmtModified != null">#{gmtModified},</if>
		</trim>
	</insert>


</mapper>