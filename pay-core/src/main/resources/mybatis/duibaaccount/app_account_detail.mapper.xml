<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="cn.com.duiba.paycenter.dao.duibaaccount.impl.AppAccountDetailDaoImpl">


	<insert id="insert" parameterType="AppAccountDetailEntity" useGeneratedKeys="true" keyProperty="id">
		insert into tb_app_account_detail
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="developerId != null">developer_id,</if>
			<if test="appId != null">app_id,</if>
			<if test="balance != null">balance,</if>
			<if test="memo != null">memo,</if>
			<if test="changeMoney != null">change_money,</if>
			<if test="operatorId != null">operator_id,</if>
			<if test="relationType != null">relation_type,</if>
			<if test="relationId != null">relation_id,</if>
			<if test="changeType != null">change_type,</if>
			<if test="transferOut != null">transfer_out,</if>
			<if test="transferIn != null">transfer_in,</if>
			<if test="gmtCreate != null">gmt_create,</if>
			<if test="gmtModified != null">gmt_modified,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="developerId != null">#{developerId},</if>
			<if test="appId != null">#{appId},</if>
			<if test="balance != null">#{balance},</if>
			<if test="memo != null">#{memo},</if>
			<if test="changeMoney != null">#{changeMoney},</if>
			<if test="operatorId != null">#{operatorId},</if>
			<if test="relationType != null">#{relationType},</if>
			<if test="relationId != null">#{relationId},</if>
			<if test="changeType != null">#{changeType},</if>
			<if test="transferOut != null">#{transferOut},</if>
			<if test="transferIn != null">#{transferIn},</if>
			<if test="gmtCreate != null">#{gmtCreate},</if>
			<if test="gmtModified != null">#{gmtModified},</if>
		</trim>
	</insert>

	<select id="find4page" resultType="AppAccountDetailEntity" parameterType="cn.com.duiba.paycenter.params.duibaaccount.AppAccountDetailQryParams">
		select <include refid="fields"/>
		from tb_app_account_detail
        where app_id = #{appId}
          <if test="startTime != null">
			  and gmt_create >= #{startTime}
		  </if>
          <if test="endTime != null">
			  and #{endTime} >= gmt_create
		  </if>
          <if test="relationType != null">
			  and relation_type = #{relationType}
		  </if>
		  <if test="relationId != null and relationId !=''">
			  and relation_id = #{relationId}
		  </if>
        order by gmt_create desc
		limit #{offset},#{max}
	</select>

	<select id="count4page" resultType="long" parameterType="cn.com.duiba.paycenter.params.duibaaccount.AppAccountDetailQryParams">
		select count(id)
		from tb_app_account_detail
		where app_id = #{appId}
		<if test="startTime != null">
			and gmt_create >= #{startTime}
		</if>
		<if test="endTime != null">
			and #{endTime} >= gmt_create
		</if>
		<if test="relationType != null">
			and relation_type = #{relationType}
		</if>
		<if test="relationId != null and relationId !=''">
			and relation_id = #{relationId}
		</if>
	</select>
	
	<select id="findByIdAndAppId" resultType="AppAccountDetailEntity">
		select <include refid="fields"/>
		from tb_app_account_detail where id = #{id} and app_id = #{appId}
	</select>

	<select id="findByRelationAndAppId" resultType="AppAccountDetailEntity">
		select <include refid="fields"/>
		from tb_app_account_detail
		where relation_id = #{relationId} and relation_type in
		<foreach collection="relationTypes" item="item" index="index" open="(" separator="," close=")">
			#{item}
		</foreach>
		and app_id = #{appId}
	</select>

	<select id="find4Export" resultType="cn.com.duiba.paycenter.dto.duibaaccount.AppAccountDetailPageDto" parameterType="cn.com.duiba.paycenter.params.duibaaccount.AppAccountDetailQryParams">
		select <include refid="fieldsForExport"/>
		from tb_app_account_detail
		where app_id = #{appId}
		<if test="startTime != null">
			and gmt_create >= #{startTime}
		</if>
		<if test="endTime != null">
			and #{endTime} >= gmt_create
		</if>
		<if test="relationType != null">
			and relation_type = #{relationType}
		</if>
		<if test="relationId != null and relationId !=''">
			and relation_id = #{relationId}
		</if>
		order by id
		limit #{offset},#{max}
	</select>

	<select id="findPage4recharge" resultType="cn.com.duiba.paycenter.dto.duibaaccount.RechargeDetailPageDto"
			parameterType="cn.com.duiba.paycenter.params.duibaaccount.RechargeDetailQryParams">
		select
		gmt_create as gmtCreate,
		app_id as appId,
		change_money as changeMoney,
		relation_type as relationType,
		memo,developer_id as developerId
		from tb_app_account_detail
		<where>
			relation_type in (1,6,7)
			<if test="relationType != null">
				and relation_type = #{relationType}
			</if>
			<if test="startDate !=null and endDate != null">
				and gmt_create between #{startDate} and #{endDate}
			</if>
			<if test="appIdList != null and appIdList.size() > 0">
				and app_id in
				<foreach collection="appIdList" item="item" index="index" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
		order by id desc
		limit #{offset},#{max}
	</select>

	<select id="findCount4recharge" resultType="integer" parameterType="cn.com.duiba.paycenter.params.duibaaccount.RechargeDetailQryParams">
		select
		count(id)
		from tb_app_account_detail
		<where>
			relation_type in (1,6,7)
			<if test="relationType != null">
				and relation_type = #{relationType}
			</if>
			<if test="startDate !=null and endDate != null">
				and gmt_create between #{startDate} and #{endDate}
			</if>
			<if test="appIdList != null and appIdList.size() > 0">
				and app_id in
				<foreach collection="appIdList" item="item" index="index" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
	</select>

	<sql id="fields">
		id,
		developer_id as developerId,
		app_id as appId,
		balance,
		memo,
		change_money as changeMoney,
		operator_id as operatorId,
		relation_id as relationId,
		relation_type as relationType,
		change_type as changeType,
		transfer_out as transferOut,
		transfer_in as transferIn,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
	</sql>

	<sql id="fieldsForExport">
		id,
		developer_id as developerId,
		app_id as appId,
		balance as afterBalance,
		memo,
		change_money as changeMoney,
		operator_id as operatorId,
		relation_id as relationId,
		relation_type as relationType,
		change_type as changeType,
		transfer_out as transferOut,
		transfer_in as transferIn,
		gmt_create as gmtCreate,
		gmt_modified as gmtModified
	</sql>

</mapper>