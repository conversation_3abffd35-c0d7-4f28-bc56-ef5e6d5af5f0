<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans.xsd"
	   default-autowire="byName">

	<bean id="payFundSqlSessionTemplate" class="org.mybatis.spring.SqlSessionTemplate">
    	<constructor-arg>
  			<bean class="org.mybatis.spring.SqlSessionFactoryBean">
		  		<property name="dataSource" ref="payFundDataSource"></property>
		        <property name="configLocation" value="classpath:mybatis/sqlMapConfig.xml"></property>
		        <property name="mapperLocations" value="classpath:mybatis/fund/*.xml"></property>
  			</bean>
  		</constructor-arg>
    </bean>

    <!-- 配置Spring的事务管理器 -->
	<bean id="payFundTransactionManager" class="cn.com.duiba.wolf.spring.datasource.AutoRoutingDataSourceTransactionManager">
		<property name="dataSource" ref="payFundDataSource" />
		<qualifier value="payFund"/>
	</bean>
	
</beans>
