package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeNotifyParams;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeNotifyResp;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteIcbcCreditsNotifyService;
import cn.com.duiba.paycenter.service.payment.IcbcCreditsPayService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
public class RemoteIcbcCreditsNotifyServiceImpl implements RemoteIcbcCreditsNotifyService {

    @Resource
    private IcbcCreditsPayService icbcCreditsPayService;

    @Override
    public IcbcCreditsChargeNotifyResp handleChargeNotify(IcbcCreditsChargeNotifyParams params) throws Exception {
        return icbcCreditsPayService.handleChargeNotify(params);
    }
}
