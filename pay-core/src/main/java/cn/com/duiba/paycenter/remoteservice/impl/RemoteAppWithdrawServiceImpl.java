package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.biz.AppWithdrawBiz;
import cn.com.duiba.paycenter.client.RpcResult;
import cn.com.duiba.paycenter.params.AppWithdrawParams;
import cn.com.duiba.paycenter.remoteservice.RemoteAppWithdrawService;
import cn.com.duiba.paycenter.result.WithdrawResult;
import cn.com.duiba.paycenter.util.SignUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 开发者余额账户提现
 * author zhanghuifeng
 * date 2018/11/8-2:05 PM
 */
@RestController
public class RemoteAppWithdrawServiceImpl implements RemoteAppWithdrawService {
    private static final Logger LOGGER = LoggerFactory.getLogger(RemoteAppWithdrawServiceImpl.class);

    @Resource
    private AppWithdrawBiz appWithdrawBiz;

    @Override
    public RpcResult<WithdrawResult> withdrawCashApply(AppWithdrawParams requestParams, String sign) {

        try {
            checkWithdrawParamAndSign(requestParams,sign);
            WithdrawResult result = appWithdrawBiz.withdrawApply(requestParams);
            return new RpcResult<>(result);
        } catch(BizException e){
            LOGGER.warn("withdrawCashApply fail ", e);
            return new RpcResult<>(e);
        }catch (Exception e) {
            LOGGER.error("withdrawCashApply fail ", e);
            return new RpcResult<>(e);
        }
    }

    @Override
    public RpcResult<WithdrawResult> withdrawCashApplyPaybackApply(AppWithdrawParams requestParams, String sign) {
        try {
            checkWithdrawParamAndSign(requestParams,sign);
            WithdrawResult result = appWithdrawBiz.withdrawPaybackApply(requestParams);
            return new RpcResult<>(result);
        } catch(BizException e){
            LOGGER.warn("withdrawCashApply fail：", e);
            return new RpcResult<>(e);
        }catch (Exception e) {
            LOGGER.error("withdrawCashApply fail", e);
            return new RpcResult<>(e);
        }
    }

    private void checkWithdrawParamAndSign(AppWithdrawParams requestParams, String sign) throws BizException {
        Map<String, String> params=new HashMap<>();
        params.put("developerId", Objects.toString(requestParams.getDeveloperId()));
        params.put("withdrawId", requestParams.getBizId());
        params.put("money", Objects.toString(requestParams.getMoney()));
        params.put("appId",Objects.toString(requestParams.getAppId()));

        String checkSign= SignUtil.sign(params);

        if (!checkSign.equals(sign)) {
            throw new BizException("签名不通过");
        }
    }
}
