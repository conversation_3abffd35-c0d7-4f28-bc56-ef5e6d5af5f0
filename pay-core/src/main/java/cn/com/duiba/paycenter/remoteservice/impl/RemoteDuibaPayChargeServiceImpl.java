package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.biz.DuibaPayChargeBiz;
import cn.com.duiba.paycenter.constant.PayCenterErrorCode;
import cn.com.duiba.paycenter.result.PayChargeResult;
import cn.com.duiba.paycenter.service.DuibaPayChargeService;
import cn.com.duiba.paycenter.util.SignUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
public class RemoteDuibaPayChargeServiceImpl implements DuibaPayChargeService{
	
	private static Logger log=LoggerFactory.getLogger(RemoteDuibaPayChargeServiceImpl.class);
	@Autowired
	private DuibaPayChargeBiz duibaPayChargeBiz;
	@Override
	public PayChargeResult chargeMoneyByManual(Long manualApplyId, Long money,
			String sign) {
		log.debug(getClass().getName()+".chargeMoneyByManual("+manualApplyId+","+money+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("relationId", manualApplyId+"");
		params.put("money", money+"");
		String checksign=SignUtil.sign(params);
		
		if(!checksign.equals(sign)){
			return new PayChargeResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		return duibaPayChargeBiz.chargeMoneyByManual(manualApplyId, money);
	}

	@Override
	public PayChargeResult reduceMoneyByManual(Long manualApplyId, Long money,
			String sign) {
		log.debug(getClass().getName()+".reduceMoneyByManual("+manualApplyId+","+money+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("relationId", manualApplyId+"");
		params.put("money", money+"");
		String checksign=SignUtil.sign(params);
		
		if(!checksign.equals(sign)){
			return new PayChargeResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		return duibaPayChargeBiz.reduceMoneyByManual(manualApplyId, money);
	}

}
