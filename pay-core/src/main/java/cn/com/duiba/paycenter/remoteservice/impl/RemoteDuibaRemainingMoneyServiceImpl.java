package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.exception.CodeException;
import cn.com.duiba.service.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;

import cn.com.duiba.paycenter.dto.DuibaRemainingMoneyDto;
import cn.com.duiba.paycenter.remoteservice.RemoteDuibaRemainingMoneyService;
import cn.com.duiba.paycenter.service.DuibaRemainingMoneyService;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class RemoteDuibaRemainingMoneyServiceImpl implements
		RemoteDuibaRemainingMoneyService {
	@Autowired
	private DuibaRemainingMoneyService duibaRemainingMoneyService;

	@Override
	public DuibaRemainingMoneyDto findRecord() {
		return duibaRemainingMoneyService.findRecord();
	}

	@Override
	public DuibaRemainingMoneyDto findRecord4update() {
		return duibaRemainingMoneyService.findRecord4update();
	}

	@Override
	public DuibaRemainingMoneyDto findDuibaIncomeRecord() {
		return duibaRemainingMoneyService.findDuibaIncomeRecord();
	}

	@Override
	public DuibaRemainingMoneyDto findDuibaIncomeRecord4update() {
		return duibaRemainingMoneyService.findDuibaIncomeRecord4update();
	}

	@Override
	public boolean reduceMoney(DuibaRemainingMoneyDto rm, Long money) throws CodeException, BusinessException {
		return duibaRemainingMoneyService.reduceMoney(rm, money);
	}

	@Override
	public boolean addMoney(DuibaRemainingMoneyDto rm, Long money)
			throws CodeException, BusinessException {
		return duibaRemainingMoneyService.addMoney(rm, money);
	}

}
