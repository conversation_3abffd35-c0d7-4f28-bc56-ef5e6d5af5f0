package cn.com.duiba.paycenter.service.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.ChannelDisplayDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/02
 */
public interface ChannelDisplayService {
    /**
     * 更新app纬度前端支付渠道显示
     * @param appId appId
     * @param displayStatus 显示状态 0 不显示 1 显示
     * @param channelId 渠道id 1 微信 2 支付宝
     * @return 是否更新成功
     * @throws BizException exception
     */
    boolean updateDisplay(Long appId, Integer displayStatus, Integer channelId) throws BizException;

    /**
     * 获取app纬度的支付显示配置列表
     * 如果没有配置会默认插入微信支付和支付宝显示的支付配置
     * @param appId appId
     * @return List<ChannelDisplayDto>
     * @throws BizException exception
     */
    List<ChannelDisplayDto> listByAppId(Long appId) throws BizException;
}
