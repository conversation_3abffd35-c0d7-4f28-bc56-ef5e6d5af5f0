package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.bankofsuzhou.BankOfSuZhouChargeNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteBankOfSuZhouNotifyService;
import cn.com.duiba.paycenter.service.payment.BankOfSuZhouNotifyService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/06/12
 */
@RestController
public class RemoteBankOfSuZhouNotifyServiceImpl implements RemoteBankOfSuZhouNotifyService {
    @Resource
    private BankOfSuZhouNotifyService notifyService;

    @Override
    public BankOfSuZhouChargeNotifyResponse orderNotify(Map<String, String> map) throws BizException {
        return notifyService.orderNotify(map);
    }
}
