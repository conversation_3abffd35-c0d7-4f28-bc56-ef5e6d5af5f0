package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4AppChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4WxChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4WxChargeQueryResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteIcbcElifeNotifyService;
import cn.com.duiba.paycenter.service.payment.impl.IcbcElifeNotifyservice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-06-11
 */
@RestController
public class RemoteIcbcElifeNotifyServiceImpl implements RemoteIcbcElifeNotifyService {

    @Autowired
    private IcbcElifeNotifyservice icbcElifeNotifyservice;


    @Override
    public IcbcElife4AppChargeNotifyResponse orderNotify4AppWithBizType(String notifyUrl, Map<String, String> params, Integer bizType) throws BizException {
        return icbcElifeNotifyservice.orderNotify4AppWithBizType(notifyUrl, params, bizType);
    }

    @Override
    public IcbcElife4AppChargeNotifyResponse orderNotify4App(String notifyUrl, Map<String, String> params) throws BizException {
        return icbcElifeNotifyservice.orderNotify4App(notifyUrl, params);
    }

    @Override
    public IcbcElife4WxChargeNotifyResponse orderNotify4Wx(String notifyUrl, Map<String, String> params) throws BizException {
        return icbcElifeNotifyservice.orderNotify4Wx(notifyUrl, params);
    }

    @Override
    public IcbcElife4WxChargeQueryResponse queryOrderStatus4Wx(String bizOrderNo, Integer bizType) {
        return icbcElifeNotifyservice.queryOrderStatus4Wx(bizOrderNo, bizType);
    }
}
