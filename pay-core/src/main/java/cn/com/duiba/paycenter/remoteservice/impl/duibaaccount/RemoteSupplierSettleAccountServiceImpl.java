package cn.com.duiba.paycenter.remoteservice.impl.duibaaccount;

import cn.com.duiba.paycenter.bean.SupplierAccountChangeBean;
import cn.com.duiba.paycenter.biz.SupplierSettleAccountBiz;
import cn.com.duiba.paycenter.constant.PayCenterErrorCode;
import cn.com.duiba.paycenter.dto.duibaaccount.SupplierRemainingMoneyDto;
import cn.com.duiba.paycenter.enums.duibaaccount.SupplierAccountTypeEnum;
import cn.com.duiba.paycenter.params.duibaaccount.SupplierAccountChangeParams;
import cn.com.duiba.paycenter.remoteservice.duibaaccount.RemoteSupplierSettleAccountService;
import cn.com.duiba.paycenter.result.PayCenterResult;
import cn.com.duiba.paycenter.service.duibaaccount.SupplierRemainingMoneyService;
import cn.com.duiba.paycenter.util.SignUtil;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * author zhanghuifeng
 * date 2018-12-13-14:43
 */
@RestController
public class RemoteSupplierSettleAccountServiceImpl implements RemoteSupplierSettleAccountService {

    @Resource
    private SupplierSettleAccountBiz supplierSettleAccountBiz;
    @Resource
    private SupplierRemainingMoneyService supplierRemainingMoneyService;

    @Override
    public PayCenterResult reduceMoney(SupplierAccountChangeParams param, String sign) {

        String checkSign= getSign(param);
        if(!checkSign.equals(sign)){
            return new PayCenterResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        return supplierSettleAccountBiz.reduceMoney(packSettleBean(param));
    }

    @Override
    public PayCenterResult addMoney(SupplierAccountChangeParams param, String sign) {
        String checkSign= getSign(param);
        if(!checkSign.equals(sign)){
            return new PayCenterResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        return supplierSettleAccountBiz.addMoney(packSettleBean(param));
    }

    @Override
    public Long findMoney(Long supplierId) {
        return supplierRemainingMoneyService.findMoney(supplierId, SupplierAccountTypeEnum.SETTLE.getCode());
    }

    @Override
    public SupplierRemainingMoneyDto create(Long supplierId) {
        return BeanUtils.copy(supplierRemainingMoneyService.create(supplierId,SupplierAccountTypeEnum.SETTLE.getCode()),
                SupplierRemainingMoneyDto.class);
    }

    private String getSign(SupplierAccountChangeParams param){
        Map<String, String> params=new HashMap<>();
        params.put("changeMoney", Objects.toString(param.getChangeMoney()));
        params.put("supplier_id", Objects.toString(param.getSupplierId()));
        params.put("relationId", param.getRelationId());
        return SignUtil.sign(params);
    }

    private SupplierAccountChangeBean packSettleBean(SupplierAccountChangeParams param){
        SupplierAccountChangeBean bean = new SupplierAccountChangeBean();
        bean.setSupplierId(param.getSupplierId());
        bean.setRelationType(param.getRelationType()==null?null:param.getRelationType().getCode());
        bean.setRelationId(param.getRelationId());
        bean.setMemo(param.getMemo());
        bean.setChangeMoney(param.getChangeMoney());
        bean.setTransferOut(param.getTransferOut()==null?null:param.getTransferOut().getCode());
        bean.setTransferIn(param.getTransferIn()==null?null:param.getTransferIn().getCode());
        bean.setOperatorId(param.getOperatorId());
        return bean;
    }
}
