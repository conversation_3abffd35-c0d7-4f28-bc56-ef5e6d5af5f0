package cn.com.duiba.paycenter.remoteservice.impl.duibaaccount;

import cn.com.duiba.paycenter.dto.duibaaccount.AppAccountDetailPageDto;
import cn.com.duiba.paycenter.dto.duibaaccount.AppMarkAccountDetailDto;
import cn.com.duiba.paycenter.params.duibaaccount.AppAccountDetailQryParams;
import cn.com.duiba.paycenter.remoteservice.duibaaccount.RemoteAppMarkAccountDetailService;
import cn.com.duiba.paycenter.service.duibaaccount.AppMarkAccountDetailService;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * author zhanghuifeng
 * date 2018-11-27-14:19
 */
@RestController
public class RemoteAppMarkAccountDetailServiceImpl implements RemoteAppMarkAccountDetailService {

    @Resource
    private AppMarkAccountDetailService appMarkAccountDetailService;

    @Override
    public List<AppAccountDetailPageDto> find4Page(AppAccountDetailQryParams params) {
        return appMarkAccountDetailService.find4Page(params);
    }

    @Override
    public Long count4page(AppAccountDetailQryParams params) {
        return appMarkAccountDetailService.count4page(params);
    }

    @Override
    public AppMarkAccountDetailDto findByIdAndAppId(Long id, Long appId) {
        return BeanUtils.copy(appMarkAccountDetailService.findByIdAndAppId(id, appId)
                ,AppMarkAccountDetailDto.class);
    }

    @Override
    public List<AppMarkAccountDetailDto> findByRelationAndAppId(String relationId, List<Integer> relationTypes, Long appId) {
        return BeanUtils.copyList(appMarkAccountDetailService.findByRelationAndAppId(relationId,relationTypes, appId),
                AppMarkAccountDetailDto.class);
    }

    @Override
    public List<AppAccountDetailPageDto> find4Export(AppAccountDetailQryParams params) {
        return appMarkAccountDetailService.find4Export(params);
    }
}
