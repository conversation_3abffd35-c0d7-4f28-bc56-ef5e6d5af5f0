package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.params.crossBorder.CancelOrderParam;
import cn.com.duiba.paycenter.params.crossBorder.CommitOrderParam;
import cn.com.duiba.paycenter.params.crossBorder.CommitPayOrderParam;
import cn.com.duiba.paycenter.remoteservice.RemoteDuibaLiveCrossBorderService;
import cn.com.duiba.paycenter.service.DuibaLiveCrossBorderService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2022/1/13 4:54 下午
 */
@RestController
public class RemoteDuibaLiveCrossBorderServiceImpl implements RemoteDuibaLiveCrossBorderService {
    @Resource
    private DuibaLiveCrossBorderService duibaLiveCrossBorderService;

    @Override
    public void commitOrder(CommitOrderParam param) {
        duibaLiveCrossBorderService.commitOrder(param);
    }

    @Override
    public void cancelOrder(CancelOrderParam param) {
        duibaLiveCrossBorderService.cancelOrder(param);
    }

    @Override
    public void commitPayOrder(CommitPayOrderParam param) {
        duibaLiveCrossBorderService.commitPayOrder(param);
    }
}
