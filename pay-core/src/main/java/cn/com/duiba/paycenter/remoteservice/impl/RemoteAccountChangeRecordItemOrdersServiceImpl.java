package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.paycenter.dao.AccountChangeRecordItemOrdersDAO;
import cn.com.duiba.paycenter.dto.AccountChangeRecordItemOrdersDto;
import cn.com.duiba.paycenter.dto.AccountPageInfo;
import cn.com.duiba.paycenter.entity.credits.AccountChangeRecordItemOrdersEntity;
import cn.com.duiba.paycenter.remoteservice.RemoteAccountChangeRecordItemOrdersService;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class RemoteAccountChangeRecordItemOrdersServiceImpl implements RemoteAccountChangeRecordItemOrdersService {
	
	@Autowired
	private AccountChangeRecordItemOrdersDAO accountChangeRecordItemOrdersDAO;

	@Override
	public List<AccountChangeRecordItemOrdersDto> findByOrderItemIdAndType(Long orderId, Long orderItemId, Integer recordType) {
		return BeanUtils.copyList(accountChangeRecordItemOrdersDAO.findByOrderItemIdAndType(orderId, orderItemId, recordType), AccountChangeRecordItemOrdersDto.class);
	}

	@Override
	public Page<AccountChangeRecordItemOrdersDto> findByOrderItemIdPage(AccountPageInfo pageInfo) {
		Page<AccountChangeRecordItemOrdersDto> page = new Page<>();
		List<AccountChangeRecordItemOrdersEntity> accountChangeRecordItemOrdersEntities = accountChangeRecordItemOrdersDAO.findByOrderItemIdPage(pageInfo);
		Integer count = accountChangeRecordItemOrdersDAO.findCountByOrderItemIdPage(pageInfo);
		page.setList(BeanUtils.copyList(accountChangeRecordItemOrdersEntities, AccountChangeRecordItemOrdersDto.class));
		page.setTotalCount(count);
		return page;
	}
}
