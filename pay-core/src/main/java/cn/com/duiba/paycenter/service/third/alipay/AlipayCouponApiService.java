package cn.com.duiba.paycenter.service.third.alipay;

import cn.com.duiba.boot.exception.BizException;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.request.AlipayUserDtbankcustChannelvoucherSendRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.response.AlipayUserDtbankcustChannelvoucherSendResponse;

/**
 * 支付宝立减金相关
 * <AUTHOR>
 * @date 2023/4/21 10:47 AM
 */
public interface AlipayCouponApiService {

    /**
     * 支付宝授权
     * <a href="https://opendocs.alipay.com/open/02ailc">官方文档</a>
     * @param appId 支付宝应用ID
     * @param request 请求参数
     * @return 支付宝响应
     * @throws BizException 获取支付宝客户端异常
     */
    AlipaySystemOauthTokenResponse certificateExecute(String appId, AlipaySystemOauthTokenRequest request) throws BizException;

    /**
     * 支付宝发放立减金
     * <a href="https://opendocs.alipay.com/pre-apis/01pnke">官方文档</a>
     * @param appId 支付宝应用ID
     * @param request 请求参数
     * @return 支付宝响应
     * @throws BizException 获取支付宝客户端异常
     */
    AlipayUserDtbankcustChannelvoucherSendResponse certificateExecute(String appId, AlipayUserDtbankcustChannelvoucherSendRequest request) throws BizException;
}
