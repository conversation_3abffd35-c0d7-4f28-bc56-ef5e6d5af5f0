package cn.com.duiba.paycenter.remoteservice.impl.equity.conf;

import cn.com.duiba.paycenter.remoteservice.equity.conf.RemoteWxpayMchConfService;
import cn.com.duiba.paycenter.service.equity.WxpayMchConfService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 微信支付商户配置
 * <AUTHOR>
 * @date 2023/8/19 17:50
 */
@RestController
public class RemoteWxpayMchConfServiceImpl implements RemoteWxpayMchConfService {
    
    @Resource
    private WxpayMchConfService wxpayMchConfService;
    
    @Override
    public Map<String, String> selectAllMchNameMap() {
        return wxpayMchConfService.selectAllMchNameMap();
    }
}
