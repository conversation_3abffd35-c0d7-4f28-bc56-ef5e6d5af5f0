package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.paycenter.remoteservice.payment.RemoteAlipayConfigService;
import cn.com.duiba.paycenter.service.payment.AlipayConfigService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018/12/12
 */
@RestController
public class RemoteAlipayConfigServiceImpl implements RemoteAlipayConfigService {
    @Resource
    private AlipayConfigService alipayConfigService;

    @Override
    public Integer getChannelMode(Long appId, String channelType) {
        return alipayConfigService.getChannelMode(appId, channelType);
    }
}
