package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay.UnionPayUmsChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay.UnionPayUmsRefundNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteUnionPayUmsNotifyService;
import cn.com.duiba.paycenter.service.payment.UnionPayNotifyService;
import cn.com.duiba.paycenter.service.payment.UnionPayUmsNotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/7/19 10:29
 */
@RestController
public class RemoteUnionPayUmsNotifyServiceImpl implements RemoteUnionPayUmsNotifyService {

    @Resource
    private UnionPayUmsNotifyService unionPayUmsNotifyService;

    @Override
    public UnionPayUmsChargeNotifyResponse orderNotify(Map<String, String> map) {
        return unionPayUmsNotifyService.orderNotify(map);
    }

    @Override
    public UnionPayUmsRefundNotifyResponse refundNotify(Map<String, String> map) {
        return unionPayUmsNotifyService.refundNotify(map);
    }
}
