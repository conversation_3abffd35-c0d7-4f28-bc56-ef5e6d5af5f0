package cn.com.duiba.paycenter.dao;

import java.util.List;

import cn.com.duiba.paycenter.model.TpcDuibaIncomeChangeDO;

public interface TpcDuibaIncomeChangeDAO {
	public TpcDuibaIncomeChangeDO insert(TpcDuibaIncomeChangeDO record);
	
	public TpcDuibaIncomeChangeDO find(Long id);
	
	/**
	 * 查询是否已经存在了这笔资金变动记录
	 * 
	 * 因为数据有唯一索引，结果只可能为0或者1
	 * @param relationType
	 * @param relationId
	 * @param actionType
	 * @return
	 */
	public int findCountExist(String relationType,Long relationId,String actionType);
	
	public TpcDuibaIncomeChangeDO findExistRecord(String relationType,Long relationId,String actionType);
	/**
	 * 根据relation信息查询所有的资金明细列表
	 * 
	 * @param relationType
	 * @param relationId
	 * @return
	 */
	public List<TpcDuibaIncomeChangeDO> findAllByRelation(String relationType,Long relationId);
	/**
	 * 获取大于id的记录batchSize条
	 * @param lastId
	 * @param batchSize
	 * @return
	 */
	public List<TpcDuibaIncomeChangeDO> findAllGreaterId(Long lastId,
			int batchSize) ;
}
