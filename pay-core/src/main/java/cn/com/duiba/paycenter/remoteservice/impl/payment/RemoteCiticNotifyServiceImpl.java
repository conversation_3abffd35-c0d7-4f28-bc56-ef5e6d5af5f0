package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.citic.CiticCallbackNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.citic.CiticChargeNotifyResponse;
import cn.com.duiba.paycenter.enums.BizTypeEnum;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteCiticNotifyService;
import cn.com.duiba.paycenter.service.payment.CiticNotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by xutao on 2020/4/24.
 */
@RestController
public class RemoteCiticNotifyServiceImpl implements RemoteCiticNotifyService {
    @Autowired
    private CiticNotifyService citicNotifyService;

    @Override
    public CiticChargeNotifyResponse queryOrderNotify(String bizOrderNo) throws BizException{
        return citicNotifyService.queryOrderNotify(bizOrderNo, BizTypeEnum.ORD.getCode(), null);
    }

    @Override
    public CiticChargeNotifyResponse queryOrderNotifyWithSupplier(String bizOrderNo, Integer supplierTag) throws BizException{
        return citicNotifyService.queryOrderNotify(bizOrderNo, BizTypeEnum.ORD.getCode(), supplierTag);
    }

    @Override
    public CiticChargeNotifyResponse queryOrderNotifyNew(String bizOrderNo, Integer bizType) throws BizException {
        return citicNotifyService.queryOrderNotify(bizOrderNo, bizType, null);
    }

    @Override
    public CiticCallbackNotifyResponse callbackPayNotify(String content) {
        return citicNotifyService.callbackPayNotify(content);
    }
}
