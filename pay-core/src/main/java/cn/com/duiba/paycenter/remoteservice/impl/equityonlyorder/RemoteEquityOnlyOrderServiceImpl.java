package cn.com.duiba.paycenter.remoteservice.impl.equityonlyorder;

import cn.com.duiba.paycenter.dto.EquityOnlyOrderDto;
import cn.com.duiba.paycenter.enums.EquityOnlyOrderBizTypeEnum;
import cn.com.duiba.paycenter.enums.PayOrderStatusEnum;
import cn.com.duiba.paycenter.remoteservice.equityonlyorder.RemoteEquityOnlyOrderService;
import cn.com.duiba.paycenter.service.EquityOnlyOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2023/4/18 2:45 PM （可以根据需要修改）
 * @Version 1.0 （版本号）
 */
@RestController
public class RemoteEquityOnlyOrderServiceImpl implements RemoteEquityOnlyOrderService {

    @Autowired
    private EquityOnlyOrderService equityOnlyOrderService;

    @Override
    public Long insert(EquityOnlyOrderDto dto) {
        return equityOnlyOrderService.insert(dto);
    }

    @Override
    public Integer updateById(EquityOnlyOrderDto dto) {
        return equityOnlyOrderService.updateById(dto);
    }

    @Override
    public Integer updateStatusByUniqueIndex(PayOrderStatusEnum orderStatusEnum, EquityOnlyOrderBizTypeEnum equityOnlyOrderBizTypeEnum, String bizNo) {
        return equityOnlyOrderService.updateStatusByUniqueIndex(orderStatusEnum, equityOnlyOrderBizTypeEnum, bizNo);
    }

    @Override
    public Integer updateSuccess(EquityOnlyOrderDto equityOnlyOrderDto) {
        return equityOnlyOrderService.updateSuccess(equityOnlyOrderDto);
    }

    @Override
    public EquityOnlyOrderDto findByUniqueIndex(EquityOnlyOrderBizTypeEnum bizType, String bizNo) {
        return equityOnlyOrderService.findByUniqueIndex(bizType, bizNo);
    }
}
