package cn.com.duiba.paycenter.service.third.jigao.impl;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.bean.jigao.request.JiGaoBaseRequest;
import cn.com.duiba.paycenter.bean.jigao.request.JiGaoChargeRequest;
import cn.com.duiba.paycenter.bean.jigao.request.JiGaoQueryRequest;
import cn.com.duiba.paycenter.bean.jigao.request.JiGaoWetPayMktTransRequest;
import cn.com.duiba.paycenter.bean.jigao.result.JiGaoBaseResult;
import cn.com.duiba.paycenter.bean.jigao.result.JiGaoChargeResult;
import cn.com.duiba.paycenter.bean.jigao.result.JiGaoQueryResult;
import cn.com.duiba.paycenter.config.equity.EquityJiGaoConfig;
import cn.com.duiba.paycenter.constant.JiGaoConstants;
import cn.com.duiba.paycenter.service.third.jigao.JiGaoService;
import cn.com.duiba.paycenter.util.wxv3.GsonUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/6/20 10:59 AM
 */
@Slf4j
@Service
public class JiGaoServiceImpl implements JiGaoService {
    
    @Resource
    private EquityJiGaoConfig equityJiGaoConfig;

    @Resource(name = "virtualHttpAsyncClient")
    private CloseableHttpClient httpClient;

    private static final SimpleDateFormat SECOND = new SimpleDateFormat("yyyyMMddHHmmss");

    private static final Set<String> NOT_SIGN_KEYS = Collections.unmodifiableSet(Stream.of("sign", "card_array").collect(Collectors.toSet()));
    
    @Override
    public JiGaoChargeResult chargeWetPayMktTrans(JiGaoChargeRequest<JiGaoWetPayMktTransRequest> request) throws BizException {
        request.setProductId("WET_PAYMKTTRANS");
        request.setBuyNum("1");
        return charge(request);
    }

    @Override
    public JiGaoQueryResult query(JiGaoQueryRequest request) throws BizException {
        return sendPost(request, JiGaoConstants.QUERY_URL, JiGaoQueryResult.class);
    }

    @Override
    public boolean checkSign(String body) {
        Map<String, String> paramsMap = GsonUtil.parseJson(body);
        String str = getMapSign(paramsMap) + equityJiGaoConfig.getAppKey();
        String sign = getMd5(str);
        return StringUtils.equals(sign, paramsMap.get("sign"));
    }

    /**
     * 直充
     * @param request 请求参数
     * @return 直充结果
     * @param <T> 商品对应的额外参数对象类型
     */
    private <T extends Serializable> JiGaoChargeResult charge(JiGaoChargeRequest<T> request) throws BizException {
        request.setTimestamp(getMillisecond());
        return sendPost(request, JiGaoConstants.CHARGE_URL, JiGaoChargeResult.class);
    }
    
    private <T extends JiGaoBaseRequest, R extends JiGaoBaseResult> R sendPost(T request, String url, Class<R> clazz) throws BizException {
        request.setChannelId(equityJiGaoConfig.getChannelId());
        String body = addSignAndBuildBody(request);
        HttpPost httpPost = buildHttpPost(body, url);
        String logStr = "url=" + url + ", body=" + body + ", request=" + request;
        return send(httpPost, logStr, clazz);
    }
    
    private <R extends JiGaoBaseResult> R send(HttpRequestBase requestBase, String baseLogStr, Class<R> clazz) throws BizException {
        long start = System.currentTimeMillis();
        R result = null;
        String responseBody = null;
        try (CloseableHttpResponse httpResponse = httpClient.execute(requestBase)) {
            if (httpResponse.getEntity() != null) {
                responseBody = EntityUtils.toString(httpResponse.getEntity(), StandardCharsets.UTF_8);
            }
            result = GsonUtil.parseJson(responseBody, clazz);
            return result;
        } catch (Exception e) {
            log.error("[继皋], send, request error, {}, responseBody={}, result={}", baseLogStr, responseBody, result, e);
            throw new BizException("请求继皋失败");
        } finally {
            log.info("[继皋], send, time={}, {}, responseBody={}, result={}", (System.currentTimeMillis()-start), baseLogStr, responseBody, result);
        }
    } 
    
    private HttpPost buildHttpPost(String body, String url) {
        HttpPost httpPost = new HttpPost(JiGaoConstants.HOST + url);
        StringEntity stringEntity = new StringEntity(body, StandardCharsets.UTF_8);
        stringEntity.setContentEncoding(StandardCharsets.UTF_8.name());
        stringEntity.setContentType("application/json;charset=UTF-8");
        httpPost.setEntity(stringEntity);
        return httpPost;
    }
    
    private String addSignAndBuildBody(Object request) throws BizException {
        String json = GsonUtil.toJson(request);
        Map<String, String> paramsMap = toMap(json);
        String str = getMapSign(paramsMap) + equityJiGaoConfig.getAppKey();
        String sign = getMd5(str);
        if (StringUtils.isBlank(sign)) {
            throw new BizException("签名失败");
        }
        paramsMap.put("sign", sign);
        return GsonUtil.toJson(paramsMap);
    }

    private static String getMillisecond() {
        synchronized (SECOND) {
            return SECOND.format(new Date());
        }
    }

    private Map<String, String> toMap(String json) {
        Map<String, Object> paramsMap = GsonUtil.parseJson(json);
        if (MapUtils.isEmpty(paramsMap)) {
            return Collections.emptyMap();
        }
        return paramsMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, en -> getValue(en.getValue())));
    }

    private String getMapSign(Map<String, String> paramsMap) {
        List<String> keys = Lists.newArrayList(paramsMap.keySet());
        Collections.sort(keys);
        StringBuilder stringBuffer = new StringBuilder();
        for (String key : keys) {
            if (NOT_SIGN_KEYS.contains(key)) {
                continue;
            }
            String value = paramsMap.get(key);
            if(StringUtils.isNotBlank(value)) {
                stringBuffer.append(key).append("=").append(value).append("&");
            }
        }
        String str = stringBuffer.toString();
        return str.endsWith("&") ? str.substring(0, str.length()-1) : str;
    }
    
    private String getValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof String) {
            return (String) value;
        }
        if (value.getClass().isPrimitive()) {
            return value.toString();
        }
        if (Number.class.isAssignableFrom(value.getClass())) {
            return value.toString();
        }
        return GsonUtil.toJson(value);
    }

    private static String getMd5(String srcSignString) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(srcSignString.getBytes(StandardCharsets.UTF_8));
            StringBuilder result = new StringBuilder();
            byte[] temp;
            temp = md5.digest("".getBytes(StandardCharsets.UTF_8));
            for (byte b : temp) {
                result.append(Integer.toHexString((0x000000ff & b) | 0xffffff00).substring(6));
            }
            return result.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
