package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.ChannelDisplayDto;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteChannelDisplayBackendService;
import cn.com.duiba.paycenter.service.payment.ChannelDisplayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/01
 */
@RestController
public class RemoteChannelDisplayBackendServiceImpl implements RemoteChannelDisplayBackendService {
    @Autowired
    private ChannelDisplayService channelDisplayService;

    @Override
    public boolean updateDisplay(Long appId, Integer displayStatus, Integer channelId) throws BizException {
        return channelDisplayService.updateDisplay(appId, displayStatus, channelId);
    }

    @Override
    public List<ChannelDisplayDto> listByAppId(Long appId) throws BizException {
        return channelDisplayService.listByAppId(appId);
    }
}
