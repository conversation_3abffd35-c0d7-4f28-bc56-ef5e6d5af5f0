package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.paycenter.config.BankOfSuZhouConfig;
import cn.com.duiba.paycenter.config.BankOfSuZhouConstants;
import cn.com.duiba.paycenter.dto.payment.charge.bankofsuzhou.BankOfSuZhouWapChargeResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteBankOfSuZhouBackendService;
import cn.com.duiba.paycenter.util.BankOfSuZhouUtils;
import cn.com.duiba.paycenter.util.RandomUtils;
import cn.com.duiba.wolf.utils.DateUtils;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/26 20:42
 */
@RestController
public class RemoteBankOfSuZhouBackendServiceImpl implements RemoteBankOfSuZhouBackendService {
    private static final Logger logger = LoggerFactory.getLogger(RemoteBankOfSuZhouBackendServiceImpl.class);
    @Resource
    private BankOfSuZhouConfig config;
    @Resource
    private BankOfSuZhouUtils bankOfSuZhouUtils;

    private static final DateTimeFormatter YYYYMMDD = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Override
    public BankOfSuZhouWapChargeResponse getBillCheckFileUrl(String acDate) {

        BankOfSuZhouWapChargeResponse response = new BankOfSuZhouWapChargeResponse();
        try {
            if (StringUtils.isNotBlank(acDate)) {
                acDate = LocalDate.parse(acDate, YYYYMMDD).format(YYYYMMDD);
            } else {
                acDate = String.valueOf(DateUtils.getDayNumber(DateUtils.daysAddOrSub(new Date(), -1)));
            }
        } catch (Exception e) {
            logger.error("日期格式转换错误");
            response.setSuccess(false);
            response.setMessage(e.getMessage());
            return response;
        }
        Map<String, String> map = Maps.newHashMap();

        map.put(BankOfSuZhouConstants.VERSION, config.getVersion());
        map.put(BankOfSuZhouConstants.CHARSET, config.getCharset());
        map.put(BankOfSuZhouConstants.SIGN_TYPE, config.getSignType());
        map.put(BankOfSuZhouConstants.CERT_ID, bankOfSuZhouUtils.getCertId());
        map.put(BankOfSuZhouConstants.SERVICE, "APIStatement");
        map.put(BankOfSuZhouConstants.REQUEST_ID, RandomUtils.getRandomString(3) + System.currentTimeMillis());
        map.put(BankOfSuZhouConstants.MERCHANT_ID, config.getMchId());
        map.put(BankOfSuZhouConstants.AC_DATE, acDate);
        try {
            String sign = bankOfSuZhouUtils.rsaSign(bankOfSuZhouUtils.getSignContent(map), BankOfSuZhouConstants.CHARSET_GBK);
            map.put(BankOfSuZhouConstants.MERCHANT_SIGN, sign);
        } catch (Exception e) {
            logger.error("签名失败", e);
            response.setSuccess(false);
            response.setMessage(e.getMessage());
            return response;
        }
        response.setParams(map);
        response.setGetWayUrl(config.getGetwayUrl());
        response.setSuccess(true);
        return response;
    }
}
