package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.bean.AppAccountChangeBean;
import cn.com.duiba.paycenter.biz.AppBalanceAccountBiz;
import cn.com.duiba.paycenter.constant.PayCenterErrorCode;
import cn.com.duiba.paycenter.enums.duibaaccount.AppAccountRelationTypeEnum;
import cn.com.duiba.paycenter.params.AppAccountPayChargeParams;
import cn.com.duiba.paycenter.remoteservice.RemoteAppAccountPayChargeService;
import cn.com.duiba.paycenter.result.PayCenterResult;
import cn.com.duiba.paycenter.util.SignUtil;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * author z<PERSON><PERSON><PERSON>
 * date 2018/11/7-11:41 AM
 */
@RestController
public class RemoteAppAccountPayChargeServiceImpl implements RemoteAppAccountPayChargeService {

    private static final String DEVELOPER_ID = "developerId";
    private static final String RELATION_ID = "relationId";
    private static final String MONEY = "money";
    private static final String APP_ID = "appId";
    @Resource
    private AppBalanceAccountBiz appBalanceAccountBiz;

    @Override
    public PayCenterResult chargeMoneyByManual(AppAccountPayChargeParams chargeParams, String sign) {
        String checkSign= getCheckSign(chargeParams);

        if(!checkSign.equals(sign)){
            return new PayCenterResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        AppAccountChangeBean bean = packBean(chargeParams);
        bean.setRelationType(AppAccountRelationTypeEnum.MANUAL_CHARGE);
        return appBalanceAccountBiz.addMoney(bean);
    }

    @Override
    public PayCenterResult chargeMoneyByOnline(AppAccountPayChargeParams chargeParams, String sign) {
        String checkSign= getCheckSign(chargeParams);

        if(!checkSign.equals(sign)){
            return new PayCenterResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        AppAccountChangeBean bean = packBean(chargeParams);
        bean.setRelationType(AppAccountRelationTypeEnum.ONLINE_CHARGE);
        return appBalanceAccountBiz.addMoney(bean);
    }

    @Override
    public PayCenterResult reduceMoneyByManual(AppAccountPayChargeParams chargeParams, String sign) {
        String checkSign= getCheckSign(chargeParams);
        if(!checkSign.equals(sign)){
            return new PayCenterResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        AppAccountChangeBean bean = packBean(chargeParams);
        bean.setRelationType(AppAccountRelationTypeEnum.MANUAL_REDUCE);
        return appBalanceAccountBiz.reduceMoney(bean);
    }

    @Override
    public PayCenterResult chargeMoneyFeeByOnline(AppAccountPayChargeParams chargeParams, String sign) {
        String checkSign= getCheckSign(chargeParams);

        if(!checkSign.equals(sign)){
            return new PayCenterResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        AppAccountChangeBean bean = packBean(chargeParams);
        bean.setRelationType(AppAccountRelationTypeEnum.ONLINE_CHARGE_FEE);
        return appBalanceAccountBiz.reduceMoney(bean);
    }

    private AppAccountChangeBean packBean(AppAccountPayChargeParams param){
        AppAccountChangeBean bean = new AppAccountChangeBean();
        bean.setAppId(param.getAppId());
        bean.setDeveloperId(param.getDeveloperId());
        bean.setMoney(param.getChangeMoney());
        bean.setRelationId(param.getRelationId());
        bean.setMemo(param.getMemo());
        bean.setOperatorId(param.getOperatorId());
        return bean;
    }

    private String getCheckSign(AppAccountPayChargeParams chargeParams){
        Map<String, String> params=new HashMap<>();
        params.put(DEVELOPER_ID, Objects.toString(chargeParams.getDeveloperId()));
        params.put(RELATION_ID, chargeParams.getRelationId());
        params.put(MONEY, Objects.toString(chargeParams.getChangeMoney()));
        params.put(APP_ID,Objects.toString(chargeParams.getAppId()));
        return SignUtil.sign(params);
    }

}
