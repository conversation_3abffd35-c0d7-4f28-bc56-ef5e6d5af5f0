package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.ccb.CcbChargeNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteCcbNotifyService;
import cn.com.duiba.paycenter.service.payment.CcbNotifyService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/09/03
 */
@RestController
public class RemoteCcbNotifyServiceImpl implements RemoteCcbNotifyService {
    @Resource
    private CcbNotifyService ccbNotifyService;

    @Override
    public CcbChargeNotifyResponse orderNotify(Map<String, String> map) throws BizException {
        return ccbNotifyService.orderNotify(map);
    }
}
