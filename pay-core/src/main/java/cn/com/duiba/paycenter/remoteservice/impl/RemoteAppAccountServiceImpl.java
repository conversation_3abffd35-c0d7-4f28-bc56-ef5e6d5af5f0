package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.bean.AppAccountChangeBean;
import cn.com.duiba.paycenter.biz.AppBalanceAccountBiz;
import cn.com.duiba.paycenter.constant.PayCenterErrorCode;
import cn.com.duiba.paycenter.params.AppAccountChangeParams;
import cn.com.duiba.paycenter.remoteservice.RemoteAppAccountService;
import cn.com.duiba.paycenter.result.PayCenterResult;
import cn.com.duiba.paycenter.util.SignUtil;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 开发者账户采购
 * author zhanghuifeng
 * date 2018/11/7-5:54 PM
 */
@RestController
public class RemoteAppAccountServiceImpl implements RemoteAppAccountService {

    @Resource
    private AppBalanceAccountBiz appBalanceAccountBiz;

    @Override
    public PayCenterResult reduceMoney(AppAccountChangeParams param, String sign) {
        String checkSign= getCheckSign(param);
        if(!checkSign.equals(sign)){
            return new PayCenterResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        AppAccountChangeBean bean = packBean(param);
        return appBalanceAccountBiz.reduceMoney(bean);
    }

    @Override
    public PayCenterResult addMoney(AppAccountChangeParams param,String sign) {
        String checkSign= getCheckSign(param);
        if(!checkSign.equals(sign)){
            return new PayCenterResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        AppAccountChangeBean bean = packBean(param);
        return appBalanceAccountBiz.addMoney(bean);
    }

    private AppAccountChangeBean packBean(AppAccountChangeParams param){
        AppAccountChangeBean bean = new AppAccountChangeBean();
        bean.setAppId(param.getAppId());
        bean.setDeveloperId(param.getDeveloperId());
        bean.setMemo(param.getMemo());
        bean.setMoney(param.getMoney());
        bean.setRelationId(param.getRelationId());
        bean.setRelationType(param.getRelationType());
        bean.setTransferOut(param.getTransferOut()==null?null:param.getTransferOut().getCode());
        bean.setTransferIn(param.getTransferIn()==null?null:param.getTransferIn().getCode());
        return bean;
    }

    private String getCheckSign(AppAccountChangeParams param){
        Map<String, String> params=new HashMap<>();
        params.put("developerId", Objects.toString(param.getDeveloperId()));
        params.put("relationId", param.getRelationId());
        params.put("money", Objects.toString(param.getMoney()));
        params.put("appId", Objects.toString(param.getAppId()));
        return SignUtil.sign(params);
    }
}
