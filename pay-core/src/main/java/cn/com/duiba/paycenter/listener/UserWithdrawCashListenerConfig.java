package cn.com.duiba.paycenter.listener;

import com.google.common.eventbus.AsyncEventBus;
import com.google.common.eventbus.EventBus;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 监听器配置
 */
@Slf4j
@Configuration
public class UserWithdrawCashListenerConfig  {

    private ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("event-bus-thread-%d").build(); // 线程工厂
    private RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy(); // 拒绝策略

    @Autowired
    private List<UserCashListener> userCashListeners;


    private final Executor executor = new ThreadPoolExecutor(
            10,
            10,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(2000),
            threadFactory,
            handler
    );



    @Bean
    public EventBus eventBus(List<UserCashListener> userCashListeners) {
        AsyncEventBus asyncEventBus = new AsyncEventBus(executor);

        if (userCashListeners != null && !userCashListeners.isEmpty()) {
            for (UserCashListener observer : userCashListeners) {
                asyncEventBus.register(observer);
            }
        } else {
            log.warn("No UserCashListener found to register");
        }
        return asyncEventBus;
    }




}
