package cn.com.duiba.paycenter.service.third.wx.wxv3;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.bean.wx.v3.request.WxV3BaseRequest;
import cn.com.duiba.paycenter.bean.wx.v3.request.favor.WxFavorStocksBodyRequest;
import cn.com.duiba.paycenter.bean.wx.v3.request.favor.WxFavorStocksPathRequest;
import cn.com.duiba.paycenter.bean.wx.v3.request.favor.WxFavorUserCouponsBodyRequest;
import cn.com.duiba.paycenter.bean.wx.v3.request.favor.WxFavorUserCouponsPathRequest;
import cn.com.duiba.paycenter.bean.wx.v3.request.favor.WxFavorUserCouponsQueryBodyRequest;
import cn.com.duiba.paycenter.bean.wx.v3.request.favor.WxFavorUserCouponsQueryPathRequest;
import cn.com.duiba.paycenter.bean.wx.v3.result.WxV3BaseResult;
import cn.com.duiba.paycenter.bean.wx.v3.result.favor.WxFavorStocksResult;
import cn.com.duiba.paycenter.bean.wx.v3.result.favor.WxFavorUserCouponsQueryResult;
import cn.com.duiba.paycenter.bean.wx.v3.result.favor.WxFavorUserCouponsResult;

/**
 * 微信立减金
 * <AUTHOR>
 * @date 2023/5/17 4:46 PM
 */
public interface WxV3FavorApiService {

    /**
     * 发放微信立减金
     * <a herf="https://pay.weixin.qq.com/wiki/doc/apiv3_partner/apis/chapter9_1_2.shtml">官方文档</a>
     * @param mchId 商户号ID
     * @param request 请求参数
     * @return 发放结果
     * @throws BizException 参数/配置/请求异常
     */
    WxV3BaseResult<WxFavorUserCouponsResult> userCoupons(String mchId, WxV3BaseRequest<WxFavorUserCouponsPathRequest, WxFavorUserCouponsBodyRequest> request) throws BizException;

    /**
     * 微信立减金查询
     * <a herf="https://pay.weixin.qq.com/wiki/doc/apiv3_partner/apis/chapter9_1_6.shtml">官方文档</a>
     * @param mchId 商户号ID
     * @param request 请求参数
     * @return 发放结果
     * @throws BizException 参数/配置/请求异常
     */
    WxV3BaseResult<WxFavorUserCouponsQueryResult> userCouponsQuery(String mchId, WxV3BaseRequest<WxFavorUserCouponsQueryPathRequest, WxFavorUserCouponsQueryBodyRequest> request) throws BizException;

    /**
     * 查询批次详情API
     * <a herf="https://pay.weixin.qq.com/wiki/doc/apiv3_partner/apis/chapter9_1_5.shtml">官方文档</a>
     * @param mchId 商户号ID
     * @param request 请求参数
     * @return 发放结果
     * @throws BizException 参数/配置/请求异常
     */
    WxV3BaseResult<WxFavorStocksResult> stocks(String mchId, WxV3BaseRequest<WxFavorStocksPathRequest, WxFavorStocksBodyRequest> request) throws BizException;
}
