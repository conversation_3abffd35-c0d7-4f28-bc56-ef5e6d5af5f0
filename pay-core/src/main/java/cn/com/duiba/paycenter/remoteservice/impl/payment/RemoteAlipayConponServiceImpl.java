package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayActivityInfoResponse;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayCouponCardRequest;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayCouponCardResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteAlipayCouponService;
import cn.com.duiba.paycenter.service.payment.AlipayCouponService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
public class RemoteAlipayConponServiceImpl implements RemoteAlipayCouponService {

    @Autowired
    private AlipayCouponService alipayCouponService;

    @Override
    public AlipayActivityInfoResponse getAlipayActivityInfo(String subject, String actId) {
        return alipayCouponService.getAlipayActivityInfo(subject, actId);
    }

    @Override
    public AlipayCouponCardResponse grantAlipayCoupon(AlipayCouponCardRequest alipayCouponCardRequest) throws BizException {
        return alipayCouponService.grantAlipayCoupon(alipayCouponCardRequest);
    }

    @Override
    public String getAccessUrl(String subject, String redirectUrl) {
        return alipayCouponService.getAccessUrl(subject, redirectUrl);
    }

    @Override
    public String queryAlipayUserId(String subject, String authCode) {
        return alipayCouponService.queryAlipayUserId(subject, authCode);
    }

    @Override
    public boolean activityNotifyBySubjectType(Map<String, String> params, String subjectType) throws BizException {
        return alipayCouponService.activityNotifyBySubjectType(params, subjectType);
    }
}
