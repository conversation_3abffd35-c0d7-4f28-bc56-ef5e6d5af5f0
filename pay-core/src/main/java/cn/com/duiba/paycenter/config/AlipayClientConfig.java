package cn.com.duiba.paycenter.config;

import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConstants;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018/12/07
 */
@Configuration
public class AlipayClientConfig {
    @Resource
    private DuibaAlipayConfig duibaAlipayConfig;
    @Resource(name = "alipayHttpClient")
    private CloseableHttpClient httpClient;

    @Bean(name = "duibaAlipayClient")
    public AlipayClient getDuibaAlipayClient() {
        return new DuibaAlipayClient(httpClient
                , duibaAlipayConfig.getAliPayGetWay()
                , duibaAlipayConfig.getChargeAppId()
                , duibaAlipayConfig.getChargePrivateKey()
                , AlipayConstants.FORMAT_JSON
                , AlipayConstants.CHARSET_UTF8
                , duibaAlipayConfig.getChargePublicKey()
                , AlipayConstants.SIGN_TYPE_RSA2);
    }

    @Bean(name = "duia2AlipayClient")
    public AlipayClient getDuia2AlipayClient() {
        return new DuibaAlipayClient(httpClient
                , duibaAlipayConfig.getAliPayGetWay()
                , duibaAlipayConfig.getDuia2ChargeAppId()
                , duibaAlipayConfig.getDuia2ChargePrivateKey()
                , AlipayConstants.FORMAT_JSON
                , AlipayConstants.CHARSET_UTF8
                , duibaAlipayConfig.getDuia2ChargePublicKey()
                , AlipayConstants.SIGN_TYPE_RSA2);
    }

    @Bean(name = "duijieAlipayClient")
    public AlipayClient getDuijieAlipayClient() {
        return new DuibaAlipayClient(httpClient
                , duibaAlipayConfig.getAliPayGetWay()
                , duibaAlipayConfig.getDuijieChargeAppId()
                , duibaAlipayConfig.getDuijieChargePrivateKey()
                , AlipayConstants.FORMAT_JSON
                , AlipayConstants.CHARSET_UTF8
                , duibaAlipayConfig.getDuijieChargePublicKey()
                , AlipayConstants.SIGN_TYPE_RSA2);
    }

    @Bean(name = "rechargeAlipayClient")
    public AlipayClient getRechargeAlipayClient() {
        return new DuibaAlipayClient(httpClient
                , duibaAlipayConfig.getAliPayGetWay()
                , duibaAlipayConfig.getRechargeAppId()
                , duibaAlipayConfig.getRechargePrivateKey()
                , AlipayConstants.FORMAT_JSON
                , AlipayConstants.CHARSET_UTF8
                , duibaAlipayConfig.getRechargePublickey()
                , AlipayConstants.SIGN_TYPE_RSA2);
    }

    @Bean(name = "duia2RechargeAlipayClient")
    public AlipayClient getDuia2RechargeAlipayClient() {
        return new DuibaAlipayClient(httpClient
                , duibaAlipayConfig.getAliPayGetWay()
                , duibaAlipayConfig.getDuia2RechargeAppId()
                , duibaAlipayConfig.getDuia2RechargePrivateKey()
                , AlipayConstants.FORMAT_JSON
                , AlipayConstants.CHARSET_UTF8
                , duibaAlipayConfig.getDuia2RechargePublickey()
                , AlipayConstants.SIGN_TYPE_RSA2);
    }

    @Bean(name = "dujieRechargeAlipayClient")
    public AlipayClient getDuijieRechargeAlipayClient() {
        return new DuibaAlipayClient(httpClient
                , duibaAlipayConfig.getAliPayGetWay()
                , duibaAlipayConfig.getDuijieRechargeAppId()
                , duibaAlipayConfig.getDuijieRechargePrivateKey()
                , AlipayConstants.FORMAT_JSON
                , AlipayConstants.CHARSET_UTF8
                , duibaAlipayConfig.getDuijieRechargePublickey()
                , AlipayConstants.SIGN_TYPE_RSA2);
    }
}
