package cn.com.duiba.paycenter.remoteservice.impl.lshm;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.LshmChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmPayNotifyRequest;
import cn.com.duiba.paycenter.remoteservice.lshm.RemoteLshmPayService;
import cn.com.duiba.paycenter.service.payment.impl.lshm.LshmPayService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class RemoteLshmPayServiceImpl implements RemoteLshmPayService {
    @Resource
    private LshmPayService lshmPayService;
    @Override
    public LshmChargeNotifyResponse payNotify(Long appId, LshmPayNotifyRequest notifyRequest) throws BizException {
        return lshmPayService.payNotify(appId, notifyRequest);
    }
}
