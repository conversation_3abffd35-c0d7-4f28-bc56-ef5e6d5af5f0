package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.constant.SettleStatusEnum;
import cn.com.duiba.paycenter.dto.AcountChangeTotalDto;
import cn.com.duiba.paycenter.model.AccountChangeRecordDO;
import cn.com.duiba.paycenter.remoteservice.RemoteAccountRecordDetailService;
import cn.com.duiba.paycenter.service.DevAcountRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Title: RemoteAccountRecordDetailServiceImpl
 * @Description:

 */
@RestController
public class RemoteAccountRecordDetailServiceImpl implements RemoteAccountRecordDetailService {

    @Autowired
    private DevAcountRecordService devAcountRecordService;

    @Override
    public List<AcountChangeTotalDto> batchQueryFrozenCapital(List<Long> developIds, Date startDate, Date endDate, SettleStatusEnum statusEnum) {
        return devAcountRecordService.batchQueryFrozenCapital(developIds,startDate,endDate,statusEnum);
    }

    @Override
    public List<AcountChangeTotalDto> batchQueryConsumeCapital(List<Long> developIds, Date startDate, Date endDate, SettleStatusEnum statusEnum) {
        return devAcountRecordService.batchQueryConsumeCapital(developIds,startDate,endDate,statusEnum);
    }

    @Override
    public List<AccountChangeRecordDO> findPageListByStartId(Long startId, SettleStatusEnum statusEnum, int limit) {
        return devAcountRecordService.findPageListByStartId(startId,statusEnum,limit);
    }


}
