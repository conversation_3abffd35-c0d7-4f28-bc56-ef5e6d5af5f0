package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.boc.BocChargeNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteBocNotifyService;
import cn.com.duiba.paycenter.service.payment.BocNotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 中国银行支付通知
 * @date 2019/10/125:46 下午
 */
@RestController
public class RemoteBocNotifyServiceImpl implements RemoteBocNotifyService {


    @Autowired
    private BocNotifyService bocNotifyService;


    @Override
    public BocChargeNotifyResponse orderNotify(Map<String, String> map) throws BizException {
        return  bocNotifyService.orderNotify(map);
    }
}
