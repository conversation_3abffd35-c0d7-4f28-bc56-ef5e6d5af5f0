package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.dto.AlipayOrderDetailDto;
import cn.com.duiba.paycenter.remoteservice.RemoteAlipayOrderDetailService;
import cn.com.duiba.paycenter.service.AlipayOrderDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by Administrator on 2018/4/27.
 */
@RestController
public class RemoteAlipayOrderDetailServiceImpl implements RemoteAlipayOrderDetailService {

    @Autowired
    private AlipayOrderDetailService alipayOrderDetailService;
    @Override
    public AlipayOrderDetailDto insert(AlipayOrderDetailDto alipayOrderDetailDto) {
        alipayOrderDetailService.insert(alipayOrderDetailDto);
        return alipayOrderDetailDto;
    }

    @Override
    public AlipayOrderDetailDto findBySelfOrderId(Long id) {
        return alipayOrderDetailService.findBySelfOrderId(id);
    }

    @Override
    public void update(AlipayOrderDetailDto alipayOrderDetailDto) {
        alipayOrderDetailService.update(alipayOrderDetailDto);
    }
}
