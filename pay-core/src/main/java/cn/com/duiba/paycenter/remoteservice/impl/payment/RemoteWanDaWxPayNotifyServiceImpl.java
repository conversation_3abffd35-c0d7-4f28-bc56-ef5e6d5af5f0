package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.paycenter.dao.payment.ChargeOrderDao;
import cn.com.duiba.paycenter.dto.payment.charge.wanda.WanDaWxChargeResult;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayChargeNotifyResponse;
import cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity;
import cn.com.duiba.paycenter.enums.ChargeOrderStatusEnum;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteWanDaWxPayNotifyService;
import cn.com.duiba.paycenter.service.payment.WxPayConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

import java.sql.Date;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * @author: pengyi
 * @description:
 * @date: 2022/8/22 下午3:09
 */
@RestController
public class RemoteWanDaWxPayNotifyServiceImpl implements RemoteWanDaWxPayNotifyService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RemoteWanDaWxPayNotifyServiceImpl.class);

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");


    @Autowired
    private ChargeOrderDao chargeOrderDao;
    @Autowired
    private WxPayConfigService wxPayConfigService;

    @Override
    public WxPayChargeNotifyResponse orderNotify(WanDaWxChargeResult result) {
        WxPayChargeNotifyResponse wxPayChargeNotifyResponse = new WxPayChargeNotifyResponse();
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByOrderNo(result.getOrderNo());
        if (chargeOrderEntity == null) {
            LOGGER.warn("orderNo={} can not be find", result.getOrderNo());
            wxPayChargeNotifyResponse.setSuccess(false);
            return wxPayChargeNotifyResponse;
        }
        wxPayChargeNotifyResponse.setBizType(chargeOrderEntity.getBizType());
        wxPayChargeNotifyResponse.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
        //先判断状态是否处理过，处理过直接返回正确
        //如果是状态已经支付过了，或者退款了，就无需再处理，防止微信重复通知
        if (chargeOrderEntity.getChargeStatus() >= ChargeOrderStatusEnum.PAY_FAIL.getCode()) {
            wxPayChargeNotifyResponse.setSuccess(true);
            //如果不是失败的状态则都是成功的
            wxPayChargeNotifyResponse.setChargeSuccess(
                    !ChargeOrderStatusEnum.PAY_FAIL.getCode().equals(chargeOrderEntity.getChargeStatus()));
            wxPayChargeNotifyResponse.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            wxPayChargeNotifyResponse.setTransactionNo(chargeOrderEntity.getTransactionNo());
            return wxPayChargeNotifyResponse;
        } else {
            // 处理支付结果
            wxPayChargeNotifyResponse.setBizType(chargeOrderEntity.getBizType());
            wxPayChargeNotifyResponse.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            wxPayChargeNotifyResponse.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
            // 工行支付
//            wxPayChargeNotifyResponse.setBankType("ICBC");
            ChargeOrderEntity entity = new ChargeOrderEntity();
            entity.setOrderNo(chargeOrderEntity.getOrderNo());
            // 支付成功
            if (result.isSuccess()) {
                entity.setTransactionNo(result.getTransactionId());
                entity.setPaidTime(Date
                        .from(LocalDateTime.parse(result.getPayTime(), FORMATTER).atZone(ZoneId.systemDefault()).toInstant()));
                entity.setChargeStatus(ChargeOrderStatusEnum.PAY_SUCCESS.getCode());
                chargeOrderDao.update(entity);

                wxPayChargeNotifyResponse.setSuccess(true);
                wxPayChargeNotifyResponse.setChargeSuccess(true);
                wxPayChargeNotifyResponse.setTransactionNo(result.getTransactionId());
                return wxPayChargeNotifyResponse;
            }
            // 支付失败
            entity.setChargeStatus(ChargeOrderStatusEnum.PAY_FAIL.getCode());
            entity.setFailureCode(result.getCode());
            entity.setFailureMsg(result.getMsg());
            chargeOrderDao.update(entity);

            wxPayChargeNotifyResponse.setSuccess(true);
            wxPayChargeNotifyResponse.setChargeSuccess(false);
            wxPayChargeNotifyResponse.setTransactionNo(result.getTransactionId());
            return wxPayChargeNotifyResponse;        }
    }
}
