package cn.com.duiba.paycenter.service.payment.impl.lshm;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.LshmChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmPayNotifyRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmPayQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmPayRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmRefundQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmRefundRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.response.LshmPayResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.response.LshmPayQueryResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.response.LshmRefundQueryResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.response.LshmRefundResponse;

public interface LshmPayService {

    /**
     * 支付
     */
    LshmPayResponse doPay(Long appId, LshmPayRequest request) throws BizException;

    /**
     * 退款
     */
    LshmRefundResponse refund(Long appId, LshmRefundRequest request) throws BizException;


    /**
     * 支付查询
     */
    LshmPayQueryResponse payQuery(Long appId, LshmPayQueryRequest request) throws BizException;

    /**
     * 退款查询
     */
    LshmRefundQueryResponse refundQuery(Long appId, LshmRefundQueryRequest request) throws BizException;


    /**
     * 支付通知
     */
    LshmChargeNotifyResponse payNotify(Long appId, LshmPayNotifyRequest notifyRequest) throws BizException;

}


