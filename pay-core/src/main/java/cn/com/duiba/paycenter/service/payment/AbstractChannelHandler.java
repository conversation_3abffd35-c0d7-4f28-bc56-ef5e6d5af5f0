package cn.com.duiba.paycenter.service.payment;

import cn.com.duiba.api.enums.SubjectTypeEnum;
import cn.com.duiba.paycenter.config.Duia2WxpayConfig;
import cn.com.duiba.paycenter.config.DuibaAlipayConfig;
import cn.com.duiba.paycenter.config.DuibaWxpayConfig;
import cn.com.duiba.paycenter.config.DuijieWxpayConfig;
import cn.com.duiba.paycenter.config.FjgwWxpayConfig;
import cn.com.duiba.paycenter.config.WaitConfirmChargeOrderConfig;
import cn.com.duiba.paycenter.dao.payment.ChargeOrderDao;
import cn.com.duiba.paycenter.dto.payment.WaitConfirmChargeOrderDto;
import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.LshmChargeRequest;
import cn.com.duiba.paycenter.dto.payment.config.AlipayConfigDto;
import cn.com.duiba.paycenter.dto.payment.config.WxPayConfigDto;
import cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity;
import cn.com.duiba.paycenter.entity.payment.RefundOrderEntity;
import com.google.common.base.Objects;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018/11/15
 */
public abstract class AbstractChannelHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractChannelHandler.class);
    private static final DateTimeFormatter DATETIMEFORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    public static final DateTimeFormatter DATETIMEFORMATTER_YYYYMMDD_HHMMSS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Autowired
    private DuibaAlipayConfig duibaAlipayConfig;
    @Autowired
    private DuibaWxpayConfig duibaWxpayConfig;
    @Autowired
    private Duia2WxpayConfig duia2WxpayConfig;
    @Autowired
    private DuijieWxpayConfig duijieWxpayConfig;
    @Autowired
    private FjgwWxpayConfig fjgwWxpayConfig;
    @Resource
    private WaitConfirmChargeOrderConfig waitConfirmChargeOrderConfig;

    @Resource
    private WaitConfirmChargeOrderService waitConfirmChargeOrderService;

    @Resource
    private ChargeOrderDao chargeOrderDao;


    /**
     * 渠道组件必须注册下渠道管理类中
     */
    @PostConstruct
    public abstract void register();

    public <T extends BaseChargeRequest> ChargeOrderEntity createChargeOrderEntity(T chargeRequest) {
        ChargeOrderEntity chargeOrderEntity = new ChargeOrderEntity();

        chargeOrderEntity.setBizOrderNo(chargeRequest.getBizOrderNo());
        chargeOrderEntity.setBizType(chargeRequest.getBizType());
        chargeOrderEntity.setMetadata(chargeRequest.getMetadata());
        chargeOrderEntity.setAmount(chargeRequest.getAmount());
        chargeOrderEntity.setAppId(chargeRequest.getAppId());
        chargeOrderEntity.setOrderNo(chargeRequest.getOrderNo());
        chargeOrderEntity.setChannelType(chargeRequest.getChannelType());

        return chargeOrderEntity;
    }

    protected String convertTimeExpire(Date expireTime) {
        if (expireTime == null) {
            return null;
        }
        return expireTime.toInstant().atZone(ZoneId.of("GMT+08:00")).toLocalDateTime().format(DATETIMEFORMATTER);
    }


    protected String convertTimeExpireForFormatter(Date expireTime, DateTimeFormatter dateTimeFormatter) {
        if (expireTime == null) {
            return null;
        }
        return expireTime.toInstant().atZone(ZoneId.of("GMT+08:00")).toLocalDateTime().format(dateTimeFormatter);
    }

    /**
     * 获取兑吧的支付配置
     */
    protected AlipayConfigDto getDuibaChargeAlipayConfig() {
        return duibaAlipayConfig.getDuibaChargeAlipayConfig();
    }

    /**
     * 获取兑啊的支付配置
     */
    protected AlipayConfigDto getDuia2ChargeAlipayConfig() {
        return duibaAlipayConfig.getDuia2ChargeAlipayConfig();
    }

    /**
     * 获取兑捷的支付配置
     */
    protected AlipayConfigDto getDuijieChargeAlipayConfig() {
        return duibaAlipayConfig.getDuijieChargeAlipayConfig();
    }

    protected WxPayConfigDto getDuibaChargeWxpayConfig() {
        return duibaWxpayConfig.getDuibaWxpayConfig();
    }

    protected WxPayConfigDto getDuia2ChargeWxpayConfig() {
        return duia2WxpayConfig.getDuibaWxpayConfig();
    }

    protected WxPayConfigDto getDuijieChargeWxpayConfig() {
        return duijieWxpayConfig.getDuibaWxpayConfig();
    }

    protected WxPayConfigDto getFjgwChargeWxpayConfig() {
        return fjgwWxpayConfig.getDuibaWxpayConfig();
    }

    protected WxPayConfigDto getDuibaServiceProviderWxpayConfig() {
        return duibaWxpayConfig.getServiceProviderConfig();
    }

    protected WxPayConfigDto getDuia2ServiceProviderWxpayConfig() {
        return duia2WxpayConfig.getServiceProviderConfig();
    }

    protected WxPayConfigDto getDuijieServiceProviderWxpayConfig() {
        return duijieWxpayConfig.getServiceProviderConfig();
    }

    protected AlipayConfigDto getDuibaRechargeAlipayConfig() {
        return duibaAlipayConfig.getDuibaRechargeAlipayConfig();
    }

    protected AlipayConfigDto getDuia2RechargeAlipayConfig() {
        return duibaAlipayConfig.getDuia2RechargeAlipayConfig();
    }

    protected AlipayConfigDto getDuijieRechargeAlipayConfig() {
        return duibaAlipayConfig.getDuijieRechargeAlipayConfig();
    }

    protected String getAlipayOrderNotifyUrl() {
        return duibaAlipayConfig.getAlipayOrderNotifyUrl();
    }

    protected String getWxPayOrderNotifyUrl() {
        return duibaWxpayConfig.getOrderNotifyUrl();
    }

    protected String getWxPayRefundNotifyUrl() {
        return duibaWxpayConfig.getRefundNotifyUrl();
    }

    // 获取兑吧业务线退款支付宝账号
    protected AlipayConfigDto getDuibaGroupRefoundAlipayConfig(String subjectType) {
        AlipayConfigDto alipayConfigDto;
        if (Objects.equal(SubjectTypeEnum.DUIBA.getType(), subjectType)) {
            alipayConfigDto = getDuibaChargeAlipayConfig();
        } else if (Objects.equal(SubjectTypeEnum.DUIA.getType(), subjectType)) {
            alipayConfigDto = getDuia2ChargeAlipayConfig();
        } else if (Objects.equal(SubjectTypeEnum.DUIJIE.getType(), subjectType)) {
            alipayConfigDto = getDuijieChargeAlipayConfig();
        } else {
            LOGGER.error("AbstractChannelHandler getDuibaGroupRefoundAlipayConfig 未获取到相应的业务主体信息，默认走兑吧配置，业务主体为={}", subjectType);
            alipayConfigDto = getDuibaChargeAlipayConfig();
        }
        return alipayConfigDto;
    }

    // 获取兑吧业务线退款微信小程序账号
    protected WxPayConfigDto getDuibaGroupRefoundWxPayLiteConfigDto(String subjectType) {
        WxPayConfigDto wxPayConfigDto;
        if (Objects.equal(SubjectTypeEnum.DUIBA.getType(), subjectType) || StringUtils.isBlank(subjectType)) {
            wxPayConfigDto = getDuibaServiceProviderWxpayConfig();
        } else if (Objects.equal(SubjectTypeEnum.DUIA.getType(), subjectType)) {
            wxPayConfigDto = getDuia2ServiceProviderWxpayConfig();
        } else if (Objects.equal(SubjectTypeEnum.DUIJIE.getType(), subjectType)) {
            wxPayConfigDto = getDuijieServiceProviderWxpayConfig();
        } else {
            LOGGER.error("AbstractChannelHandler getDuibaGroupRefoundWxPayLiteConfigDto 未获取到相应的业务主体信息，默认走兑吧配置，业务主体为={}", subjectType);
            wxPayConfigDto = getDuibaServiceProviderWxpayConfig();
        }
        return wxPayConfigDto;
    }

    // 获取兑吧业务线退款微信公众号账号
    protected WxPayConfigDto getDuibaGroupRefoundWxPayMpConfigDto(String subjectType) {
        WxPayConfigDto wxPayConfigDto;
        if (Objects.equal(SubjectTypeEnum.DUIBA.getType(), subjectType)) {
            wxPayConfigDto = getDuibaChargeWxpayConfig();
        } else if (Objects.equal(SubjectTypeEnum.DUIA.getType(), subjectType)) {
            wxPayConfigDto = getDuia2ChargeWxpayConfig();
        } else if (Objects.equal(SubjectTypeEnum.DUIJIE.getType(), subjectType)) {
            wxPayConfigDto = getDuijieChargeWxpayConfig();
        } else if (Objects.equal(SubjectTypeEnum.FUJIAN_GUANWEN.getType(), subjectType)) {
            wxPayConfigDto = getFjgwChargeWxpayConfig();
        } else {
            LOGGER.error("AbstractChannelHandler getDuibaGroupRefoundWxPayMpConfigDto 未获取到相应的业务主体信息，默认走兑吧配置，业务主体为={}", subjectType);
            wxPayConfigDto = getDuibaChargeWxpayConfig();
        }
        return wxPayConfigDto;
    }

    /**
     * 创建退款退款查询任务
     */
    protected void createRefundQueryTask(ChargeOrderEntity chargeOrderEntity, RefundOrderEntity refundOrderEntity) {
        if(!waitConfirmChargeOrderConfig.getCustomQueryAppIds().contains(refundOrderEntity.getAppId())){
            return;
        }

        WaitConfirmChargeOrderDto waitConfirmChargeOrderDto = waitConfirmChargeOrderService.findRefundByBizOrderNoAndBizTypeAndChannel(refundOrderEntity.getOrderNo(), chargeOrderEntity.getBizType(), chargeOrderEntity.getChannelType());
        if (waitConfirmChargeOrderDto != null) {
            return;
        }

        WaitConfirmChargeOrderDto dto = new WaitConfirmChargeOrderDto();
        dto.setAppId(chargeOrderEntity.getAppId());
        dto.setOrderNo(refundOrderEntity.getOrderNo());
        dto.setBizOrderNo(refundOrderEntity.getBizOrderNo());
        dto.setBizType(chargeOrderEntity.getBizType());
        dto.setChannelType(chargeOrderEntity.getChannelType());
        dto.setOrderType(WaitConfirmChargeOrderDto.ORDER_TYPE_REFUND);
        dto.setOrderDate(new Date());
        dto.setNextTime(new Date(System.currentTimeMillis() + waitConfirmChargeOrderConfig.getRetryTimeIntervalMillis().get(0)));
        waitConfirmChargeOrderService.insert(dto);
    }

    /**
     * 创建支付主动查询任务
     */
    protected void createPayQueryTask(String orderNo) {
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByOrderNo(orderNo);

        //已存在则不做操作
        WaitConfirmChargeOrderDto waitConfirmChargeOrderDto = waitConfirmChargeOrderService
                .findChargeByBizOrderNoAndBizTypeAndChannel(chargeOrderEntity.getBizOrderNo(), chargeOrderEntity.getBizType(), chargeOrderEntity.getChannelType());
        if (waitConfirmChargeOrderDto != null) {
            return;
        }

        WaitConfirmChargeOrderDto dto = new WaitConfirmChargeOrderDto();
        dto.setAppId(chargeOrderEntity.getAppId());
        dto.setOrderNo(chargeOrderEntity.getOrderNo());
        dto.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
        dto.setBizType(chargeOrderEntity.getBizType());
        dto.setChannelType(chargeOrderEntity.getChannelType());
        dto.setOrderType(WaitConfirmChargeOrderDto.ORDER_TYPE_CHARGE);
        dto.setOrderDate(new Date());
        dto.setNextTime(new Date(System.currentTimeMillis() + waitConfirmChargeOrderConfig.getRetryTimeIntervalMillis().get(0)));
        waitConfirmChargeOrderService.insert(dto);
    }


    /**
     * 查询支付单
     * @param entity 退款实体
     * @return 支付单
     */
    protected ChargeOrderEntity getOrder(RefundOrderEntity entity) {
        ChargeOrderEntity chargeOrderEntity = null;
        if (StringUtils.isNotBlank(entity.getTransactionNo())) {
            chargeOrderEntity = chargeOrderDao.findByTransactionNo(entity.getTransactionNo());
        } else if (StringUtils.isNotBlank(entity.getChargeOrderNo())) {
            chargeOrderEntity = chargeOrderDao.findByOrderNo(entity.getChargeOrderNo());
        }
        return chargeOrderEntity;
    }



}
