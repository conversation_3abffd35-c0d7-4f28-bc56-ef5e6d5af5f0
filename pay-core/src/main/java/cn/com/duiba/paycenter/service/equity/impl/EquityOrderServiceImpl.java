package cn.com.duiba.paycenter.service.equity.impl;

import cn.com.duiba.paycenter.dao.equity.EquityOrderMapper;
import cn.com.duiba.paycenter.entity.equity.EquityOrderDetailEntity;
import cn.com.duiba.paycenter.entity.equity.EquityOrderEntity;
import cn.com.duiba.paycenter.service.equity.EquityOrderService;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/4/10 3:42 PM
 */
@Slf4j
@Service
public class EquityOrderServiceImpl implements EquityOrderService {
    
    @Resource
    private EquityOrderMapper equityOrderMapper;
    
    private final LoadingCache<String, EquityOrderEntity> orderCache = Caffeine.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES).maximumSize(1000).refreshAfterWrite(30, TimeUnit.SECONDS).build(this::loadByBizKey);
    
    @Override
    public EquityOrderEntity selectByBiz(Integer bizType, String bizNo) {
        if (bizType == null || bizNo == null) {
            return null;
        }
        return equityOrderMapper.selectByBiz(bizType, bizNo);
    }

    @Override
    public EquityOrderEntity selectByBizWithLocal(Integer bizType, String bizNo) {
        if (bizType == null || bizNo == null) {
            return null;
        }
        String bizKey = bizType + "_" + bizNo;
        EquityOrderEntity orderEntity = null;
        try {
            orderEntity = orderCache.get(bizKey);
        } catch (Exception e) {
            log.error("EquityOrder, bizType={}, bizNo={}", bizKey, bizNo, e);
        }
        if (orderEntity == null) {
            log.info("EquityOrder, cache not find, bizType={}, bizNo={}", bizKey, bizNo);
            return selectByBiz(bizType, bizNo);
        }
        return orderEntity;
    }
    
    private EquityOrderEntity loadByBizKey(String bizKey) {
        String[] keys = bizKey.split("_");
        Integer bizType = NumberUtils.toInt(keys[0], 0);
        return selectByBiz(bizType, keys[1]);
    }

    @Override
    public EquityOrderDetailEntity selectDetailByBiz(Integer bizType, String bizNo) {
        if (bizType == null || bizNo == null) {
            return null;
        }
        return equityOrderMapper.selectDetailByBiz(bizType, bizNo);
    }

    @Override
    public List<EquityOrderEntity> selectPageByTimeAndStatus(Date startTime, Date endTime, List<Integer> orderStatusList, Long lastId, Integer pageSize) {
        if (startTime == null || endTime == null || pageSize == null) {
            return Collections.emptyList();
        }
        return equityOrderMapper.selectPageByTimeAndStatus(startTime, endTime, orderStatusList, lastId, pageSize);
    }

    @Override
    public List<EquityOrderEntity> selectByBizList(List<Integer> bizTypeList, List<String> bizNoList) {
        if (CollectionUtils.isEmpty(bizTypeList) || CollectionUtils.isEmpty(bizNoList)) {
            return Collections.emptyList();
        }
        return equityOrderMapper.selectByBizList(bizTypeList, bizNoList);
    }

    @Override
    public int insert(EquityOrderDetailEntity entity) {
        if (entity == null || entity.getBizType() == null || entity.getBizNo() == null) {
            return 0;
        }
        return equityOrderMapper.insert(entity);
    }

    @Override
    public int updateProcessingStatus(EquityOrderDetailEntity entity, Integer beforeStatus) {
        if (entity == null || entity.getBizType() == null || entity.getBizNo() == null || beforeStatus == null || entity.getOutBizNo() == null || entity.getRecordNo() == null) {
            return 0;
        }
        return equityOrderMapper.updateProcessingStatus(entity, beforeStatus);
    }

    @Override
    public int updateResult(EquityOrderDetailEntity entity, Integer beforeStatus) {
        if (entity == null || entity.getBizType() == null || entity.getBizNo() == null || entity.getOrderStatus() == null || beforeStatus == null) {
            return 0;
        }
        return equityOrderMapper.updateResult(entity, beforeStatus);
    }
}
