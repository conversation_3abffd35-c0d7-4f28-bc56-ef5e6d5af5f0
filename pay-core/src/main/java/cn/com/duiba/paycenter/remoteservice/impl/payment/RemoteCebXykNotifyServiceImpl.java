package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.cebXyk.CebXykChargeNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteCebXykNotifyService;
import cn.com.duiba.paycenter.service.payment.CebXykNotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021-01-30
 */
@RestController
public class RemoteCebXykNotifyServiceImpl implements RemoteCebXykNotifyService {

    @Autowired
    private CebXykNotifyService cebXykNotifyService;

    @Override
    public CebXykChargeNotifyResponse orderNotify(String encryptData) throws BizException {
        return cebXykNotifyService.orderNotify(encryptData);
    }
}
