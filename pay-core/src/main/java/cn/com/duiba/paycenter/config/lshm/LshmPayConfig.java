package cn.com.duiba.paycenter.config.lshm;

import com.google.common.collect.Sets;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashSet;
import java.util.Set;

@Data
@Configuration
@ConfigurationProperties("lshm")
public class LshmPayConfig {
    private Set<Long> appIds = Sets.newHashSet(98752L,98751L);
    /**
     * 赵一鸣appId
     */
    private Set<Long> ymAppId = Sets.newHashSet(98751L);
    /**
     * 零食很忙appId
     */
    private Set<Long> hmAppId = Sets.newHashSet(98769L);
    /**
     * 正式	https://zympay.hnlshm.com/
     */
    private String ymPayDomain = "https://docs.dui88.com/mock/1944";
    /**
     * 正式：https://lshmpay.hnlshm.com/
     */
    private String hmPayDomain = "https://docs.dui88.com/mock/1944";

    /**
     * 一鸣rsa域名
     * 生产：https://o2o-portal.prod.zymls.com/api/open/out/pay
     */
    private String ymRsaDomain = "https://o2o-portal-lshm-staging.noprod.hnlshm.com/api/open/out/pay";
    /**
     * 很忙rsa域名
     * 生产：https://o2o-portal.prod.hnlshm.com/api/open/out/pay
     */
    private String hmRsaDomain = "https://o2o-portal-lshm-staging.noprod.hnlshm.com/api/open/out/pay";

    /**
     * 订单类型
     */
    private String orderType = "BOT058";

    /**
     * 支付方式-小程序微信支付
     */
    private String payMethodNo = "PM011";
    /**
     * 订单来源
     */
    private String orderFrom = "DUIBA";
    /**
     * 操作人
     */
    private String operator = "兑吧";
    /**
     * 终端来源
     */
    private String clientFrom = "兑吧积分商城";

    /**
     * 一鸣AES密钥
     * 生产：zAzHCUwfJ5en28sU
     */
    private String ymAesKey = "lPB3ulzIQ8yyNvJH";

    /**
     * 很忙AES密钥
     * 生产：54bfFlx3PkZ8ADPZ
     */
    private String hmAesKey = "lPB3ulzIQ8yyNvJH";

    /**
     * rsa接口请求的aesKey
     * 生产：VXDLcFRr/fI5c8dfsDsFZQ==
     */
    private String rasAesKey = "g8wXHmwsEGUIdB7LmwQOaw==";
    /**
     * 签名path
     */
    public String signPath = "/sign";
    /**
     * 加密path
     */
    public String privateEncryptPath = "/privateEncrypt";

    /**
     * 很忙RAS密钥
     */
    /**
     * 支付请求path
     */
    private String path = "/opp/aggregated/open/collect/sc";

    public static final String APP_ID = "appId";
    public static final String STORE_CODE = "storeCode";
    public static final String PAY_SERVICE_ID = "PC002A";
    public static final String PAY_QUERY_SERVICE_ID = "PC003A";
    public static final String REFUND_SERVICE_ID = "PC004A";
    public static final String REFUND_QUERY_SERVICE_ID = "PC005A";




    public String getUrl(Long appId) {
        if (ymAppId.contains(appId)) {
            return ymPayDomain + path;
        }
        if (hmAppId.contains(appId)) {
            return hmPayDomain + path;
        }
        throw new IllegalStateException("非法的appId");
    }

    public String getAesKey(Long appId) {
        if (ymAppId.contains(appId)) {
            return ymAesKey;
        }
        if (hmAppId.contains(appId)) {
            return hmAesKey;
        }
        throw new IllegalStateException("非法的appId");
    }

    public String getRsaUrl(Long appId){
        if (ymAppId.contains(appId)) {
            return ymRsaDomain;
        }
        if (hmAppId.contains(appId)) {
            return hmRsaDomain;
        }
        throw new IllegalStateException("非法的appId");
    }
}
