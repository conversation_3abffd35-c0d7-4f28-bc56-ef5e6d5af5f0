package cn.com.duiba.paycenter.remoteservice.impl;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import cn.com.duiba.paycenter.biz.DuibaPayOrdersBiz;
import cn.com.duiba.paycenter.constant.PayCenterErrorCode;
import cn.com.duiba.paycenter.params.DuibaPayOrdersExtraParams;
import cn.com.duiba.paycenter.result.PayOrdersResult;
import cn.com.duiba.paycenter.service.DuibaPayOrdersService;
import cn.com.duiba.paycenter.util.SignUtil;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class RemoteDuibaPayOrdersServiceImpl implements DuibaPayOrdersService{
	
	private static Logger log=LoggerFactory.getLogger(RemoteDuibaPayOrdersServiceImpl.class);
	@Autowired
	private DuibaPayOrdersBiz duibaPayOrdersBiz;
	@Override
	public PayOrdersResult payOrder(Long orderId, Long money, String sign,DuibaPayOrdersExtraParams p) {
		log.debug(getClass().getName()+".payOrder("+orderId+","+money+","+sign+",p"+")");
		Map<String, String> params=new HashMap<>();
		params.put("orderId", orderId+"");
		params.put("money", money+"");
		String checksign=SignUtil.sign(params);
		
		if(!checksign.equals(sign)){
			return new PayOrdersResult(false, PayCenterErrorCode.CodeParamsSignError, null);
		}
		return duibaPayOrdersBiz.payOrder(orderId, money,p);
	}

	@Override
	public PayOrdersResult backpayOrder(Long orderId, Long money, String sign) {
		log.debug(getClass().getName()+".backpayOrder("+orderId+","+money+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("orderId", orderId+"");
		params.put("money", money+"");
		String checksign=SignUtil.sign(params);
		
		if(!checksign.equals(sign)){
			return new PayOrdersResult(false, PayCenterErrorCode.CodeParamsSignError, null);
		}
		return duibaPayOrdersBiz.backpayOrder(orderId, money);
	}

	@Override
	public boolean checkActionSuccess(Long orderId, String actionType) {
		log.debug(getClass().getName()+".checkActionSuccess("+orderId+","+actionType+")");
		return duibaPayOrdersBiz.checkActionSuccess(orderId, actionType);
	}

}
