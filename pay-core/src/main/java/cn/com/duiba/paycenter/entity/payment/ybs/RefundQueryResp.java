package cn.com.duiba.paycenter.entity.payment.ybs;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.util.ybs.YbsXStream;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * @ClassName RefundQueryResp
 * @Description 退款查询返回
 * <AUTHOR>
 * @Date 2022/8/26 15:17
 */
public class RefundQueryResp {

    @XStreamAlias("version")
    private String version;

    @XStreamAlias("charset")
    private String charset;

    @XStreamAlias("sign_type")
    private String signType;

    @XStreamAlias("status")
    private String status;

    @XStreamAlias("message")
    private String message;

    @XStreamAlias("result_code")
    private String resultCode;

    @XStreamAlias("mch_id")
    private String mchId;

    /**
     * 交易完成时间，格式为 yyyyMMddHHmmss，
     * 如 2009 年 12 月 27 日 9 点 10 分 10 秒表示为 20091227091010。时区为 GMT+8 beijing。该时间取自平台服务器
     */
    @XStreamAlias("time_end")
    private String timeEnd;

    /**
     * 申请退款金额
     */
    @XStreamAlias("refund_fee")
    private String refundFee;

    @XStreamAlias("total_fee")
    private String totalFee;

    /**
     * 商户退款单号
     */
    @XStreamAlias("out_refund_no")
    private String outRefundNo;

    /**
     * 原交易类型，alipay、wechat、unionpay
     */
    @XStreamAlias("trade_type_0")
    private String tradeType0;

    @XStreamAlias("channel_id")
    private String channelId;

    @XStreamAlias("device_info")
    private String deviceInfo;

    @XStreamAlias("nonce_str")
    private String nonceStr;

    @XStreamAlias("err_code")
    private String errCode;

    @XStreamAlias("err_msg")
    private String errMsg;

    @XStreamAlias("sign")
    private String sign;

    /**以下字段在 status 和 result_code 都为 0 的时候有返回**/
    /**
     * 微信、支付宝、云闪付第三方单号
     */
    @XStreamAlias("transaction_id")
    private String transactionId;
    /**
     * 商户系统内部的订单号
     */
    @XStreamAlias("out_trade_no")
    private String outTradeNo;
    /**
     * 商户退款单号
     */
    @XStreamAlias("out_refund_no_0")
    private String outRefundNo0;

    /**
     * 退款 id
     */
    @XStreamAlias(" refund_id_0")
    private String  refundId0;

    /**
     * 商户退款单号
     * ORIGINAL—原路退款，默认
     */
    @XStreamAlias("refund_channel_0")
    private String refundChannel0;

    /**
     * 申请退款金额
     * 退款总金额,单位为分,可以做部分退款
     */
    @XStreamAlias("refund_fee_0")
    private String refundFee0;

    /**
     * 退款金额
     * 去掉非充值代金券退款金额后的退款金额，
     * 退款金额=申请退款金额-非充值代金券退款金额，
     * 退款金额<=申请退款金额
     */
    @XStreamAlias("settlement_refund_fee_0")
    private String settlementRefundFee0;

    /**
     * 现金券退款金额
     *  现金券退款金额 <= 退款金额， 退款金额-现金券退款金额为现 金
     */
    @XStreamAlias("coupon_refund_fee_0")
    private String couponRefundFee0;

    /**
     * 退款时间
     *  退款成功时存在，退款成功时间 yyyyMMddHHmmss
     */
    @XStreamAlias("refund_time_0")
    private String refundTime0;

    /**
     * 退款状态
     *  SUCCESS—退款成功
     *  FAIL—退款失败
     *  PROCESSING—退款处理中
     *  CHANGE—转入代发，退款到银行发现用户的卡作废或者冻结 了，导致原路退款银行卡失败，资金回流到商户的现金帐号，需 要商户人工干预，通过线下或者平台转账的方式进行退款。
     */
    @XStreamAlias("refund_status_0")
    private String refundStatus0;

    /**
     * 以下变量文档中未描述
     */
    @XStreamAlias("trade_state")
    private String tradeState;

    @XStreamAlias("service")
    private String service;

    @XStreamAlias("refund_count")
    private String refundCount;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getTimeEnd() {
        return timeEnd;
    }

    public void setTimeEnd(String timeEnd) {
        this.timeEnd = timeEnd;
    }

    public String getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(String refundFee) {
        this.refundFee = refundFee;
    }

    public String getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(String totalFee) {
        this.totalFee = totalFee;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getTradeType0() {
        return tradeType0;
    }

    public void setTradeType0(String tradeType0) {
        this.tradeType0 = tradeType0;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getOutRefundNo0() {
        return outRefundNo0;
    }

    public void setOutRefundNo0(String outRefundNo0) {
        this.outRefundNo0 = outRefundNo0;
    }

    public String getRefundId0() {
        return refundId0;
    }

    public void setRefundId0(String refundId0) {
        this.refundId0 = refundId0;
    }

    public String getRefundChannel0() {
        return refundChannel0;
    }

    public void setRefundChannel0(String refundChannel0) {
        this.refundChannel0 = refundChannel0;
    }

    public String getRefundFee0() {
        return refundFee0;
    }

    public void setRefundFee0(String refundFee0) {
        this.refundFee0 = refundFee0;
    }

    public String getSettlementRefundFee0() {
        return settlementRefundFee0;
    }

    public void setSettlementRefundFee0(String settlementRefundFee0) {
        this.settlementRefundFee0 = settlementRefundFee0;
    }

    public String getCouponRefundFee0() {
        return couponRefundFee0;
    }

    public void setCouponRefundFee0(String couponRefundFee0) {
        this.couponRefundFee0 = couponRefundFee0;
    }

    public String getRefundTime0() {
        return refundTime0;
    }

    public void setRefundTime0(String refundTime0) {
        this.refundTime0 = refundTime0;
    }

    public String getRefundStatus0() {
        return refundStatus0;
    }

    public void setRefundStatus0(String refundStatus0) {
        this.refundStatus0 = refundStatus0;
    }

    public String getTradeState() {
        return tradeState;
    }

    public void setTradeState(String tradeState) {
        this.tradeState = tradeState;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getRefundCount() {
        return refundCount;
    }

    public void setRefundCount(String refundCount) {
        this.refundCount = refundCount;
    }

    public static RefundQueryResp fromXml(String xmlStr) throws BizException {
        try {
            xmlStr = xmlStr.trim();
            XStream xstream = YbsXStream.getRefundQueryInstance();
            xstream.alias("xml", RefundQueryResp.class);
            xstream.processAnnotations(RefundQueryResp.class);
            return (RefundQueryResp) xstream.fromXML(xmlStr);
        } catch (Exception e) {
            throw new BizException(e.getMessage()+ ", xmlResp:" +xmlStr);
        }
    }
}
