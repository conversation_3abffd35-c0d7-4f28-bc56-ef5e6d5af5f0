package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.mock.MockChargeNotifyResponse;
import cn.com.duiba.paycenter.enums.BizTypeEnum;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteMockNotifyService;
import cn.com.duiba.paycenter.service.payment.MockNotifyService;
import cn.com.duiba.paycenter.util.MockUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by xutao on 2020/4/24.
 */
@RestController
public class RemoteMockNotifyServiceImpl implements RemoteMockNotifyService {
    @Autowired
    private MockNotifyService mockNotifyService;

    @Override
    public MockChargeNotifyResponse orderNotify(String bizOrderNo) throws BizException {
        MockUtils.isMockCharge();
        return mockNotifyService.orderNotify(bizOrderNo, BizTypeEnum.ORD.getCode());
    }
}
