package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.dto.DeveloperMoneyInfoDto;
import cn.com.duiba.paycenter.remoteservice.RemoteTpcDlpIncomeMoneyService;
import cn.com.duiba.paycenter.service.TpcDlpIncomeMoneyService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * author z<PERSON><PERSON><PERSON>
 * date 2018-12-12-14:40
 */
@RestController
public class RemoteTpcDlpIncomeMoneyServiceImpl implements RemoteTpcDlpIncomeMoneyService {

    @Resource
    private TpcDlpIncomeMoneyService tpcDlpIncomeMoneyService;

    @Override
    public List<DeveloperMoneyInfoDto> batchFindIncomeMoney(List<Long> developerIds) {
        return tpcDlpIncomeMoneyService.batchFindIncomeMoney(developerIds);
    }
}
