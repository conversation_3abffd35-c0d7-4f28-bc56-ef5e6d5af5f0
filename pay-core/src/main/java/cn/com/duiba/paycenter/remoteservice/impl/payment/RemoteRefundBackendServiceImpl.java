package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.paycenter.dto.payment.refund.RefundOrderDto;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteRefundBackendService;
import cn.com.duiba.paycenter.service.payment.RefundService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/02/26
 */
@RestController
public class RemoteRefundBackendServiceImpl implements RemoteRefundBackendService {
    @Resource
    private RefundService refundService;

    @Override
    public RefundOrderDto findByTransactionNo(String transactionNo) {
        return refundService.findByTransactionNo(transactionNo);
    }

    @Override
    public RefundOrderDto findByRefundOrderNo(String refundOrderNo) {
        return refundService.findByRefundOrderNo(refundOrderNo);
    }

    @Override
    public List<RefundOrderDto> findByBizNo(String bizOrderNo) {
        return refundService.findByBizNo(bizOrderNo);
    }

    @Override
    public List<RefundOrderDto> selectByBizOrderNosAndTypeForRefundSuccess(List<String> bizOrderNos, Integer bizType) {
        return refundService.selectByBizOrderNosAndTypeForRefundSuccess(bizOrderNos,bizType);
    }
}
