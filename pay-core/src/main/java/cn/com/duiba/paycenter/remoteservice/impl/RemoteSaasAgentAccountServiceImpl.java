package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.constant.AccountChangeConstant;
import cn.com.duiba.paycenter.dao.SaasAgentAccountDao;
import cn.com.duiba.paycenter.dao.SaasAgentAccountLogDao;
import cn.com.duiba.paycenter.dto.SaasAgentAccountDto;
import cn.com.duiba.paycenter.entity.SaasAgentAccountEntity;
import cn.com.duiba.paycenter.entity.SaasAgentAccountLogEntity;
import cn.com.duiba.paycenter.remoteservice.RemoteSaasAgentAccountService;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/5/23 09:37
 */
@RestController
public class RemoteSaasAgentAccountServiceImpl implements RemoteSaasAgentAccountService {

    private static final Logger logger = LoggerFactory.getLogger(RemoteSaasAgentAccountServiceImpl.class);

    @Autowired
    private SaasAgentAccountDao saasAgentAccountDao;

    @Autowired
    private SaasAgentAccountLogDao saasAgentAccountLogDao;

    @Override
    public SaasAgentAccountDto findByAgentId(Long agentId) {
        SaasAgentAccountEntity agentAccount = saasAgentAccountDao.findByAgentId(agentId);
        return BeanUtils.copy(agentAccount, SaasAgentAccountDto.class);
    }

    @Override
    public List<SaasAgentAccountDto> findByAgentIds(List<Long> agentIds) {
        List<SaasAgentAccountEntity> agentAccounts = saasAgentAccountDao.findByAgentIds(agentIds);
        return BeanUtils.copyList(agentAccounts, SaasAgentAccountDto.class);
    }

    @Override
    public SaasAgentAccountDto findByAgentId4update(Long agentId) {
        SaasAgentAccountEntity agentAccount = saasAgentAccountDao.findByAgentId4update(agentId);
        return BeanUtils.copy(agentAccount, SaasAgentAccountDto.class);
    }

    @Override
    public SaasAgentAccountDto insert(Long agentId, Long adminId) {
        logger.info("新建代理商账户，agentId[{}]，adminId:[{}].", agentId, adminId);
        SaasAgentAccountEntity agentAccount;
        try {
            agentAccount = new SaasAgentAccountEntity();
            Date date = new Date();
            agentAccount.setVersion(1);
            agentAccount.setMoney(0L);
            agentAccount.setGmtModified(date);
            agentAccount.setGmtCreate(date);
            agentAccount.setAgentId(agentId);
            saasAgentAccountDao.insert(agentAccount);

            // 插入日志
            SaasAgentAccountLogEntity accountLog = new SaasAgentAccountLogEntity();
            accountLog.setAgentId(agentId);
            accountLog.setRelationType("create-agent-" + agentId);
            accountLog.setRelationId(0L);
            accountLog.setActionType("create");
            accountLog.setChangeMoney(0L);
            accountLog.setMemo("新建代理商账户");
            accountLog.setOperationId(adminId);
            accountLog.setBeforeBalance(0L);
            accountLog.setChangeKind(AccountChangeConstant.CHANGE_KIND_ADD);
            accountLog.setAfterBalance(0L);
            saasAgentAccountLogDao.insert(accountLog);
        } catch (DuplicateKeyException e) {
            logger.error("代理商账户已经存在，请勿重复添加", e);
            agentAccount = saasAgentAccountDao.findByAgentId(agentId);
        }
        return BeanUtils.copy(agentAccount, SaasAgentAccountDto.class);
    }
}
