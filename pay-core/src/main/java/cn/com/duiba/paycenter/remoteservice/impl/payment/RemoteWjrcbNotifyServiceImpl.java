package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.paycenter.dto.payment.charge.wjrcb.WjrcbChargeNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteWjrcbNotifyService;
import cn.com.duiba.paycenter.service.payment.WjrcbNotifyService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019/07/31
 */
@RestController
public class RemoteWjrcbNotifyServiceImpl implements RemoteWjrcbNotifyService {
    @Resource
    private WjrcbNotifyService notifyService;
    @Override
    public WjrcbChargeNotifyResponse orderNotify(String queryString) {
        return notifyService.orderNotify(queryString);
    }
}
