package cn.com.duiba.paycenter.service.equity.impl;

import cn.com.duiba.paycenter.dao.equity.AlipayMchConfMapper;
import cn.com.duiba.paycenter.entity.equity.AlipayMchConfDetailEntity;
import cn.com.duiba.paycenter.entity.equity.AlipayMchConfEntity;
import cn.com.duiba.paycenter.service.equity.AlipayMchConfService;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.CertAlipayRequest;
import com.alipay.api.DefaultAlipayClient;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 支付宝商户配置表 服务实现类
 * <AUTHOR>
 * @date 2023/4/17 3:22 PM
 */
@Slf4j
@Service
public class AlipayMchConfServiceImpl implements AlipayMchConfService {
    
    @Resource
    private AlipayMchConfMapper alipayMchConfMapper;

    /**
     * 支付宝网关（固定）。
     */
    private static final String URL = "https://openapi.alipay.com/gateway.do";

    /**
     * 参数返回格式，只支持 JSON 格式（固定）。
     */
    private static final String FORMAT = "JSON";

    /**
     * 编码集，支持 GBK/UTF-8。
     */
    private static final String CHARSET = "UTF-8";

    /**
     * 生成签名字符串所使用的签名算法类型，目前支持 RSA2 和 RSA，推荐使用 RSA2。
     */
    private static final String SIGN_TYPE = "RSA2";

    private final LoadingCache<String, Optional<AlipayMchConfEntity>> mchConfCache = Caffeine.newBuilder().maximumSize(100).expireAfterWrite(5, TimeUnit.SECONDS).refreshAfterWrite(2, TimeUnit.SECONDS).build(appId -> {
        AlipayMchConfEntity alipayMchConfEntity = selectByAppId(appId);
        return Optional.ofNullable(alipayMchConfEntity);
    });
    
    private final LoadingCache<String, Optional<AlipayMchConfDetailEntity>> mchConfDetailCache = Caffeine.newBuilder().maximumSize(100).expireAfterWrite(5, TimeUnit.SECONDS).refreshAfterWrite(2, TimeUnit.SECONDS).build(appId -> {
        AlipayMchConfDetailEntity detailEntity = selectDetailByAppId(appId);
        return Optional.ofNullable(detailEntity);
    });

    private final LoadingCache<String, Optional<AlipayClient>> alipayClientCache = Caffeine.newBuilder().maximumSize(100).expireAfterWrite(2, TimeUnit.SECONDS).refreshAfterWrite(1, TimeUnit.SECONDS).build(appId -> {
        AlipayClient client = buildCertClient(appId);
        return Optional.ofNullable(client);
    });
    


    @Override
    public AlipayMchConfEntity selectByAppIdWithLocal(String appId) {
        if (StringUtils.isBlank(appId)) {
            return null;
        }
        Optional<AlipayMchConfEntity> optional = mchConfCache.get(appId);
        if (Objects.isNull(optional)) {
            return null;
        }
        return optional.orElse(null);
    }

    @Override
    public AlipayMchConfDetailEntity selectDetailByAppIdWithLocal(String appId) {
        if (StringUtils.isBlank(appId)) {
            return null;
        }
        Optional<AlipayMchConfDetailEntity> optional = mchConfDetailCache.get(appId);
        if (Objects.isNull(optional)) {
            return null;
        }
        return optional.orElse(null);
    }

    /**
     * 根据主键查询
     * @param appId 支付宝应用id
     * @return 支付宝商户配置
     */
    private AlipayMchConfEntity selectByAppId(String appId) {
        if (StringUtils.isBlank(appId)) {
            return null;
        }
        return alipayMchConfMapper.selectByAppId(appId);
    }



    /**
     * 根据主键查询详情
     * @param appId 支付宝应用id
     * @return 支付宝商户配置
     */
    private AlipayMchConfDetailEntity selectDetailByAppId(String appId) {
        if (StringUtils.isBlank(appId)) {
            return null;
        }
        return alipayMchConfMapper.selectDetailByAppId(appId);
    }

    @Override
    public AlipayClient getCertClientWithLocal(String appId) {
        if (StringUtils.isBlank(appId)) {
            return null;
        }
        Optional<AlipayClient> optional = alipayClientCache.get(appId);
        if (Objects.isNull(optional)) {
            return null;
        }
        return optional.orElse(null);
    }

    /**
     * 构建调用支付宝的客户端（公钥证书模式）
     * <a herf="https://opendocs.alipay.com/common/02kkv2?pathHash=358ff034">官方文档</a>
     * @param appId 支付宝应用id
     * @return 客户端
     */
    private AlipayClient buildCertClient(String appId) {
        AlipayMchConfDetailEntity detailEntity = selectDetailByAppIdWithLocal(appId);
        if (detailEntity == null) {
            return null;
        }
        try {
            CertAlipayRequest certAlipayRequest = new CertAlipayRequest();
            certAlipayRequest.setServerUrl(URL);
            certAlipayRequest.setAppId(appId);
            certAlipayRequest.setPrivateKey(detailEntity.getPrivateKey());
            certAlipayRequest.setFormat(FORMAT);
            certAlipayRequest.setCharset(CHARSET);
            certAlipayRequest.setSignType(SIGN_TYPE);
            //应用公钥证书
            certAlipayRequest.setCertContent(getCertStr(detailEntity.getAppCert()));
            //支付宝公钥证书
            certAlipayRequest.setAlipayPublicCertContent(getCertStr(detailEntity.getAlipayCert()));
            //支付宝CA根证书
            certAlipayRequest.setRootCertContent(getCertStr(detailEntity.getAlipayRootCert()));
            return new DefaultAlipayClient(certAlipayRequest);
        } catch (DecoderException e) {
            log.error("AlipayMchConf, decoder cert error, appId={}", appId, e);
            return null;
        } catch (AlipayApiException e) {
            log.error("AlipayMchConf, buildAlipayClient error, appId={}", appId, e);
            return null;
        }
    }

    private String getCertStr(String dbCertStr) throws DecoderException {
        byte[] data = Hex.decodeHex(dbCertStr.toCharArray());
        return new String(data);
    }
}
