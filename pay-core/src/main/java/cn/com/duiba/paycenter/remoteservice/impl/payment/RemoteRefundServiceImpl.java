package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifeCreateRefundResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifeRefundRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifeRefundResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cus.YbsRefundOrderReq;
import cn.com.duiba.paycenter.dto.payment.charge.cus.YbsRefundOrderResp;
import cn.com.duiba.paycenter.dto.payment.charge.elife.IcbcELifeCreateRefundResponse;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.refund.NbcbRefundRequestDto;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.refund.NbcbRefundResponseDto;
import cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay.UnionPayUmsRefundRequest;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayRefundRequest;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayRefundResponse;
import cn.com.duiba.paycenter.dto.payment.charge.xib.refund.XibRefundRequestDTO;
import cn.com.duiba.paycenter.dto.payment.refund.RefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.RefundResponse;
import cn.com.duiba.paycenter.dto.payment.refund.abc.AbcRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.alipay.AlipayRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.boc.BocRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.citic.CiticRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.cmbonenet.CmbOneNetRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.duibaLive.bf.DuibaLiveBFRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.duibaLive.installment.DuibaLiveInstallmentRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.duibaLive.mp.DuibaLiveMpRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.icbc.IcbcRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.icbc.IcbcRefundResponse;
import cn.com.duiba.paycenter.dto.payment.refund.icbc.elife.IcbcELifeRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.mock.MockRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.shouxin.ShouxinPayRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.shouxin.ShouxinPayRefundResponse;
import cn.com.duiba.paycenter.dto.payment.refund.wjrcb.WjrcbRefundRequest;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteRefundService;
import cn.com.duiba.paycenter.service.payment.RefundService;
import cn.com.duiba.paycenter.util.MockUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018/11/27
 */
@RestController
public class RemoteRefundServiceImpl implements RemoteRefundService {

    @Resource
    private RefundService refundService;

    @Override
    public RefundResponse refund(RefundRequest refundRequest) throws BizException {
        return refundService.refund(refundRequest);
    }

    @Override
    public RefundResponse commonRefund(RefundRequest refundRequest) throws BizException {
        return refundService.commonRefund(refundRequest);
    }

    @Override
    public RefundResponse alipayRefund(AlipayRefundRequest alipayRefundRequest) throws BizException {
        return refundService.alipayRefund(alipayRefundRequest);
    }

    @Override
    public RefundResponse wjrcbRefund(WjrcbRefundRequest refundRequest) throws BizException {
        return refundService.wjrcbRefund(refundRequest);
    }

    @Override
    public RefundResponse abcRefund(AbcRefundRequest request) {
        return refundService.abcRefund(request);
    }

    @Override
    public RefundResponse bocRefund(BocRefundRequest request) {
        return refundService.bocRefund(request);
    }

    @Override
    public RefundResponse wandaRefund(RefundRequest request) {
        return refundService.wandaRefund(request);
    }

    @Override
    public RefundResponse citicRefund(CiticRefundRequest request) {
        return refundService.citicRefund(request);
    }

    @Override
    public RefundResponse cmbOneNetPayRefund(CmbOneNetRefundRequest refundRequest) throws BizException {
        return refundService.cmbOneNetPayRefund(refundRequest);
    }

    @Override
    public ShouxinPayRefundResponse shouxinPayRefund(ShouxinPayRefundRequest refundRequest) throws BizException {
        return refundService.shouxinPayRefund(refundRequest);
    }

    @Override
    public RefundResponse mockRefund(MockRefundRequest request) throws BizException{
        MockUtils.isMockCharge();
        return refundService.mockRefund(request);
    }

    @Override
    public UnionPayRefundResponse unionPayRefund(UnionPayRefundRequest refundRequest) throws BizException {
        return refundService.unionPayRefund(refundRequest);
    }

    @Override
    public RefundResponse icbcPayRefund(IcbcRefundRequest refundRequest) throws BizException {
        return refundService.icbcRefund(refundRequest);
    }

    @Override
    public RefundResponse duibaLiveMpPayPayRefund(DuibaLiveMpRefundRequest refundRequest) throws BizException {
        return refundService.duibaLiveMpPayPayRefund(refundRequest);
    }

    @Override
    public RefundResponse duibaLiveInstallmentPayPayRefund(DuibaLiveInstallmentRefundRequest refundRequest) throws BizException {
        return refundService.duibaLiveInstallmentPayPayRefund(refundRequest);
    }

    @Override
    public RefundResponse duibaLiveBFPayPayRefund(DuibaLiveBFRefundRequest refundRequest) throws BizException {
        return refundService.duibaLiveBFPayPayRefund(refundRequest);
    }

    @Override
    public RefundResponse ningboBankPayRefund(NbcbRefundRequestDto nbcbRefundRequestDto) {
        return refundService.ningboBankPayRefund(nbcbRefundRequestDto);
    }

    @Override
    public YbsRefundOrderResp activity62VipRefund(YbsRefundOrderReq refundOrderReq) {
        return refundService.activity62VipRefund(refundOrderReq);
    }

    @Override
    public RefundResponse xibPayRefund(XibRefundRequestDTO xibRefundRequestDTO) {
        return refundService.xibPayRefund(xibRefundRequestDTO);
    }

    @Override
    public RefundResponse unionPayUmsRefund(UnionPayUmsRefundRequest refundRequest) {
        return refundService.unionPayUmsRefund(refundRequest);
    }

    @Override
    public CmbLifeCreateRefundResponse cmbLifeRefund(CmbLifeRefundRequest refundRequest) {
        return refundService.cmbLifeRefund(refundRequest);
    }

    @Override
    public IcbcELifeCreateRefundResponse icbcELifePayRefund(IcbcELifeRefundRequest refundRequest) {
        return refundService.icbcELifePayRefund(refundRequest);
    }
}
