package cn.com.duiba.paycenter.remoteservice.impl.equity;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.bean.wx.v3.result.WxV3BaseResult;
import cn.com.duiba.paycenter.bean.wx.v3.result.cert.WxDownloadCertificateData;
import cn.com.duiba.paycenter.bean.wx.v3.result.cert.WxDownloadCertificateResult;
import cn.com.duiba.paycenter.dao.equity.WxpayMchConfMapper;
import cn.com.duiba.paycenter.entity.equity.WxpayMchConfCertEntity;
import cn.com.duiba.paycenter.entity.equity.WxpayMchConfEntity;
import cn.com.duiba.paycenter.enums.WxpayMchCertTypeEnum;
import cn.com.duiba.paycenter.remoteservice.equity.RemoteSaveMchConfigService;
import cn.com.duiba.paycenter.service.equity.WxpayMchConfCertService;
import cn.com.duiba.paycenter.service.third.wx.wxv3.WxV3CertApiService;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.*;
import java.net.URLEncoder;
import java.security.PublicKey;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/7/23 17:50
 */
@RestController
public class RemoteSaveMchConfigServiceImpl implements RemoteSaveMchConfigService {
    private static final SimpleDateFormat dayDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");

    private static final SimpleDateFormat dayNumDateFormat = new SimpleDateFormat("yyyyMMdd");


    @Resource
    private WxV3CertApiService wxV3CertApiService;
    @Resource
    private WxpayMchConfMapper wxpayMchConfMapper;

    @Resource
    private WxpayMchConfCertService wxpayMchConfCertService;

    public static void main(String[] args) throws IOException, CertificateException {
        // 打印微信信息
        /*String mchName = "新疆天润";
        String prizeKeyPath = "/Users/<USER>/Downloads/新疆天润/apiclient_key.pem";
        String certPath = "/Users/<USER>/Downloads/新疆天润/apiclient_cert.pem";
        printWx(mchName, prizeKeyPath, certPath);*/
    }

    private static void printWx(String mchName, String prizeKeyPath, String certPath) throws IOException {
        String privateKey = getPrizeKey(prizeKeyPath);
        String merchantCert = getCertStr(certPath);

        print("mchName", urlEncode(mchName));
        print("privateKey", privateKey);
        print("merchantCert", merchantCert);

    }

    private static String getPrizeKey(String path) throws IOException {
        try (FileInputStream inputStream = new FileInputStream(path); BufferedInputStream bis = new BufferedInputStream(inputStream); ByteArrayOutputStream bos = new ByteArrayOutputStream();) {
            int date;
            while ((date = bis.read()) != -1) {
                bos.write(date);
            }
            String keyString = bos.toString();
            String newKey = keyString.replace("-----BEGIN PRIVATE KEY-----", "").replace("-----END PRIVATE KEY-----", "").replaceAll("\\s+", "");
            return urlEncode(newKey);
        }
    }

    private static String urlEncode(String str) throws UnsupportedEncodingException {
        return URLEncoder.encode(str, "utf-8");
    }

    private static String getCertStr(String path) throws IOException {
        try (FileInputStream inputStream = new FileInputStream(path); BufferedInputStream bis = new BufferedInputStream(inputStream); ByteArrayOutputStream bos = new ByteArrayOutputStream();) {
            int date;
            while ((date = bis.read()) != -1) {
                bos.write(date);
            }
            byte[] bytes = bos.toByteArray();
            char[] chars = Hex.encodeHex(bytes);
            return new String(chars);
        }
    }

    private static String getPublicKeyByCrt(String path) throws CertificateException, FileNotFoundException {
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        X509Certificate cert = (X509Certificate) cf.generateCertificate(new FileInputStream(path));
        PublicKey publicKey = cert.getPublicKey();
        String encode = Base64.getEncoder().encodeToString(publicKey.getEncoded());
        return encode.replaceAll("\\s+", "").replaceAll("\n", "");
    }

    private static void print(String name, String value) {
        System.out.println("-----------------" + name + "--------------------");
        System.out.println(value);
        System.out.println("-----------------" + name + "--------------------");
        System.out.println();
        System.out.println();
        System.out.println();
    }


    /**
     * 添加微信商户号
     * curl '127.0.0.1:6686/test/addWxMch?mchId=&mchName=&apiV3Key=&serialNumber=&effectiveTime=&expireTime=&privateKey=&merchantCert='
     *
     * @param mchId         商户号
     * @param mchName       商户号名称，用于区分是哪个商户号，请执行main方法encode
     * @param apiV3Key      v3密钥
     * @param privateKey    商户证书私钥，请执行main方法获取
     * @param serialNumber  商户证书序列号
     * @param merchantCert  商户证书，请执行main方法获取
     * @param effectiveTime 商户证书生效时间 yyyyMMdd
     * @param expireTime    商户证书失效时间 yyyyMMdd
     * @param mchType       商户类型，1：内部商户号，2：外部商户号
     * @return 结果
     */
    @Override
    public String addWxMch(String mchId, String mchName, String apiV3Key, String privateKey, String serialNumber, String merchantCert, String effectiveTime, String expireTime, Integer mchType) throws BizException {
        if (StringUtils.isAnyBlank(mchId, mchName, privateKey, apiV3Key, serialNumber, merchantCert) || Objects.isNull(mchType)) {
            return "error";
        }
        saveConf(mchId, mchName, apiV3Key);
        WxpayMchConfCertEntity merchantCertEntity = buildWxpayMchConfCertEntity(mchId, serialNumber, privateKey, merchantCert, WxpayMchCertTypeEnum.MERCHANT, getDayDate(effectiveTime), getDayDate(expireTime));
        saveCert(merchantCertEntity);
        WxV3BaseResult<WxDownloadCertificateResult> result = wxV3CertApiService.downloadCertificate(mchId);
        WxDownloadCertificateResult certificateResult = result.getResult();
        if (certificateResult == null) {
            return "download certificate fail";
        }
        List<WxDownloadCertificateData> dataList = certificateResult.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            return "dataList is empty";
        }
        Optional<WxpayMchConfCertEntity> first = dataList.stream().map(data -> buildWxpayMchConfCertEntity(mchId, data.getSerialNo(), "", data.getCertStr(), WxpayMchCertTypeEnum.PLATFORM, getDate(data.getEffectiveTime()), getDate(data.getExpireTime()))).max(Comparator.comparing(WxpayMchConfCertEntity::getExpireTime));
        WxpayMchConfCertEntity entity = first.orElse(null);
        if (entity == null) {
            return "platform cert entity is null";
        }
        saveCert(entity);
        return "ok";

    }

    private void saveConf(String mchId, String mchName, String apiV3Key) {
        WxpayMchConfEntity entity = new WxpayMchConfEntity();
        entity.setMchId(mchId);
        entity.setMchName(mchName);
        entity.setApiKey("");
        entity.setApiV3Key(apiV3Key);
        wxpayMchConfMapper.insert(entity);
    }

    private void saveCert(WxpayMchConfCertEntity entity) {
        wxpayMchConfCertService.saveIfAbsent(entity);
    }

    private WxpayMchConfCertEntity buildWxpayMchConfCertEntity(String mchId, String serialNumber, String privateKey, String cert, WxpayMchCertTypeEnum certTypeEnum, Date effectiveTime, Date expireTime) {
        WxpayMchConfCertEntity entity = new WxpayMchConfCertEntity();
        entity.setMchId(mchId);
        entity.setCertType(certTypeEnum.getType());
        entity.setCertSerialNumber(serialNumber);
        entity.setPrivateKey(privateKey);
        entity.setEffectiveTime(effectiveTime);
        entity.setExpireTime(expireTime);
        entity.setPemCert(cert);
        return entity;
    }


    private static synchronized Date strToDate(String dateStr, SimpleDateFormat dateFormat) {
        if ("".equals(dateStr) || dateStr == null) {
            return null;
        }
        try {
            return dateFormat.parse(dateStr);
        } catch (ParseException e) {
            //log.error("format {} , dateStr={}, error:", dateFormat.toPattern(), dateStr, e);
            return null;
        }
    }

    private static Date getDayDate(String dateStr) {
        return strToDate(dateStr, dayNumDateFormat);
    }

    private static Date getDate(String dateStr) {
        return strToDate(dateStr, dayDateFormat);
    }
}
