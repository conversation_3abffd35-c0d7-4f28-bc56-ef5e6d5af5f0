package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.dto.payment.charge.DevChargeNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.RemotePayWaitConfirmService;
import cn.com.duiba.paycenter.service.payment.WaitConfirmChargeQueryService;
import cn.com.duiba.paycenter.service.payment.waitconfirmhandler.WxTransferWaitConfirmHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class RemotePayWaitConfirmServiceImpl implements RemotePayWaitConfirmService {

	private static final Logger logger = LoggerFactory.getLogger(RemotePayWaitConfirmServiceImpl.class);


	@Autowired
	private WaitConfirmChargeQueryService waitConfirmChargeQueryService;

	@Autowired
	private WxTransferWaitConfirmHandler wxTransferWaitConfirmHandler;

	@Override
	public List<DevChargeNotifyResponse> queryWaitConfirmChargeOrder() {
		return waitConfirmChargeQueryService.queryWaitConfirmChargeOrder();
	}

	@Override
	public void queryWxWaitConfirmTransferOrder() {
		wxTransferWaitConfirmHandler.queryWxWaitConfirmTransfer();
	}
}
