package cn.com.duiba.paycenter.elasticjob;

import cn.com.duiba.paycenter.config.equity.EquityJobConfig;
import cn.com.duiba.paycenter.entity.equity.EquityOrderBatchDetailEntity;
import cn.com.duiba.paycenter.entity.equity.EquityOrderEntity;
import cn.com.duiba.paycenter.enums.equity.EquityOrderStatusEnum;
import cn.com.duiba.paycenter.handler.equity.EquityHandlerAdapter;
import cn.com.duiba.paycenter.service.equity.EquityOrderBatchDetailService;
import cn.com.duiba.paycenter.service.equity.EquityOrderService;
import cn.com.duiba.paycenter.util.common.ThreadSleepUtil;
import cn.com.duiba.wolf.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 权益订单批量操作-明细订单，触发查询第三方
 * 每5分钟查询一次
 * <AUTHOR>
 * @date 2023/4/24 4:38 PM
 */
@Slf4j
@Component
//@ElasticJob(name = "equityOrderBatchDetailSyncJob", cron = "13 */5 * * * ?", overwrite = true)
public class EquityOrderBatchDetailSyncJob {

    @Resource
    private EquityOrderService equityOrderService;
    
    @Resource
    private EquityOrderBatchDetailService equityOrderBatchDetailService;
    
    @Resource
    private EquityHandlerAdapter equityHandlerAdapter;
    
    @Resource
    private EquityJobConfig equityJobConfig;

    public void doProcess() {
        if (!equityJobConfig.isOrderBatchDetailOpen()) {
            return;
        }
        try {
            Date now = new Date();
            Date startTime = DateUtils.minutesAddOrSub(now, -equityJobConfig.getOrderBatchDetailStartTime());
            Date endTime = DateUtils.minutesAddOrSub(now, -equityJobConfig.getOrderEndTime() - 10);
            log.info("EquityOrderBatchDetailSyncJob, startTime={}, endTime={}", DateUtils.getSecondStr(startTime), DateUtils.getSecondStr(endTime));
            int totalSize = doProcess(startTime, endTime);
            log.info("EquityOrderBatchDetailSyncJob, startTime={}, endTime={}, totalSize={}", DateUtils.getSecondStr(startTime), DateUtils.getSecondStr(endTime), totalSize);
        } catch (Exception e) {
            log.error("EquityOrderBatchDetailSyncJob, ", e);
        }
    }
    
    private int doProcess(Date startTime, Date endTime) {
        List<EquityOrderBatchDetailEntity> list;
        Long lastId = null;
        int pageSize = 100;
        int totalSize = 0;
        while (CollectionUtils.isNotEmpty(list = equityOrderBatchDetailService.selectPageByTimeAndStatus(startTime, endTime, getQueryStatusList(), lastId, pageSize))) {
            lastId = list.get(list.size() - 1).getId();
            // 查询主订单状态
            Map<Integer, Map<String, Integer>> orderStatusMap = getOrderStatusMap(list);
            // 过滤掉主订单未完成的
            list = list.stream().filter(entity -> isSuccess(orderStatusMap, entity)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(entity -> {
                    equityHandlerAdapter.queryBatchDetail(entity);
                    ThreadSleepUtil.sleep(10);
                });
            }
            totalSize += list.size();
        }
        return totalSize;
    }
    
    private boolean isSuccess(Map<Integer, Map<String, Integer>> orderStatusMap, EquityOrderBatchDetailEntity entity) {
        if (MapUtils.isEmpty(orderStatusMap)) {
            return false;
        }
        Map<String, Integer> bizNoStatusMap = orderStatusMap.get(entity.getBizType());
        if (MapUtils.isEmpty(bizNoStatusMap)) {
            return false;
        }
        return EquityOrderStatusEnum.THIRD_SUCCESS.getType().equals(bizNoStatusMap.get(entity.getBizNo()));
    }
    
    private Map<Integer, Map<String, Integer>> getOrderStatusMap(List<EquityOrderBatchDetailEntity> list) {
        List<Integer> bizTypeList = list.stream().map(EquityOrderBatchDetailEntity::getBizType).distinct().collect(Collectors.toList());
        List<String> bizNoList = list.stream().map(EquityOrderBatchDetailEntity::getBizNo).distinct().collect(Collectors.toList());
        List<EquityOrderEntity> orderList = equityOrderService.selectByBizList(bizTypeList, bizNoList);
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyMap();
        }
        return orderList.stream().collect(Collectors.groupingBy(EquityOrderEntity::getBizType, Collectors.mapping(Function.identity(), Collectors.toMap(EquityOrderEntity::getBizNo, EquityOrderEntity::getOrderStatus, (v1,v2) -> v2))));
    }
    
    private List<Integer> getQueryStatusList() {
        List<Integer> orderStatusList = new ArrayList<>();
        orderStatusList.add(EquityOrderStatusEnum.CALL_THIRD_EXCEPTION.getType());
        orderStatusList.add(EquityOrderStatusEnum.THIRD_PROCESSING.getType());
        orderStatusList.add(EquityOrderStatusEnum.EXCEPTION.getType());
        return orderStatusList;
    }
}
