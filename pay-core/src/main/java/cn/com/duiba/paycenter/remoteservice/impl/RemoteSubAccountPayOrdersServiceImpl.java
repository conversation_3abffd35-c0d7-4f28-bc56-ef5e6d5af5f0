package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.biz.SubAccountPayOrdersBiz;
import cn.com.duiba.paycenter.constant.PayCenterErrorCode;
import cn.com.duiba.paycenter.params.PayOrdersExtraParams;
import cn.com.duiba.paycenter.result.PayOrdersResult;
import cn.com.duiba.paycenter.service.SubAccountPayOrdersService;
import cn.com.duiba.paycenter.util.SignUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 分账付款相关接口服务
 * <AUTHOR>
 *
 */
@RestController
public class RemoteSubAccountPayOrdersServiceImpl implements SubAccountPayOrdersService {

	private static final Logger log=LoggerFactory.getLogger(RemoteSubAccountPayOrdersServiceImpl.class);

	@Autowired
	private SubAccountPayOrdersBiz subAccountPayOrdersBiz;

	@Override
	public PayOrdersResult payOrder(Long developerId, Long orderId, Long money,String sign,PayOrdersExtraParams p) {
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("orderId", orderId+"");
		params.put("money", money+"");
		String checksign=SignUtil.sign(params);
		if(!checksign.equals(sign)){
			return new PayOrdersResult(false, PayCenterErrorCode.CodeParamsSignError, null);
		}
		return subAccountPayOrdersBiz.payOrder(developerId, orderId, money, p);
	}

	@Override
	public PayOrdersResult backpayOrder(Long developerId, Long orderId,
										Long money,String sign,PayOrdersExtraParams p)  {
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("orderId", orderId+"");
		params.put("money", money+"");
		String checksign=SignUtil.sign(params);

		if(!checksign.equals(sign)){
			return new PayOrdersResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		return subAccountPayOrdersBiz.backpayOrder(developerId, orderId, money, p);
	}


	@Override
	public PayOrdersResult chargeMoney(Long developerId, Long orderId, Long money, String sign, PayOrdersExtraParams p) {
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("orderId", orderId+"");
		params.put("money", money+"");
		String checksign=SignUtil.sign(params);
		if(!checksign.equals(sign)){
			return new PayOrdersResult(false, PayCenterErrorCode.CodeParamsSignError, null);
		}
		return subAccountPayOrdersBiz.chargeMoney(developerId, orderId, money, p);
	}

	@Override
	public PayOrdersResult refundMoney(Long developerId, Long orderId, Long money, String sign, PayOrdersExtraParams p) {
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("orderId", orderId+"");
		params.put("money", money+"");
		String checksign=SignUtil.sign(params);
		if(!checksign.equals(sign)){
			return new PayOrdersResult(false, PayCenterErrorCode.CodeParamsSignError, null);
		}
		return subAccountPayOrdersBiz.refundMoney(developerId, orderId, money, p);
	}
}
