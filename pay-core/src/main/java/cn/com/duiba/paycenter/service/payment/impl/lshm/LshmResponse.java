package cn.com.duiba.paycenter.service.payment.impl.lshm;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class LshmResponse {
    public static final String SUCCESS_CODE = "0";
    public static final String FAIL_CODE = "1";
    /**
     * 状态 0 成功 1 异常
     */
    @JSONField(name = "resp_code")
    private String respCode;

    /**
     * 描述
     */
    @JSONField(name = "resp_msg")
    private String respMsg;

    /**
     * 具体数据	aes加密字符串，解密后对应具体业务出参
     */
    @JSONField(name = "datas")
    private String datas;
}
