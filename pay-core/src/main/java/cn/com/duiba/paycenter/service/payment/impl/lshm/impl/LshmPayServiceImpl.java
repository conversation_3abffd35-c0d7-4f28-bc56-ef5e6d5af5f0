package cn.com.duiba.paycenter.service.payment.impl.lshm.impl;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.utils.SpringEnvironmentUtils;
import cn.com.duiba.paycenter.config.lshm.LshmPayConfig;
import cn.com.duiba.paycenter.dao.payment.ChargeOrderDao;
import cn.com.duiba.paycenter.dao.payment.RefundOrderDao;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.LshmChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.enums.LshmPayNotifyStatus;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.enums.LshmPayStatus;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmPayNotifyRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmPayQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmPayRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmRefundQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmRefundRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.response.LshmPayNotifyData;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.response.LshmPayQueryResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.response.LshmPayResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.response.LshmRefundQueryResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.response.LshmRefundResponse;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboChargeNotifyData;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboChargeNotifyResponse;
import cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity;
import cn.com.duiba.paycenter.enums.ChargeOrderStatusEnum;
import cn.com.duiba.paycenter.service.payment.impl.lshm.LshmPayService;
import cn.com.duiba.paycenter.service.payment.impl.lshm.LshmRequest;
import cn.com.duiba.paycenter.service.payment.impl.lshm.LshmResponse;
import cn.com.duiba.paycenter.service.payment.impl.lshm.LshmRsaRequest;
import cn.com.duiba.paycenter.service.payment.impl.lshm.LshmRsaResponse;
import cn.com.duiba.paycenter.util.LshmPayUtils;
import cn.com.duiba.paycenter.util.RedisKeyFactory;
import cn.com.duiba.wolf.redis.RedisLock;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class LshmPayServiceImpl implements LshmPayService {

    @Resource
    private LshmPayConfig lshmPayConfig;

    @Resource(name = "lshmHttpClient")
    private CloseableHttpClient httpClient;

    @Resource
    private ChargeOrderDao chargeOrderDao;

    @Resource
    private RefundOrderDao refundOrderDao;

    /**
     * 零食很忙请求
     * @param request 请求
     * @return 响应
     * @throws BizException 业务异常
     */
    private LshmResponse doRequest(LshmRequest request) throws BizException {
        Long appId = request.getAppId();
        String url = lshmPayConfig.getUrl(appId);
        log.info("零食很忙 接口请求 request={} url={}", JSON.toJSONString(request), url);
        if (SpringEnvironmentUtils.isTestEnv()){
            url = url + "?serviceCode="+request.getServiceCode();
        }
        HttpPost httpPost = new HttpPost(url);
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("serviceCode", request.getServiceCode());
        long currentTimeMillis = System.currentTimeMillis();
        requestBody.put("currentTime", currentTimeMillis);
        requestBody.put("storeCode", request.getStoreCode());
        requestBody.put("aesKey", getRsaKey(appId,lshmPayConfig.getAesKey(appId)));
        requestBody.put("sign", getSign(appId,request.getStoreCode(), currentTimeMillis));
        requestBody.put("body", request.getBody());
        log.info("零食很忙 接口请求 request={} requestBody={}", JSON.toJSONString(request), JSON.toJSONString(requestBody));
        try {
            String json = JSON.toJSONString(requestBody);
            StringEntity entity = new StringEntity(json, "UTF-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            CloseableHttpResponse httpResponse = httpClient.execute(httpPost);
            String response = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
            log.info("零食很忙 接口请求 响应 request={} response={}", JSON.toJSONString(request), response);
            LshmResponse lshmResponse = JSON.parseObject(response, LshmResponse.class);
            if (!LshmResponse.SUCCESS_CODE.equals(lshmResponse.getRespCode())) {
                log.warn("零食很忙 支付请求失败 request={} response={}", JSON.toJSONString(request), response);
                throw new BizException(lshmResponse.getRespMsg());
            }
            return lshmResponse;
        } catch (Exception e) {
            log.warn("零食很忙 支付请求异常 request={}", JSON.toJSONString(request), e);
            throw new BizException("支付请求异常");
        } finally {
            try {
                httpPost.releaseConnection();
            } catch (Exception e) {
                log.error("httpPost releaseConnection error", e);
            }
        }
    }

    private LshmRsaResponse doRsaRequest(LshmRsaRequest request) throws BizException {
        String url = request.getUrl();
        log.info("零食很忙 rsa接口请求 request={} url={}", JSON.toJSONString(request), url);
        HttpPost httpPost = new HttpPost(url);
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("data",aesRsaEncrypt(request.getData()));
        log.info("零食很忙 rsa接口请求 request={} requestBody={}", JSON.toJSONString(request), JSON.toJSONString(requestBody));
        try {
            String json = JSON.toJSONString(requestBody);
            StringEntity entity = new StringEntity(json, "UTF-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            CloseableHttpResponse httpResponse = httpClient.execute(httpPost);
            String response = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
            log.info("零食很忙 rsa接口请求 响应 request={} response={}", JSON.toJSONString(request), response);
            LshmRsaResponse lshmResponse = JSON.parseObject(response, LshmRsaResponse.class);
            if (!LshmRsaResponse.SUCCESS_CODE.equals(lshmResponse.getCode())) {
                log.warn("零食很忙 rsa接口请求 失败 request={} response={}", JSON.toJSONString(request), response);
                throw new BizException(lshmResponse.getErrorMsg());
            }
            return lshmResponse;
        } catch (Exception e) {
            log.warn("零食很忙 rsa接口请求 异常 request={}", JSON.toJSONString(request), e);
            throw new BizException("rsa接口请求失败");
        } finally {
            try {
                httpPost.releaseConnection();
            } catch (Exception e) {
                log.error("httpPost releaseConnection error", e);
            }
        }
    }

    private String getRsaKey(Long appId,String aesKey) throws BizException {
        return rsaPrivateEncrypt(appId,aesKey);
    }

    private String getSign(Long appId,String storeCode, long currentTimeMillis) throws BizException {
        String data = storeCode + currentTimeMillis;
        return rsaSign(appId,data);
    }

    private String aesEncrypt(Long appId,String datas) {
        return LshmPayUtils.aesEncrypt(lshmPayConfig.getAesKey(appId),datas);
    }

    private String aesRsaEncrypt(String datas) {
        return LshmPayUtils.aesEncrypt(lshmPayConfig.getRasAesKey(),datas);
    }

    private String aesDecrypt(Long appId,String datas) {
        String decrypt = LshmPayUtils.aesDecrypt(lshmPayConfig.getAesKey(appId), datas);
        log.info("零食很忙 解密原文 decrypt={}",decrypt);
        return decrypt;
    }

    private String rsaAesDecrypt(String datas) {
        String decrypt = LshmPayUtils.aesDecrypt(lshmPayConfig.getRasAesKey(), datas);
        log.info("零食很忙 解密原文 decrypt={}",decrypt);
        return decrypt;
    }

    public String rsaSign(Long appId,String data) throws BizException {
        LshmRsaRequest request = new LshmRsaRequest();
        request.setData(data);
        request.setUrl(lshmPayConfig.getRsaUrl(appId) + lshmPayConfig.getSignPath());
        LshmRsaResponse lshmRsaResponse = doRsaRequest(request);
        return rsaAesDecrypt(lshmRsaResponse.getRes());
    }


    public String rsaPrivateEncrypt(Long appId,String data) throws BizException {
        LshmRsaRequest request = new LshmRsaRequest();
        request.setData(data);
        request.setUrl(lshmPayConfig.getRsaUrl(appId) + lshmPayConfig.getPrivateEncryptPath());
        LshmRsaResponse lshmRsaResponse = doRsaRequest(request);
        return rsaAesDecrypt(lshmRsaResponse.getRes());
    }

    public LshmPayResponse doPay(Long appId, LshmPayRequest request) throws BizException {
        LshmRequest lshmRequest = new LshmRequest();
        lshmRequest.setAppId(appId);
        lshmRequest.setStoreCode(request.getMerchantNo());
        lshmRequest.setServiceCode(LshmPayConfig.PAY_SERVICE_ID);

        // aes加密
        log.info("零食很忙 请求原始参数 {}",JSON.toJSONString(request));
        lshmRequest.setBody(aesEncrypt(appId,JSON.toJSONString(request)));
        LshmResponse lshmResponse = doRequest(lshmRequest);
        // 解密
        String datas = lshmResponse.getDatas();
        String decrypt = aesDecrypt(appId,datas);
        return JSON.parseObject(decrypt, LshmPayResponse.class);
    }

    public LshmRefundResponse refund(Long appId, LshmRefundRequest request) throws BizException {
        LshmRequest lshmRequest = new LshmRequest();
        lshmRequest.setAppId(appId);
        lshmRequest.setStoreCode(request.getStoreCode());
        lshmRequest.setServiceCode(LshmPayConfig.REFUND_SERVICE_ID);

        // aes加密
        log.info("零食很忙 请求原始参数 {}",JSON.toJSONString(request));

        lshmRequest.setBody(aesEncrypt(appId,JSON.toJSONString(request)));
        LshmResponse lshmResponse = doRequest(lshmRequest);
        // 解密
        String datas = lshmResponse.getDatas();
        String decrypt = aesDecrypt(appId,datas);
        return JSON.parseObject(decrypt, LshmRefundResponse.class);
    }


    public LshmRefundQueryResponse refundQuery(Long appId, LshmRefundQueryRequest request) throws BizException {
        LshmRequest lshmRequest = new LshmRequest();
        lshmRequest.setAppId(appId);
        lshmRequest.setStoreCode(request.getMerchantNo());
        lshmRequest.setServiceCode(LshmPayConfig.REFUND_QUERY_SERVICE_ID);

        // aes加密
        log.info("零食很忙 请求原始参数 {}",JSON.toJSONString(request));

        lshmRequest.setBody(aesEncrypt(appId,JSON.toJSONString(request)));
        LshmResponse lshmResponse = doRequest(lshmRequest);
        // 解密
        String datas = lshmResponse.getDatas();
        String decrypt = aesDecrypt(appId,datas);
        return JSON.parseObject(decrypt, LshmRefundQueryResponse.class);
    }

    @Override
    public LshmPayQueryResponse payQuery(Long appId, LshmPayQueryRequest request) throws BizException {
        LshmRequest lshmRequest = new LshmRequest();
        lshmRequest.setAppId(appId);
        lshmRequest.setStoreCode(request.getMerchantNo());
        lshmRequest.setServiceCode(LshmPayConfig.PAY_QUERY_SERVICE_ID);

        // aes加密
        log.info("零食很忙 请求原始参数 {}",JSON.toJSONString(request));

        lshmRequest.setBody(aesEncrypt(appId,JSON.toJSONString(request)));
        LshmResponse lshmResponse = doRequest(lshmRequest);
        String datas = lshmResponse.getDatas();
        String decrypt = aesDecrypt(appId,datas);
        return JSON.parseObject(decrypt, LshmPayQueryResponse.class);
    }

    @Override
    public LshmChargeNotifyResponse payNotify(Long appId, LshmPayNotifyRequest notifyRequest) throws BizException {
        LshmPayNotifyData notifyData = JSON.parseObject(aesDecrypt(appId,notifyRequest.getBody()), LshmPayNotifyData.class);
        LshmChargeNotifyResponse payNotifyResponse = new LshmChargeNotifyResponse();
        // 加锁，防止主动轮询和行方通知并发
        try{

            // 找到支付单记录
            ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByOrderNo(notifyData.getMerchantOrderNo());
            if (Objects.isNull(chargeOrderEntity)) {
                log.error("零食很忙 支付回调 支付单为空 notifyRequest={}", JSON.toJSONString(notifyData));
                payNotifyResponse.setSuccess(false);
                return payNotifyResponse;
            }

            // 防止重复处理
            if (chargeOrderEntity.getChargeStatus() > ChargeOrderStatusEnum.PAY_FAIL.getCode()) {
                payNotifyResponse.setSuccess(false);
                payNotifyResponse.setChargeSuccess(!ChargeOrderStatusEnum.PAY_FAIL.getCode().equals(chargeOrderEntity.getChargeStatus()));
                return payNotifyResponse;
            }

            // 支付状态
            boolean isPaySuccess = LshmPayNotifyStatus.SUCCESS.getStatus().equals(notifyData.getPayStatus());

            // 更新记录
            boolean isUpdateSuccess = updateChargeOrder(notifyData, chargeOrderEntity.getOrderNo(), isPaySuccess);

            payNotifyResponse = buildPayNotifyResponse(notifyData, isPaySuccess, isUpdateSuccess, chargeOrderEntity);
            log.info("零食很忙 支付回调 notifyResponse = {}", JSON.toJSONString(payNotifyResponse));
            return payNotifyResponse;
        } catch (Exception e) {
            log.error("零食很忙 支付回调 notifyRequest:{}", JSON.toJSONString(notifyData), e);
            throw new BizException("系统繁忙请稍后再试");
        }
    }

    private LshmChargeNotifyResponse buildPayNotifyResponse(LshmPayNotifyData notifyData,
                                                             boolean isPaySuccess,
                                                             boolean isUpdateSuccess,
                                                             ChargeOrderEntity chargeOrderEntity) {
        LshmChargeNotifyResponse payNotifyResponse = new LshmChargeNotifyResponse();
        payNotifyResponse.setSuccess(isUpdateSuccess);
        payNotifyResponse.setChargeSuccess(isPaySuccess);
        payNotifyResponse.setChargeOrderNo(chargeOrderEntity.getOrderNo());
        payNotifyResponse.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
        payNotifyResponse.setBizType(chargeOrderEntity.getBizType());
        payNotifyResponse.setTransactionNo(notifyData.getTradeNo());
        return payNotifyResponse;
    }

    /**
     * 更新支付单
     * @param  notifyData 支付通知请求
     * @param orderNo          订单号
     * @param isPaySuccess     是成功支付
     * @return boolean
     */
    private boolean updateChargeOrder(LshmPayNotifyData notifyData, String orderNo,
                                      boolean isPaySuccess) {
        ChargeOrderEntity updateEntity = new ChargeOrderEntity();
        updateEntity.setOrderNo(orderNo);
        if (isPaySuccess) {
            updateEntity.setPaidTime(new Date());
            updateEntity.setChargeStatus(ChargeOrderStatusEnum.PAY_SUCCESS.getCode());
            updateEntity.setTransactionNo(notifyData.getTradeNo());
        } else {
            updateEntity.setChargeStatus(ChargeOrderStatusEnum.PAY_FAIL.getCode());
        }

        if (SpringEnvironmentUtils.isTestEnv() || SpringEnvironmentUtils.isPreEnv()) {
            log.info("零食很忙 回调 更新tb_charge_order updateEntity = {}", JSON.toJSONString(updateEntity));
        }
        return chargeOrderDao.update(updateEntity) > 0;
    }

}
