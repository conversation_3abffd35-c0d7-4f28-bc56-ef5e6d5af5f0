package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.elife.IcbcELifePayQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.elife.IcbcELifePayQueryResponse;
import cn.com.duiba.paycenter.dto.payment.charge.elife.IcbcELifeRefundQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.elife.IcbcELifeRefundQueryResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteIcbcELifePayService;
import cn.com.duiba.paycenter.service.payment.IcbcELifePayService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 江苏工行-e生活-remote
 *
 * <AUTHOR>
 * @date 2024/5/11 6:11 下午
 */
@RestController
public class RemoteIcbcELifePayServiceImpl implements RemoteIcbcELifePayService {

    @Resource
    private IcbcELifePayService icbcELifePayService;

    @Override
    public IcbcELifeRefundQueryResponse refundQuery(IcbcELifeRefundQueryRequest request) throws BizException {
        return icbcELifePayService.refundQuery(request);
    }

    @Override
    public IcbcELifePayQueryResponse payQuery(IcbcELifePayQueryRequest request) throws BizException {
        return icbcELifePayService.payQuery(request);
    }
}
