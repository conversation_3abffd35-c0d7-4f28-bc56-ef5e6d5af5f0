package cn.com.duiba.paycenter.entity.payment;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018/11/28
 */
public class RefundHandlerResult {
    private boolean success;
    private String refundNo;
    private String failCode;
    private String failMsg;
    private String status;
    //申请退款金额RefundHandlerResult
    private Integer reundAmt;
    //交易完成时间
    private Date tranEndDate;

    private String extra;

    /**
     * 同步返回退款结果-是否成功
     */
    private boolean finishSuccess;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getRefundNo() {
        return refundNo;
    }

    public void setRefundNo(String refundNo) {
        this.refundNo = refundNo;
    }

    public String getFailCode() {
        return failCode;
    }

    public void setFailCode(String failCode) {
        this.failCode = failCode;
    }

    public String getFailMsg() {
        return failMsg;
    }

    public void setFailMsg(String failMsg) {
        this.failMsg = failMsg;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getReundAmt() {
        return reundAmt;
    }

    public void setReundAmt(Integer reundAmt) {
        this.reundAmt = reundAmt;
    }

    public Date getTranEndDate() {
        return tranEndDate;
    }

    public void setTranEndDate(Date tranEndDate) {
        this.tranEndDate = tranEndDate;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public boolean isFinishSuccess() {
        return finishSuccess;
    }

    public void setFinishSuccess(boolean finishSuccess) {
        this.finishSuccess = finishSuccess;
    }
}
