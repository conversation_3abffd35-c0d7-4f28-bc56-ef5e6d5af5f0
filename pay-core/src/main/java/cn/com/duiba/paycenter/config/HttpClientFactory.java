package cn.com.duiba.paycenter.config;

import cn.com.duiba.paycenter.manager.YbsCertManager;
import com.alibaba.fastjson.JSON;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.protocol.HttpContext;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.SSLContexts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.util.Objects;

/**
 * http线程池配置类
 *
 * <AUTHOR>
 * @date 2018/11/29
 */
@Configuration
public class HttpClientFactory {
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpClientFactory.class);
    /**
     * 连接超时
     */
    public static final int CONNECT_TIMEOUT = 2 * 1000;

    /**
     * 从连接池中获取连接的超时时间
     */
    public static final int DEFAULT_REQUEST_TIMEOUT = 500;

    /**
     * 长链接空闲时间
     */
    private static final int KEEPALIVE_TIMEOUT = 5 * 1000;

    /**
     * 处理超时
     */
    public static final int SOCKET_TIMEOUT = 2 * 1000;

    /**
     * 最大总连接值
     */
    private static final int MAX_CONNECT = 1000;
    /**
     * 兑吧退款总的连接值
     */
    private static final int REFUND_MAX_CONNECT = 500;

    /**
     * 每个路由最大连接{并发}值
     */
    private static final int MAX_ROUTE_CONNECT = 500;
    /**
     * 兑吧退款连接池每个路由最大连接{并发}值
     */
    private static final int REFUND_MAX_ROUTE_CONNECT = 100;

    @Resource
    private DuibaWxpayConfig duibaWxpayConfig;
    @Resource
    private Duia2WxpayConfig duia2WxpayConfig;
    @Resource
    private DuijieWxpayConfig duijieWxpayConfig;
    @Resource
    private WjrcbConfig wjrcbConfig;
    @Resource
    private CmbOneNetPayConfig cmbOneNetPayConfig;
    @Resource
    private FjgwWxpayConfig fjgwWxpayConfig;


    @Resource
    private YbsConfig ybsConfig;
    @Resource
    private YbsCertManager ybsCertManager;

    @Bean(name="virtualHttpAsyncClient")
    public CloseableHttpClient virtualHttpAsyncClient(){
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT).build();
        CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultRequestConfig(config)
                .setMaxConnTotal(MAX_CONNECT)
                .setMaxConnPerRoute(MAX_ROUTE_CONNECT)
                .setKeepAliveStrategy(getStrategy())
                .build();
        LOGGER.info("virtualHttpAsyncClient started");
        return httpClient;
    }

    /**
     * 微信支付同步http连接池
     */
    @Bean(name = "wxPayHttpClient")
    public CloseableHttpClient wxPayHttpClient() {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();
        CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultRequestConfig(config)
                .setMaxConnTotal(MAX_CONNECT)
                .setMaxConnPerRoute(MAX_ROUTE_CONNECT)
                .setKeepAliveStrategy(getStrategy())
                .disableAuthCaching()
                .disableCookieManagement()
                .disableAutomaticRetries()
                .build();
        LOGGER.info("wxPayHttpClient started");
        return httpClient;
    }

    @Bean(name = "wxPayRefundHttpClient", destroyMethod = "close")
    public CloseableHttpClient wxPayRefundHttpClient() throws CertificateException
            , UnrecoverableKeyException
            , DecoderException
            , KeyStoreException
            , KeyManagementException
            , NoSuchAlgorithmException
            , IOException {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();
        CloseableHttpClient httpClient = createHttpClientBuilder(
                duibaWxpayConfig.getApiCert(), duibaWxpayConfig.getMchId())
                .setDefaultRequestConfig(config)
                .setKeepAliveStrategy(getStrategy())
                .setMaxConnTotal(REFUND_MAX_CONNECT)
                .setMaxConnPerRoute(REFUND_MAX_ROUTE_CONNECT)
                .disableAutomaticRetries()
                .disableAuthCaching()
                .disableCookieManagement()
                .build();
        LOGGER.info("wxPayRefundHttpClient started");
        return httpClient;
    }

    @Bean(name = "wxPayDuia2RefundHttpClient", destroyMethod = "close")
    public CloseableHttpClient wxPayDuia2RefundHttpClient() throws CertificateException
            , UnrecoverableKeyException
            , DecoderException
            , KeyStoreException
            , KeyManagementException
            , NoSuchAlgorithmException
            , IOException {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();
        CloseableHttpClient httpClient = createHttpClientBuilder(
                duia2WxpayConfig.getApiCert(), duia2WxpayConfig.getMchId())
                .setDefaultRequestConfig(config)
                .setKeepAliveStrategy(getStrategy())
                .setMaxConnTotal(REFUND_MAX_CONNECT)
                .setMaxConnPerRoute(REFUND_MAX_ROUTE_CONNECT)
                .disableAutomaticRetries()
                .disableAuthCaching()
                .disableCookieManagement()
                .build();
        LOGGER.info("wxPayDuia2RefundHttpClient started");
        return httpClient;
    }

    @Bean(name = "wxPayDuijieRefundHttpClient", destroyMethod = "close")
    public CloseableHttpClient wxPayDuijieRefundHttpClient() throws CertificateException
            , UnrecoverableKeyException
            , DecoderException
            , KeyStoreException
            , KeyManagementException
            , NoSuchAlgorithmException
            , IOException {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();
        CloseableHttpClient httpClient = createHttpClientBuilder(
                duijieWxpayConfig.getApiCert(), duijieWxpayConfig.getMchId())
                .setDefaultRequestConfig(config)
                .setKeepAliveStrategy(getStrategy())
                .setMaxConnTotal(REFUND_MAX_CONNECT)
                .setMaxConnPerRoute(REFUND_MAX_ROUTE_CONNECT)
                .disableAutomaticRetries()
                .disableAuthCaching()
                .disableCookieManagement()
                .build();
        LOGGER.info("wxPayDuijieRefundHttpClient started");
        return httpClient;
    }

    @Bean(name = "wxPayFjgwRefundHttpClient", destroyMethod = "close")
    public CloseableHttpClient wxPayFjgwRefundHttpClient() throws CertificateException
            , UnrecoverableKeyException
            , DecoderException
            , KeyStoreException
            , KeyManagementException
            , NoSuchAlgorithmException
            , IOException {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();
        CloseableHttpClient httpClient = createHttpClientBuilder(
                fjgwWxpayConfig.getApiCert(), fjgwWxpayConfig.getMchId())
                .setDefaultRequestConfig(config)
                .setKeepAliveStrategy(getStrategy())
                .setMaxConnTotal(REFUND_MAX_CONNECT)
                .setMaxConnPerRoute(REFUND_MAX_ROUTE_CONNECT)
                .disableAutomaticRetries()
                .disableAuthCaching()
                .disableCookieManagement()
                .build();
        LOGGER.info("wxPayFjgwRefundHttpClient started");
        return httpClient;
    }

    /**
     * 支付宝同步http连接池
     */
    @Bean(name = "alipayHttpClient")
    public CloseableHttpClient alipayHttpClient() {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();
        CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultRequestConfig(config)
                .setKeepAliveStrategy(getStrategy())
                .setMaxConnTotal(MAX_CONNECT)
                .setMaxConnPerRoute(MAX_ROUTE_CONNECT)
                .disableCookieManagement()
                .disableAuthCaching()
                .disableAutomaticRetries()
                .build();
        LOGGER.info("alipayHttpClient started");
        return httpClient;
    }

    @Bean(name = "wjrcbHttpClient")
    public CloseableHttpClient wjrcbHttpClient() {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();
        CloseableHttpClient httpClient = HttpClients.custom().
                setDefaultRequestConfig(config)
                .setKeepAliveStrategy(getStrategy())
                .setMaxConnTotal(100)
                .setMaxConnPerRoute(50)
                .disableCookieManagement()
                .disableAuthCaching()
                .disableAutomaticRetries().build();
        LOGGER.info("wjrcbHttpClient started");
        return httpClient;
    }


    @Bean(name = "wechatCouponHttpClient")
    public CloseableHttpClient wechatCouponHttpClient() {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();
        CloseableHttpClient httpClient = HttpClients.custom().
                setDefaultRequestConfig(config)
                .setKeepAliveStrategy(getStrategy())
                .setMaxConnTotal(100)
                .setMaxConnPerRoute(50)
                .disableCookieManagement()
                .disableAuthCaching()
                .disableAutomaticRetries().build();
        LOGGER.info("wechatCouponHttpClient started");
        return httpClient;
    }

    @Bean(name = "helloHttpClient")
    public CloseableHttpClient helloHttpClient() {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();
        CloseableHttpClient httpClient = HttpClients.custom().
                setDefaultRequestConfig(config)
                .setKeepAliveStrategy(getStrategy())
                .setMaxConnTotal(100)
                .setMaxConnPerRoute(50)
                .disableCookieManagement()
                .disableAuthCaching()
                .disableAutomaticRetries().build();
        LOGGER.info("helloHttpClient started");
        return httpClient;
    }

    @Bean(name = "duibaLiveHttpClient")
    public CloseableHttpClient duibaLiveHttpClient() {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();
        CloseableHttpClient httpClient = HttpClients.custom().
                setDefaultRequestConfig(config)
                .setKeepAliveStrategy(getStrategy())
                .setMaxConnTotal(100)
                .setMaxConnPerRoute(50)
                .disableCookieManagement()
                .disableAuthCaching()
                .disableAutomaticRetries().build();
        LOGGER.info("duibaLiveHttpClient started");
        return httpClient;
    }

    @Bean(name = "bocHttpClient")
    public CloseableHttpClient bocHttpClient() {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();
        CloseableHttpClient httpClient = HttpClients.custom().
                setDefaultRequestConfig(config)
                .setKeepAliveStrategy(getStrategy())
                .setMaxConnTotal(100)
                .setMaxConnPerRoute(50)
                .disableCookieManagement()
                .disableAuthCaching()
                .disableAutomaticRetries().build();
        LOGGER.info("bocHttpClient started");
        return httpClient;
    }

    @Bean(name = "weiboHttpClient")
    public CloseableHttpClient weiboHttpClient() {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();
        CloseableHttpClient httpClient = HttpClients.custom().
                setDefaultRequestConfig(config)
                .setKeepAliveStrategy(getStrategy())
                .setMaxConnTotal(100)
                .setMaxConnPerRoute(50)
                .disableCookieManagement()
                .disableAuthCaching()
                .disableAutomaticRetries().build();
        LOGGER.info("bocHttpClient started");
        return httpClient;
    }

    @Bean(name = "lshmHttpClient")
    public CloseableHttpClient lshmHttpClient() {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();
        CloseableHttpClient httpClient = HttpClients.custom().
                setDefaultRequestConfig(config)
                .setKeepAliveStrategy(getStrategy())
                .setMaxConnTotal(100)
                .setMaxConnPerRoute(50)
                .disableCookieManagement()
                .disableAuthCaching()
                .disableAutomaticRetries().build();
        LOGGER.info("lshmHttpClient started");
        return httpClient;
    }

    @Bean(name = "cmbOneNetPayRestTemplate")
    public RestTemplate cmbOneNetPayRestTemplate() {
        SSLContext sslContext;
//        try (InputStream inputStream = new ByteArrayInputStream(Base64.decodeBase64(cmbOneNetPayConfig.getCaCertBase64()))) {
//            KeyStore keyStore = KeyStore.getInstance("jks");
//            keyStore.load(inputStream, cmbOneNetPayConfig.getKeyStorePass().toCharArray());
//            sslContext = SSLContexts.custom()
//                    .loadTrustMaterial(keyStore, new TrustSelfSignedStrategy())
//                    .build();
//
//        } catch (Exception e) {
//            LOGGER.warn("", e);
//            sslContext = null;
//        }
        try {
            // 信任所有
            sslContext = new SSLContextBuilder().loadTrustMaterial(null, (TrustStrategy) (chain, authType) -> true).build();
        } catch (Exception e) {
            LOGGER.warn("cmbOneNetPayRestTemplate create error", e);
            sslContext = null;
        }
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.INSTANCE)
                .register("https", new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE))
                .build();
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        //创建自定义的httpclient对象
        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connManager)
                .setSSLSocketFactory(new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE))
                .setDefaultRequestConfig(RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT).build()).build();
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
        return new RestTemplate(clientHttpRequestFactory);
    }

    @Bean(name = "cmbLifePayRestTemplate")
    public RestTemplate cmbLifePayRestTemplate() {
        SSLContext sslContext;
        try {
            // 信任所有
            sslContext = new SSLContextBuilder().loadTrustMaterial(null, (TrustStrategy) (chain, authType) -> true).build();
        } catch (Exception e) {
            LOGGER.error("cmbLifePayRestTemplate create error", e);
            sslContext = null;
        }
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.INSTANCE)
                .register("https", new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE))
                .build();
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        //创建自定义的httpclient对象
        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connManager)
                .setSSLSocketFactory(new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE))
                .setDefaultRequestConfig(RequestConfig.custom()
                        .setConnectTimeout(CONNECT_TIMEOUT)
                        .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                        .setSocketTimeout(SOCKET_TIMEOUT)
                        .build())
                .build();
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
        return new RestTemplate(clientHttpRequestFactory);
    }

    public static DefaultConnectionKeepAliveStrategy getStrategy() {
        return new DefaultConnectionKeepAliveStrategy() {
            @Override
            public long getKeepAliveDuration(HttpResponse response, HttpContext context) {
                long duration = super.getKeepAliveDuration(response, context);
                if (duration == -1) {
                    return KEEPALIVE_TIMEOUT;
                }
                return duration;
            }
        };
    }

    private static SSLContext getSSLContext(String apiCert, String mchId) throws KeyStoreException
            , DecoderException
            , UnrecoverableKeyException
            , NoSuchAlgorithmException
            , IOException
            , CertificateException
            , KeyManagementException {
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        char[] password = mchId.toCharArray();
        keyStore.load(new ByteArrayInputStream(Hex.decodeHex(apiCert.toCharArray())), password);
        return SSLContexts.custom().loadKeyMaterial(keyStore, password).build();
    }

    public static HttpClientBuilder createHttpClientBuilder(String apiCert, String mchId) throws CertificateException
            , UnrecoverableKeyException
            , DecoderException
            , KeyStoreException
            , NoSuchAlgorithmException
            , IOException
            , KeyManagementException {
        return HttpClients.custom()
                .disableAutomaticRetries()
                .disableCookieManagement()
                .disableAuthCaching()
                .useSystemProperties()
                .setSSLContext(getSSLContext(apiCert, mchId));
    }


    /**
     * 易办事 ssl链接工厂
     *
     * @return 链接工厂
     * @throws Exception -
     */
    private SSLConnectionSocketFactory buildYbsConnectionSocketFactory() throws Exception{
        SSLContext sslcontext = SSLContext.getInstance("TLS");

        KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
        KeyStore ks = KeyStore.getInstance("pkcs12");

        SSLConnectionSocketFactory sslsf;
        InputStream privateKeyInputStream = null;
        InputStream publicKeyInputStream = null;

        try {
            //LOGGER.info("[ybs] 初始化httpClient ybsConfig = {}", JSON.toJSONString(ybsConfig));
            privateKeyInputStream = ybsCertManager.getResource(ybsConfig.getHttpsPrivateCertPath()).getInputStream();
            char[] password = ybsConfig.getCertPassword().toCharArray();
            ks.load(privateKeyInputStream, password);
            kmf.init(ks, password);


            TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            KeyStore ks2 = KeyStore.getInstance("jks");



            //ClassPathResource c1 = new ClassPathResource("static/ybss/ca.truststore");
            publicKeyInputStream = new ByteArrayInputStream(Base64.decodeBase64(ybsConfig.getCaCertBase64()));
            ks2.load(publicKeyInputStream, password);
            tmf.init(ks2);


            sslcontext.init(kmf.getKeyManagers(), tmf.getTrustManagers(), null);


            sslsf = new SSLConnectionSocketFactory(sslcontext, new String[]{"TLSv1", "TLSv1.1", "TLSv1.2"}, (String[])null, (hostname, sslSession) -> {
                if (hostname != null && !"".equals(hostname)) {
                    return !ybsConfig.getVerifyHostNameList().contains(hostname);
                } else {
                    return false;
                }
            });
        } catch(Exception e) {
            throw e;
        } finally {
            if(Objects.nonNull(privateKeyInputStream)) {
                privateKeyInputStream.close();
            }
            if(Objects.nonNull(publicKeyInputStream)) {
                publicKeyInputStream.close();
            }
        }

        return sslsf;
    }


    @Bean(name = "ybsHttpClient")
    public CloseableHttpClient ybsHttpClient() throws Exception{
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();


        CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultRequestConfig(config)
                .setKeepAliveStrategy(getStrategy())
                .setMaxConnTotal(100)
                .setMaxConnPerRoute(50)
                .setSSLSocketFactory(buildYbsConnectionSocketFactory())
                .disableCookieManagement()
                .disableAuthCaching()
                .disableAutomaticRetries().build();

        return httpClient;
    }

    @Bean(name = "commHttpClient")
    public CloseableHttpClient commHttpClient() {
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setConnectionRequestTimeout(DEFAULT_REQUEST_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .build();
        CloseableHttpClient httpClient = HttpClients.custom().
                setDefaultRequestConfig(config)
                .setKeepAliveStrategy(getStrategy())
                .setMaxConnTotal(100)
                .setMaxConnPerRoute(50)
                .disableCookieManagement()
                .disableAuthCaching()
                .disableAutomaticRetries().build();
        LOGGER.info("commHttpClient started");
        return httpClient;
    }


}
