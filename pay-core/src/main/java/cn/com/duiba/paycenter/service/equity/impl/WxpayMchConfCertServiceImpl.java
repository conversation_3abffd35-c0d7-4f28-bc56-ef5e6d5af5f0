package cn.com.duiba.paycenter.service.equity.impl;

import cn.com.duiba.paycenter.dao.equity.WxpayMchConfCertMapper;
import cn.com.duiba.paycenter.entity.equity.WxpayMchConfCertEntity;
import cn.com.duiba.paycenter.service.equity.WxpayMchConfCertService;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/5/4 6:34 PM
 */
@Slf4j
@Service
public class WxpayMchConfCertServiceImpl implements WxpayMchConfCertService {
    
    @Resource
    private WxpayMchConfCertMapper wxpayMchConfCertMapper;

    private final LoadingCache<String, List<WxpayMchConfCertEntity>> certListCache = Caffeine.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES).maximumSize(1000).refreshAfterWrite(30, TimeUnit.SECONDS).build(this::loadByMchType);


    @Override
    public List<WxpayMchConfCertEntity> selectByMchTypeWithLocal(String mchId, Integer certType) {
        if (mchId == null || certType == null) {
            return Collections.emptyList();
        }
        try {
            List<WxpayMchConfCertEntity> list = certListCache.get(mchId + "," + certType);
            if (list == null) {
                // 缓存加载失败
                return selectByMchType(mchId, certType);
            }
            return list;
        } catch (Exception e) {
            log.error("WxpayCert, mchId={}, certType={}", mchId, certType, e);
            return selectByMchType(mchId, certType);
        }
    }
    
    private List<WxpayMchConfCertEntity> loadByMchType(String key) {
        String[] split = key.split(",");
        return selectByMchType(split[0], NumberUtils.toInt(split[1], 0));
    }

    /**
     * 根据商户号、证书类型查询可用证书
     * @param mchId 商户ID
     * @param certType 证书类型
     * {@link cn.com.duiba.paycenter.enums.WxpayMchCertTypeEnum}
     * @return 微信支付商户证书列表
     */
    private List<WxpayMchConfCertEntity> selectByMchType(String mchId, Integer certType) {
        if (mchId == null || certType == null) {
            return Collections.emptyList();
        }
        return wxpayMchConfCertMapper.selectByMchType(mchId, certType);
    }

    @Override
    public int saveIfAbsent(WxpayMchConfCertEntity entity) {
        List<WxpayMchConfCertEntity> list = selectByMchType(entity.getMchId(), entity.getCertType());
        if (CollectionUtils.isNotEmpty(list)) {
            return 0;
        }
        return insert(entity);
    }

    public int insert(WxpayMchConfCertEntity entity) {
        return wxpayMchConfCertMapper.insert(entity);
    }
}
