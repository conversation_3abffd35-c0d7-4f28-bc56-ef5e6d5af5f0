package cn.com.duiba.paycenter.remoteservice.impl.equity;

import cn.com.duiba.paycenter.dto.equity.request.BaseEquityRequest;
import cn.com.duiba.paycenter.dto.equity.request.wx.transfer.EquityWxInitiateBatchTransferRequest;
import cn.com.duiba.paycenter.dto.equity.response.BaseEquityResultResponse;
import cn.com.duiba.paycenter.dto.equity.response.EquityResponse;
import cn.com.duiba.paycenter.dto.equity.response.wx.transfer.BaseEquityDetailResultResponse;
import cn.com.duiba.paycenter.dto.equity.response.wx.transfer.EquityWxInitiateBatchTransferResponse;
import cn.com.duiba.paycenter.handler.equity.impl.EquityWxTransferBatchHandler;
import cn.com.duiba.paycenter.remoteservice.equity.RemoteEquityWxTransferBatchService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/4/25 1:50 PM
 */
@RestController
public class RemoteEquityWxTransferBatchServiceImpl implements RemoteEquityWxTransferBatchService {
   
    @Resource
    private EquityWxTransferBatchHandler equityWxTransferBatchHandler;

    @Override
    public EquityResponse distribute(BaseEquityRequest<EquityWxInitiateBatchTransferRequest> request) {
        return equityWxTransferBatchHandler.distribute(request);
    }

    @Override
    public BaseEquityResultResponse<EquityWxInitiateBatchTransferResponse> distributeResult(Integer bizType, String bizNo) {
        return equityWxTransferBatchHandler.distributeResult(bizType, bizNo);
    }

    @Override
    public BaseEquityDetailResultResponse distributeDetailResult(Integer bizType, String bizNo, String outDetailNo) {
        return equityWxTransferBatchHandler.distributeDetailResult(bizType, bizNo, outDetailNo, BaseEquityDetailResultResponse.class);
    }


}
