package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbOneNetPayNotifyRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbOneNetPayNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteCmbOneNetPayNotifyService;
import cn.com.duiba.paycenter.service.payment.CmbOneNetPayNotifyService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @program: pay-center
 * @description: 招商银行一网通支付通知
 * @author: Simba
 * @create: 2019-09-26 14:37
 **/
@RestController
public class RemoteCmbOneNetPayNotifyServiceImpl implements RemoteCmbOneNetPayNotifyService {

    @Resource
    private CmbOneNetPayNotifyService cmbOneNetPayNotifyService;

    @Override
    public CmbOneNetPayNotifyResponse orderNotify(CmbOneNetPayNotifyRequest request) throws BizException {
        return cmbOneNetPayNotifyService.orderNotify(request);
    }
}

