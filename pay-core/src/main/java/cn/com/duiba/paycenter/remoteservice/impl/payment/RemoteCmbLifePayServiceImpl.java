package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifePayNotifyDto;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifePayNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifePayQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifePayQueryResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifeRefundQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifeRefundQueryResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteCmbLifePayService;
import cn.com.duiba.paycenter.service.payment.CmbLifePayService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 招商银行-掌上生活-H5支付remote
 *
 * <AUTHOR>
 * @date 2024/2/18 8:49 下午
 */
@RestController
public class RemoteCmbLifePayServiceImpl implements RemoteCmbLifePayService {

    @Resource
    private CmbLifePayService cmbLifePayService;

    @Override
    public String doSign(String reqParam) throws BizException {
        return cmbLifePayService.doSign(reqParam);
    }

    @Override
    public CmbLifePayNotifyResponse notify(String notifyParam) throws BizException {
        return cmbLifePayService.notify(notifyParam);
    }

    @Override
    public CmbLifePayNotifyDto decryptNotify(String notifyParam) throws BizException {
        return cmbLifePayService.decryptNotify(notifyParam);
    }

    @Override
    public CmbLifeRefundQueryResponse refundQuery(CmbLifeRefundQueryRequest request) throws BizException {
        return cmbLifePayService.refundQuery(request);
    }

    @Override
    public CmbLifePayQueryResponse payQuery(CmbLifePayQueryRequest request) throws BizException {
        return cmbLifePayService.payQuery(request);
    }
}
