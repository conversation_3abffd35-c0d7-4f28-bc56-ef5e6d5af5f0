package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.dao.payment.ChargeOrderDao;
import cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay.*;
import cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity;
import cn.com.duiba.paycenter.remoteservice.RemoteUnionPayUmsQueryService;
import cn.com.duiba.paycenter.service.payment.UnionPayUmsService;
import cn.com.duiba.paycenter.util.UnionPayUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/7/21 14:03
 */
@RestController
public class RemoteUnionPayUmsQueryServiceImpl implements RemoteUnionPayUmsQueryService {

    @Resource
    private UnionPayUmsService unionPayUmsService;

    @Resource
    private ChargeOrderDao chargeOrderDao;


    @Override
    public UnionPayUmsOrderQueryResponse queryOrder(UnionPayUmsOrderQueryRequest request) {

        UnionPayUmsOrderQueryResponse unionPayUmsOrderQueryResponse = new UnionPayUmsOrderQueryResponse();

        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(request.getBizOrderNo(), request.getBizType());
        if (chargeOrderEntity == null) {
            unionPayUmsOrderQueryResponse.setSuccess(false);
            unionPayUmsOrderQueryResponse.setMessage("订单不存在或查询失败");
            return unionPayUmsOrderQueryResponse;
        }

        String extra = chargeOrderEntity.getExtra();
        if (StringUtils.isNotBlank(extra)) {
            JSONObject jsonObject = JSON.parseObject(extra);
            if (StringUtils.isNotBlank(jsonObject.getString("tid"))
                    && StringUtils.isNotBlank(jsonObject.getString("mid"))) {
                request.setMid(jsonObject.getString("mid"));
                request.setTid(jsonObject.getString("tid"));
            }
        }

        String merOrderId = UnionPayUtils.convert2OrderId(chargeOrderEntity.getOrderNo());

        UnionPayUmsOrderQueryResponseData unionPayUmsOrderQueryResponseData = unionPayUmsService.queryOrder(merOrderId, request);
        if (Objects.equals(unionPayUmsOrderQueryResponseData.getErrCode(), "SUCCESS")) {
            unionPayUmsOrderQueryResponse.setSuccess(true);
            unionPayUmsOrderQueryResponse.setMerOrderId(unionPayUmsOrderQueryResponseData.getMerOrderId());
            unionPayUmsOrderQueryResponse.setStatus(unionPayUmsOrderQueryResponseData.getStatus());
        } else {
            unionPayUmsOrderQueryResponse.setSuccess(false);
            unionPayUmsOrderQueryResponse.setMerOrderId(unionPayUmsOrderQueryResponseData.getMerOrderId());
            if (StringUtils.isBlank(unionPayUmsOrderQueryResponseData.getErrCode()) || StringUtils.isBlank(unionPayUmsOrderQueryResponseData.getErrMsg())) {
                unionPayUmsOrderQueryResponse.setMessage("订单查询失败");
            } else {
                unionPayUmsOrderQueryResponse.setMessage(unionPayUmsOrderQueryResponseData.getErrCode() + ":" + unionPayUmsOrderQueryResponseData.getErrMsg());
            }
        }
        return unionPayUmsOrderQueryResponse;
    }

    @Override
    public UnionPayUmsOrderQueryResponse queryRefund(UnionPayUmsRefundQueryRequest refundQueryRequest) {
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(refundQueryRequest.getBizOrderNo(), refundQueryRequest.getBizType());
        UnionPayUmsOrderQueryResponse payUmsRefundQueryResponseData = new UnionPayUmsOrderQueryResponse();
        if (chargeOrderEntity == null) {
            payUmsRefundQueryResponseData.setSuccess(false);
            payUmsRefundQueryResponseData.setMessage("订单不存在或查询失败");
            return payUmsRefundQueryResponseData;
        }

        String merOrderId = UnionPayUtils.convert2OrderId(chargeOrderEntity.getOrderNo());
        UnionPayUmsRefundQueryResponseData responseData = unionPayUmsService.queryRefund(merOrderId, refundQueryRequest);
        String extra = chargeOrderEntity.getExtra();
        if (StringUtils.isNotBlank(extra)) {
            JSONObject jsonObject = JSON.parseObject(extra);
            if (StringUtils.isNotBlank(jsonObject.getString("tid"))
                    && StringUtils.isNotBlank(jsonObject.getString("mid"))) {
                refundQueryRequest.setMid(jsonObject.getString("mid"));
                refundQueryRequest.setTid(jsonObject.getString("tid"));
            }
        }
        if (Objects.equals(responseData.getErrCode(), "SUCCESS")) {
            payUmsRefundQueryResponseData.setSuccess(true);
            payUmsRefundQueryResponseData.setMerOrderId(chargeOrderEntity.getBizOrderNo());
            payUmsRefundQueryResponseData.setStatus(responseData.getStatus());
        } else {
            payUmsRefundQueryResponseData.setSuccess(false);
            payUmsRefundQueryResponseData.setMerOrderId(chargeOrderEntity.getBizOrderNo());
            payUmsRefundQueryResponseData.setMessage(responseData.getErrCode() + ":" + responseData.getErrMsg());
        }
        return payUmsRefundQueryResponseData;
    }
}
