package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.constant.WxCouponSubjectEnum;
import cn.com.duiba.paycenter.dto.payment.WxCouponDetailDto;
import cn.com.duiba.paycenter.dto.payment.WxRedpacketOrderDto;
import cn.com.duiba.paycenter.dto.payment.charge.ChargeOrderDto;
import cn.com.duiba.paycenter.dto.payment.charge.WxCouponResponse;
import cn.com.duiba.paycenter.dto.payment.charge.abc.AbcWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.abc.AbcWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayNativeChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayNativeChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.bankofsuzhou.BankOfSuZhouWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.bankofsuzhou.BankOfSuZhouWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.boc.BocWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.boc.BocWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.ccb.CcbWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.ccb.CcbWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cebXyk.CebXykWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cebXyk.CebXykWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cib.CibPayWxChargeRequestDTO;
import cn.com.duiba.paycenter.dto.payment.charge.cib.CibPayWxChargeResponseDTO;
import cn.com.duiba.paycenter.dto.payment.charge.citic.CiticWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.citic.CiticWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifeChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifePayRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbOneNetPayRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbOneNetPayResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cus.Activity62VipPayRequestDto;
import cn.com.duiba.paycenter.dto.payment.charge.cus.Activity62VipPayRequestWrap;
import cn.com.duiba.paycenter.dto.payment.charge.cus.Activity62VipPayRespDto;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.bf.DuibaLiveBFChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.bf.DuibaLiveBFChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.installment.DuibaLiveInstallmentChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.installment.DuibaLiveInstallmentChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.mp.DuibaLiveMpChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.mp.DuibaLiveMpChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.hello.HelloPayChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.hello.HelloPayChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.IcbcH5ChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.IcbcH5ChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeResp;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.elife.IcbcELifeChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.elife.IcbcELifeChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4AppChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4AppChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4WxChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4WxChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.LshmChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.LshmChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.mock.MockWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.mock.MockWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.charge.NbcbChargeRequestDto;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.charge.NbcbChargeResponseDto;
import cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay.UnionPayUmsChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay.UnionPayUmsChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.shouxin.ShouxinPayChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.shouxin.ShouxinPayChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wjrcb.WjrcbPayWxPubChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wjrcb.WjrcbPayWxPubChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.BuildAuthorizationUrlRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxCloseOrderRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxCouponSendRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayLiteChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayLiteChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayMpChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayMpChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayNativeChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayNativeChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayRedPacketQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayRedPacketSendRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.xib.charge.XibChargeRequestDTO;
import cn.com.duiba.paycenter.dto.payment.charge.xib.charge.XibChargeResponseDTO;
import cn.com.duiba.paycenter.entity.payment.ybs.UserScanPayResp;
import cn.com.duiba.paycenter.enums.BizTypeEnum;
import cn.com.duiba.paycenter.enums.ChannelEnum;
import cn.com.duiba.paycenter.enums.PayOrderStatusEnum;
import cn.com.duiba.paycenter.mq.producer.WxRedPacketCallbackProducer;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteChargeService;
import cn.com.duiba.paycenter.result.FundTransferResult;
import cn.com.duiba.paycenter.service.payment.ChargeService;
import cn.com.duiba.paycenter.util.MockUtils;
import cn.com.duiba.wolf.utils.BeanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.RejectedExecutionException;

/**
 * <AUTHOR>
 * @date 2018/11/14
 */
@RestController
public class RemoteChargeServiceImpl implements RemoteChargeService {
    private static final Logger logger = LoggerFactory.getLogger(RemoteChargeServiceImpl.class);

    @Resource
    private ChargeService chargeService;

    @Resource
    private Validator validator;

    @Resource(name = "asynSendWxRedPacketExecutorService")
    private ExecutorService asynSendWxRedPacketExecutorService;

    @Resource
    private WxRedPacketCallbackProducer wxRedPacketCallbackProducer;

    @Override
    public WxPayWapChargeResponse createWxPayWapCharge(WxPayWapChargeRequest chargeRequest) throws BizException {
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public WxPayMpChargeResponse createWxPayMpCharge(WxPayMpChargeRequest chargeRequest) throws BizException {
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public WxPayLiteChargeResponse createWxPayLiteCharge(WxPayLiteChargeRequest chargeRequest) throws BizException {
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public WxPayNativeChargeResponse createWxPayNativeCharge(WxPayNativeChargeRequest chargeRequest) throws BizException {
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public AlipayWapChargeResponse createAlipayWapCharge(AlipayWapChargeRequest chargeRequest) throws BizException {
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public AlipayNativeChargeResponse createAlipayNativeCharge(AlipayNativeChargeRequest chargeRequest) throws BizException {
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public BankOfSuZhouWapChargeResponse createSuZhouCharge(BankOfSuZhouWapChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.BANK_OF_SUZHOU_WAP.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public CmbOneNetPayResponse createCmbOneNetCharge(CmbOneNetPayRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.ONE_NET_PAY_OF_CMB.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public CcbWapChargeResponse createCcbCharge(CcbWapChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.BANK_OF_CCB_PAY.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public WjrcbPayWxPubChargeResponse createWjrcbWxPubCharge(WjrcbPayWxPubChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.WJRCB_WX_PUB.getChannelType());
        //定制
        chargeRequest.setMetadata(null);
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public ChargeOrderDto findByOrderNo(String orderNo) throws BizException {
        return chargeService.findByOrderNo(orderNo);
    }

    @Override
    public List<ChargeOrderDto> findByBizOrderNo(String bizOrderNo) throws BizException {
        return chargeService.findByBizOrderNo(bizOrderNo);
    }

    @Override
    public List<ChargeOrderDto> batchFindByOrderNo(List<String> orderNos) throws BizException {
        return chargeService.batchFindByOrderNo(orderNos);
    }

    @Override
    public List<ChargeOrderDto> batchFindByTransactionNo(List<String> transactionNos) throws BizException {
        return chargeService.batchFindByTransactionNo(transactionNos);
    }

    @Override
    public String oauth2buildAuthorizationUrl(Long appId, String redirectURI) throws BizException {
        return chargeService.oauth2buildAuthorizationUrl(appId, redirectURI);
    }

    @Override
    public ChargeOrderDto findByBizNoAndBizType(String bizNo, Integer bizType) {
        return chargeService.findByBizNoAndBizType(bizNo, bizType);
    }

    @Override
    public List<ChargeOrderDto> selectByBizNosAndBizType(List<String> bizNos, Integer bizType) {
        return chargeService.selectByBizNosAndBizType(bizNos, bizType);
    }

    @Override
    public List<ChargeOrderDto> listByBizNosAndBizType(List<String> bizNos, Integer bizType) {
        return chargeService.listByBizNosAndBizType(bizNos, bizType);
    }

    @Override
    public AbcWapChargeResponse createAbcCharge(AbcWapChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.BANK_OF_ABC_PAY.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public BocWapChargeResponse createBocCharge(BocWapChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.BANK_OF_BOC_PAY.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }


    @Override
    public UnionPayWapChargeResponse createUnionPayCharge(UnionPayWapChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.UNION_PAY.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public CiticWapChargeResponse createCiticCharge(CiticWapChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.BANK_OF_CITIC_PAY.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public MockWapChargeResponse createMockCharge(MockWapChargeRequest chargeRequest) throws BizException {
        MockUtils.isMockCharge();
        chargeRequest.setChannelType(ChannelEnum.MOCK_PAY.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public String oauth2buildAuthorizationUrlBySubject(BuildAuthorizationUrlRequest request) throws BizException {
        return chargeService.oauth2buildAuthorizationUrl(request);
    }

    @Override
    public HelloPayChargeResponse createHelloPayCharge(HelloPayChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.HELLO_PAY.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public ShouxinPayChargeResponse createShouxinPayCharge(ShouxinPayChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.SHOUXIN_PAY.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public WxPayLiteChargeResponse createWxPayLiteChargeForKejiji(WxPayLiteChargeRequest chargeRequest) throws BizException {
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public void closeOrder(WxCloseOrderRequest request) throws BizException {
        chargeService.closeOrder(request);
    }

    @Override
    public WxRedpacketOrderDto sendWxRedPacket(WxPayRedPacketSendRequest request) throws BizException {
        return chargeService.sendWxRedPacket(request);
    }

    @Override
    public FundTransferResult asynSendWxRedPacket(WxPayRedPacketSendRequest request) {
        //只负责交给线程池处理，以快速响应星速台
        try {
            asynSendWxRedPacketExecutorService.execute(() -> {
                try {
                    logger.info("异步发送微信红包request:{}", JSONObject.toJSONString(request));
                    WxRedpacketOrderDto wxRedpacketOrderDto = chargeService.sendWxRedPacket(request);
                    logger.info("异步发送微信红包resp:{}", JSONObject.toJSONString(wxRedpacketOrderDto));
                    if (wxRedpacketOrderDto != null && StringUtils.isNotBlank(wxRedpacketOrderDto.getMchBillno())) {
                        WxPayRedPacketQueryRequest queryRequest = BeanUtils.copy(request, WxPayRedPacketQueryRequest.class);
                        // 订单类型 MCHT:通过商户订单号获取红包信息。
                        queryRequest.setBillType("MCHT");
                        queryRequest.setSubjectType(request.getSubjectType());
                        logger.info("异步发送微信红包状态查询request:{}", JSONObject.toJSONString(queryRequest));
                        wxRedPacketCallbackProducer.sendMsg(queryRequest);
                    }
                } catch (BizException e) {
                    //失败
                    logger.warn("异步发送微信红包失败", e);
                }
            });
        } catch (RejectedExecutionException e) {
            return new FundTransferResult(PayOrderStatusEnum.FAIL, "队列已满");
        }
        return new FundTransferResult(PayOrderStatusEnum.PROCESSING, null);
    }

    @Override
    public WxRedpacketOrderDto queryWxRedPacketInfo(WxPayRedPacketQueryRequest request) throws BizException {
        return chargeService.queryWxRedPacketInfo(request);
    }

    @Override
    public IcbcH5ChargeResponse createIcbcCharge(IcbcH5ChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.BANK_OF_ICBC_PAY_H5.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public IcbcElife4AppChargeResponse createIcbcElifeCharge4App(IcbcElife4AppChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.ICBC_ELIFE_PAY_APP.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public IcbcElife4WxChargeResponse createIcbcElifeCharge4Wx(IcbcElife4WxChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.ICBC_ELIFE_PAY_WX.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public WxCouponResponse sendWxCoupon(WxCouponSendRequest request) throws BizException {
        return chargeService.sendWxCoupon(request);
    }

    @Override
    public WxCouponResponse sendFujianDuibaWxCoupon(WxCouponSendRequest request) throws BizException {
        return chargeService.sendFujianDuibaWxCoupon(request);
    }

    @Override
    public WxCouponResponse sendWxCouponByWxSubject(WxCouponSendRequest request) throws BizException {
        return chargeService.sendWxCouponByWxSubject(request);
    }


    @Override
    public CebXykWapChargeResponse createCebXykCharge(CebXykWapChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.BANK_OF_CEB_XYK_PAY.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public DuibaLiveMpChargeResponse createDuibaLiveMpCharge(DuibaLiveMpChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.DUIBA_LIVE_MP_PAY.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public DuibaLiveInstallmentChargeResponse createDuibaLiveInstallmentCharge(DuibaLiveInstallmentChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.DUIBA_LIVE_INSTALLMENT_PAY.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public DuibaLiveBFChargeResponse createDuibaLiveBFCharge(DuibaLiveBFChargeRequest chargeRequest) throws BizException {
        chargeRequest.setChannelType(ChannelEnum.DUIBA_LIVE_BF_PAY.getChannelType());
        return chargeService.createCharge(chargeRequest);
    }

    @Override
    public NbcbChargeResponseDto createNingboBankPayCharge(NbcbChargeRequestDto chargeRequestDto) throws BizException {
        chargeRequestDto.setChannelType(ChannelEnum.NINGBO_BANK_PAY.getChannelType());
        return chargeService.createCharge(chargeRequestDto);
    }

    @Override
    public Activity62VipPayRespDto createActivity62VipCharge(Activity62VipPayRequestDto activity62VipPayRequestDto) throws BizException {

        Set<ConstraintViolation<Activity62VipPayRequestDto>> errorSet = validator.validate(activity62VipPayRequestDto);

        if (CollectionUtils.isNotEmpty(errorSet)) {
            throw new BizException("[ybs] 支付下单失败，参数异常: " + errorSet.iterator().next().getMessage());
        }

        Activity62VipPayRequestWrap payRequestWrap = BeanUtils.copy(activity62VipPayRequestDto, Activity62VipPayRequestWrap.class);

        payRequestWrap.setBizOrderNo(activity62VipPayRequestDto.getOutTradeNo());
        payRequestWrap.setAmount(activity62VipPayRequestDto.getTotalFee());
        //星速台活动
        payRequestWrap.setBizType(BizTypeEnum.XST.getCode());
        payRequestWrap.setChannelType(ChannelEnum.UNION_PAY_OF_SHENZHEN_YBS.getChannelType());

        UserScanPayResp userScanPayResp = chargeService.createCharge(payRequestWrap);

        return BeanUtils.copy(userScanPayResp, Activity62VipPayRespDto.class);
    }

    @Override
    public IcbcCreditsChargeResp createIcbcCreditsPayCharge(IcbcCreditsChargeRequest icbcCreditsChargeRequest) throws BizException {
        icbcCreditsChargeRequest.setChannelType(ChannelEnum.BANK_OF_ICBC_PAY_CREDITS.getChannelType());
        return chargeService.createCharge(icbcCreditsChargeRequest);
    }

    @Override
    public WxCouponDetailDto findWxCouponDetail(String wxAppId, String couponId, String openId) throws BizException {
        return chargeService.findWxCouponDetail(wxAppId, couponId, openId);
    }

    @Override
    public WxCouponDetailDto findWxCouponDetailBySubject(String wxAppId, String couponId, String openId, String subjectCode) throws BizException {
        return chargeService.findWxCouponDetailBySubject(wxAppId, couponId, openId, subjectCode);
    }

    @Override
    public XibChargeResponseDTO createXibPayCharge(XibChargeRequestDTO xibChargeRequestDTO) throws BizException {
        xibChargeRequestDTO.setChannelType(ChannelEnum.XIB_PAY.getChannelType());
        return chargeService.createCharge(xibChargeRequestDTO);
    }

    @Override
    public CibPayWxChargeResponseDTO createCibWxPayCharge(CibPayWxChargeRequestDTO wxChargeRequestDTO) throws BizException {
        wxChargeRequestDTO.setChannelType(ChannelEnum.CIB_WX_PAY.getChannelType());
        return chargeService.createCharge(wxChargeRequestDTO);
    }

    @Override
    public WxCouponResponse reissueWxCoupon(String bizId, String openId, String sotckId, String wxappid, String subject) {
        WxCouponSendRequest wxChargeRequestDTO = new WxCouponSendRequest();
        wxChargeRequestDTO.setAppId(89897L);
        wxChargeRequestDTO.setOpenid(openId);
        wxChargeRequestDTO.setBizId(bizId);
        wxChargeRequestDTO.setStockId(sotckId);
        wxChargeRequestDTO.setSubjectType("null");
        wxChargeRequestDTO.setWxCouponSubject(WxCouponSubjectEnum.getByCode(subject));
        wxChargeRequestDTO.setWxAppId(wxappid);
        WxCouponResponse reissueWxCoupon = null;
        try{
            reissueWxCoupon = chargeService.reissueWxCoupon(wxChargeRequestDTO);
            logger.info("修复数据结果fix,WxCouponSendRequest={},WxCouponResponse={}", JSON.toJSONString(wxChargeRequestDTO), JSON.toJSONString(reissueWxCoupon));
        } catch (Exception e) {
            logger.info("修复数据结果fix,WxCouponSendRequest={}", JSON.toJSONString(wxChargeRequestDTO), e);
        }
        return reissueWxCoupon;
    }

    @Override
    public UnionPayUmsChargeResponse createUnionPayUmsCharge(UnionPayUmsChargeRequest request) throws BizException {
        request.setChannelType(ChannelEnum.UNION_PAY_UMS.getChannelType());
        return chargeService.createCharge(request);
    }

    @Override
    public CmbLifeChargeResponse createCmbLifeCharge(CmbLifePayRequest request) throws BizException {
        request.setChannelType(ChannelEnum.CMB_LIFE_PAY.getChannelType());
        return chargeService.createCharge(request);
    }

    @Override
    public IcbcELifeChargeResponse createIcbcELifeCharge(IcbcELifeChargeRequest request) throws BizException {
        request.setChannelType(ChannelEnum.BANK_OF_ICBC_ELIFE_PAY.getChannelType());
        return chargeService.createCharge(request);
    }

    @Override
    public WeiboChargeResponse createWeiboCharge(WeiboChargeRequest request) throws BizException {
        request.setChannelType(ChannelEnum.WEIBO_PAY.getChannelType());
        return chargeService.createCharge(request);
    }

    @Override
    public LshmChargeResponse createLshmCharge(LshmChargeRequest request) throws BizException {
        request.setChannelType(ChannelEnum.LSHM_PAY.getChannelType());
        return chargeService.createCharge(request);
    }
}
