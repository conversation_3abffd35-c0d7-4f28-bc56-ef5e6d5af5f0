package cn.com.duiba.paycenter.elasticjob;

import cn.com.duiba.paycenter.config.equity.EquityJobConfig;
import cn.com.duiba.paycenter.entity.equity.EquityOrderEntity;
import cn.com.duiba.paycenter.enums.equity.EquityOrderStatusEnum;
import cn.com.duiba.paycenter.handler.equity.EquityHandlerAdapter;
import cn.com.duiba.paycenter.service.equity.EquityOrderService;
import cn.com.duiba.paycenter.util.common.ThreadSleepUtil;
import cn.com.duiba.wolf.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 权益订单，触发查询第三方
 * 每分钟查询一次
 * <AUTHOR>
 * @date 2023/4/24 4:38 PM
 */
@Slf4j
@Component
//@ElasticJob(name = "equityOrderSyncJob", cron = "23 */1 * * * ?", overwrite = true)
public class EquityOrderSyncJob{

    @Resource
    private EquityOrderService equityOrderService;
    
    @Resource
    private EquityHandlerAdapter equityHandlerAdapter;
    
    @Resource
    private EquityJobConfig equityJobConfig;

    public void doProcess() {
        if (!equityJobConfig.isOrderOpen()) {
            return;
        }
        try {
            Date now = new Date();
            Date startTime = DateUtils.minutesAddOrSub(now, -equityJobConfig.getOrderStartTime());
            Date endTime = DateUtils.minutesAddOrSub(now, -equityJobConfig.getOrderEndTime());
            log.info("EquityOrderSyncJob, startTime={}, endTime={}", DateUtils.getSecondStr(startTime), DateUtils.getSecondStr(endTime));
            int totalSize = doProcess(startTime, endTime);
            log.info("EquityOrderSyncJob, startTime={}, endTime={}, totalSize={}", DateUtils.getSecondStr(startTime), DateUtils.getSecondStr(endTime), totalSize);
        } catch (Exception e) {
            log.error("EquityOrderSyncJob, ", e);
        }
    }
    
    private int doProcess(Date startTime, Date endTime) {
        List<EquityOrderEntity> list;
        Long lastId = null;
        int pageSize = 100;
        int totalSize = 0;
        while (CollectionUtils.isNotEmpty(list = equityOrderService.selectPageByTimeAndStatus(startTime, endTime, getQueryStatusList(), lastId, pageSize))) {
            lastId = list.get(list.size() - 1).getId();
            list.forEach(entity -> {
                equityHandlerAdapter.query(entity);
                ThreadSleepUtil.sleep(10);
            });
            totalSize += list.size();
        }
        return totalSize;
    }
    
    private List<Integer> getQueryStatusList() {
        List<Integer> orderStatusList = new ArrayList<>();
        orderStatusList.add(EquityOrderStatusEnum.CALL_THIRD_EXCEPTION.getType());
        orderStatusList.add(EquityOrderStatusEnum.THIRD_PROCESSING.getType());
        orderStatusList.add(EquityOrderStatusEnum.EXCEPTION.getType());
        return orderStatusList;
    }
}
