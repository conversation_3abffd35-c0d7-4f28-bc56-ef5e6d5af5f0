package cn.com.duiba.paycenter.service.third.wx.wxv3;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.bean.wx.v3.result.WxV3BaseResult;
import cn.com.duiba.paycenter.bean.wx.v3.result.cert.WxDownloadCertificateResult;

import java.util.List;

/**
 * 证书下载
 * <AUTHOR>
 * @date 2023/5/4 5:22 PM
 */
public interface WxV3CertApiService {

    /**
     * 下载平台证书
     * @param mchId 商户ID
     * @return 证书列表
     * @throws BizException 参数/配置/请求异常
     */
    WxV3BaseResult<WxDownloadCertificateResult> downloadCertificate(String mchId) throws BizException;
}
