package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.ChannelDto;
import cn.com.duiba.paycenter.dto.payment.config.AlipayConfigDto;
import cn.com.duiba.paycenter.dto.payment.config.WxPayConfigDto;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteChannelBackendService;
import cn.com.duiba.paycenter.service.payment.AlipayConfigService;
import cn.com.duiba.paycenter.service.payment.WxPayConfigService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018/11/07
 */
@RestController
public class RemoteChannelBackendServiceImpl implements RemoteChannelBackendService {
    @Resource
    private WxPayConfigService wxPayConfigService;
    @Resource
    private AlipayConfigService alipayConfigService;

    @Override
    public boolean updateWxPayConfig(WxPayConfigDto wxPayConfigDto) throws BizException {
        return wxPayConfigService.update(wxPayConfigDto);
    }

    @Override
    public boolean updateAlipayConfig(AlipayConfigDto alipayConfigDto) throws BizException {
        return alipayConfigService.update(alipayConfigDto);
    }

    @Override
    public ChannelDto listByAppId(Long appId) throws BizException {
        ChannelDto channelDto = new ChannelDto();

        channelDto.setWxPayConfigDtoList(wxPayConfigService.listByAppId(appId));
        channelDto.setAlipayConfigDtoList(alipayConfigService.listByAppId(appId));
        return channelDto;
    }
}
