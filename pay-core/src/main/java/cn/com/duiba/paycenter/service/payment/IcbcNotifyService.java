package cn.com.duiba.paycenter.service.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.abc.AbcChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifePayNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.IcbcChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.elife.IcbcELifeNotifyResponse;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/11
 */
public interface IcbcNotifyService {
    /**
     * 工行支付通知
     * @param notifyUrl
     * @param map params
     * @return response
     * @throws BizException exception
     */
    IcbcChargeNotifyResponse orderNotify(String notifyUrl, Map<String, String> map) throws BizException;

    /**
     * 支付通知
     *
     * @param notifyParam 通知参数
     * @return 解析后的支付通知参数
     * @throws BizException 异常
     */
    IcbcELifeNotifyResponse payNotify(String notifyParam) throws BizException;
}
