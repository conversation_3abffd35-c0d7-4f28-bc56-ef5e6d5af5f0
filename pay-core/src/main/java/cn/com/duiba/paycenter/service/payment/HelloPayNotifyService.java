package cn.com.duiba.paycenter.service.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.hello.HelloPayChargeNotifyRequest;
import cn.com.duiba.paycenter.dto.payment.charge.hello.HelloPayChargeNotifyResponse;


/**
 * Created by sunyan on 2019/10/24.
 */
public interface HelloPayNotifyService {
    /**
     * 哈啰零钱支付通知
     * @param helloPayChargeNotifyRequest
     * @return response
     * @throws BizException exception
     */
    HelloPayChargeNotifyResponse orderNotify(HelloPayChargeNotifyRequest helloPayChargeNotifyRequest) throws BizException;
}
