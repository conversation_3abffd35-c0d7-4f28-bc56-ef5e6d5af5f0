package cn.com.duiba.paycenter.remoteservice.impl.duibaaccount;

import cn.com.duiba.paycenter.dto.duibaaccount.SupplierAccountDetailPageDto;
import cn.com.duiba.paycenter.dto.duibaaccount.SupplierSettleAccountDetailDto;
import cn.com.duiba.paycenter.params.duibaaccount.SupplierAccountDetailQryParams;
import cn.com.duiba.paycenter.remoteservice.duibaaccount.RemoteSupplierSettleAccountDetailService;
import cn.com.duiba.paycenter.service.duibaaccount.SupplierSettleAccountDetailService;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * author zhanghuifeng
 * date 2018-12-13-14:27
 */
@RestController
public class RemoteSupplierSettleAccountDetailServiceImpl implements RemoteSupplierSettleAccountDetailService {

    @Resource
    private SupplierSettleAccountDetailService supplierSettleAccountDetailService;

    @Override
    public List<SupplierAccountDetailPageDto> find4Page(SupplierAccountDetailQryParams params) {
        return supplierSettleAccountDetailService.find4Page(params);
    }

    @Override
    public Integer count4page(SupplierAccountDetailQryParams params) {
        return supplierSettleAccountDetailService.count4page(params);
    }

    @Override
    public SupplierSettleAccountDetailDto findByIdAndSupplierId(Long id, Long supplierId) {
        return BeanUtils.copy(supplierSettleAccountDetailService.findByIdAndSupplierId(id, supplierId)
                ,SupplierSettleAccountDetailDto.class);
    }

    @Override
    public List<SupplierSettleAccountDetailDto> findByRelationAndSupplierId(String relationId, List<Integer> relationTypes, Long supplierId) {
        return BeanUtils.copyList(supplierSettleAccountDetailService.findByRelationAndSupplierId(relationId, relationTypes, supplierId),
                SupplierSettleAccountDetailDto.class);
    }
}
