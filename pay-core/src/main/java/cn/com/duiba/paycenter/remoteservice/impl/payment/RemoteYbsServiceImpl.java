package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.paycenter.config.YbsConfig;
import cn.com.duiba.paycenter.dao.payment.ChargeOrderDao;
import cn.com.duiba.paycenter.dao.payment.RefundOrderDao;
import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cus.*;
import cn.com.duiba.paycenter.dto.payment.refund.RefundRequest;
import cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity;
import cn.com.duiba.paycenter.entity.payment.RefundOrderEntity;
import cn.com.duiba.paycenter.enums.BizTypeEnum;
import cn.com.duiba.paycenter.enums.ChannelEnum;
import cn.com.duiba.paycenter.enums.RefundErrorCodeEnum;
import cn.com.duiba.paycenter.enums.RefundOrderStatusEnum;
import cn.com.duiba.paycenter.enums.ybs.YbsRefundStatusEnum;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteYbsService;
import cn.com.duiba.paycenter.service.payment.YbsPayService;
import cn.com.duiba.paycenter.util.ybs.YbsUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.Date;
import java.util.Objects;
import java.util.Set;


/**
 * <AUTHOR>
 */
@RestController
public class RemoteYbsServiceImpl implements RemoteYbsService {

    @Resource
    private YbsUtils ybsUtils;

    @Resource
    private YbsConfig ybsConfig;

    @Resource
    private YbsPayService ybsPayService;

    @Resource
    private ChargeOrderDao chargeOrderDao;

    @Resource
    private RefundOrderDao refundOrderDao;

    @Resource
    private Validator validator;

    private Logger log = LoggerFactory.getLogger(this.getClass());


    @Override
    public BaseChargeNotifyResponse orderNotify(YbsNotifyReqDto ybsNotifyReqDto) {
        Set<ConstraintViolation<YbsNotifyReqDto>> errorSet = validator.validate(ybsNotifyReqDto);

        if (CollectionUtils.isNotEmpty(errorSet)) {
            throw new IllegalArgumentException("[ybs] 支付通知失败，参数异常: " + errorSet.iterator().next().getMessage());
        }



        return ybsPayService.orderNotify(ybsNotifyReqDto);
    }

    @Override
    public YbsOrderQueryRespDto orderQuery(YbsOrderQueryReqDto ybsOrderQueryReqDto) {
        Set<ConstraintViolation<YbsOrderQueryReqDto>> errorSet = validator.validate(ybsOrderQueryReqDto);

        if (CollectionUtils.isNotEmpty(errorSet)) {
            throw new IllegalArgumentException("[ybs] 订单查询失败，参数异常: " + errorSet.iterator().next().getMessage());
        }


        return ybsPayService.doOrderQuery(ybsOrderQueryReqDto);
    }

    @Override
    public boolean signVerify(String data, String originSign) {
        String originStr = data + "&key=" + ybsConfig.getSecret();

        boolean isValid = ybsUtils.signVerify(originStr, originSign);
        log.info("[ybs] 三方调用签名工具类 原串 = 【{}】 验签结果 = 【{}】", originStr, isValid);

        return isValid;
    }

    @Override
    public String chargeTest() {

        Activity62VipPayRequestWrap wrap = new Activity62VipPayRequestWrap();

        wrap.setBody("11");
        wrap.setAttach("22");
        wrap.setNotifyUrl("https://www.baidu.com");
        wrap.setProductId("12345");
        wrap.setTimeoutExpress("300");
        wrap.setChargeDate(new Date());
        wrap.setClientIp("127.0.0.1");
        wrap.setOrderNo("charge_123456789");
        wrap.setAmount(100);
        wrap.setBizOrderNo("123456789");
        wrap.setBizType(BizTypeEnum.XST.getCode());
        wrap.setChannelType(ChannelEnum.UNION_PAY_OF_SHENZHEN_YBS.getChannelType());
        wrap.setAppId(19441L);


        return JSON.toJSONString(ybsPayService.doCreateCharge(wrap));
    }

    @Override
    public YbsRefundQueryResp doRefundQuery(YbsRefundQueryDto ybsRefundQueryDto) {
        Set<ConstraintViolation<YbsRefundQueryDto>> errorSet = validator.validate(ybsRefundQueryDto);

        YbsRefundQueryResp resp = new YbsRefundQueryResp();
        if (CollectionUtils.isNotEmpty(errorSet)) {
            resp.setCode(YbsRefundStatusEnum.PARAM_ERROR.getCode());
            resp.setMsg(errorSet.iterator().next().getMessage());
            return resp;
        }
        // 交易号核定单号 至少一个不为空
        String bizOrderNo = ybsRefundQueryDto.getBizOrderNo();
        String chargeOrderNo = ybsRefundQueryDto.getChargeOrderNo();
        if (StringUtils.isAllBlank(bizOrderNo, chargeOrderNo)) {
            resp.setCode(YbsRefundStatusEnum.PARAM_ERROR.getCode());
            resp.setMsg("bizOrderNo与chargeOrderNo 至少一个不为空");
        }
        ChargeOrderEntity chargeOrderEntity = StringUtils.isNotBlank(chargeOrderNo) ? chargeOrderDao.findByOrderNo(chargeOrderNo):chargeOrderDao.findByBizOrderNoAndBizType(bizOrderNo, BizTypeEnum.XST.getCode());
        if (Objects.isNull(chargeOrderEntity)) {
            resp.setCode(YbsRefundStatusEnum.ORDER_NOT_EXIST.getCode());
            resp.setMsg(YbsRefundStatusEnum.ORDER_NOT_EXIST.getDesc());
            return resp;
        }
        // 退单查询
        RefundOrderEntity refundOrder = refundOrderDao.findByChargeOrderNo(chargeOrderEntity.getOrderNo(), null);
        if (Objects.isNull(refundOrder)) {
            resp.setCode(YbsRefundStatusEnum.REFUND_ORDER_NOT_EXIST.getCode());
            resp.setMsg(YbsRefundStatusEnum.REFUND_ORDER_NOT_EXIST.getDesc());
            return resp;
        }

        Integer refundStatus = refundOrder.getRefundStatus();
        // 已退款成功
        if (Objects.nonNull(refundStatus) && refundStatus.equals(RefundOrderStatusEnum.ORDER_SUCCEEDED.getCode())) {
            resp.setCode(YbsRefundStatusEnum.REFUNDED.getCode());
            resp.setMsg(YbsRefundStatusEnum.REFUNDED.getDesc());
            return resp;
        }
        return ybsPayService.doRefundQuery(refundOrder, ybsRefundQueryDto);
    }
}
