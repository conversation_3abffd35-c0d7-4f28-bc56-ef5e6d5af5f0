package cn.com.duiba.paycenter.service.payment.impl.lshm;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.config.WaitConfirmChargeOrderConfig;
import cn.com.duiba.paycenter.config.lshm.LshmPayConfig;
import cn.com.duiba.paycenter.dao.payment.ChargeOrderDao;
import cn.com.duiba.paycenter.dto.payment.WaitConfirmChargeOrderDto;
import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.ccb.CcbWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.LshmChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.LshmChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.enums.LshmPayStatus;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.enums.LshmRefundStatus;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmPayRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmRefundRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.response.LshmPayResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.response.LshmRefundResponse;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboChargeRequest;
import cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity;
import cn.com.duiba.paycenter.entity.payment.RefundHandlerResult;
import cn.com.duiba.paycenter.entity.payment.RefundOrderEntity;
import cn.com.duiba.paycenter.enums.ChannelEnum;
import cn.com.duiba.paycenter.service.payment.AbstractChannelHandler;
import cn.com.duiba.paycenter.service.payment.ChannelHandler;
import cn.com.duiba.paycenter.service.payment.ChannelHandlerManager;
import cn.com.duiba.paycenter.service.payment.WaitConfirmChargeOrderService;
import cn.com.duiba.paycenter.service.payment.impl.lshm.impl.LshmPayServiceImpl;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Optional;

@Component
@Slf4j
public class LshmPayHandler extends AbstractChannelHandler implements ChannelHandler {

    @Resource
    private LshmPayService lshmPayService;

    @Resource
    private LshmPayConfig lshmPayConfig;

    @Override
    public void register() {
        ChannelHandlerManager.register(ChannelEnum.LSHM_PAY.getChannelType(), this);
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T extends BaseChargeResponse, E extends BaseChargeRequest> T createCharge(E chargeRequest) {
        log.info("零食很忙 支付请求 chargeRequest={}", JSON.toJSONString(chargeRequest));
        LshmChargeRequest lshmChargeRequest = (LshmChargeRequest) chargeRequest;
        LshmChargeResponse chargeResponse = new LshmChargeResponse();
        if (StringUtils.isNotBlank(chargeRequest.getMetadata())){
            String metadata = lshmChargeRequest.getMetadata();
            if (StringUtils.isNotBlank(metadata)) {
                String payParams = Optional.of(metadata)
                        .map(JSON::parseObject)
                        .map(x -> x.getString("payParams"))
                        .orElse(null);
                chargeResponse.setSuccess(true);
                chargeResponse.setPayParams(payParams);
                chargeResponse.setStoreCode(lshmChargeRequest.getStoreCode());
                log.info("零食很忙 支付请求  chargeResponse={}", JSON.toJSONString(chargeResponse));
                return (T) chargeResponse;
            }
        }
        Long appId = chargeRequest.getAppId();
        try {
            chargeResponse.setOrderNo(lshmChargeRequest.getOrderNo());
            // 构建支付参数
            LshmPayRequest payRequest = builderPayRequest(lshmChargeRequest);
            // 请求零食很忙支付接口
            LshmPayResponse lshmPayResponse = lshmPayService.doPay(appId, payRequest);
            if (!LshmPayStatus.PAYING.getStatus().equals(lshmPayResponse.getPayStatus())) {
                throw new BizException("支付响应状态异常");
            }
            chargeResponse.setTransactionNo(lshmPayResponse.getTradeNo());
            chargeResponse.setStoreCode(lshmChargeRequest.getStoreCode());
            chargeResponse.setPayParams(lshmPayResponse.getPayParams());

            // 创建支付主动查询任务
            createPayQueryTask(lshmChargeRequest.getOrderNo());
        } catch (BizException bizException) {
            log.error("零食很忙 创建支付异常 chargeRequest={}", JSON.toJSONString(chargeRequest), bizException);
            chargeResponse.setSuccess(false);
            chargeResponse.setMessage(bizException.getMessage());
            return (T) chargeResponse;
        } catch (Exception e) {
            log.error("零食很忙 创建支付异常 chargeRequest={}", JSON.toJSONString(chargeRequest), e);
            chargeResponse.setSuccess(false);
            chargeResponse.setMessage("系统异常");
            return (T) chargeResponse;
        }
        log.info("零食很忙 支付请求  chargeResponse={}", JSON.toJSONString(chargeResponse));
        chargeResponse.setSuccess(true);
        return (T) chargeResponse;
    }

    @Override
    public <T extends BaseChargeRequest> ChargeOrderEntity createChargeOrderEntity(T chargeRequest) {
        ChargeOrderEntity chargeOrderEntity = super.createChargeOrderEntity(chargeRequest);
        LshmChargeRequest lshmChargeRequest = (LshmChargeRequest) chargeRequest;
        chargeOrderEntity.setTitle(lshmChargeRequest.getTitle());
        return chargeOrderEntity;
    }

    /**
     * 构建支付请求参数
     * @param chargeRequest 支付请求
     * @return 支付请求参数
     */
    private LshmPayRequest builderPayRequest(LshmChargeRequest chargeRequest) {
        LshmPayRequest lshmPayRequest = new LshmPayRequest();
        lshmPayRequest.setMerchantNo(chargeRequest.getStoreCode());
        lshmPayRequest.setOrderNo(chargeRequest.getOrderNo());
        lshmPayRequest.setOrderType(lshmPayConfig.getOrderType());
        lshmPayRequest.setPayMethodNo(lshmPayConfig.getPayMethodNo());
        lshmPayRequest.setOrderFrom(lshmPayConfig.getOrderFrom());
        lshmPayRequest.setPayAmount(new BigDecimal(chargeRequest.getAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        lshmPayRequest.setCurrentTime(System.currentTimeMillis());
        lshmPayRequest.setOperator(lshmPayConfig.getOperator());
        lshmPayRequest.setClientFrom(lshmPayConfig.getClientFrom());
        lshmPayRequest.setCloseOrderTime(chargeRequest.getCloseOrderTime());
        lshmPayRequest.setWxOpenId(chargeRequest.getWxOpenId());
        lshmPayRequest.setNotifyUrl(chargeRequest.getNotifyUrl());
        return lshmPayRequest;
    }

    /**
     * 构建退款参数
     * @param refundOrderEntity 退款实体
     * @param chargeOrder 支付实体
     * @return 退款参数
     */
    private LshmRefundRequest buildRefundRequest(RefundOrderEntity refundOrderEntity, ChargeOrderEntity chargeOrder) {
        LshmRefundRequest refundRequest = new LshmRefundRequest();
        String storeCode = JSON.parseObject(chargeOrder.getExtra()).getString("storeCode");
        refundRequest.setStoreCode(storeCode);
        refundRequest.setTradeNo(chargeOrder.getTransactionNo());
        refundRequest.setRefundAmount(new BigDecimal(refundOrderEntity.getApplyRefundAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        refundRequest.setRefundOrderNo(refundOrderEntity.getOrderNo());
        return refundRequest;
    }

    @Override
    public RefundHandlerResult createRefund(RefundOrderEntity refundOrderEntity, String subjectType) {
        RefundHandlerResult result = new RefundHandlerResult();
        log.info("零食很忙 申请退款 refundOrderEntity={}", JSON.toJSONString(refundOrderEntity));
        try {
            ChargeOrderEntity chargeOrder = getOrder(refundOrderEntity);
            LshmRefundRequest lshmRefundRequest = buildRefundRequest(refundOrderEntity, chargeOrder);
            LshmRefundResponse refundResponse = lshmPayService.refund(refundOrderEntity.getAppId(), lshmRefundRequest);
            if (LshmRefundStatus.FAIL.getStatus().equals(refundResponse.getRefundStatus())) {
                result.setFailMsg("退款失败");
                result.setSuccess(false);
                return result;
            }
            if (LshmRefundStatus.SUCCESS.getStatus().equals(refundResponse.getRefundStatus()) || LshmRefundStatus.REFUNDING.getStatus().equals(refundResponse.getRefundStatus())) {
                // 退款成功
                result.setSuccess(true);
                result.setRefundNo(refundResponse.getRefundOrderNo());

                // 创建退款主动查询任务
                createRefundQueryTask(chargeOrder, refundOrderEntity);
                return result;
            }
            result.setFailMsg("未知的退款状态");
            result.setSuccess(false);
            return result;
        } catch (Exception e) {
            log.error("微博支付 申请退款 退款异常 refundOrderEntity={}", JSON.toJSONString(refundOrderEntity), e);
            result.setFailMsg("系统异常");
            result.setSuccess(false);
            return result;
        }
    }
}
