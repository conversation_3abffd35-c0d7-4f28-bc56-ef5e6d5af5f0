package cn.com.duiba.paycenter.remoteservice.impl;


import cn.com.duiba.paycenter.biz.AmbBiz;
import cn.com.duiba.paycenter.constant.PayCenterErrorCode;
import cn.com.duiba.paycenter.result.AmbResult;
import cn.com.duiba.paycenter.service.AmbPayCenterService;
import cn.com.duiba.paycenter.util.SignUtil;
import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
public class RemoteAmbPayCenterServiceImpl implements AmbPayCenterService{
	
	private static Logger log=LoggerFactory.getLogger(RemoteAmbPayCenterServiceImpl.class);
	@Autowired
	private AmbBiz ambBiz;
	@Override
	public AmbResult consumerPay(Long developerId, Long orderId,
			Long consumerPrice, AmbPayParams p,String sign) {
		log.debug(getClass().getName()+".consumerPay("+developerId+","+orderId+","+consumerPrice+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("orderId", orderId+"");
		params.put("consumerPrice", consumerPrice+"");
		String checksign=SignUtil.sign(params);
		
		if(!checksign.equals(sign)){
			return new AmbResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		return ambBiz.consumerPay(developerId, orderId, consumerPrice, p);
	}

	@Override
	public AmbResult consumerPayBack(Long developerId, Long orderId,
			Long paybackMoney, Long ordersItemId, AmbPayParams p,String sign) {
		log.debug(getClass().getName()+".consumerPayBack("+developerId+","+orderId+","+paybackMoney+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("orderId", orderId+"");
		params.put("paybackMoney", paybackMoney+"");
		params.put("ordersItemId", ordersItemId+"");
		String checksign=SignUtil.sign(params);
		
		if(!checksign.equals(sign)){
			return new AmbResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		return ambBiz.consumerPayBack(developerId, orderId, paybackMoney, ordersItemId, p);
	}

	@Override
	public AmbResult consumerPayBackList(Long developerId, Long orderId, Map<Long, Long> orderItemIdsAndMoney, AmbPayParams p, String sign) {
		log.debug(getClass().getName()+".consumerPayBack("+developerId+","+orderId+","+ JSON.toJSONString(orderItemIdsAndMoney)+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("orderId", orderId+"");
		params.put("orderItemIdsAndMoney", JSON.toJSONString(orderItemIdsAndMoney));
		String checksign=SignUtil.sign(params);

		if(!checksign.equals(sign)){
			return new AmbResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		return ambBiz.consumerPayBackList(developerId, orderId, orderItemIdsAndMoney, p);
	}

	@Override
	public AmbResult dlpWithdrawToRemaining(Long developerId,
			Long withdrawCashOrderId, Long money, AmbPayChargeExtraParams p,String sign) {
		log.debug(getClass().getName()+".dlpWithdrawToRemaining("+developerId+","+withdrawCashOrderId+","+money+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("withdrawCashOrderId", withdrawCashOrderId+"");
		params.put("money", money+"");
		String checksign=SignUtil.sign(params);
		
		if(!checksign.equals(sign)){
			return new AmbResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		return ambBiz.dlpWithdrawToRemaining(developerId, withdrawCashOrderId, money, p);
	}

	@Override
	public AmbResult orderSettle(Long orderId, Long duibaMoney, Long devMoney,
			AmbPayChargeExtraParams p,String sign) {
		log.debug(getClass().getName()+".orderSettle("+orderId+","+duibaMoney+","+devMoney+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("orderId", orderId+"");
		params.put("duibaMoney", duibaMoney+"");
		params.put("devMoney", devMoney+"");
		
		String checksign=SignUtil.sign(params);
		
		if(!checksign.equals(sign)){
			return new AmbResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		return ambBiz.orderSettle(orderId, duibaMoney, devMoney,0L, p);
	}

	@Override
	public AmbResult orderSettleWithFee(Long orderId, Long duibaMoney, Long devMoney, Long fee, AmbPayChargeExtraParams p, String sign) {
		log.debug(getClass().getName()+".orderSettleWithFee("+orderId+","+duibaMoney+","+devMoney+","+fee+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("orderId", orderId+"");
		params.put("duibaMoney", duibaMoney+"");
		params.put("devMoney", devMoney+"");
		params.put("fee", fee+"");

		String checksign=SignUtil.sign(params);

		if(!checksign.equals(sign)){
			return new AmbResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		return ambBiz.orderSettle(orderId, duibaMoney, devMoney,fee, p);
	}

	@Override
	public AmbResult dlpWithdrawCashApply(Long developerId,
			Long ambDeveloperWithdrawCashOrderId, Long drawCachMoney,
			AmbPayChargeExtraParams p,String sign) {
		log.debug(getClass().getName()+".dlpWithdrawCashApply("+developerId+","+ambDeveloperWithdrawCashOrderId+","+drawCachMoney+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("ambDeveloperWithdrawCashOrderId", ambDeveloperWithdrawCashOrderId+"");
		params.put("drawCachMoney", drawCachMoney+"");
		
		String checksign=SignUtil.sign(params);
		
		if(!checksign.equals(sign)){
			return new AmbResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		return ambBiz.dlpWithdrawCashApply(developerId, ambDeveloperWithdrawCashOrderId, drawCachMoney, p);
	}

	@Override
	public AmbResult dlpWithdrawCashRefuse(Long developerId,
			Long ambDeveloperWithdrawCashOrderId, Long drawCachMoney,
			AmbPayChargeExtraParams p,String sign) {
		log.debug(getClass().getName()+".dlpWithdrawCashRefuse("+developerId+","+ambDeveloperWithdrawCashOrderId+","+drawCachMoney+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("ambDeveloperWithdrawCashOrderId", ambDeveloperWithdrawCashOrderId+"");
		params.put("drawCachMoney", drawCachMoney+"");
		
		String checksign=SignUtil.sign(params);
		
		if(!checksign.equals(sign)){
			return new AmbResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		return ambBiz.dlpWithdrawCashRefuse(developerId, ambDeveloperWithdrawCashOrderId, drawCachMoney, p, null);
	}

	@Override
	public AmbResult dlpWithdrawCashRefuseBySubject(Long developerId, Long ambDeveloperWithdrawCashOrderId, Long drawCachMoney, AmbPayChargeExtraParams p, String sign, String subjectType) {
		log.debug(getClass().getName()+".dlpWithdrawCashRefuse("+developerId+","+ambDeveloperWithdrawCashOrderId+","+drawCachMoney+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("ambDeveloperWithdrawCashOrderId", ambDeveloperWithdrawCashOrderId+"");
		params.put("drawCachMoney", drawCachMoney+"");

		String checksign=SignUtil.sign(params);

		if(!checksign.equals(sign)){
			return new AmbResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		return ambBiz.dlpWithdrawCashRefuse(developerId, ambDeveloperWithdrawCashOrderId, drawCachMoney, p, Integer.valueOf(subjectType));
	}

	@Override
	public AmbResult xstOrderSettleWithFee(Long orderId, Long devMoney, Long fee, AmbPayChargeExtraParams p, String sign) {
		if (devMoney.compareTo(0L) <= 0 || fee.compareTo(0L) < 0 || devMoney.compareTo(fee) <= 0) {
			return new AmbResult(false, PayCenterErrorCode.PayMoneyParamErrir, null);
		}
		if(p.getAppId() == null || p.getConusmerId() == null || p.getDeveloperId() == null){
			return new AmbResult(false, PayCenterErrorCode.PayMoneyParamErrir, null);
		}

		log.info(getClass().getName() + ".xstOrderSettleWithFee(" + orderId + "," + devMoney + "," + fee + "," + sign + ")");
		Map<String, String> params = new HashMap<>();
		params.put("orderId", orderId + "");
		params.put("devMoney", devMoney + "");
		params.put("fee", fee + "");

		String checksign = SignUtil.sign(params);

		if (!checksign.equals(sign)) {
			return new AmbResult(false, PayCenterErrorCode.CodeParamsSignError, null);
		}
		return ambBiz.xstOrderSettleWithFee(orderId, devMoney,fee, p);
	}

}
