package cn.com.duiba.paycenter.service.equity.impl;

import cn.com.duiba.paycenter.dao.equity.EquityOrderRecordMapper;
import cn.com.duiba.paycenter.entity.equity.EquityOrderRecordDetailEntity;
import cn.com.duiba.paycenter.entity.equity.EquityOrderRecordEntity;
import cn.com.duiba.paycenter.service.equity.EquityOrderRecordService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/4/10 3:32 PM
 */
@Service
public class EquityOrderRecordServiceImpl implements EquityOrderRecordService {
    
    @Resource
    private EquityOrderRecordMapper equityOrderRecordMapper;

    @Override
    public EquityOrderRecordEntity selectByOutBizNo(String outBizNo) {
        if (StringUtils.isBlank(outBizNo)) {
            return null;
        }
        return equityOrderRecordMapper.selectByOutBizNo(outBizNo);
    }

    @Override
    public EquityOrderRecordEntity selectByRecordNo(String recordNo) {
        if (StringUtils.isBlank(recordNo)) {
            return null;
        }
        return equityOrderRecordMapper.selectByRecordNo(recordNo);
    }

    @Override
    public EquityOrderRecordDetailEntity selectDetailByRecordNo(String recordNo) {
        if (StringUtils.isBlank(recordNo)) {
            return null;
        }
        return equityOrderRecordMapper.selectDetailByRecordNo(recordNo);
    }

    @Override
    public EquityOrderRecordEntity selectLastByBiz(Integer bizType, String bizNo) {
        if (bizType == null || bizNo == null) {
            return null;
        }
        return equityOrderRecordMapper.selectLastByBiz(bizType, bizNo);
    }

    @Override
    public int insert(EquityOrderRecordDetailEntity entity) {
        if (entity == null || entity.getBizType() == null || entity.getBizNo() == null) {
            return 0;
        }
        return equityOrderRecordMapper.insert(entity);
    }

    @Override
    public int updateProcessingStatus(String recordNo, Integer beforeStatus) {
        if (recordNo == null || beforeStatus == null) {
            return 0;
        }
        return equityOrderRecordMapper.updateProcessingStatus(recordNo, beforeStatus);
    }

    @Override
    public int updateResult(EquityOrderRecordDetailEntity entity, Integer beforeStatus) {
        if (entity == null || entity.getRecordNo() == null || entity.getOrderStatus() == null || beforeStatus == null) {
            return 0;
        }
        return equityOrderRecordMapper.updateResult(entity, beforeStatus);
    }
}
