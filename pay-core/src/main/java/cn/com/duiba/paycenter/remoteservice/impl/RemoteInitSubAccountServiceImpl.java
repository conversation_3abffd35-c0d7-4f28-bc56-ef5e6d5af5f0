package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dao.DsConstants;
import cn.com.duiba.paycenter.dao.SubAccountDao;
import cn.com.duiba.paycenter.dao.SubAccountDetailDao;
import cn.com.duiba.paycenter.dto.SubAccountDetailDto;
import cn.com.duiba.paycenter.dto.SubAccountDto;
import cn.com.duiba.paycenter.entity.credits.SubAccountDetailEntity;
import cn.com.duiba.paycenter.entity.credits.SubAccountEntity;
import cn.com.duiba.paycenter.remoteservice.RemoteInitSubAccountService;
import cn.com.duiba.wolf.utils.BeanUtils;
import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * @Date 2023/2/22 15:50
 * <AUTHOR>
 */
@RestController
public class RemoteInitSubAccountServiceImpl implements RemoteInitSubAccountService {

    private static Logger logger = LoggerFactory.getLogger(RemoteInitSubAccountServiceImpl.class);


    @Resource
   private SubAccountDao subAccountDao;
   @Resource
   private SubAccountDetailDao subAccountDetailDao;

    @Transactional(transactionManager = DsConstants.DATABASE_CREDITS,rollbackFor = Exception.class)
    @Override
    public void initSubAccount(List<SubAccountDto> subAccountDtoList) throws BizException {
        List<SubAccountEntity> subAccountEntityList = BeanUtils.copyList(subAccountDtoList, SubAccountEntity.class);
        logger.info("传入数据为={}", JSON.toJSONString(subAccountEntityList));
        Boolean res = subAccountDao.batchInsert(subAccountEntityList);
        if (!res){
            throw new BizException("子账户插入异常");
        }
        List<SubAccountDetailEntity> subAccountDetailEntityArrayList = new ArrayList<>();
        for (SubAccountEntity subAccount:subAccountEntityList) {
            SubAccountDetailEntity subAccountDetailEntity = new SubAccountDetailEntity();
            subAccountDetailEntity.setSubAccountId(subAccount.getId());
            subAccountDetailEntity.setBalance(subAccount.getBalance());
            subAccountDetailEntity.setMemo("初始化子账户");
            subAccountDetailEntity.setMoneyChange(subAccount.getBalance().intValue());
            subAccountDetailEntity.setType(SubAccountDetailDto.TypeIncome);
            subAccountDetailEntity.setRechargeType(SubAccountDetailDto.RechargeTypeInit);
            subAccountDetailEntity.setDeveloperId(subAccount.getDeveloperId());
            subAccountDetailEntityArrayList.add(subAccountDetailEntity);
        }
        res = subAccountDetailDao.batchInsert(subAccountDetailEntityArrayList);
        if (!res){
            throw new BizException("子账户详情插入异常");
        }
    }
}
