package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.hello.HelloPayChargeNotifyRequest;
import cn.com.duiba.paycenter.dto.payment.charge.hello.HelloPayChargeNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteHelloPayNotifyService;
import cn.com.duiba.paycenter.service.payment.HelloPayNotifyService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Created by sunyan on 2019/10/24.
 */
@RestController
public class RemoteHelloPayNotifyServiceImpl implements RemoteHelloPayNotifyService {
    @Resource
    private HelloPayNotifyService helloPayNotifyService;
    @Override
    public HelloPayChargeNotifyResponse orderNotify(HelloPayChargeNotifyRequest helloPayChargeNotifyRequest) throws BizException {
        return helloPayNotifyService.orderNotify(helloPayChargeNotifyRequest);
    }
}
