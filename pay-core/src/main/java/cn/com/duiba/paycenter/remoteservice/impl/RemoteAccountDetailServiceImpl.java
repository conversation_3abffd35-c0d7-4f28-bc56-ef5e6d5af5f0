package cn.com.duiba.paycenter.remoteservice.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import cn.com.duiba.paycenter.dto.AccountDetailTotalDto;
import cn.com.duiba.paycenter.params.AccountChangeParam;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import cn.com.duiba.paycenter.dto.ADeveloperFlowQuery;
import cn.com.duiba.paycenter.dto.AccountDetailDto;
import cn.com.duiba.paycenter.dto.AccountPageInfo;
import cn.com.duiba.paycenter.remoteservice.RemoteAccountDetailService;
import cn.com.duiba.paycenter.service.AccountDetailService;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class RemoteAccountDetailServiceImpl implements
		RemoteAccountDetailService {
	
	@Autowired
	private AccountDetailService accountDetailService;

	@Override
	public AccountDetailDto insert(AccountDetailDto ad) {
		accountDetailService.insert(ad);
		return ad;
	}

	@Override
	public AccountDetailDto find(Long id) {
		return accountDetailService.find(id);
	}

	@Override
	public List<AccountDetailDto> findAllByPageInfo(AccountPageInfo pageInfo) {
		return accountDetailService.findAllByPageInfo(pageInfo);
	}

	@Override
	public Long getCountByPageInfo(AccountPageInfo pageInfo) {
		return accountDetailService.getCountByPageInfo(pageInfo);
	}

	@Override
	public List<AccountDetailDto> findAllByOrder(Long orderId) {
		return accountDetailService.findAllByOrder(orderId);
	}

	@Override
	public List<AccountDetailDto> findLastDetails(Integer count) {
		return accountDetailService.findLastDetails(count);
	}

	@Override
	public List<AccountDetailDto> findPageByDeveloperId(
			ADeveloperFlowQuery query) {
		return accountDetailService.findPageByDeveloperId(query);
	}

	@Override
	public Long findPageByDeveloperIdCount(ADeveloperFlowQuery query) {
		return accountDetailService.findPageByDeveloperIdCount(query);
	}

	@Override
	public List<AccountDetailDto> findAccountDetailByPageInfo(
			AccountPageInfo pageInfo) {
		return accountDetailService.findAccountDetailByPageInfo(pageInfo);
	}

	@Override
	public Long countAccountDetailByPageInfo(AccountPageInfo pageInfo) {
		return accountDetailService.countAccountDetailByPageInfo(pageInfo);
	}

	@Override
	public Map<String, Long> getRowAndMax(AccountPageInfo pageInfo) {
		return accountDetailService.getRowAndMax(pageInfo);
	}

	@Override
	public List<AccountDetailDto> findAccountDetailExport(
			AccountPageInfo pageInfo) {
		return accountDetailService.findAccountDetailExport(pageInfo);
	}

	@Override
	public int update(AccountDetailDto accountDetailDO) {
		return accountDetailService.update(accountDetailDO);
	}

	@Override
	public Long getSumByTypeAndDay(AccountChangeParam param) {
		return accountDetailService.getSumByTypeAndDay(param);
	}

	@Override
	public List<AccountDetailTotalDto> getSumByIdsAndDate(List<Long> developIds, Date startdate, Date endDate) {
		return accountDetailService.getSumByIdsAndDate(developIds,startdate,endDate);
	}

	@Override
	public List<AccountDetailDto> getDetailByOrderIds(List<Long> orderIds, Long appId) {
		if (CollectionUtils.isEmpty(orderIds) || Objects.isNull(appId)) {
			return Lists.newArrayList();
		}
		return accountDetailService.getDetailByOrderIds(orderIds, appId);
	}
}
