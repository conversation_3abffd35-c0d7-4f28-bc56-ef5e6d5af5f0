package cn.com.duiba.paycenter.remoteservice.impl.equity;

import cn.com.duiba.paycenter.dto.equity.request.BaseEquityRequest;
import cn.com.duiba.paycenter.dto.equity.request.jigao.EquityJiGaoWetPayMktTransRequest;
import cn.com.duiba.paycenter.dto.equity.response.BaseEquityResultResponse;
import cn.com.duiba.paycenter.dto.equity.response.EquityResponse;
import cn.com.duiba.paycenter.dto.equity.response.jigao.EquityJiGaoWetPayMktTransResponse;
import cn.com.duiba.paycenter.handler.equity.impl.EquityJiGaoWetPayMktTransHandler;
import cn.com.duiba.paycenter.remoteservice.equity.RemoteEquityJiGaoWetPayMktTransService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/6/20 5:45 PM
 */
@RestController
public class RemoteEquityJiGaoWetPayMktTransServiceImpl implements RemoteEquityJiGaoWetPayMktTransService {
    
    @Resource
    private EquityJiGaoWetPayMktTransHandler equityJiGaoWetPayMktTransHandler;

    @Override
    public EquityResponse distribute(BaseEquityRequest<EquityJiGaoWetPayMktTransRequest> request) {
        return equityJiGaoWetPayMktTransHandler.distribute(request);
    }

    @Override
    public boolean notify(String body) {
        return equityJiGaoWetPayMktTransHandler.notify(body);
    }

    @Override
    public BaseEquityResultResponse<EquityJiGaoWetPayMktTransResponse> distributeResult(Integer bizType, String bizNo) {
        return equityJiGaoWetPayMktTransHandler.distributeResult(bizType, bizNo);
    }
}
