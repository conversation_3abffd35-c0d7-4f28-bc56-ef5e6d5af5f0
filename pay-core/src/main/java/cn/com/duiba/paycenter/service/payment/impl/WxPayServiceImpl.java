package cn.com.duiba.paycenter.service.payment.impl;

import cn.com.duiba.api.bo.reqresult.Result;
import cn.com.duiba.api.enums.SubjectTypeEnum;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.config.WxPayConstant;
import cn.com.duiba.paycenter.config.wxlite.WxLiteAPIV3Registry;
import cn.com.duiba.paycenter.dto.payment.charge.WxCouponResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.coupon.WxCouponQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.coupon.WxCouponQueryResp;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.coupon.WxJsSessionResp;
import cn.com.duiba.paycenter.dto.payment.config.WxPayConfigDto;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponNotifyUrlSetResponse;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponUserFlowDetail;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponUserFlowResponse;
import cn.com.duiba.paycenter.entity.payment.wxpay.notify.WxPayOrderNotifyResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayAuthcode2OpenidRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayCloseOrderRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayOrderQueryRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayRedPacketQueryXmlRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayRedPacketSendXmlRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayRefundRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayUnifiedOrderCouponRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayUnifiedOrderRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxTransferPayQueryXmlRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxTransferPayXmlRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.BaseWxPayResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxCouponDetailResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPayAuthcode2OpenidResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPayCloseOrderResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPayOrderQueryResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPayRedPacketQueryXmlResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPayRedPacketSendXmlResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPayRefundResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPayUnifiedOrderResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPlatformCert;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxTransferPayQueryXmlResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxTransferPaySendXmlResult;
import cn.com.duiba.paycenter.mq.RocketMqTopic;
import cn.com.duiba.paycenter.service.payment.WxPayService;
import cn.com.duiba.paycenter.service.payment.impl.unionpay.acp.sdk.SecureUtil;
import cn.com.duiba.paycenter.util.RedisKeyFactory;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.redis.RedisLock;
import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.CreateRequest;
import com.wechat.pay.java.service.refund.model.Refund;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

import static cn.com.duiba.paycenter.config.WxPayConstant.*;


/**
 * <AUTHOR>
 * @date 2018/11/16
 */
@Service
public class WxPayServiceImpl extends AbstractWxPayServiceImpl implements WxPayService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WxPayServiceImpl.class);

    @Autowired
    private ExecutorService executorService;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, List<WxCouponUserFlowDetail>> listRedisTemplate;
    @Autowired
    private DefaultMQProducer producer;
    @Autowired
    private RocketMqTopic rocketMqTopic;
    @Resource
    private WxLiteAPIV3Registry wxLiteApiV3Registry;
    @Resource(name = "redisTemplate")
    private RedisAtomicClient redisAtomicClient;

    @Override
    public WxPayCloseOrderResult closeOrder(WxPayCloseOrderRequest request, String apiKey) throws BizException {
        request.checkConstraintsAndSetSign(request.getSignType(), apiKey);
        String response = this.sendPostRequestWithOutCert(getPayBaseUrl() + CLOSE_ORDER_URL, request.toXML());
        WxPayCloseOrderResult result = BaseWxPayResult.fromXML(response, WxPayCloseOrderResult.class);
        checkResult(result, request.getSignType(), apiKey);
        return result;
    }

    @Override
    public WxPayUnifiedOrderResult unifiedOrder(WxPayUnifiedOrderRequest request, String apiKey) throws BizException {
        request.checkConstraintsAndSetSign(request.getSignType(), apiKey);
        String response = this.sendPostRequestWithOutCert(getPayBaseUrl() + UNIFIED_ORDER_URL, request.toXML());
        WxPayUnifiedOrderResult result = BaseWxPayResult.fromXML(response, WxPayUnifiedOrderResult.class);
        checkResult(result, request.getSignType(), apiKey);
        return result;
    }

    @Override
    public String authcode2Openid(WxPayAuthcode2OpenidRequest request, String apiKey) throws BizException {
        request.checkConstraintsAndSetSign(request.getSignType(), apiKey);

        String response = this.sendPostRequestWithOutCert(getPayBaseUrl() + AUTHCODE2OPENID_URL, request.toXML());

        WxPayAuthcode2OpenidResult result = BaseWxPayResult.fromXML(response, WxPayAuthcode2OpenidResult.class);
        checkResult(result, request.getSignType(), apiKey);
        return result.getOpenid();
    }

    @Override
    public WxPayOrderQueryResult queryOrder(WxPayOrderQueryRequest request, String apikey) throws BizException {
        request.checkConstraintsAndSetSign(request.getSignType(), apikey);
        String response = this.sendPostRequestWithOutCert(getPayBaseUrl() + ORDERQUERY_URL, request.toXML());
        WxPayOrderQueryResult result = BaseWxPayResult.fromXML(response, WxPayOrderQueryResult.class);
        checkResult(result, request.getSignType(), apikey);

        return result;
    }

    @Override
    public WxPayOrderNotifyResult parseOrderNotifyResult(String xmlData) throws BizException {
        return BaseWxPayResult.fromXML(xmlData, WxPayOrderNotifyResult.class);
    }

    @Override
    public WxPayRefundResult refund(WxPayRefundRequest wxPayRefundRequest, String apiKey, String apiCert) throws BizException {
        wxPayRefundRequest.checkConstraintsAndSetSign(wxPayRefundRequest.getSignType(), apiKey);
        String response = apiCert == null ?
                this.sendPostRequestWithDuibaCert(getPayBaseUrl() + REFUND_URL, wxPayRefundRequest.toXML()) :
                this.senPostRequest(getPayBaseUrl() + REFUND_URL, wxPayRefundRequest.toXML(), apiCert, wxPayRefundRequest.getMchId());
        WxPayRefundResult result = WxPayRefundResult.fromXML(response, WxPayRefundResult.class);
        checkResult(result, wxPayRefundRequest.getSignType(), apiKey);
        return result;
    }

    @Override
    public WxPayRefundResult refundBySubjectType(WxPayRefundRequest wxPayRefundRequest, String apiKey, String apiCert, String subjectType) throws BizException {
        wxPayRefundRequest.checkConstraintsAndSetSign(wxPayRefundRequest.getSignType(), apiKey);
        String response = apiCert == null ?
                this.sendPostRequestBySubjectType(getPayBaseUrl() + REFUND_URL, wxPayRefundRequest.toXML(), subjectType) :
                this.senPostRequest(getPayBaseUrl() + REFUND_URL, wxPayRefundRequest.toXML(), apiCert, wxPayRefundRequest.getMchId());
        WxPayRefundResult result = WxPayRefundResult.fromXML(response, WxPayRefundResult.class);
        checkResult(result, wxPayRefundRequest.getSignType(), apiKey);
        return result;
    }

    @Override
    public Refund refundBySubjectTypeByAPIV3(CreateRequest createRequest) throws BizException {
        RefundService refundV3Service = wxLiteApiV3Registry.getWxLiteApiV3ConfigHandler(SubjectTypeEnum.DUIA.getType()).acquireWxLiteRefundService();
        return refundV3Service.create(createRequest);
    }

    @Override
    public WxPayRedPacketSendXmlResult sendWxRedPacket(WxPayRedPacketSendXmlRequest request, String apiKey, String apiCert, String subjectType) throws BizException {
        request.checkConstraintsAndSetSign(request.getSignType(), apiKey);
        String response = apiCert == null ?
                this.sendPostRequestBySubjectType(getPayBaseUrl() + SEND_RED_PACKET_URL, request.toXML(), subjectType) :
                this.senPostRequest(getPayBaseUrl() + SEND_RED_PACKET_URL, request.toXML(), apiCert, request.getMchId());
        WxPayRedPacketSendXmlResult result = WxPayRedPacketSendXmlResult.fromXML(response, WxPayRedPacketSendXmlResult.class);
        checkResult4WxRedpacket(result);
        return result;
    }

    @Override
    public WxPayRedPacketQueryXmlResult queryWxRedPacketInfo(WxPayRedPacketQueryXmlRequest request, String apiKey, String apiCert, String subjectType) throws BizException {
        request.checkConstraintsAndSetSign(request.getSignType(), apiKey);
        String response = apiCert == null ?
                this.sendPostRequestBySubjectType(getPayBaseUrl() + QUERY_RED_PACKET_URL, request.toXML(), subjectType) :
                this.senPostRequest(getPayBaseUrl() + QUERY_RED_PACKET_URL, request.toXML(), apiCert, request.getMchId());
        WxPayRedPacketQueryXmlResult result = WxPayRedPacketQueryXmlResult.fromXML(response, WxPayRedPacketQueryXmlResult.class);
        checkResult4WxRedpacket(result);
        return result;
    }

    @Override
    public WxCouponResponse sendWxCoupon(WxPayConfigDto wxPayConfigDto, String body, String openId) throws BizException {
        JSONObject jsonObject = JSONObject.parseObject(body);
        String outRequestNo = jsonObject.getString("out_request_no");
        try (RedisLock lock = redisAtomicClient.getLock(RedisKeyFactory.K117.join(outRequestNo), 5)){
            if(lock == null) {
                LOGGER.warn("发放优惠券获取锁失败, wxPayConfigDto={}, body={}, openId={}", JSON.toJSONString(wxPayConfigDto), body, openId);
                throw new BizException("微信发送优惠券请求失败");
            }
            String response = this.sendWxCouponPostRequestWithCert(openId, body, wxPayConfigDto);
            WxCouponResponse wxCouponResponse = JSON.parseObject(response, WxCouponResponse.class);
            wxCouponResponse.setSuccess(StringUtils.isBlank(wxCouponResponse.getCode()) && StringUtils.isNotBlank(wxCouponResponse.getCouponId()));
            return wxCouponResponse;
        } catch (BizException e) {
            LOGGER.warn("发放优惠券失败, wxPayConfigDto={}, body={}, openId={}", JSON.toJSONString(wxPayConfigDto), body, openId);
            throw e;
        } catch (Exception e) {
            LOGGER.warn("发放优惠券失败, wxPayConfigDto={}, body={}, openId={}", JSON.toJSONString(wxPayConfigDto), body, openId);
            throw new BizException("微信发送优惠券请求失败");
        }
    }

    @Override
    public WxTransferPaySendXmlResult sendWxTransfer(WxTransferPayXmlRequest request, String apiKey, String apiCert, String subjectType) throws BizException {
        request.checkConstraintsAndSetSign(request.getSignType(), apiKey);
        String response = apiCert == null ?
                this.sendPostRequestBySubjectType(getPayBaseUrl() + SEND_TRANSFER_URL, request.toXML(), subjectType) :
                this.senPostRequest(getPayBaseUrl() + SEND_TRANSFER_URL, request.toXML(), apiCert, request.getMchId());
        WxTransferPaySendXmlResult result = WxTransferPaySendXmlResult.fromXML(response, WxTransferPaySendXmlResult.class);
        String errMsg = result.checkTransferResult(request.getSignType(), apiKey);
        if(StringUtils.isNotBlank(errMsg)){
            if(errMsg.length() > 255){
                errMsg = errMsg.substring(0, 255);
            }
            result.setErrCodeDes(errMsg);
        }
        return result;
    }

    @Override
    public WxTransferPayQueryXmlResult queryWxTransfer(WxTransferPayQueryXmlRequest request, String apiKey, String apiCert, String subjectType) throws BizException {
        request.checkConstraintsAndSetSign(request.getSignType(), apiKey);
        String response = apiCert == null ?
                this.sendPostRequestBySubjectType(getPayBaseUrl() + QUERY_TRANSFER_URL, request.toXML(), subjectType) :
                this.senPostRequest(getPayBaseUrl() + QUERY_TRANSFER_URL, request.toXML(), apiCert, request.getMchId());
        WxTransferPayQueryXmlResult result = WxTransferPayQueryXmlResult.fromXML(response, WxTransferPayQueryXmlResult.class);
        String errMsg = result.checkTransferResult(request.getSignType(), apiKey);
        if(StringUtils.isNotBlank(errMsg)){
            if(errMsg.length() > 255){
                errMsg = errMsg.substring(0, 255);
            }
            result.setErrCodeDes(errMsg);
        }
        return result;
    }

    @Override
    public WxPayUnifiedOrderResult unifiedOrderDuibaLive(WxPayUnifiedOrderCouponRequest request, String apiKey) throws BizException {
        request.checkConstraintsAndSetSign(request.getSignType(), apiKey);
        String response = this.sendPostRequestWithOutCert(getPayBaseUrl() + UNIFIED_ORDER_URL, request.toXML());
        WxPayUnifiedOrderResult result = BaseWxPayResult.fromXML(response, WxPayUnifiedOrderResult.class);
        checkResult(result, request.getSignType(), apiKey);
        return result;
    }

    @Override
    public WxCouponDetailResult findWxCouponDetail(String wxAppId, String couponId, String openId, WxPayConfigDto wxPayConfigDto) throws BizException {
        String response = null;
        try {
            response = sendGetRequestNew(getPayBaseUrl() + WxPayConstant.QUERY_WXCOUPON_URL + openId + "/coupons/" + couponId + "?appid=" + wxAppId, wxPayConfigDto);
        } catch (NoSuchAlgorithmException | InvalidKeyException | SignatureException e) {
            LOGGER.info("查询微信立减金状态异常,wxAppId={},couponId={},openId={}",wxAppId, couponId, openId, e);
            throw new BizException("网络异常请稍后再试");
        }
        return JSONObject.parseObject(response, WxCouponDetailResult.class);
    }

    @Override
    public List<WxPlatformCert> getWxPlatformCert(WxPayConfigDto wxPayConfigDto) {
        String response;
        try {
            response = sendGetRequestNew(getPayBaseUrl() + WxPayConstant.PLATFORM_CERT_URL, wxPayConfigDto);
            LOGGER.info("成功获取微信平台证书原始信息=【{}】",JSONObject.toJSONString(response));
        } catch (Exception e) {
            LOGGER.info("获取微信平台证书异常", e);
            return null;
        }
        // 获取返回的所有证书
        return JSONObject.parseObject(response).getJSONArray("data").toJavaList(WxPlatformCert.class);
    }

    @Override
    public WxCouponNotifyUrlSetResponse setNotifyUrl(String notifyUrl, WxPayConfigDto wxPayConfigDto) throws BizException {
        return this.setNotifyUrlWithCert(notifyUrl,wxPayConfigDto);
    }

    @Override
    public WxCouponQueryResp getWechatStocksInfo(WxCouponQueryRequest couponQueryRequest, WxPayConfigDto wxPayConfigDto) {
        String response;
        try {
            String mchId = StringUtils.isBlank(couponQueryRequest.getCreateMchId()) ? wxPayConfigDto.getMchId() : couponQueryRequest.getCreateMchId();
            response = sendGetRequestNew(getPayBaseUrl() + WxPayConstant.FIND_STOCKS + couponQueryRequest.getStockId()
                    + "/?stock_creator_mchid=" + mchId, wxPayConfigDto);
            LOGGER.info("成功查询微信立减金批次=【{}】",JSONObject.toJSONString(response));
        } catch (Exception e) {
            LOGGER.info("获取微信立减金批次异常", e);
            return null;
        }
        return JSONObject.parseObject(response,WxCouponQueryResp.class);
    }

    @Override
    public WxJsSessionResp authApplets(String authCode, WxPayConfigDto wxPayConfigDto) {
        String response;
        try {
            response = sendGetRequest("https://api.weixin.qq.com/" + WxPayConstant.JSCODE_SESSION
                    + "?appid=" + wxPayConfigDto.getWxAppId()
                    + "&secret=" + wxPayConfigDto.getAppSecret()
                    + "&js_code=" + authCode + "&grant_type=authorization_code");
            LOGGER.info("微信小程序授权查询openId返回结果=【{}】",JSONObject.toJSONString(response));
        } catch (Exception e) {
            LOGGER.info("微信小程序授权查询openId异常", e);
            return null;
        }
        return JSONObject.parseObject(response,WxJsSessionResp.class);
    }

    @Override
    public void getWxCouponUserFlowDownloadUrl(String stockId, WxPayConfigDto wxPayConfigDto) throws BizException {
        String url = String.format(getPayBaseUrl() + STOCK_USER_FLOW_URL, stockId);
        LOGGER.info("微信立减金批次核销明细下载，url={}", url);
        try {
            String responseStr = sendGetRequestNew(url, wxPayConfigDto);
            LOGGER.info("微信立减金批次核销明细下载，response={}", responseStr);
            WxCouponUserFlowResponse wxCouponUserFlowResponse = JSON.parseObject(responseStr, WxCouponUserFlowResponse.class);
            if (StringUtils.isBlank(wxCouponUserFlowResponse.getUrl())) {
                LOGGER.info("微信立减金批次核销明细请求异常");
                throw new BizException(wxCouponUserFlowResponse.getMessage()).withCode(wxCouponUserFlowResponse.getCode());
            }
            // 异步处理，到activity-custom-center处理，数据落库
            executorService.submit(() -> dealText(stockId, wxPayConfigDto, wxCouponUserFlowResponse));
        } catch (Exception e) {
            LOGGER.warn("微信立减金批次核销明细下载异常，stockId={}，mchId={}", stockId, wxPayConfigDto.getMchId(), e);
            throw new BizException(e.getMessage());
        }
    }

    private void dealText(String stockId, WxPayConfigDto wxPayConfigDto, WxCouponUserFlowResponse wxCouponUserFlowResponse) {
        try {
            String text = sendGetRequestNew(wxCouponUserFlowResponse.getUrl(), wxPayConfigDto);
            String hash = Hex.encodeHexString(Objects.requireNonNull(SecureUtil.sha1(text, "UTF-8")));
            LOGGER.info("微信立减金批次核销明细文本，duibaHash={}，wxHash={}", hash, wxCouponUserFlowResponse.getHashValue());
            if (!StringUtils.equals(hash, wxCouponUserFlowResponse.getHashValue())) {
                LOGGER.warn("微信立减金批次核销明细下载异常，stockId={}，mchId={}", stockId, wxPayConfigDto.getMchId());
                throw new BizException("微信立减金批次核销明细文本hash不一致");
            }
            List<String> resultList = Lists.newArrayList(Arrays.asList(text.split("\n")));
            if (CollectionUtils.isEmpty(resultList) || resultList.size() < 3) {
                LOGGER.info("微信立减金批次核销明细-暂无核销记录，stockId={}，mchId={}", stockId, wxPayConfigDto.getMchId());
                return;
            }
            // 删除表头
            resultList.remove(0);
            List<WxCouponUserFlowDetail> detailList = transformUserFlowDetailList(resultList);
            List<List<WxCouponUserFlowDetail>> partition = Lists.partition(detailList, 5000);
            for (int i = 0; i < partition.size(); i++) {
                String key = RedisKeyFactory.K102.toString() + stockId + "_" + i;
                listRedisTemplate.opsForValue().set(key, partition.get(i), 3, TimeUnit.HOURS);
                Message message = new Message(rocketMqTopic.getWxCouponUseFlowDataTopic(), "wxCouponUseFlowTag", key.getBytes());
                SendResult sendResult = producer.send(message);
                if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
                    LOGGER.error("微信立减金核销明细MQ消息发送失败,stockId={},index={}", stockId, i);
                }
                LOGGER.info("微信立减金核销明细redisKey,stockId={},index={}, redisKey={}", stockId, i, key);
                Thread.sleep(1000);
            }
        } catch (Exception e) {
            LOGGER.warn("微信立减金批次核销明细下载异常", e);
        }
    }

    private List<WxCouponUserFlowDetail> transformUserFlowDetailList(List<String> resultList) throws IllegalAccessException {
        List<WxCouponUserFlowDetail> result = Lists.newArrayList();
        for (String s : resultList) {
            // 去除文本中的`"等符号
            s = s.replace("`","").replace("\"","").replace("\r","");
            String[] fields = s.split(",");
            if (fields.length < 3) {
                continue;
            }
            WxCouponUserFlowDetail detail = transformUserFlowDetail(fields);
            if (detail != null) {
                result.add(detail);
            }
        }
        return result;
    }

    private static WxCouponUserFlowDetail transformUserFlowDetail(String[] fields) {
        try {
            WxCouponUserFlowDetail detail = new WxCouponUserFlowDetail();
            Field[] fields1 = WxCouponUserFlowDetail.class.getDeclaredFields();
            for (int i = 0; i < fields.length; i++) {
                if (i >= fields1.length) {
                    break;
                }
                Field field = fields1[i + 1];
                field.setAccessible(true);
                if (StringUtils.equals("java.util.Date", field.getGenericType().getTypeName())) {
                    field.set(detail, DateUtils.getSecondDate(fields[i]));
                } else {
                    field.set(detail, fields[i]);
                }
            }
            return detail;
        } catch (Exception e) {
            LOGGER.warn("微信立减金核销文本转换异常，text={}", JSON.toJSONString(fields) ,e);
        }
        return null;
    }

    private <T extends BaseWxPayResult> void checkResult(T result, String signType, String signkey) throws BizException {
        Result checkResult = result.checkResult(signType, signkey);

        if (!checkResult.getSuccess()) {
            throw new BizException(checkResult.getDesc());
        }
    }

    /**
     * 检查响应结果（针对微信现金红包）
     *
     * @param result 结果
     * @throws BizException 业务异常
     */
    private <T extends BaseWxPayResult> void checkResult4WxRedpacket(T result) throws BizException {
        Result checkResult = result.checkResultWithoutSign();
        if (!checkResult.getSuccess()) {
            throw new BizException(checkResult.getDesc());
        }
    }
}
