package cn.com.duiba.paycenter.remoteservice.impl.duibaaccount;

import cn.com.duiba.paycenter.dto.duibaaccount.DuibaAccountDeltaDetailDto;
import cn.com.duiba.paycenter.remoteservice.duibaaccount.RemoteDeltaDetailService;
import cn.com.duiba.paycenter.service.duibaaccount.DeltaDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/21
 */

/**
 * 兑吧余额
 */
@RestController
public class RemoteDeltaDetailServiceImpl implements RemoteDeltaDetailService {
    @Autowired
    private DeltaDetailService deltaDetailService;

    @Override
    public List<DuibaAccountDeltaDetailDto> findPageList(Date startTime, Date endTime, String operationType, int max, int offset) {
        return deltaDetailService.findPageList(startTime,endTime,operationType,max,offset);
    }

    @Override
    public Long findPageCount(Date startTime, Date endTime, String operationType) {
        return deltaDetailService.findPageCount(startTime,endTime,operationType);
    }
}
