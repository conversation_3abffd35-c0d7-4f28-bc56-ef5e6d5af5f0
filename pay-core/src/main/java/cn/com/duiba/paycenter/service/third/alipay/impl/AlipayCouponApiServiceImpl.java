package cn.com.duiba.paycenter.service.third.alipay.impl;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.service.third.alipay.AlipayBaseApiService;
import cn.com.duiba.paycenter.service.third.alipay.AlipayCouponApiService;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.request.AlipayUserDtbankcustChannelvoucherSendRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.response.AlipayUserDtbankcustChannelvoucherSendResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/4/21 10:54 AM
 */
@Slf4j
@Service
public class AlipayCouponApiServiceImpl implements AlipayCouponApiService {

    @Resource
    private AlipayBaseApiService alipayBaseApiService;

    @Override
    public AlipaySystemOauthTokenResponse certificateExecute(String appId, AlipaySystemOauthTokenRequest request) throws BizException {
        return alipayBaseApiService.certificateExecute(appId, request);
    }

    @Override
    public AlipayUserDtbankcustChannelvoucherSendResponse certificateExecute(String appId, AlipayUserDtbankcustChannelvoucherSendRequest request) throws BizException {
        return alipayBaseApiService.certificateExecute(appId, request);
    }
}
