package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.biz.SubAccountPayOrdersBiz;
import cn.com.duiba.paycenter.dao.SubAccountDao;
import cn.com.duiba.paycenter.dto.SubAccountDto;
import cn.com.duiba.paycenter.entity.credits.SubAccountEntity;
import cn.com.duiba.paycenter.remoteservice.RemoteSubAccountService;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description
 * @Date 2023/2/20 14:50
 * <AUTHOR>
 */
@RestController
public class RemoteSubAccountServiceImpl implements RemoteSubAccountService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RemoteSubAccountServiceImpl.class);

    @Resource
    private SubAccountPayOrdersBiz subAccountPayOrdersBiz;
    @Resource
    private SubAccountDao subAccountDao;

    @Override
    public SubAccountDto findById(Long id) {
        return subAccountPayOrdersBiz.findById(id);
    }

    @Override
    public List<SubAccountDto> findByDeveloperId(Long developerId) {
        return BeanUtils.copyList(subAccountDao.findByDeveloperId(developerId), SubAccountDto.class);
    }

    @Override
    public SubAccountDto findByDeveloperIdAndAccountIdentity(Long developerId, String accountIdentity) {
        return BeanUtils.copy(subAccountDao.findByIdentity(developerId, accountIdentity), SubAccountDto.class);
    }

    @Override
    public List<SubAccountDto> findByDeveloperIds(List<Long> developerIds) {
        return BeanUtils.copyList(subAccountDao.findByDeveloperIds(developerIds), SubAccountDto.class);
    }

    @Override
    public Boolean batchInsert(List<SubAccountDto> subAccountDtoList) {
        return subAccountDao.batchInsert(BeanUtils.copyList(subAccountDtoList, SubAccountEntity.class));
    }
}
