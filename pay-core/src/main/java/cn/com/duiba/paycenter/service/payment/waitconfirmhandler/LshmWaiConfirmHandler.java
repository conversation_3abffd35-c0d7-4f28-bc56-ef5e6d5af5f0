package cn.com.duiba.paycenter.service.payment.waitconfirmhandler;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.utils.SpringEnvironmentUtils;
import cn.com.duiba.paycenter.config.lshm.LshmPayConfig;
import cn.com.duiba.paycenter.dao.payment.ChargeOrderDao;
import cn.com.duiba.paycenter.dao.payment.RefundOrderDao;
import cn.com.duiba.paycenter.dto.payment.WaitConfirmChargeOrderDto;
import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.DevChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.enums.LshmPayStatus;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.enums.LshmRefundStatus;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmPayQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.request.LshmRefundQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.response.LshmPayQueryResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.response.LshmRefundQueryResponse;
import cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity;
import cn.com.duiba.paycenter.entity.payment.RefundOrderEntity;
import cn.com.duiba.paycenter.enums.ChannelEnum;
import cn.com.duiba.paycenter.enums.ChargeOrderStatusEnum;
import cn.com.duiba.paycenter.enums.RefundOrderStatusEnum;
import cn.com.duiba.paycenter.service.payment.impl.lshm.LshmPayService;
import cn.com.duiba.paycenter.util.RedisKeyFactory;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.redis.RedisLock;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class LshmWaiConfirmHandler implements WaitConfirmChargeOrderQueryHandler {

    @Resource(name = "redisTemplate")
    private RedisAtomicClient redisAtomicClient;

    @Resource
    private ChargeOrderDao chargeOrderDao;

    @Resource
    private RefundOrderDao refundOrderDao;

    @Resource
    private LshmPayService lshmPayService;

    @Resource
    private LshmPayConfig lshmPayConfig;

    @Override
    @SuppressWarnings("unchecked")
    public <T extends BaseChargeNotifyResponse> Boolean queryWaitConfirmChargeOrder(WaitConfirmChargeOrderDto waitConfirmChargeOrderDto, List<T> baseChargeResponses) {
        String key = RedisKeyFactory.K121 + waitConfirmChargeOrderDto.getOrderNo();
        try (RedisLock lock = redisAtomicClient.getLock(key, 10)) {
            if (Objects.isNull(lock)) {
                return false;
            }
            // 预校验
            if (!chargeOrderPreValid(waitConfirmChargeOrderDto)) {
                return true;
            }
            ChargeOrderEntity chargeOrder = chargeOrderDao.findByOrderNo(waitConfirmChargeOrderDto.getOrderNo());
            Long appId = waitConfirmChargeOrderDto.getAppId();
            LshmPayQueryRequest lshmPayQueryRequest = buildPayQueryRequest(waitConfirmChargeOrderDto, chargeOrder);

            LshmPayQueryResponse lshmPayQueryResponse = lshmPayService.payQuery(appId, lshmPayQueryRequest);
            if (lshmPayQueryResponse == null) {
                return false;
            }
            // 支付只关心成功的查询结果
            if (LshmPayStatus.SUCCESS.getStatus().equals(lshmPayQueryResponse.getPayStatus())) {
                handlerChargeNotify(waitConfirmChargeOrderDto,lshmPayQueryResponse);
                DevChargeNotifyResponse resp = buildChargeNotifyResp(waitConfirmChargeOrderDto, lshmPayQueryResponse);
                baseChargeResponses.add((T) resp);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("零食很忙 支付主动查询 异常 waitConfirmChargeOrderDto={}",JSON.toJSONString(waitConfirmChargeOrderDto), e);
            return false;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T extends BaseChargeNotifyResponse> Boolean querRefundConfirmChargeOrder(WaitConfirmChargeOrderDto waitConfirmChargeOrderDto, List<T> baseChargeResponses) {
        String key = RedisKeyFactory.K120 + waitConfirmChargeOrderDto.getOrderNo();
        try (RedisLock lock = redisAtomicClient.getLock(key, 10)) {
            if (Objects.isNull(lock)) {
                return false;
            }
            RefundOrderEntity refundOrder = refundOrderDao.findByRefundOrderNo(waitConfirmChargeOrderDto.getOrderNo());
            // 预校验
            if (!refundOrderPreValid(waitConfirmChargeOrderDto, refundOrder)) {
                return true;
            }


            ChargeOrderEntity chargeOrder = chargeOrderDao.findByOrderNo(refundOrder.getChargeOrderNo());
            Long appId = waitConfirmChargeOrderDto.getAppId();
            LshmRefundQueryRequest queryRequest = buildRefundQueryRequest(waitConfirmChargeOrderDto, chargeOrder);
            LshmRefundQueryResponse lshmRefundQueryResponse = lshmPayService.refundQuery(appId, queryRequest);
            if (lshmRefundQueryResponse == null) {
                return false;
            }

            // 查询到终态才处理
            if (LshmRefundStatus.SUCCESS.getStatus().equals(lshmRefundQueryResponse.getRefundStatus()) || LshmRefundStatus.FAIL.getStatus().equals(lshmRefundQueryResponse.getRefundStatus())) {
                handlerRefundQuery(waitConfirmChargeOrderDto,lshmRefundQueryResponse);
                DevChargeNotifyResponse resp = buildRefundNotifyResp(waitConfirmChargeOrderDto);
                baseChargeResponses.add((T) resp);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("零食很忙 退款主动查询 异常 waitConfirmChargeOrderDto={}", JSON.toJSONString(waitConfirmChargeOrderDto), e);
            return false;
        }
    }

    /**
     * 构建支付回调响应
     */
    private DevChargeNotifyResponse buildChargeNotifyResp(WaitConfirmChargeOrderDto waitConfirmChargeOrderDto, LshmPayQueryResponse lshmPayQueryResponse) {
        DevChargeNotifyResponse resp = new DevChargeNotifyResponse();
        resp.setSuccess(true);
        resp.setChargeSuccess(true);
        resp.setChargeOrderNo(waitConfirmChargeOrderDto.getOrderNo());
        resp.setTransactionNo(lshmPayQueryResponse.getTradeNo());
        resp.setBizType(waitConfirmChargeOrderDto.getBizType());
        resp.setBizOrderNo(waitConfirmChargeOrderDto.getBizOrderNo());
        resp.setAppId(waitConfirmChargeOrderDto.getAppId());
        resp.setOrderType(waitConfirmChargeOrderDto.getOrderType());
        return resp;
    }

    /**
     * 构建退款回调响应
     */
    private DevChargeNotifyResponse buildRefundNotifyResp(WaitConfirmChargeOrderDto waitConfirmChargeOrderDto) {
        DevChargeNotifyResponse resp = new DevChargeNotifyResponse();
        resp.setSuccess(true);
        resp.setRefundSuccess(true);
        resp.setBizType(waitConfirmChargeOrderDto.getBizType());
        resp.setBizOrderNo(waitConfirmChargeOrderDto.getBizOrderNo());
        resp.setAppId(waitConfirmChargeOrderDto.getAppId());
        resp.setOrderType(waitConfirmChargeOrderDto.getOrderType());
        return resp;
    }

    /**
     * 支付回调处理
     */
    private void handlerChargeNotify(WaitConfirmChargeOrderDto waitConfirmChargeOrderDto,LshmPayQueryResponse queryResponse) throws BizException {
        try {
            // 找到支付单记录
            ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByOrderNo(waitConfirmChargeOrderDto.getOrderNo());
            if (Objects.isNull(chargeOrderEntity)) {
                log.warn("零食很忙 支付回调 支付单为空 queryResponse={}", JSON.toJSONString(queryResponse));
                return ;
            }

            // 防止重复处理
            if (chargeOrderEntity.getChargeStatus() > ChargeOrderStatusEnum.PAY_FAIL.getCode()) {
                return;
            }

            // 支付状态
            boolean isPaySuccess = LshmPayStatus.SUCCESS.getStatus().equals(queryResponse.getPayStatus());

            // 更新记录
            updateChargeOrder(queryResponse, chargeOrderEntity.getOrderNo(), isPaySuccess);
        } catch (Exception e) {
            log.error("零食很忙 支付回调 queryResponse:{}", JSON.toJSONString(queryResponse), e);
            throw new BizException("系统繁忙请稍后再试");
        }
    }

    /**
     * 退款回调处理
     */
    private void handlerRefundQuery(WaitConfirmChargeOrderDto waitConfirmChargeOrderDto,LshmRefundQueryResponse response) throws BizException {
        try {
            // 找到退款单
            RefundOrderEntity refundOrderEntity = refundOrderDao.findByRefundOrderNo(waitConfirmChargeOrderDto.getOrderNo());
            if (refundOrderEntity == null) {
                log.warn("零食很忙 退款回调 退款单为空 response={}", JSON.toJSONString(response));
                return;
            }

            // 防止重复处理
            if (RefundOrderStatusEnum.SUCCEEDED.getCode().equals(refundOrderEntity.getRefundStatus()) || RefundOrderStatusEnum.FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
                return;
            }

            // 更新退款单
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            // 退款成功
            if (LshmRefundStatus.SUCCESS.getStatus().equals(response.getRefundStatus())) {
                entity.setRefundStatus(RefundOrderStatusEnum.SUCCEEDED.getCode());
                if (response.getRefundAmount() != null) {
                    entity.setRefundAmount(response.getRefundAmount().intValue());
                }
                refundOrderDao.update(entity);
            }
            // 退款失败
            else if (LshmRefundStatus.FAIL.getStatus().equals(response.getRefundStatus())) {
                entity.setRefundStatus(RefundOrderStatusEnum.FAILED.getCode());
                refundOrderDao.update(entity);
            }
        } catch (Exception e) {
            log.warn("零食很忙 退款回调 notifyRequest:{}", JSON.toJSONString(response), e);
            throw new BizException("系统繁忙请稍后再试");
        }
    }

    /**
     * 更新支付单
     * @param lshmPayQueryResponse 支付通知请求
     * @param orderNo          订单号
     * @param isPaySuccess     是成功支付
     * @return boolean
     */
    private boolean updateChargeOrder(LshmPayQueryResponse lshmPayQueryResponse, String orderNo,
                                      boolean isPaySuccess) {
        ChargeOrderEntity updateEntity = new ChargeOrderEntity();
        updateEntity.setOrderNo(orderNo);
        if (isPaySuccess) {
            updateEntity.setPaidTime(new Date());
            updateEntity.setChargeStatus(ChargeOrderStatusEnum.PAY_SUCCESS.getCode());
            updateEntity.setTransactionNo(lshmPayQueryResponse.getTradeNo());
        } else {
            updateEntity.setChargeStatus(ChargeOrderStatusEnum.PAY_FAIL.getCode());
        }

        if (SpringEnvironmentUtils.isTestEnv() || SpringEnvironmentUtils.isPreEnv()) {
            log.info("零食很忙 回调 更新tb_charge_order updateEntity = {}", JSON.toJSONString(updateEntity));
        }
        return chargeOrderDao.update(updateEntity) > 0;
    }




    private LshmPayQueryRequest buildPayQueryRequest(WaitConfirmChargeOrderDto waitConfirmChargeOrderDto, ChargeOrderEntity chargeOrder) {
        LshmPayQueryRequest queryRequest = new LshmPayQueryRequest();
        String storeCode = Optional.ofNullable(chargeOrder)
                .map(ChargeOrderEntity::getExtra)
                .map(JSON::parseObject)
                .map(x -> x.getString(LshmPayConfig.STORE_CODE))
                .orElseThrow(() -> new IllegalStateException("storeCode 为空"));
        queryRequest.setMerchantNo(storeCode);
        queryRequest.setMerchantOrderNo(waitConfirmChargeOrderDto.getOrderNo());
        return queryRequest;
    }

    private LshmRefundQueryRequest buildRefundQueryRequest(WaitConfirmChargeOrderDto waitConfirmChargeOrderDto, ChargeOrderEntity chargeOrder) {
        LshmRefundQueryRequest queryRequest = new LshmRefundQueryRequest();
        String storeCode = Optional.ofNullable(chargeOrder)
                .map(ChargeOrderEntity::getExtra)
                .map(JSON::parseObject)
                .map(x -> x.getString(LshmPayConfig.STORE_CODE))
                .orElseThrow(() -> new IllegalStateException("storeCode 为空"));
        queryRequest.setMerchantNo(storeCode);
        queryRequest.setRefundNo(waitConfirmChargeOrderDto.getOrderNo());
        return queryRequest;
    }


    /**
     * 退款单预校验
     *
     * @param waitConfirmChargeOrderDto 等待确认订单预校验
     * @return true: 通过校验、false: 无法通过校验
     */
    private boolean refundOrderPreValid(WaitConfirmChargeOrderDto waitConfirmChargeOrderDto, RefundOrderEntity refundOrder) {
        String orderNo = waitConfirmChargeOrderDto.getOrderNo();

        if (Objects.isNull(refundOrder)) {
            log.error(String.format("零食很忙 主动轮询订单支付状态异常，tb_refund_order 为空，orderNo = %s", orderNo));
            return false;
        }

        // 只处理收单成功的订单
        if (!RefundOrderStatusEnum.ORDER_SUCCEEDED.getCode().equals(refundOrder.getRefundStatus())) {
            log.error(String.format("零食很忙 主动轮询订单支付状态异常，tb_refund_order 状态非收单成功，chargeOrder = %s", JSON.toJSONString(refundOrder)));
            return false;
        }
        return true;
    }

    /**
     * 支付单预校验
     *
     * @param waitConfirmChargeOrderDto 等待确认订单预校验
     * @return true: 通过校验、false: 无法通过校验
     */
    private boolean chargeOrderPreValid(WaitConfirmChargeOrderDto waitConfirmChargeOrderDto) {
        String orderNo = waitConfirmChargeOrderDto.getOrderNo();
        ChargeOrderEntity chargeOrder = chargeOrderDao.findByOrderNo(orderNo);

        if (Objects.isNull(chargeOrder)) {
            log.error(String.format("零食很忙 主动轮询订单支付状态异常，tb_charge_order为空，orderNo = %s", orderNo));
            return false;
        }

        //只处理收单成功的订单
        if (!ChargeOrderStatusEnum.ORDER_SUCCESS.getCode().equals(chargeOrder.getChargeStatus())) {
            log.error(String.format("零食很忙 主动轮询订单支付状态异常，tb_charge_order状态非收单成功，chargeOrder = %s", JSON.toJSONString(chargeOrder)));
            return false;
        }
        return true;
    }

    @PostConstruct
    public void init() {
        WaitConfirmChargeOrderQueryHandlerManager.register(ChannelEnum.LSHM_PAY.getChannelType(), this);
    }
}
