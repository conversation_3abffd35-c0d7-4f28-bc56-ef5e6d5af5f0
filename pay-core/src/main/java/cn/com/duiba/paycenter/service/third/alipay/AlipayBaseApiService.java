package cn.com.duiba.paycenter.service.third.alipay;

import cn.com.duiba.boot.exception.BizException;
import com.alipay.api.AlipayRequest;
import com.alipay.api.AlipayResponse;

/**
 * 支付宝通用api服务
 * <AUTHOR>
 * @date 2023/4/21 11:26 AM
 */
public interface AlipayBaseApiService {

    /**
     * 调用支付宝接口（公钥证书模式）
     * @param appId 支付宝应用ID
     * @param request 请求参数
     * @return 支付宝响应
     * @throws BizException 获取支付宝客户端异常
     */
    <T extends AlipayRequest<R>, R extends AlipayResponse> R certificateExecute(String appId, T request) throws BizException;
}
