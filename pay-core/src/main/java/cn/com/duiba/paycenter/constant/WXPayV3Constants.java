package cn.com.duiba.paycenter.constant;

/**
 * 微信v3接口常量
 * <AUTHOR>
 * @date 2023/1/5 2:08 下午
 */
public class WXPayV3Constants {

    private WXPayV3Constants() {}

    /**
     * Signature对象时指定的算法SHA256withRSA
     */
    public static final String SHA256WITHRSA = "SHA256withRSA";

    public static final int HEX = 16;


    /**
     * http header名称
     */
    public static final String WECHAT_PAY_TIMESTAMP = "Wechatpay-Timestamp";

    /**
     * http header名称
     */
    public static final String REQUEST_ID = "Request-ID";

    /**
     * http header名称
     */
    public static final String WECHAT_PAY_NONCE = "Wechatpay-Nonce";

    /**
     * http header 名称
     */
    public static final String WECHAT_PAY_SERIAL = "Wechatpay-Serial";

    /**
     * http header 名称
     */
    public static final String WECHAT_PAY_SIGNATURE = "Wechatpay-Signature";

    /**
     * 微信域名
     */
    public static final String SERVER_URL = "https://api.mch.weixin.qq.com";

    /**
     * 下载平台证书
     */
    public static final String DOWNLOAD_CERTIFICATE_URL = "/v3/certificates";

    /**
     * 商家转账到零钱-发起商家转账
     */
    public static final String BATCHES_URL = "/v3/transfer/batches";

    /**
     * 商家转账到零钱-通过商家批次单号查询批次单
     */
    public static final String BATCHES_QUERY_URL = "/v3/transfer/batches/out-batch-no/{out_batch_no}";

    /**
     * 商家转账到零钱-通过商家明细单号查询明细单
     */
    public static final String BATCHES_QUERY_DETAIL_URL = "/v3/transfer/batches/out-batch-no/{out_batch_no}/details/out-detail-no/{out_detail_no}";

    /**
     * 代金劵-发放代金券批次
     */
    public static final String FAVOR_USER_COUPONS_URL = "/v3/marketing/favor/users/{openid}/coupons";
    
    /**
     * 代金劵-查询代金券详情
     */
    public static final String FAVOR_USER_COUPONS_QUERY_URL = "/v3/marketing/favor/users/{openid}/coupons/{coupon_id}";

    /**
     * 代金劵-查询批次详情
     */
    public static final String FAVOR_STOCKS_URL = "/v3/marketing/favor/stocks/{stock_id}";
}
