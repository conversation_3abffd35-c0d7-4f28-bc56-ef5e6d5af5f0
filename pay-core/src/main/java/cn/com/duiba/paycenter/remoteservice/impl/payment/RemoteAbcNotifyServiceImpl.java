package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.abc.AbcChargeNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteAbcNotifyService;
import cn.com.duiba.paycenter.service.payment.AbcNotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 农行支付通知
 * @date 2019/10/125:46 下午
 */
@RestController
public class RemoteAbcNotifyServiceImpl implements RemoteAbcNotifyService {


    @Autowired
    private AbcNotifyService abcNotifyService;


    @Override
    public AbcChargeNotifyResponse orderNotify(Map<String, String> map) throws BizException {
        return  abcNotifyService.orderNotify(map);
    }
}
