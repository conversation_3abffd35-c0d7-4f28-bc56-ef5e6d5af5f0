package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.biz.DuibaPayAccountDrawBiz;
import cn.com.duiba.paycenter.constant.PayCenterErrorCode;
import cn.com.duiba.paycenter.params.DuibaPayOrdersExtraParams;
import cn.com.duiba.paycenter.result.PayOrdersResult;
import cn.com.duiba.paycenter.service.DuibaPayAccountDrawService;
import cn.com.duiba.paycenter.util.SignUtil;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * Created by hww on 2018/10/21 下午11:35.
 */
@RestController
public class RemoteDuibaPayAccountDrawServiceImpl implements DuibaPayAccountDrawService {

    @Autowired
    private DuibaPayAccountDrawBiz duibaPayAccountDrawBiz;

    @Override
    public PayOrdersResult pay(Long orderId, Long money, String sign, DuibaPayOrdersExtraParams p) {
        Map<String, String> params = Maps.newHashMap();
        params.put("orderId", orderId + "");
        params.put("money", money + "");
        String checkSign = SignUtil.sign(params);

        if (!checkSign.equals(sign)) {
            return new PayOrdersResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        return duibaPayAccountDrawBiz.payOrder(orderId, money,p);
    }

    @Override
    public PayOrdersResult backPay(Long orderId, Long money, String sign) {
        Map<String, String> params = Maps.newHashMap();
        params.put("orderId", orderId + "");
        params.put("money", money + "");
        String checkSign = SignUtil.sign(params);

        if (!checkSign.equals(sign)) {
            return new PayOrdersResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        return duibaPayAccountDrawBiz.backpayOrder(orderId, money);
    }
}
