package cn.com.duiba.paycenter.service.third.alipay.impl;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.service.equity.AlipayMchConfService;
import cn.com.duiba.paycenter.service.third.alipay.AlipayBaseApiService;
import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayRequest;
import com.alipay.api.AlipayResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/4/21 11:27 AM
 */
@Slf4j
@Service
public class AlipayBaseApiServiceImpl implements AlipayBaseApiService {

    @Resource
    private AlipayMchConfService alipayMchConfService;
    @Override
    public <T extends AlipayRequest<R>, R extends AlipayResponse> R certificateExecute(String appId, T request) throws BizException {
        long start = System.currentTimeMillis();
        // 获取支付宝客户端的耗时
        long getClientCost = 0;
        // 总耗时
        long totalCost = 0;
        // 支付宝响应
        R response = null;
        try {
            AlipayClient alipayClient = alipayMchConfService.getCertClientWithLocal(appId);
            getClientCost = System.currentTimeMillis() - start;
            if (alipayClient == null) {
                throw new BizException("获取支付宝客户端失败，appId=" + appId);
            }
            response = alipayClient.certificateExecute(request);
            totalCost = System.currentTimeMillis() - start;
            return response;
        } catch (AlipayApiException e) {
            log.error("[支付宝请求], apiMethodName={}, appId={}, getClientCost={}ms, totalCost={}ms, request={}",
                    request.getApiMethodName(), appId, getClientCost, totalCost, JSON.toJSONString(request), e);
            throw new BizException("调用支付宝失败");
        } finally {
            log.info("[支付宝请求], apiMethodName={}, appId={}, getClientCost={}ms, totalCost={}ms, request={}, response={}",
                    request.getApiMethodName(), appId, getClientCost, totalCost, JSON.toJSONString(request), JSON.toJSONString(response));
        }
    }
}
