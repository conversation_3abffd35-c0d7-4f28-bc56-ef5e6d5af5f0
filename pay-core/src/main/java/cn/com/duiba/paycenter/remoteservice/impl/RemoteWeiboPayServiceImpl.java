package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboChargeNotifyData;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboSettleData;
import cn.com.duiba.paycenter.remoteservice.RemoteWeiboPayService;
import cn.com.duiba.paycenter.service.payment.WeiboPayService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class RemoteWeiboPayServiceImpl implements RemoteWeiboPayService {

    @Resource
    private WeiboPayService weiboPayService;
    @Override
    public WeiboChargeNotifyResponse chargeAndRefundNotify(WeiboChargeNotifyData notifyRequest) throws BizException {
        return weiboPayService.chargeAndRefundNotify(notifyRequest);
    }

    @Override
    public WeiboSettleData settle(String bizOrderNo) {
        return weiboPayService.settle(bizOrderNo);
    }
}
