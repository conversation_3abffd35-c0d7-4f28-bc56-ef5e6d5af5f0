package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.bean.AppAccountChangeBean;
import cn.com.duiba.paycenter.biz.AppMarkAccountBiz;
import cn.com.duiba.paycenter.constant.PayCenterErrorCode;
import cn.com.duiba.paycenter.params.AppAccountChangeParams;
import cn.com.duiba.paycenter.remoteservice.RemoteAppMarkAccountService;
import cn.com.duiba.paycenter.result.PayCenterResult;
import cn.com.duiba.paycenter.util.SignUtil;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 开发者应用标记账户
 * author zhanghuifeng
 * date 2018/11/20-11:07 AM
 */
@RestController
public class RemoteAppMarkAccountServiceImpl implements RemoteAppMarkAccountService {

    @Resource
    private AppMarkAccountBiz appMarkAccountBiz;

    @Override
    public PayCenterResult reduceMoney(AppAccountChangeParams param, String sign) {
        String checkSign= getCheckSign(param);

        if(!checkSign.equals(sign)){
            return new PayCenterResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        AppAccountChangeBean bean = packBean(param);
        return appMarkAccountBiz.reduceMoney(bean);
    }

    @Override
    public PayCenterResult addMoney(AppAccountChangeParams param, String sign) {
        String checkSign= getCheckSign(param);
        if(!checkSign.equals(sign)){
            return new PayCenterResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        AppAccountChangeBean bean = packBean(param);
        return appMarkAccountBiz.addMoney(bean);
    }

    private AppAccountChangeBean packBean(AppAccountChangeParams param){
        AppAccountChangeBean bean = new AppAccountChangeBean();
        bean.setAppId(param.getAppId());
        bean.setDeveloperId(param.getDeveloperId());
        bean.setMemo(param.getMemo());
        bean.setMoney(param.getMoney());
        bean.setRelationType(param.getRelationType());
        bean.setRelationId(param.getRelationId());
        bean.setTransferOut(param.getTransferOut()==null?null:param.getTransferOut().getCode());
        bean.setTransferIn(param.getTransferIn()==null?null:param.getTransferIn().getCode());
        return bean;
    }

    private String getCheckSign(AppAccountChangeParams param){
        Map<String, String> params=new HashMap<>();
        params.put("developerId", Objects.toString(param.getDeveloperId()));
        params.put("relationId", param.getRelationId());
        params.put("money", Objects.toString(param.getMoney()));
        params.put("appId",Objects.toString(param.getAppId()));
        return SignUtil.sign(params);
    }
}
