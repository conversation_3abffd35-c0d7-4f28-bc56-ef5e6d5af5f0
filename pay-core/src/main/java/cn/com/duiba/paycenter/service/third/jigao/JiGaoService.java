package cn.com.duiba.paycenter.service.third.jigao;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.bean.jigao.request.JiGaoChargeRequest;
import cn.com.duiba.paycenter.bean.jigao.request.JiGaoQueryRequest;
import cn.com.duiba.paycenter.bean.jigao.request.JiGaoWetPayMktTransRequest;
import cn.com.duiba.paycenter.bean.jigao.result.JiGaoChargeResult;
import cn.com.duiba.paycenter.bean.jigao.result.JiGaoQueryResult;

/**
 * 继皋直充服务
 * <AUTHOR>
 * @date 2023/6/20 10:59 AM
 */
public interface JiGaoService {

    /**
     * 直充-企业付款到零钱
     * @param request 请求参数
     * @return 结果
     * @throws BizException 请求异常
     */
    JiGaoChargeResult chargeWetPayMktTrans(JiGaoChargeRequest<JiGaoWetPayMktTransRequest> request) throws BizException;

    /**
     * 直充-结果查询
     * @param request 请求参数
     * @return 直充结果
     * @throws BizException 请求异常
     */
    JiGaoQueryResult query(JiGaoQueryRequest request) throws BizException;

    /**
     * 校验签名
     * @param body 通知结果
     * @return 签名是否正确
     */
    boolean checkSign(String body);
}
