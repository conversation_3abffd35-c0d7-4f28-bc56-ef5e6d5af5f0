package cn.com.duiba.paycenter.dao.impl;

import cn.com.duiba.paycenter.dao.DatabaseSchema;
import cn.com.duiba.paycenter.dao.PayCenterBaseDao;
import cn.com.duiba.paycenter.dao.SubAccountDao;
import cn.com.duiba.paycenter.entity.credits.SubAccountEntity;
import cn.com.duiba.service.exception.BusinessException;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * author z<PERSON><PERSON><PERSON>
 * date 2018-12-24-11:38
 */
@Repository
public class SubAccountDaoImpl extends PayCenterBaseDao implements SubAccountDao {

    @Override
    public SubAccountEntity findByIdentity(Long developerId, String accountIdentity) {
        Map<String, Object> map = new HashMap<>();
        map.put("developerId", developerId);
        map.put("accountIdentity", accountIdentity);
        return selectOne("findByIdentity",map);
    }

    @Override
    public boolean updateByAppId(Long developerId, String accountIdentity, Long newBalance, Long oldBalance) {
        Map<String, Object> map = new HashMap<>();
        map.put("developerId", developerId);
        map.put("accountIdentity", accountIdentity);
        map.put("newBalance", newBalance);
        map.put("oldBalance", oldBalance);
        return update("findByIdentity",map) > 0;
    }

    @Override
    public SubAccountEntity findById4update(Long subAccountId) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("id", subAccountId);
        return selectOne("findById4update",paramMap);
    }

    @Override
    public SubAccountEntity findById(Long id) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("id", id);
        return selectOne("findById",paramMap);
    }


    @Override
    public Boolean reduceMoney(SubAccountEntity newrm, int money) throws BusinessException {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("id", newrm.getId());
        paramMap.put("money", money);
        int rows = update("reduceMoney", paramMap);
        if(rows==1){
            return true;
        }else if(rows < 1){
            throw new BusinessException("版本过期");
        }else{
            throw new BusinessException("修改了多行数据");
        }
    }

    @Override
    public boolean addMoney(SubAccountEntity newrm, int money) throws BusinessException {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("id", newrm.getId());
        paramMap.put("money", money);
        int rows = update("addMoney", paramMap);
        if(rows==1){
            return true;
        }else if(rows < 1){
            throw new BusinessException("版本过期");
        }else{
            throw new BusinessException("修改了多行数据");
        }
    }

    @Override
    public List<SubAccountEntity> findByDeveloperId(Long developerId) {
        return selectList("findByDeveloperId", developerId);
    }

    @Override
    public List<SubAccountEntity> findByDeveloperIds(List<Long> developerIds) {
        return selectList("findByDeveloperIds", developerIds);
    }

    @Override
    public Boolean batchInsert(List<SubAccountEntity> list) {
        int rows = insert("batchInsert", list);
        if (rows == list.size()){
            return true;
        }
        return false;
    }

    @Override
    protected DatabaseSchema chooseSchema() {
        return DatabaseSchema.CREDITS;
    }
}
