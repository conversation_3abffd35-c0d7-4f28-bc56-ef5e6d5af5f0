package cn.com.duiba.paycenter.service.payment;

import cn.com.duiba.paycenter.dto.payment.WaitConfirmChargeOrderDto;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeNotifyParams;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeNotifyResp;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeResp;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsQueryResp;
import cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity;

/**
 * <AUTHOR>
 */
public interface IcbcCreditsPayService {
    /**
     * 生成支付订单
     *
     * @param request 预支付请求
     * @return 支付订单
     */
    ChargeOrderEntity createChargeOrderEntity(IcbcCreditsChargeRequest request);


    /**
     * 调用sdk预支付
     *
     * @param request 预支付请求
     * @return 预支付返回
     */
    IcbcCreditsChargeResp doCreateCharge(IcbcCreditsChargeRequest request);


    /**
     * 主动轮询chargeOrder状态
     *
     * @param payTradeNo 支付流水号
     * @param request    预支付请求
     */
    void createWaitConfirmChargeOrder(IcbcCreditsChargeRequest request, String payTradeNo);


    /**
     * 处理预支付通知请求
     *
     * @param params 工商银行 预支付通知请求
     * @return 工商银行通知响应
     * @throws Exception -
     */
    IcbcCreditsChargeNotifyResp handleChargeNotify(IcbcCreditsChargeNotifyParams params) throws Exception;


    /**
     * 支付单更新成功或失败
     *
     * @param params          通知参数
     * @param chargeOrderNum  支付订单号
     * @param isChargeSuccess true: 支付成功
     * @return true: 更新记录成功
     */
    boolean makeChargeOrderSuccessOrFail(IcbcCreditsChargeNotifyParams params, String chargeOrderNum, boolean isChargeSuccess);


    /**
     * 通知是否被处理（防止重复处理通知）
     *
     * @param chargeOrderEntity 支付单
     * @return true: 已处理
     */
    boolean isNotifyHandled(ChargeOrderEntity chargeOrderEntity);

    /**
     * 查询支付状态
     *
     * @param waitConfirmChargeOrderDto 查询信息
     * @return 响应信息
     */
    IcbcCreditsQueryResp queryChargeStatus(WaitConfirmChargeOrderDto waitConfirmChargeOrderDto);
}
