package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.notify.NbcbNotifyRequestWrap;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.notify.NbcbNotifyRespWrap;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteNingboNotifyService;
import cn.com.duiba.paycenter.service.NingboBankPayService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
public class RemoteNingboNotifyServiceImpl implements RemoteNingboNotifyService {

    @Resource
    private NingboBankPayService ningboBankPayService;


    @Override
    public NbcbNotifyRespWrap orderNotify(NbcbNotifyRequestWrap notifyRequestWrap) throws Exception {
        return ningboBankPayService.orderNotify(notifyRequestWrap);
    }
}
