package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.shouxin.ShouxinPayChargeNotifyRequest;
import cn.com.duiba.paycenter.dto.payment.charge.shouxin.ShouxinPayChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.refund.shouxin.ShouxinPayRefundNotifyRequest;
import cn.com.duiba.paycenter.dto.payment.refund.shouxin.ShouxinPayRefundNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteShouxinPayNotifyService;
import cn.com.duiba.paycenter.service.payment.ShouxinPayNotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 * @date 2019/11/21
 */
@RestController
public class RemoteShouxinPayNotifyServiceImpl implements RemoteShouxinPayNotifyService {
    @Autowired
    private ShouxinPayNotifyService shouxinPayNotifyService;
    @Override
    public ShouxinPayChargeNotifyResponse orderNotify(ShouxinPayChargeNotifyRequest shouxinPayChargeNotifyRequest) throws BizException {
        return shouxinPayNotifyService.orderNotify(shouxinPayChargeNotifyRequest);
    }

    @Override
    public ShouxinPayRefundNotifyResponse refundNotify(ShouxinPayRefundNotifyRequest shouxinPayRefundNotifyRequest) {
        return shouxinPayNotifyService.refundNotify(shouxinPayRefundNotifyRequest);
    }
}
