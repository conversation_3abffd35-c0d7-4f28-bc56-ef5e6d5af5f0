package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.biz.PayOrdersBiz;
import cn.com.duiba.paycenter.constant.PayCenterErrorCode;
import cn.com.duiba.paycenter.model.AccountChangeRecordDO;
import cn.com.duiba.paycenter.params.PayOrdersExtraParams;
import cn.com.duiba.paycenter.result.PayOrdersResult;
import cn.com.duiba.paycenter.service.AppAccountOutInnerRecordService;
import cn.com.duiba.paycenter.service.PayOrdersService;
import cn.com.duiba.paycenter.util.SignUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 主订单付款相关接口服务
 * <AUTHOR>
 *
 */
@RestController
public class RemotePayOrdersServiceImpl implements PayOrdersService {
	
	private static Logger log=LoggerFactory.getLogger(RemotePayOrdersServiceImpl.class);
	@Autowired
	private PayOrdersBiz payOrdersBiz;
	@Autowired
	private AppAccountOutInnerRecordService accountOutInnerRecordService;

	@Override
	public PayOrdersResult payOrder(Long developerId, Long orderId, Long money,String sign,PayOrdersExtraParams p)
			 {
		log.debug(getClass().getName()+".payOrder("+developerId+","+orderId+","+money+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("orderId", orderId+"");
		params.put("money", money+"");
		String checksign=SignUtil.sign(params);
		
		if(!checksign.equals(sign)){
			return new PayOrdersResult(false, PayCenterErrorCode.CodeParamsSignError, null);
		}
		PayOrdersResult payOrdersResult = payOrdersBiz.payOrder(developerId, orderId, money, p);
		accountOutInnerRecordService.riskInnerRecord(payOrdersResult.isBizSuccess() && payOrdersResult.isCurrentSuccess(),p.getAppId(),money);
		return payOrdersResult;
	}

	@Override
	public PayOrdersResult backpayOrder(Long developerId, Long orderId,
			Long money,String sign,PayOrdersExtraParams p)  {
		log.debug(getClass().getName()+".backpayOrder("+developerId+","+orderId+","+money+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("orderId", orderId+"");
		params.put("money", money+"");
		String checksign=SignUtil.sign(params);
		
		if(!checksign.equals(sign)){
			return new PayOrdersResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		return payOrdersBiz.backpayOrder(developerId, orderId, money, p);
	}

	@Override
	public boolean checkActionSuccess( Long orderId,
			String actionType)  {
		log.debug(getClass().getName()+".checkActionSuccess("+orderId+","+actionType+")");
		return payOrdersBiz.checkActionSuccess(orderId, actionType);
	}

	@Override
	public boolean batchUpdateRecordStatus( List<AccountChangeRecordDO> list)  {

		return payOrdersBiz.batchUpdateRecordStatus(list);
	}

	@Override
	public List<AccountChangeRecordDO> findByStatusLimit(Integer settleStatus,Integer limit){
		return payOrdersBiz.findByStatusLimit(settleStatus,limit);
	}

	@Override
	public PayOrdersResult backpayOrderByOrderItemIds(Long developerId, Long orderId, Long money, String orderItemIds, String sign, PayOrdersExtraParams p) {
		log.debug(getClass().getName()+".backpayOrder("+developerId+","+orderId+","+money+","+orderItemIds+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("orderId", orderId+"");
		params.put("money", money+"");
		params.put("orderItemIds", orderItemIds);
		String checksign=SignUtil.sign(params);
		if(!checksign.equals(sign)){
			return new PayOrdersResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		return payOrdersBiz.backpayOrderByOrderItemIds(developerId, orderId, money, orderItemIds, p);
	}

}
