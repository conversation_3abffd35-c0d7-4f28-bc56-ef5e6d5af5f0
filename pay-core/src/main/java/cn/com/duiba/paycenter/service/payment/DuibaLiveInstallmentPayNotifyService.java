package cn.com.duiba.paycenter.service.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.installment.DuibaLiveInstallmentNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.mp.DuibaLiveMpNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.refund.BaseRefundNotifyResponse;

import java.util.Map;


/**
 * 兑吧直播小程序支付
 * <AUTHOR>
 * @Date 2021/5/18 1:51 下午
 */

public interface DuibaLiveInstallmentPayNotifyService {

    /**
     * 兑吧直播分期支付通知
     * @param params
     * @return
     * @throws BizException
     */
    DuibaLiveInstallmentNotifyResponse orderNotify(Map<String,String> params) throws BizException;

    /**
     * 银联分期支付退款通知
     * @param params
     * @return
     * @throws BizException
     */
    BaseRefundNotifyResponse orderRefundNotify(Map<String,String> params) throws BizException;
}
