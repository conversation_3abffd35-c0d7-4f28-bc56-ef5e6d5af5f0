package cn.com.duiba.paycenter.remoteservice.impl.duibaaccount;

import cn.com.duiba.paycenter.dto.duibaaccount.AppRemainingMoneyDto;
import cn.com.duiba.paycenter.dto.DeveloperMoneyInfoDto;
import cn.com.duiba.paycenter.remoteservice.duibaaccount.RemoteAppRemainingMoneyService;
import cn.com.duiba.paycenter.service.AppRemainingMoneyService;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * author zhang<PERSON><PERSON>
 * date 2018-11-27-14:47
 */
@RestController
public class RemoteAppRemainingMoneyServiceImpl implements RemoteAppRemainingMoneyService {

    @Resource
    private AppRemainingMoneyService appRemainingMoneyService;

    @Override
    public Long findMoney(Long developerId, Long appId, Integer accountType) {
        return appRemainingMoneyService.findMoney(developerId, appId, accountType);
    }

    @Override
    public List<AppRemainingMoneyDto> findByAppIdsAndType(List<Long> appIds, Integer accountType) {
        return BeanUtils.copyList(appRemainingMoneyService.findByAppIdsAndType(appIds, accountType),AppRemainingMoneyDto.class);
    }

    @Override
    public List<Long> findAllDeveloperId() {
        return appRemainingMoneyService.findAllDeveloperId();
    }

    @Override
    public List<DeveloperMoneyInfoDto> findDeveloperMoney(List<Long> developerIds, List<Integer> accountTypes) {
        return appRemainingMoneyService.findDeveloperMoney(developerIds, accountTypes);
    }
}
