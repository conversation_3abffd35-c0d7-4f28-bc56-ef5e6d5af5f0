package cn.com.duiba.paycenter.service.payment.impl.ningboBank;

import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.charge.NbcbChargeRequestDto;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.refund.NbcbRefundRequestDto;
import cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity;
import cn.com.duiba.paycenter.entity.payment.RefundHandlerResult;
import cn.com.duiba.paycenter.entity.payment.RefundOrderEntity;
import cn.com.duiba.paycenter.enums.ChannelEnum;
import cn.com.duiba.paycenter.service.NingboBankPayService;
import cn.com.duiba.paycenter.service.payment.ChannelHandler;
import cn.com.duiba.paycenter.service.payment.ChannelHandlerManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022-03-12
 */
@Service
public class NingboBankPayHandler implements ChannelHandler, InitializingBean {


    @Resource
    private NingboBankPayService ningboBankPayService;


    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Override
    @SuppressWarnings("unchecked")
    public <T extends BaseChargeResponse, E extends BaseChargeRequest> T createCharge(E chargeRequest) {
        return (T) ningboBankPayService.doCreateCharge((NbcbChargeRequestDto) chargeRequest);
    }

    @Override
    public <T extends BaseChargeRequest> ChargeOrderEntity createChargeOrderEntity(T chargeRequest) {
        NbcbChargeRequestDto request = (NbcbChargeRequestDto) chargeRequest;
        ChargeOrderEntity entity = new ChargeOrderEntity();
        entity.setBizOrderNo(chargeRequest.getBizOrderNo());
        //主订单 brief
        entity.setTitle(request.getOrderDesc());
        entity.setClientIp(request.getIp());
        entity.setBizType(chargeRequest.getBizType());
        entity.setMetadata(chargeRequest.getMetadata());
        entity.setAppId(chargeRequest.getAppId());
        //四位随机数 + bizOrderNo
        entity.setOrderNo(chargeRequest.getOrderNo());
        entity.setAmount(chargeRequest.getAmount());
        entity.setChannelType(chargeRequest.getChannelType());
        return entity;
    }

    @Override
    public RefundHandlerResult createRefund(RefundOrderEntity refundOrderEntity, String subjectType) {
        throw new IllegalStateException("不可用");
    }

    public RefundHandlerResult createRefund(RefundOrderEntity refundOrderEntity, NbcbRefundRequestDto refundRequestDto) {
        return ningboBankPayService.doRefund(refundOrderEntity, refundRequestDto);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ChannelHandlerManager.register(ChannelEnum.NINGBO_BANK_PAY.getChannelType(), this);
    }
}
