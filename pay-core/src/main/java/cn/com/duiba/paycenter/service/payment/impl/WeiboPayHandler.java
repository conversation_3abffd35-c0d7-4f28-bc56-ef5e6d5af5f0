package cn.com.duiba.paycenter.service.payment.impl;

import cn.com.duiba.api.tools.MoneyUtil;
import cn.com.duiba.credits.sdk.AssembleTool;
import cn.com.duiba.paycenter.config.WaitConfirmChargeOrderConfig;
import cn.com.duiba.paycenter.config.weibo.WeiboPayConfig;
import cn.com.duiba.paycenter.dao.payment.ChargeOrderDao;
import cn.com.duiba.paycenter.dto.payment.WaitConfirmChargeOrderDto;
import cn.com.duiba.paycenter.dto.payment.WeiboBaseResponse;
import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.xib.charge.XibChargeRequestDTO;
import cn.com.duiba.paycenter.dto.payment.refund.weibo.WeiboRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.weibo.WeiboRefundResponse;
import cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity;
import cn.com.duiba.paycenter.entity.payment.RefundHandlerResult;
import cn.com.duiba.paycenter.entity.payment.RefundOrderEntity;
import cn.com.duiba.paycenter.enums.ChannelEnum;
import cn.com.duiba.paycenter.service.payment.AbstractChannelHandler;
import cn.com.duiba.paycenter.service.payment.ChannelHandler;
import cn.com.duiba.paycenter.service.payment.ChannelHandlerManager;
import cn.com.duiba.paycenter.service.payment.WaitConfirmChargeOrderService;
import cn.com.duiba.paycenter.util.WeiboRsaUtil;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WeiboPayHandler extends AbstractChannelHandler implements ChannelHandler {
    @Resource
    private WeiboPayConfig weiboPayConfig;

    @Resource(name = "weiboHttpClient")
    private CloseableHttpClient httpClient;


    @Resource
    private ChargeOrderDao chargeOrderDao;

    @Resource
    private WaitConfirmChargeOrderService waitConfirmChargeOrderService;

    @Resource
    private WaitConfirmChargeOrderConfig waitConfirmChargeOrderConfig;


    @Override
    public void register() {
        ChannelHandlerManager.register(ChannelEnum.WEIBO_PAY.getChannelType(), this);

    }

    /**
     * 创建支付
     */
    @Override
    @SuppressWarnings("unchecked")
    public <T extends BaseChargeResponse, E extends BaseChargeRequest> T createCharge(E chargeRequest) {
        log.info("微博支付 支付请求 chargeRequest={}", JSON.toJSONString(chargeRequest));
        WeiboChargeRequest weiboChargeRequest = (WeiboChargeRequest) chargeRequest;
        WeiboChargeResponse chargeResponse = new WeiboChargeResponse();

        try {
            chargeResponse.setOrderNo(weiboChargeRequest.getOrderNo());
            // 构建支付参数
            Map<String, Object> payInfo = builderPayInfo(weiboChargeRequest);
            chargeResponse.setPayInfo(payInfo);
            // 创建支付主动查询任务
            createPayQueryTask(weiboChargeRequest);
        } catch (Exception e) {
            log.error("微博支付 创建支付异常 chargeRequest={}", JSON.toJSONString(chargeRequest), e);
            chargeResponse.setSuccess(false);
            chargeResponse.setMessage("系统异常");
            return (T) chargeResponse;
        }
        log.info("微博支付 支付请求  chargeResponse={}", JSON.toJSONString(chargeResponse));
        chargeResponse.setSuccess(true);
        return (T) chargeResponse;
    }

    @Override
    public <T extends BaseChargeRequest> ChargeOrderEntity createChargeOrderEntity(T chargeRequest) {
        ChargeOrderEntity chargeOrderEntity = super.createChargeOrderEntity(chargeRequest);

        WeiboChargeRequest weiboChargeRequest = (WeiboChargeRequest) chargeRequest;
        String subject = weiboChargeRequest.getSubject();
        if(StringUtils.isNotBlank(subject) && subject.length() > 60) {
            subject = subject.substring(0, 55) + "...";
        }
        chargeOrderEntity.setTitle(subject);
        chargeOrderEntity.setBody(weiboChargeRequest.getBody());
        return chargeOrderEntity;
    }

    /**
     * 构建支付参数
     */
    private Map<String, Object> builderPayInfo(WeiboChargeRequest chargeRequest) throws UnsupportedEncodingException {
        Map<String, Object> payInfo = new HashMap<>();
        payInfo.put("appkey", weiboPayConfig.getWeiboAppKey());
        payInfo.put("seller_id", weiboPayConfig.getSellerId());
        payInfo.put("sub_merchant_id", weiboPayConfig.getSubMerchantId());
        payInfo.put("sys_type", WeiboPayConfig.SYS_TYPE);
        payInfo.put("out_pay_id", chargeRequest.getOrderNo());
        payInfo.put("notify_url", weiboPayConfig.getDuibaNotifyUrl());
        payInfo.put("return_url", chargeRequest.getReturnUrl());
        payInfo.put("subject", chargeRequest.getSubject());
        payInfo.put("total_amount", chargeRequest.getAmount());
        payInfo.put("expire", chargeRequest.getExpire());
        this.putIfNotEmpty(payInfo, "extra", chargeRequest.getExtra());
        this.putIfNotEmpty(payInfo, "cfg_share_opt", chargeRequest.getCfgShareOpt());
        this.putIfNotEmpty(payInfo, "cfg_follow_opt", chargeRequest.getCfgFollowOpt());
        this.putIfNotEmpty(payInfo, "cfg_direct_jump", chargeRequest.getCfgDirectJump());
        this.putIfNotEmpty(payInfo, "subsidy_interest", chargeRequest.getSubsidyInterest());
        this.putIfNotEmpty(payInfo, "goods_type", chargeRequest.getGoodsType());
        this.putIfNotEmpty(payInfo, "half_control", chargeRequest.getHalfControl());
        payInfo.put("sign",URLEncoder.encode( WeiboRsaUtil.sign(payInfo, weiboPayConfig.getDuibaPrivateKey()),"UTF-8"));
        payInfo.put("notify_url", URLEncoder.encode(weiboPayConfig.getDuibaNotifyUrl(), "UTF-8"));
        payInfo.put("return_url", URLEncoder.encode(chargeRequest.getReturnUrl(), "UTF-8"));
        payInfo.put("subject",URLEncoder.encode(chargeRequest.getSubject(), "UTF-8"));
        payInfo.put("sign_type", WeiboPayConfig.SIGN_TYPE);
        return payInfo;
    }


    private void putIfNotEmpty(Map<String, Object> map, String key, Object value) {
        if (Objects.nonNull(value) && StringUtils.isNotBlank(value.toString())) {
            map.put(key, value);
        }
    }

    /**
     * 申请退款
     */
    @Override
    public RefundHandlerResult createRefund(RefundOrderEntity refundOrderEntity, String subjectType) {
        RefundHandlerResult result = new RefundHandlerResult();
        log.info("微博支付 申请退款 refundOrderEntity={}", JSON.toJSONString(refundOrderEntity));
        try {
            ChargeOrderEntity chargeOrder = getOrder(refundOrderEntity);
            WeiboRefundRequest weiboRefundRequest = buildRefundRequest(Long.parseLong(chargeOrder.getTransactionNo()),refundOrderEntity,chargeOrder);
            Map<String, Object> params = BeanUtil.beanToMap(weiboRefundRequest,true,true);
            log.info("微博支付 申请退款 组装的参数 params={}", params);
            Map<String, String> urlParams = params.entrySet().stream()
                    .filter(entry -> entry.getValue() != null)
                    .collect(Collectors.toMap(Map.Entry::getKey, x -> x.getValue().toString()));
            String assembleUrl = AssembleTool.assembleUrl(weiboPayConfig.getRefundUrl(), urlParams);
            assembleUrl = assembleUrl.substring(0, assembleUrl.length() - 1);
            log.info("微博支付 申请退款 组装的url={}", assembleUrl);
            HttpPost httpPost = new HttpPost(assembleUrl);
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                String resp = EntityUtils.toString(entity, "UTF-8");
                log.info("微博支付 申请退款 退款响应 resp={}", resp);
                WeiboBaseResponse<WeiboRefundResponse> weiboRefundResponse = JSON.parseObject(resp, new TypeReference<WeiboBaseResponse<WeiboRefundResponse>>(){});
                // 响应为空
                if (weiboRefundResponse == null){
                    log.error("微博支付 申请退款 响应为空");
                    result.setFailMsg("响应为空");
                    result.setSuccess(false);
                    return result;
                }
                // 请求失败
                if (!"100000".equals(weiboRefundResponse.getCode())) {
                    log.error("微博支付 申请退款 响应失败 resp={}", resp);
                    result.setSuccess(false);
                    result.setFailCode(weiboRefundResponse.getCode());
                    result.setFailMsg("接口请求失败："+weiboRefundResponse.getMsg());
                    return result;
                }
                // 响应异常
                WeiboRefundResponse refundResponseData = weiboRefundResponse.getData();
                if (refundResponseData == null){
                    log.error("微博支付 申请退款 响应数据为空 resp={}",resp);
                    result.setFailMsg("退款响应数据为空");
                    result.setSuccess(false);
                    return result;
                }
                // 退款收单失败
                if (WeiboRefundResponse.RefundStatus.STATUS_FAIL.getCode().equals(refundResponseData.getStatus())){
                    log.error("微博支付 申请退款 退款处理失败 resp={}",resp);
                    result.setFailMsg("退款处理失败");
                    result.setSuccess(false);
                    return result;
                }
                // 其他情况视为-退款申请收单成功
                result.setSuccess(true);
                refundOrderEntity.setRefundAmount(MoneyUtil.convertToCent(refundResponseData.getRefundAmount().toString()).intValue());
                refundOrderEntity.setRefundNo(refundResponseData.getRefundId().toString());

                // 创建退款主动查询任务
                createRefundQueryTask(chargeOrder, refundOrderEntity);
            }
        } catch (Exception e) {
            log.error("微博支付 申请退款 退款异常 refundOrderEntity={}", JSON.toJSONString(refundOrderEntity), e);
            result.setFailMsg("系统异常");
            result.setSuccess(false);
            return result;
        }
        return result;
    }

    /**
     * 创建支付主动查询任务

     */
    public void createPayQueryTask(WeiboChargeRequest weiboChargeRequest) {
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByOrderNo(weiboChargeRequest.getOrderNo());

        //已存在则不做操作
        WaitConfirmChargeOrderDto waitConfirmChargeOrderDto = waitConfirmChargeOrderService
                .findChargeByBizOrderNoAndBizTypeAndChannel(weiboChargeRequest.getBizOrderNo(), chargeOrderEntity.getBizType(), chargeOrderEntity.getChannelType());
        if (waitConfirmChargeOrderDto != null) {
            log.info("微博支付 创建支付 创建支付查询任务 此记录已存在 orderNum= {}", weiboChargeRequest.getOrderNo());
            return;
        }

        WaitConfirmChargeOrderDto dto = new WaitConfirmChargeOrderDto();
        dto.setAppId(chargeOrderEntity.getAppId());
        dto.setOrderNo(chargeOrderEntity.getOrderNo());
        dto.setBizOrderNo(weiboChargeRequest.getBizOrderNo());
        dto.setBizType(chargeOrderEntity.getBizType());
        dto.setChannelType(chargeOrderEntity.getChannelType());
        dto.setOrderType(WaitConfirmChargeOrderDto.ORDER_TYPE_CHARGE);
        dto.setOrderDate(new Date());
        dto.setNextTime(new Date(System.currentTimeMillis() + waitConfirmChargeOrderConfig.getRetryTimeIntervalMillis().get(0)));
        waitConfirmChargeOrderService.insert(dto);
    }


    /**
     * 创建退款主动查询任务
     */
    public void createRefundQueryTask(ChargeOrderEntity chargeOrderEntity, RefundOrderEntity refundOrderEntity) {
        if(!waitConfirmChargeOrderConfig.getCustomQueryAppIds().contains(refundOrderEntity.getAppId())){
            return;
        }

        WaitConfirmChargeOrderDto waitConfirmChargeOrderDto = waitConfirmChargeOrderService.findRefundByBizOrderNoAndBizTypeAndChannel(refundOrderEntity.getOrderNo(), chargeOrderEntity.getBizType(), chargeOrderEntity.getChannelType());
        if (waitConfirmChargeOrderDto != null) {
            log.info("微博支付 申请退款 创建退款查询任务 此记录已存在 orderNum= {}", refundOrderEntity.getOrderNo());
            return;
        }

        WaitConfirmChargeOrderDto dto = new WaitConfirmChargeOrderDto();
        dto.setAppId(chargeOrderEntity.getAppId());
        dto.setOrderNo(refundOrderEntity.getOrderNo());
        dto.setBizOrderNo(refundOrderEntity.getBizOrderNo());
        dto.setBizType(chargeOrderEntity.getBizType());
        dto.setChannelType(chargeOrderEntity.getChannelType());
        dto.setOrderType(WaitConfirmChargeOrderDto.ORDER_TYPE_REFUND);
        dto.setOrderDate(new Date());
        dto.setNextTime(new Date(System.currentTimeMillis() + waitConfirmChargeOrderConfig.getRetryTimeIntervalMillis().get(0)));
        waitConfirmChargeOrderService.insert(dto);
    }

    /**
     * 构建退款请求参数
     * @param refundOrderEntity 退款实体
     * @return 退款请求参数
     */
    private WeiboRefundRequest buildRefundRequest(Long payId,RefundOrderEntity refundOrderEntity, ChargeOrderEntity chargeOrder) {
        WeiboRefundRequest refundRequest = new WeiboRefundRequest();
        refundRequest.setSellerId(weiboPayConfig.getSellerId());
        refundRequest.setOutRefundId(refundOrderEntity.getOrderNo());
        refundRequest.setPayId(payId);
        refundRequest.setRefundAmount(chargeOrder.getAmount());
        refundRequest.setReason(refundRequest.getReason());
        Map<String, Object> refundParam = BeanUtil.beanToMap(refundRequest, true, true);
        refundRequest.setSign(WeiboRsaUtil.sign(refundParam, weiboPayConfig.getDuibaPrivateKey()));
        refundRequest.setSignType(WeiboPayConfig.SIGN_TYPE);
        return refundRequest;
    }

    /**
     * 查询支付单
     * @param entity 退款实体
     * @return 支付单
     */
    public ChargeOrderEntity getOrder(RefundOrderEntity entity) {
        ChargeOrderEntity chargeOrderEntity = null;
        if (StringUtils.isNotBlank(entity.getTransactionNo())) {
            chargeOrderEntity = chargeOrderDao.findByTransactionNo(entity.getTransactionNo());
        } else if (StringUtils.isNotBlank(entity.getChargeOrderNo())) {
            chargeOrderEntity = chargeOrderDao.findByOrderNo(entity.getChargeOrderNo());
        }
        return chargeOrderEntity;
    }
}
