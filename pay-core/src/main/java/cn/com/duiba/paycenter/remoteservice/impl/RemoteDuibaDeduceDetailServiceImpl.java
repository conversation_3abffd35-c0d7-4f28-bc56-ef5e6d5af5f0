package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.dto.DuibaDeduceDetailDto;
import cn.com.duiba.paycenter.remoteservice.RemoteDuibaDeduceDetailService;
import cn.com.duiba.paycenter.service.DuibaDeduceDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
public class RemoteDuibaDeduceDetailServiceImpl implements
		RemoteDuibaDeduceDetailService {
	@Autowired
	public DuibaDeduceDetailService duibaDeduceDetailService;

	@Override
	public List<DuibaDeduceDetailDto> findPageList(Map<String, Object> queryMap) {
		return duibaDeduceDetailService.findPageList(queryMap);
	}

	@Override
	public Long findPageCount(Map<String, Object> queryMap) {
		return duibaDeduceDetailService.findPageCount(queryMap);
	}

	@Override
	public DuibaDeduceDetailDto insert(DuibaDeduceDetailDto duibaDeduceDetailDO) {
		duibaDeduceDetailService.insert(duibaDeduceDetailDO);
		return duibaDeduceDetailDO;
	}

	@Override
	public int update(DuibaDeduceDetailDto duibaDeduceDetailDO) {
		return duibaDeduceDetailService.update(duibaDeduceDetailDO);
	}

	@Override
	public int updateDuibaDeduceDetail(Long id, String operationStatus) {
		return duibaDeduceDetailService.updateDuibaDeduceDetail(id, operationStatus);
	}

}
