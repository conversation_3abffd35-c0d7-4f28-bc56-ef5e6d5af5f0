package cn.com.duiba.paycenter.service;

import cn.com.duiba.paycenter.dto.EquityOnlyOrderDto;
import cn.com.duiba.paycenter.dto.PayOrderDto;
import cn.com.duiba.paycenter.enums.EquityOnlyOrderBizTypeEnum;
import cn.com.duiba.paycenter.enums.PayOrderBizTypeEnum;
import cn.com.duiba.paycenter.enums.PayOrderStatusEnum;

public interface EquityOnlyOrderService {

    /**
     * 插入操作
     * @param dto 数据表对应的实体类
     * @return
     */
    long insert(EquityOnlyOrderDto dto);
    /**
     * 根据主键更新对应数据条目，如果entity数据域为null则不更新对应数据域
     * @param dto
     * @return
     */
    int updateById(EquityOnlyOrderDto dto);

    /**
     * 修改状态
     * @return
     */
    int updateStatusByUniqueIndex(PayOrderStatusEnum orderStatusEnum, EquityOnlyOrderBizTypeEnum equityOnlyOrderBizTypeEnum, String bizNo);

    /**
     * 修改状态
     * 为成功时增加备注和三方返回的流水号
     * @return
     */
    int updateSuccess(EquityOnlyOrderDto equityOnlyOrderDto);

    /**
     * 根据主键查询订单号
     * @param bizType
     * @param bizNo
     * @return
     */
    EquityOnlyOrderDto findByUniqueIndex(EquityOnlyOrderBizTypeEnum bizType, String bizNo);
}
