package cn.com.duiba.paycenter.listener;

import cn.com.duiba.developer.center.api.remoteservice.RemoteDeveloperService;
import cn.com.duiba.paycenter.service.PayCenterService;
import com.alibaba.fastjson.JSON;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/10/29 17:57 （可以根据需要修改）
 * @Version 1.0 （版本号）
 */
@Slf4j
@Service
public class WithdrawCashEventListener implements UserCashListener{


    @Autowired
    private RemoteDeveloperService remoteDeveloperService;

    @Autowired
    private PayCenterService payCenterService;

    @Subscribe
    public void withdrawCashEvent(WithdrawCashEvent event) {
        try {
            log.info("[提现余额判断], developerId={}", event.getDeveloperId());
            Long balance = payCenterService.getBalance(event.getDeveloperId());

            remoteDeveloperService.balanceNotifyIfNecessry(null,event.getDeveloperId(), balance.intValue());
        } catch (Exception e) {
            log.info("balanceNotifyIfNecessry error:{}", JSON.toJSONString(event), e);
        }
    }

}
