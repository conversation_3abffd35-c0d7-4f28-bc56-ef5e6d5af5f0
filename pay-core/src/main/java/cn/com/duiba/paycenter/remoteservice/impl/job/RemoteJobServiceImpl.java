package cn.com.duiba.paycenter.remoteservice.impl.job;

import cn.com.duiba.paycenter.elasticjob.EquityOrderBatchDetailSyncJob;
import cn.com.duiba.paycenter.elasticjob.EquityOrderSyncJob;
import cn.com.duiba.paycenter.remoteservice.job.RemoteJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2024/10/30 14:55 （可以根据需要修改）
 * @Version 1.0 （版本号）
 */
@RestController
public class RemoteJobServiceImpl implements RemoteJobService {

    @Autowired
    private EquityOrderBatchDetailSyncJob equityOrderBatchDetailSyncJob;

    @Autowired
    private EquityOrderSyncJob equityOrderSyncJob;
    @Override
    public void equityOrderBatchDetailSyncJob() {
        equityOrderBatchDetailSyncJob.doProcess();
    }

    @Override
    public void equityOrderSyncJob() {
        equityOrderSyncJob.doProcess();
    }
}
