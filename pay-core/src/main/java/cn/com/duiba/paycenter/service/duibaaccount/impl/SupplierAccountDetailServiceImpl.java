package cn.com.duiba.paycenter.service.duibaaccount.impl;

import cn.com.duiba.paycenter.dao.duibaaccount.SupplierAccountDetailDao;
import cn.com.duiba.paycenter.dto.duibaaccount.SupplierAccountDetailPageDto;
import cn.com.duiba.paycenter.entity.duibaaccount.SupplierAccountDetailEntity;
import cn.com.duiba.paycenter.params.duibaaccount.SupplierAccountDetailQryParams;
import cn.com.duiba.paycenter.service.duibaaccount.SupplierAccountDetailService;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * author zhanghuifeng
 * date 2018-12-13-11:02
 */
@Service
public class SupplierAccountDetailServiceImpl implements SupplierAccountDetailService {
    @Resource
    private SupplierAccountDetailDao supplierAccountDetailDao;

    @Override
    public List<SupplierAccountDetailPageDto> find4Page(SupplierAccountDetailQryParams params) {
        return BeanUtils.copyList(supplierAccountDetailDao.find4page(params),SupplierAccountDetailPageDto.class);
    }

    @Override
    public Integer count4page(SupplierAccountDetailQryParams params) {
        return supplierAccountDetailDao.count4page(params);
    }

    @Override
    public SupplierAccountDetailEntity findByIdAndSupplierId(Long id, Long supplierId) {
        return supplierAccountDetailDao.findByIdAndSupplierId(id, supplierId);
    }

    @Override
    public List<SupplierAccountDetailEntity> findByRelationAndSupplierId(String relationId, List<Integer> relationTypes, Long supplierId) {
        return supplierAccountDetailDao.findByRelationAndSupplierId(relationId, relationTypes, supplierId);
    }
}
