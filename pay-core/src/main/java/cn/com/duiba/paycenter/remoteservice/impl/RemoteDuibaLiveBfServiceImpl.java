package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.config.duibalive.DuibaLiveBFPayConfig;
import cn.com.duiba.paycenter.dto.payment.config.DuiBaLiveBaofuConfigDto;
import cn.com.duiba.paycenter.remoteservice.RemoteDuibaLiveBfService;
import cn.com.duiba.paycenter.util.duibaLive.RsaReadUtil;
import cn.com.duiba.paycenter.util.duibaLive.SignatureUtils;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.security.PrivateKey;
import java.security.PublicKey;

/**
 * @date:2022/1/12 15:43
 * @author:zhaozhanghai
 * @Description:
 */
@RestController
public class RemoteDuibaLiveBfServiceImpl implements RemoteDuibaLiveBfService {
    @Autowired
    private DuibaLiveBFPayConfig duibaLiveBFPayConfig;
    @Override
    public DuiBaLiveBaofuConfigDto getConfig() {
        return BeanUtils.copy(duibaLiveBFPayConfig,DuiBaLiveBaofuConfigDto.class);
    }
}
