package cn.com.duiba.paycenter.service.duibaaccount.impl;

import cn.com.duiba.paycenter.dao.duibaaccount.AppAccountDetailDao;
import cn.com.duiba.paycenter.dto.duibaaccount.AppAccountDetailPageDto;
import cn.com.duiba.paycenter.dto.duibaaccount.RechargeDetailPageDto;
import cn.com.duiba.paycenter.entity.duibaaccount.AppAccountDetailEntity;
import cn.com.duiba.paycenter.params.duibaaccount.AppAccountDetailQryParams;
import cn.com.duiba.paycenter.params.duibaaccount.RechargeDetailQryParams;
import cn.com.duiba.paycenter.service.duibaaccount.AppAccountDetailService;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * author <PERSON><PERSON><PERSON><PERSON>
 * date 2018-11-27-11:02
 */
@Service
public class AppAccountDetailServiceImpl implements AppAccountDetailService {
    @Resource
    private AppAccountDetailDao appAccountDetailDao;

    @Override
    public List<AppAccountDetailPageDto> find4Page(AppAccountDetailQryParams params) {
        return BeanUtils.copyList(appAccountDetailDao.find4page(params),AppAccountDetailPageDto.class);
    }

    @Override
    public Long count4page(AppAccountDetailQryParams params) {
        return appAccountDetailDao.count4page(params);
    }

    @Override
    public AppAccountDetailEntity findByIdAndAppId(Long id, Long appId) {
        return appAccountDetailDao.findByIdAndAppId(id, appId);
    }

    @Override
    public List<AppAccountDetailEntity> findByRelationAndAppId(String relationId, List<Integer> relationTypes, Long appId) {
        return appAccountDetailDao.findByRelationAndAppId(relationId,relationTypes, appId);
    }

    @Override
    public List<AppAccountDetailPageDto> find4Export(AppAccountDetailQryParams params) {
        return appAccountDetailDao.find4Export(params);
    }

    @Override
    public List<RechargeDetailPageDto> findPage4recharge(RechargeDetailQryParams params) {
        return appAccountDetailDao.findPage4recharge(params);
    }

    @Override
    public Integer findCount4recharge(RechargeDetailQryParams params) {
        return appAccountDetailDao.findCount4recharge(params);
    }
}
