/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package cn.com.duiba.paycenter.entity.duibaLive.crossBorder;

import com.sun.xml.bind.marshaller.NamespacePrefixMapper;
import org.eclipse.persistence.jaxb.JAXBContextFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.XMLReader;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.parsers.SAXParserFactory;
import javax.xml.transform.Source;
import javax.xml.transform.sax.SAXSource;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Objects;


/**
 * 封装了XML转换成object，object转换成XML的代码
 *
 * <AUTHOR>
 *
 */
public final class XmlUtil {

    private static final Logger LOG = LoggerFactory.getLogger(XmlUtil.class);

    /**
     * 将对象直接转换成String类型的 XML输出
     *
     * @param <T>
     * @param t
     * @return
     */
    public static <T> String convertToXml(T t) {
        return convertToXml(t, null);
    }

    /**
     * 将对象直接转换成String类型的 XML输出
     *
     * @param <T>
     * @param t
     * @param mapper
     * @return
     */
    public static <T> String convertToXml(T t, NamespacePrefixMapper mapper) {
        String xml = null;
        try {
            // 创建输出流
            StringWriter sw = new StringWriter();
            JAXBContext context = JAXBContextFactory.createContext(new Class[]{t.getClass()}, new HashMap<>());
            Marshaller marshaller = context.createMarshaller();
            // 格式化xml输出的格式
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);
            // 编码格式
            marshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8");
            // 是否省略头 true 省略 fanlse不省略
            marshaller.setProperty(Marshaller.JAXB_FRAGMENT, Boolean.FALSE);
            if (Objects.nonNull(mapper)) {
                marshaller.setProperty("com.sun.xml.bind.namespacePrefixMapper", mapper);
            }
            // 将对象转换成输出流形式的xml
            marshaller.marshal(t, sw);
            xml = sw.toString();
        } catch (JAXBException ex) {
            ex.printStackTrace();
            LOG.error("failed to convert javabean to xml {}", ex.getMessage());
        } 
        return xml;
    }

    /**
     * 将String类型的xml转换成对象
     *
     * @param <T>
     * @param clazz
     * @param xmlStr
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T convertXmlStrToObject(Class<T> clazz, String xmlStr){
        T xmlObject = null;
        try {
            JAXBContext context = JAXBContext.newInstance(clazz);
            // 进行将Xml转成对象的核心接口
            Unmarshaller unmarshaller = context.createUnmarshaller();
            StringReader sr = new StringReader(xmlStr);
            SAXParserFactory sax = SAXParserFactory.newInstance();
            sax.setNamespaceAware(false);
            XMLReader xmlReader = sax.newSAXParser().getXMLReader();
            Source source = new SAXSource(xmlReader, new InputSource(sr));
            xmlObject = (T) unmarshaller.unmarshal(source);
        } catch (JAXBException | ParserConfigurationException | SAXException ex) {
            ex.printStackTrace();
            LOG.error("failed to convert xml to java object {}", ex.getMessage());
        }
        return xmlObject;
    }

    /**
     * 将file类型的xml转换成对象
     *
     * @param clazz
     * @param xmlPath
     * @return
     */
    @SuppressWarnings("unchecked")
    public static Object convertXmlFileToObject(Class clazz, String xmlPath) {
        Object xmlObject = null;
        try {
            JAXBContext context = JAXBContext.newInstance(clazz);
            Unmarshaller unmarshaller = context.createUnmarshaller();
            FileReader fr = new FileReader(xmlPath);
            xmlObject = unmarshaller.unmarshal(fr);
        } catch (FileNotFoundException | JAXBException e) {
            e.printStackTrace();
            LOG.error("failed to convert xml file to java object {}", e.getMessage());
        }
        return xmlObject;
    }
}
