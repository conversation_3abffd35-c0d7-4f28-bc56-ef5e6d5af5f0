package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.biz.WithdrawBiz;
import cn.com.duiba.paycenter.client.RpcResult;
import cn.com.duiba.paycenter.enums.WithdrawBizTypeEnum;
import cn.com.duiba.paycenter.listener.WithdrawCashEvent;
import cn.com.duiba.paycenter.params.WithdrawParams;
import cn.com.duiba.paycenter.remoteservice.RemoteWithdrawService;
import cn.com.duiba.paycenter.result.WithdrawResult;
import cn.com.duiba.paycenter.service.AppAccountOutInnerRecordService;
import cn.com.duiba.paycenter.util.SignUtil;
import cn.com.duiba.service.exception.BusinessException;
import com.google.common.eventbus.AsyncEventBus;
import com.google.common.eventbus.EventBus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by xiaoxuda on 2018/1/30.
 */
@RestController
public class RemoteWithdrawServiceImpl implements RemoteWithdrawService {
    private static final Logger LOGGER = LoggerFactory.getLogger(RemoteWithdrawServiceImpl.class);
    @Autowired
    private WithdrawBiz withdrawBiz;
    @Autowired
    private EventBus eventBus;
    @Autowired
    private AppAccountOutInnerRecordService accountOutInnerRecordService;

    @Override
    public RpcResult<WithdrawResult> userWithdrawCashApply(WithdrawParams requestParams, String sign) {
        try {
            checkUserWithdrawParamAndSign(requestParams, sign);

            //增加一个监听器，用于触发对应的逻辑，但不影响主流程的执行
            eventBus.post(new WithdrawCashEvent(requestParams.getDeveloperId()));
            WithdrawResult result = withdrawBiz.withdrawApply(requestParams);
            accountOutInnerRecordService.riskInnerRecord(result.getSuccess(),requestParams.getAppId(),requestParams.getMoney());
            return new RpcResult<>(result);
        } catch(BusinessException e){
            LOGGER.info("userWithdrawCashApply fail msg={}", e.getMessage());
            LOGGER.debug("userWithdrawCashApply fail", e);
            return new RpcResult<>(e);
        }catch (Exception e) {
            LOGGER.error("userWithdrawCashApply fail", e);
            return new RpcResult<>(e);
        }
    }

    @Override
    public RpcResult<WithdrawResult> userWithdrawCashPaybackApply(WithdrawParams requestParams, String sign) {
        try {
            checkUserWithdrawParamAndSign(requestParams, sign);
            WithdrawResult result = withdrawBiz.withdrawPaybackApply(requestParams);

            return new RpcResult<>(result);
        } catch(BusinessException e){
            LOGGER.info("userWithdrawCashPaybackApply fail msg={}", e.getMessage());
            LOGGER.debug("userWithdrawCashPaybackApply fail", e);
            return new RpcResult<>(e);
        }catch (Exception e) {
            LOGGER.error("userWithdrawCashPaybackApply fail", e);
            return new RpcResult<>(e);
        }
    }

    private void checkUserWithdrawParamAndSign(WithdrawParams requestParams, String sign) throws BusinessException {
        if(!WithdrawBizTypeEnum.isUserWithdraw(requestParams.getBizType())){
            throw new BusinessException("不支持的操作");
        }
        Map<String, String> params = new HashMap<>();
        params.put("developerId", requestParams.getDeveloperId().toString());
        params.put("withdrawId", requestParams.getBizType().name() + "_" + requestParams.getBizId());
        params.put("money", String.valueOf(requestParams.getMoney()));
        params.put("consumerId", requestParams.getConsumerId().toString());
        String checkSign = SignUtil.sign(params);

        if (!checkSign.equals(sign)) {
            throw new BusinessException("签名不通过");
        }
    }
}
