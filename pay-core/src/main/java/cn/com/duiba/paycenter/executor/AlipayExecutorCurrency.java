package cn.com.duiba.paycenter.executor;

import cn.com.duiba.api.tools.MoneyUtil;
import cn.com.duiba.paycenter.dto.PayOrderDto;
import cn.com.duiba.paycenter.enums.AlipayMerchantTypeEnum;
import cn.com.duiba.paycenter.enums.PayOrderBizTypeEnum;
import cn.com.duiba.paycenter.enums.PayOrderStatusEnum;
import cn.com.duiba.paycenter.result.FundTransferResult;
import cn.com.duiba.paycenter.service.PayOrderService;
import com.alipay.api.AlipayApiException;
import com.alipay.api.domain.AlipayFundTransUniTransferModel;
import com.alipay.api.domain.Participant;
import com.alipay.api.request.AlipayFundTransUniTransferRequest;
import com.alipay.api.response.AlipayFundTransToaccountTransferResponse;
import com.alipay.api.response.AlipayFundTransUniTransferResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AlipayExecutorCurrency {

    private static final Logger logger = LoggerFactory.getLogger(AlipayExecutorCurrency.class);

    @Autowired
    private PayOrderService payOrderService;
    @Autowired
    private AlipayClientPool alipayClientPool;

    public AlipayFundTransUniTransferResponse doTransferExecute(PayOrderDto payOrder, String realName, String identityType) {
        AlipayFundTransUniTransferRequest request = new AlipayFundTransUniTransferRequest();

        AlipayFundTransUniTransferModel model = new AlipayFundTransUniTransferModel();

        model.setOutBizNo(generateOutbizNo(payOrder));
        Participant payeeInfo = new Participant();
        payeeInfo.setIdentityType(identityType);
        payeeInfo.setIdentity(payOrder.getPayeeNo());
        payeeInfo.setName(Alipay2AlipayByUserIdExecutor.IDENTITY_TYPE.equals(identityType) ? null : realName);
        model.setPayeeInfo(payeeInfo);
        model.setTransAmount(MoneyUtil.formatMoneyToKeep2Point(payOrder.getAmount()));
        model.setProductCode("TRANS_ACCOUNT_NO_PWD");//固定值
        model.setBizScene("DIRECT_TRANSFER");//固定值
        model.setRemark("["+ payOrder.getBizRemark() +"]支付宝转账");
        model.setOrderTitle(payOrder.getBizRemark());
        request.setBizModel(model);
        AlipayFundTransUniTransferResponse response = null;
        try {
            logger.info("Alipay2AlipayExecutor callAlipay request:{}",request.getBizContent());
            response = alipayClientPool.getAlipayClientByAlipayAppId(payOrder.getPayerNo()).certificateExecute(request);
        } catch (AlipayApiException e) {
            logger.error("调用支付宝转账发生异常", e);
        }
        return response;
    }


    /**
     * 支付宝响应后回调(新)
     *
     * @return
     */
    public FundTransferResult alipayCallBack(PayOrderDto payOrder, AlipayFundTransUniTransferResponse response) {
        PayOrderDto update = new PayOrderDto();
        update.setId(payOrder.getId());
        if (response == null) {
            update.setPayStatus(PayOrderStatusEnum.EXCEPTION);
            update.setRemark("支付宝无响应");
        } else if (response.isSuccess()) {
            update.setExecutorBizId(response.getOrderId());
            update.setPayStatus(PayOrderStatusEnum.SUCCESS);
            //成功后，存PayFundOrderId到remark
            update.setRemark(response.getPayFundOrderId());
        } else if(AlipayResponseCode.CODE_40004.equals(response.getCode())
                && AlipayResponseCode.failStatus.contains(response.getSubCode())){
            update.setExecutorBizId(response.getOrderId());
            update.setPayStatus(PayOrderStatusEnum.FAIL);
            update.setRemark(response.getCode() + ":" + response.getMsg() + "_" +
                    response.getSubCode() + ":" + response.getSubMsg());
        } else{
            update.setExecutorBizId(response.getOrderId());
            update.setPayStatus(PayOrderStatusEnum.EXCEPTION);
            update.setRemark(response.getCode() + "_" + response.getSubCode());
            if(AlipayResponseCode.CODE_40004.equals(response.getCode())
                    && AlipayResponseCode.invalidAlipayClientStatus.contains(response.getSubCode())){
                alipayClientPool.invalidAlipayClient(payOrder.getPayerNo());
            }
        }
        payOrderService.updateById(update);
        return new FundTransferResult(update.getPayStatus(), update.getRemark());
    }

    /**
     * 支付宝响应后回调
     *
     * @return
     */
    public FundTransferResult alipayCallBack(PayOrderDto payOrder, AlipayFundTransToaccountTransferResponse response) {
        PayOrderDto update = new PayOrderDto();
        update.setId(payOrder.getId());
        if (response == null) {
            update.setPayStatus(PayOrderStatusEnum.EXCEPTION);
            update.setRemark("支付宝无响应");
        } else if (response.isSuccess()) {
            update.setExecutorBizId(response.getOrderId());
            update.setPayStatus(PayOrderStatusEnum.SUCCESS);
            update.setRemark("");
        } else if(AlipayResponseCode.CODE_40004.equals(response.getCode())
                && AlipayResponseCode.failStatus.contains(response.getSubCode())){
            update.setExecutorBizId(response.getOrderId());
            update.setPayStatus(PayOrderStatusEnum.FAIL);
            update.setRemark(response.getCode() + ":" + response.getMsg() + "_" +
                    response.getSubCode() + ":" + response.getSubMsg());
        } else{
            update.setExecutorBizId(response.getOrderId());
            update.setPayStatus(PayOrderStatusEnum.EXCEPTION);
            update.setRemark(response.getCode() + "_" + response.getSubCode());
            if(AlipayResponseCode.CODE_40004.equals(response.getCode())
                    && AlipayResponseCode.invalidAlipayClientStatus.contains(response.getSubCode())){
                alipayClientPool.invalidAlipayClient(payOrder.getPayerNo());
            }
        }
        payOrderService.updateById(update);
        return new FundTransferResult(update.getPayStatus(), update.getRemark());
    }


    public String getNewOrOldPayerNo(PayOrderBizTypeEnum bizType, String subjectType) {
//        if (alipayClientPool.getIsAlipaySwitch() > 0) {
            return alipayClientPool.getByTypeAndSubjectNew(bizType, subjectType).getAlipayAppId();
//        } else {
//            return alipayClientPool.getByTypeAndSubject(bizType, subjectType).getAlipayAppId();
//        }
    }

    /**
     * 判断是否是自定义商户并返回配置
     * @param bizType
     * @param subjectType
     * @param merchantTypeEnum
     * @param merchantNo
     * @return
     */
    public String getNewOrOldPayerNo(PayOrderBizTypeEnum bizType, String subjectType, AlipayMerchantTypeEnum merchantTypeEnum, String merchantNo) {
        // 自定义的支付宝账号
        if (AlipayMerchantTypeEnum.CUSTOM.equals(merchantTypeEnum)) {
            return merchantNo;
        }
        return getNewOrOldPayerNo(bizType, subjectType);
    }

        private String generateOutbizNo(PayOrderDto payOrder){
        return payOrder.getBizType() + "_" + payOrder.getBizNo();
    }
}
