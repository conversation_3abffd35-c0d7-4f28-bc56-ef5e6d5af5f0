package cn.com.duiba.paycenter.remoteservice.impl.duibaaccount;

import cn.com.duiba.paycenter.dto.duibaaccount.AppAccountDetailPageDto;
import cn.com.duiba.paycenter.dto.duibaaccount.AppSettleAccountDetailDto;
import cn.com.duiba.paycenter.params.duibaaccount.AppAccountDetailQryParams;
import cn.com.duiba.paycenter.remoteservice.duibaaccount.RemoteAppSettleAccountDetailService;
import cn.com.duiba.paycenter.service.duibaaccount.AppSettleAccountDetailService;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * author zhanghuifeng
 * date 2018-11-27-14:27
 */
@RestController
public class RemoteAppSettleAccountDetailServiceImpl implements RemoteAppSettleAccountDetailService {

    @Resource
    private AppSettleAccountDetailService appSettleAccountDetailService;

    @Override
    public List<AppAccountDetailPageDto> find4Page(AppAccountDetailQryParams params) {
        return appSettleAccountDetailService.find4Page(params);
    }

    @Override
    public Long count4page(AppAccountDetailQryParams params) {
        return appSettleAccountDetailService.count4page(params);
    }

    @Override
    public AppSettleAccountDetailDto findByIdAndAppId(Long id, Long appId) {
        return BeanUtils.copy(appSettleAccountDetailService.findByIdAndAppId(id, appId)
                ,AppSettleAccountDetailDto.class);
    }

    @Override
    public List<AppSettleAccountDetailDto> findByRelationAndAppId(String relationId, List<Integer> relationTypes, Long appId) {
        return BeanUtils.copyList(appSettleAccountDetailService.findByRelationAndAppId(relationId,relationTypes, appId),
                AppSettleAccountDetailDto.class);
    }

    @Override
    public List<AppAccountDetailPageDto> find4Export(AppAccountDetailQryParams params) {
        return appSettleAccountDetailService.find4Export(params);
    }
}
