package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponNotifyUrlSetResponse;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponUsedMessage;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponUsedNotifyRequest;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponUserFlowDetail;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponUserFlowResponse;
import cn.com.duiba.paycenter.enums.WxCouponSubjectEnum;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteWxCouponNotifyService;
import cn.com.duiba.paycenter.service.payment.WxCouponNotifyService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description:
 * <p>
 * date: 2022/10/12 10:41 上午
 *
 * <AUTHOR>
 */
@RestController
public class RemoteWxCouponNotifyServiceImpl implements RemoteWxCouponNotifyService {
    @Resource
    private WxCouponNotifyService wxCouponNotifyService;

    @Override
    public WxCouponNotifyUrlSetResponse setNotifyUrl(WxCouponSubjectEnum wxCouponSubjectEnum) throws BizException {
        return wxCouponNotifyService.setNotifyUrl(wxCouponSubjectEnum);
    }

    @Override
    public WxCouponUsedMessage usedNotifyPreHandle(WxCouponUsedNotifyRequest notifyRequest, WxCouponSubjectEnum wxCouponSubjectEnum) {
        return wxCouponNotifyService.usedNotifyPreHandle(notifyRequest, wxCouponSubjectEnum);
    }

    @Override
    public void getWxCouponUserFlow(String stockId, WxCouponSubjectEnum wxCouponSubjectEnum) throws BizException {
        wxCouponNotifyService.wxCouponUserFlowDownloadUrl(stockId, wxCouponSubjectEnum);
    }
}
