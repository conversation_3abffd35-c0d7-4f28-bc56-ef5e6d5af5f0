package cn.com.duiba.paycenter.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 是否需要加密
 * <AUTHOR>
 * @date 2023/3/22 10:09 上午
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Encryption {

    /**
     * 解密数组和集合类型的时候必填
     * 因为java范型获取不到具体类型，无法解析。需要显示指定
     * 声明：集合/数组仅支持List<T>，其他未实现。不支持对T的集合/数组字段加解密
     */
    Class<?> type() default Object.class;
}
