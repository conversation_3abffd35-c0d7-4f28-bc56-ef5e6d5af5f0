package cn.com.duiba.paycenter.service.third.wx.wxv3;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.bean.wx.v3.request.WxV3BaseRequest;
import cn.com.duiba.paycenter.bean.wx.v3.request.transfer.WxGetTransferBatchByOutNoRequest;
import cn.com.duiba.paycenter.bean.wx.v3.request.transfer.WxGetTransferDetailByOutNoRequest;
import cn.com.duiba.paycenter.bean.wx.v3.request.transfer.WxInitiateBatchTransferRequest;
import cn.com.duiba.paycenter.bean.wx.v3.result.transfer.WxInitiateBatchTransferResult;
import cn.com.duiba.paycenter.bean.wx.v3.result.transfer.WxTransferBatchResult;
import cn.com.duiba.paycenter.bean.wx.v3.result.transfer.WxTransferDetailResult;
import cn.com.duiba.paycenter.bean.wx.v3.result.WxV3BaseResult;
import cn.com.duiba.paycenter.bean.wx.v3.result.transfer.WxTransferQueryReq;

import java.io.Serializable;

/**
 * 微信支付v3接口规则-商家转账到零钱
 * <AUTHOR>
 * @date 2023/3/21 5:56 下午
 */
public interface WxV3TransferBatchApiService {

    /**
     * 发起商家转账
     * <a herf="https://pay.weixin.qq.com/docs/merchant/apis/batch-transfer-to-balance/transfer-batch/initiate-batch-transfer.html">官方文档</a>
     * @param mchId 商户号ID
     * @param request 请求参数
     * @return 微信响应结果
     * @throws BizException 参数/配置/请求异常
     */
    WxV3BaseResult<WxInitiateBatchTransferResult> initiateBatchTransfer(String mchId, WxV3BaseRequest<Serializable, WxInitiateBatchTransferRequest> request) throws BizException;

    /**
     * 通过商家批次单号查询批次单
     * <a herf="https://pay.weixin.qq.com/docs/merchant/apis/batch-transfer-to-balance/transfer-batch/get-transfer-batch-by-out-no.html">官方文档</a>
     * @param mchId 商户号ID
     * @param request 请求参数
     * @return 微信响应结果
     * @throws BizException 参数/配置/请求异常
     */
    WxV3BaseResult<WxTransferBatchResult> getTransferBatchByOutNo(String mchId, WxV3BaseRequest<WxTransferQueryReq, WxGetTransferBatchByOutNoRequest> request) throws BizException;
    
    /**
     * 通过商家明细单号查询明细单
     * <a href="https://pay.weixin.qq.com/docs/merchant/apis/batch-transfer-to-balance/transfer-detail/get-transfer-detail-by-out-no.html">官方文档</a>
     * @param mchId 商户号ID
     * @param request 请求参数
     * @return 微信响应结果
     * @throws BizException 参数/配置/请求异常
     */
    WxV3BaseResult<WxTransferDetailResult> getTransferDetailByOutNo(String mchId, WxV3BaseRequest<WxGetTransferDetailByOutNoRequest, Serializable> request) throws BizException;

}
