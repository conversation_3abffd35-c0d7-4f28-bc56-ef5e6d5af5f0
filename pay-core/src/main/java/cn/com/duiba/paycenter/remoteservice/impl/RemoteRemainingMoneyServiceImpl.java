package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.dto.DeveloperMoneyInfoDto;
import cn.com.duiba.paycenter.exception.CodeException;
import cn.com.duiba.service.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;

import cn.com.duiba.paycenter.dto.RemainingMoneyDto;
import cn.com.duiba.paycenter.remoteservice.RemoteRemainingMoneyService;
import cn.com.duiba.paycenter.service.RemainingMoneyService;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class RemoteRemainingMoneyServiceImpl implements
		RemoteRemainingMoneyService {
	@Autowired
	private RemainingMoneyService remainingMoneyService;

	@Override
	public RemainingMoneyDto findByDeveloperId(Long developerId) {
		return remainingMoneyService.findByDeveloperId(developerId);
	}

	@Override
	public List<RemainingMoneyDto> findByDeveloperIdList(List<Long> developerIds) {
		return remainingMoneyService.findByDeveloperIdList(developerIds);
	}

	@Override
	public RemainingMoneyDto findByDeveloperId4update(Long developerId) {
		return remainingMoneyService.findByDeveloperId4update(developerId);
	}

	@Override
	public boolean reduceMoney(RemainingMoneyDto rm, Integer money)
			throws BusinessException,CodeException {
		return remainingMoneyService.reduceMoney(rm, money);
	}

	@Override
	public boolean addMoney(RemainingMoneyDto rm, Integer money)
			throws BusinessException,CodeException {
		return remainingMoneyService.addMoney(rm, money);
	}

	@Override
	public RemainingMoneyDto insert(RemainingMoneyDto remainingMoney) {
		remainingMoneyService.insert(remainingMoney);
		return remainingMoney;
	}

	@Override
	public List<DeveloperMoneyInfoDto> batchFindBalance(List<Long> developerIds) {
		return remainingMoneyService.batchFindBalance(developerIds);
	}

}
