package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.bean.AppAccountChangeBean;
import cn.com.duiba.paycenter.biz.AppSettleAccountBiz;
import cn.com.duiba.paycenter.constant.PayCenterErrorCode;
import cn.com.duiba.paycenter.params.AppSettleAccountParams;
import cn.com.duiba.paycenter.remoteservice.RemoteAppSettleAccountService;
import cn.com.duiba.paycenter.result.PayCenterResult;
import cn.com.duiba.paycenter.util.SignUtil;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * author zhanghuifeng
 * date 2018/11/14-8:01 PM
 */
@RestController
public class RemoteAppSettleAccountServiceImpl implements RemoteAppSettleAccountService {

    @Resource
    private AppSettleAccountBiz appSettleAccountBiz;

    @Override
    public PayCenterResult reduceMoney(@Valid AppSettleAccountParams settleParams, String sign) {
        String checkSign= getCheckSign(settleParams);

        if(!checkSign.equals(sign)){
            return new PayCenterResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        return appSettleAccountBiz.reduceMoney(packBean(settleParams));
    }

    @Override
    public PayCenterResult addMoney(AppSettleAccountParams settleParams, String sign) {
        String checkSign= getCheckSign(settleParams);

        if(!checkSign.equals(sign)){
            return new PayCenterResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        return appSettleAccountBiz.addMoney(packBean(settleParams));
    }

    private AppAccountChangeBean packBean(AppSettleAccountParams settleParams){
        AppAccountChangeBean bean = new AppAccountChangeBean();
        bean.setAppId(settleParams.getAppId());
        bean.setDeveloperId(settleParams.getDeveloperId());
        bean.setMoney(settleParams.getMoney());
        bean.setRelationId(settleParams.getRelationId());
        bean.setMemo(settleParams.getMemo());
        bean.setTransferIn(settleParams.getTransferIn() == null?null:settleParams.getTransferIn().getCode());
        bean.setTransferOut(settleParams.getTransferOut()==null?null:settleParams.getTransferOut().getCode());
        bean.setRelationType(settleParams.getRelationType());
        return bean;
    }

    private String getCheckSign(AppSettleAccountParams settleParams){
        Map<String, String> params=new HashMap<>();
        params.put("developerId", Objects.toString(settleParams.getDeveloperId()));
        params.put("relationId", settleParams.getRelationId());
        params.put("money", Objects.toString(settleParams.getMoney()));
        params.put("appId", Objects.toString(settleParams.getAppId()));
        return SignUtil.sign(params);
    }
}
