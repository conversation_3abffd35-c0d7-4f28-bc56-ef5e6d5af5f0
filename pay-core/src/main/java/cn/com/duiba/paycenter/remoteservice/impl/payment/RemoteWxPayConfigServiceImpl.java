package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.config.WxPayConfigDto;
import cn.com.duiba.paycenter.params.miniProgram.MiniProgramConfigParam;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteWxPayConfigService;
import cn.com.duiba.paycenter.service.payment.WxPayConfigService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018/12/05
 */
@RestController
public class RemoteWxPayConfigServiceImpl implements RemoteWxPayConfigService {
    @Resource
    private WxPayConfigService wxPayConfigService;

    @Override
    public Integer getChannelMode(Long appId, String channelType) {
        return wxPayConfigService.getChannelMode(appId, channelType);
    }

    @Override
    public WxPayConfigDto findByWxAppIdAndMchId(String wxAppId, String mchId) throws BizException {
        return wxPayConfigService.findByWxAppIdAndMchId(wxAppId, mchId);
    }

    @Override
    public WxPayConfigDto findByWxAppIdAndChannelType(Long appId, String channelType)  {
        if(appId == null || channelType == null){
            return null;
        }

        return wxPayConfigService.findByAppIdAndChannelType(appId,channelType);
    }

    @Override
    public Integer updateMiniProgramConfigById(MiniProgramConfigParam miniProgramConfigParam) throws BizException {
        return wxPayConfigService.updateMiniProgramConfigById(miniProgramConfigParam);
    }

    @Override
    public Integer insertMiniProgram(MiniProgramConfigParam miniProgramConfigParam) throws BizException {
        return wxPayConfigService.insertMiniProgram(miniProgramConfigParam);
    }

    @Override
    public void inValideCacheKey(Long id) {
         wxPayConfigService.inValideCacheKey(id);
    }
}
