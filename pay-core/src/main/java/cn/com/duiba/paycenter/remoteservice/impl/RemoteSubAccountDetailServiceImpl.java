package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.paycenter.dao.SubAccountDetailDao;
import cn.com.duiba.paycenter.dto.SubAccountDetailDto;
import cn.com.duiba.paycenter.entity.credits.SubAccountDetailEntity;
import cn.com.duiba.paycenter.remoteservice.RemoteSubAccountDetailService;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * @Date 2023/2/20 15:09
 * <AUTHOR>
 */
@RestController
public class RemoteSubAccountDetailServiceImpl implements RemoteSubAccountDetailService {

    @Resource
    private SubAccountDetailDao subAccountDetailDao;

    @Override
    public Page<SubAccountDetailDto> pageBySubAccountId(Long subAccountId, Integer pageNo, Integer pageSize) {
        Page<SubAccountDetailDto> page = new Page<>();
        List<SubAccountDetailDto> list = new ArrayList<>();
        Integer count = subAccountDetailDao.countBySubAccountId(subAccountId);
        if (count > 0) {
            list = BeanUtils.copyList(subAccountDetailDao.listBySubAccountId(subAccountId, pageNo, pageSize), SubAccountDetailDto.class);
        }
        page.setTotalCount(count);
        page.setList(list);
        return page;
    }

    @Override
    public Boolean batchInsert(List<SubAccountDetailDto> list) {
        return subAccountDetailDao.batchInsert(BeanUtils.copyList(list, SubAccountDetailEntity.class));
    }
}
