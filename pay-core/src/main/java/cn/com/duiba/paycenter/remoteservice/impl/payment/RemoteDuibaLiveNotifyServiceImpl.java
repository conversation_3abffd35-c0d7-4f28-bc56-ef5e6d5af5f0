package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.bf.DuibaLiveBfSubAccountRequest;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.citic.DuibaLiveCiticNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.installment.DuibaLiveInstallmentNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.mp.DuibaLiveMpNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.refund.BaseRefundNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.refund.wxpay.WxPayRefundNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteDuibaLiveNotifyService;
import cn.com.duiba.paycenter.service.payment.CiticNotifyService;
import cn.com.duiba.paycenter.service.payment.DuibaLiveBFPayNotifyService;
import cn.com.duiba.paycenter.service.payment.DuibaLiveInstallmentPayNotifyService;
import cn.com.duiba.paycenter.service.payment.DuibaLiveMpPayNotifyService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/5/18 4:27 下午
 */
@RestController
public class RemoteDuibaLiveNotifyServiceImpl implements RemoteDuibaLiveNotifyService {
    @Resource
    private DuibaLiveMpPayNotifyService duibaLiveMpPayNotifyService;
    @Resource
    private DuibaLiveInstallmentPayNotifyService duibaLiveInstallmentPayNotifyService;
    @Resource
    private CiticNotifyService citicNotifyService;
    @Resource
    private DuibaLiveBFPayNotifyService duibaLiveBFPayNotifyService;
    @Override
    public DuibaLiveMpNotifyResponse orderNotify(Map<String,String> params) throws BizException {
        return duibaLiveMpPayNotifyService.orderNotify(params);
    }

    @Override
    public DuibaLiveMpNotifyResponse wxOrderNotify(String xmlData) throws BizException {
        return duibaLiveMpPayNotifyService.wxOrderNotify(xmlData);
    }

    @Override
    public WxPayRefundNotifyResponse refundNotify(String xmlData) throws BizException {
        return duibaLiveMpPayNotifyService.refundNotify(xmlData);
    }

    @Override
    public DuibaLiveInstallmentNotifyResponse installmentOrderNotify(Map<String, String> params) throws BizException {
        return duibaLiveInstallmentPayNotifyService.orderNotify(params);
    }

    @Override
    public BaseRefundNotifyResponse installmentOrderRefundNotify(Map<String, String> params) throws BizException {
        return duibaLiveInstallmentPayNotifyService.orderRefundNotify(params);
    }

    @Override
    public DuibaLiveCiticNotifyResponse duibaLiveCiticCallbackPayNotify(String content) {
        return citicNotifyService.duibaLiveCiticCallbackPayNotify(content);
    }

    @Override
    public BaseChargeNotifyResponse duibaLiveBFPayNotify(Map<String, String> params) {
        return duibaLiveBFPayNotifyService.orderNotify(params);
    }

    @Override
    public BaseRefundNotifyResponse duibaLiveBFRefundNotify(Map<String, String> params) {
        return duibaLiveBFPayNotifyService.refundNotify(params);
    }

    @Override
    public void confirmSubAccount(DuibaLiveBfSubAccountRequest request) {
        duibaLiveBFPayNotifyService.confirmSubAccount(request);
    }

    @Override
    public void confirmSubAccountNodify(Map<String, String> params) {
        duibaLiveBFPayNotifyService.confirmSubAccountNodify(params);
    }
}
