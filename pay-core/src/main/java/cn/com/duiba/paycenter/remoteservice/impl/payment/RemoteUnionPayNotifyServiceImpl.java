package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayRefundNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteUnionPayNotifyService;
import cn.com.duiba.paycenter.service.payment.UnionPayNotifyService;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@RestController
public class RemoteUnionPayNotifyServiceImpl implements RemoteUnionPayNotifyService {

    @Resource
    private UnionPayNotifyService unionPayNotifyService;

    @Override
    public UnionPayChargeNotifyResponse orderNotify(Map<String, String> map) throws BizException {
        return unionPayNotifyService.orderNotify(map);
    }

    /**
     * 退货接口后台通知
     *
     * @param map
     * @return
     * @throws BizException
     */
    @Override
    public UnionPayRefundNotifyResponse refundNotify(Map<String, String> map) throws BizException {
        return unionPayNotifyService.refundNotify(map);
    }
}
