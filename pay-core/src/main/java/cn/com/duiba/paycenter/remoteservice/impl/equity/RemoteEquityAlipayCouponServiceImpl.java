package cn.com.duiba.paycenter.remoteservice.impl.equity;

import cn.com.duiba.paycenter.dto.equity.request.BaseEquityRequest;
import cn.com.duiba.paycenter.dto.equity.request.EquityAlipayCouponCardRequest;
import cn.com.duiba.paycenter.dto.equity.response.BaseEquityResultResponse;
import cn.com.duiba.paycenter.dto.equity.response.EquityResponse;
import cn.com.duiba.paycenter.dto.equity.response.alipay.EquityAlipayCouponCardResponse;
import cn.com.duiba.paycenter.handler.equity.impl.EquityAlipayCouponHandler;
import cn.com.duiba.paycenter.remoteservice.equity.RemoteEquityAlipayCouponService;
import cn.com.duiba.paycenter.service.payment.AlipayCouponService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/4/20 1:55 PM
 */
@RestController
public class RemoteEquityAlipayCouponServiceImpl implements RemoteEquityAlipayCouponService {
    
    @Resource
    private AlipayCouponService alipayCouponService;
    
    @Resource
    private EquityAlipayCouponHandler equityAlipayCouponHandler;

    @Override
    public EquityResponse distribute(BaseEquityRequest<EquityAlipayCouponCardRequest> request) {
        return equityAlipayCouponHandler.distribute(request);
    }

    @Override
    public String queryAlipayUserIdByAuthCode(String appId, String authCode) {
        return alipayCouponService.queryAlipayUserIdByAuthCode(appId, authCode);
    }

    @Override
    public BaseEquityResultResponse<EquityAlipayCouponCardResponse> alipayCouponDistributeResult(Integer bizType, String bizNo) {
        return equityAlipayCouponHandler.distributeResult(bizType, bizNo);
    }
}
