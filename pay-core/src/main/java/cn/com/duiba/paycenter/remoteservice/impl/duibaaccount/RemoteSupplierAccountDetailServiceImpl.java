package cn.com.duiba.paycenter.remoteservice.impl.duibaaccount;

import cn.com.duiba.paycenter.dto.duibaaccount.SupplierAccountDetailDto;
import cn.com.duiba.paycenter.dto.duibaaccount.SupplierAccountDetailPageDto;
import cn.com.duiba.paycenter.params.duibaaccount.SupplierAccountDetailQryParams;
import cn.com.duiba.paycenter.remoteservice.duibaaccount.RemoteSupplierAccountDetailService;
import cn.com.duiba.paycenter.service.duibaaccount.SupplierAccountDetailService;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * author zhanghuifeng
 * date 2018-12-13-13:54
 */
@RestController
public class RemoteSupplierAccountDetailServiceImpl implements RemoteSupplierAccountDetailService {

    @Resource
    private SupplierAccountDetailService supplierAccountDetailService;

    @Override
    public List<SupplierAccountDetailPageDto> find4page(SupplierAccountDetailQryParams params) {
        return supplierAccountDetailService.find4Page(params);
    }

    @Override
    public Integer count4page(SupplierAccountDetailQryParams params) {
        return supplierAccountDetailService.count4page(params);
    }

    @Override
    public SupplierAccountDetailDto findByIdAndSupplierId(Long id, Long supplierId) {
        return BeanUtils.copy(supplierAccountDetailService.findByIdAndSupplierId(id, supplierId),SupplierAccountDetailDto.class);
    }

    @Override
    public List<SupplierAccountDetailDto> findByRelationAndSupplierId(String relationId, List<Integer> relationTypes, Long supplierId) {
        return BeanUtils.copyList(supplierAccountDetailService.findByRelationAndSupplierId(relationId, relationTypes, supplierId),
                SupplierAccountDetailDto.class);
    }
}
