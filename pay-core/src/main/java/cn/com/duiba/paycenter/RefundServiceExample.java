package cn.com.duiba.paycenter;

import cn.com.duiba.paycenter.remoteservice.impl.RemoteDuibaPayOrdersServiceImpl;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.exception.HttpException;
import com.wechat.pay.java.core.exception.MalformedMessageException;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.util.GsonUtil;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.Account;
import com.wechat.pay.java.service.refund.model.AmountReq;
import com.wechat.pay.java.service.refund.model.CreateRequest;
import com.wechat.pay.java.service.refund.model.FundsFromItem;
import com.wechat.pay.java.service.refund.model.QueryByOutRefundNoRequest;
import com.wechat.pay.java.service.refund.model.Refund;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**


 import com.google.gson.JsonObject;
 import com.wechat.pay.java.core.Config;
 import com.wechat.pay.java.core.RSAAutoCertificateConfig;
 import com.wechat.pay.java.core.exception.HttpException;
 import com.wechat.pay.java.core.exception.MalformedMessageException;
 import com.wechat.pay.java.core.exception.ServiceException;
 import com.wechat.pay.java.core.util.GsonUtil;
 import com.wechat.pay.java.service.refund.model.Account;
 import com.wechat.pay.java.service.refund.model.AmountReq;
 import com.wechat.pay.java.service.refund.model.CreateRequest;
 import com.wechat.pay.java.service.refund.model.FundsFromItem;
 import com.wechat.pay.java.service.refund.model.QueryByOutRefundNoRequest;
 import com.wechat.pay.java.service.refund.model.Refund;

 import java.util.ArrayList;
 import java.util.List;

 /** RefundService使用示例 */
public class RefundServiceExample {

    private static Logger log= LoggerFactory.getLogger(RefundServiceExample.class);

    /** 商户号 */
    public static String merchantId = "**********";
    /** 商户API私钥路径 */
    public static String privateKeyPath = "/Users/<USER>/Desktop/兑啊服务商第三个证书/**********_20230916_cert/apiclient_key.pem";
    /** 商户证书序列号 */
    public static String merchantSerialNumber = "2D3C5A4937560C811BEE2D6FA87889AF5A9E7325";
    /** 商户APIV3密钥 */
    public static String apiV3Key = "Hz9mkeZXGJ6Tes2tMHM4xjt6BWgy4BWf";

    public static RefundService service;

    public static void main(String[] args) {
        // 初始化商户配置
        Config config =
                new RSAAutoCertificateConfig.Builder()
                        .merchantId(merchantId)
                        // 使用 com.wechat.pay.java.core.util 中的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
                        .privateKeyFromPath(privateKeyPath)
                        .merchantSerialNumber(merchantSerialNumber)
                        .apiV3Key(apiV3Key)
                        .build();

        // 初始化服务
        service = new RefundService.Builder().config(config).build();
        // ... 调用接口
        try {
            Refund response = create();
            log.info("退款info {}",GsonUtil.toJson(response));
        } catch (HttpException e) { // 发送HTTP请求失败
            // 调用e.getHttpRequest()获取请求打印日志或上报监控，更多方法见HttpException定义
            log.info("退款失败",e);
        } catch (ServiceException e) { // 服务返回状态小于200或大于等于300，例如500
            // 调用e.getResponseBody()获取返回体打印日志或上报监控，更多方法见ServiceException定义
            log.info("退款失败",e);
        } catch (MalformedMessageException e) { // 服务返回成功，返回体类型不合法，或者解析返回体失败
            // 调用e.getMessage()获取信息打印日志或上报监控，更多方法见MalformedMessageException定义
            log.info("退款失败",e);
        }catch (Exception e){
            log.info("退款失败",e);
        }
    }
    /** 退款申请 */
    public static Refund create() {
        CreateRequest request = new CreateRequest();
        AmountReq amountReq = new AmountReq();
        amountReq.setTotal(1L);
        amountReq.setRefund(1L);
        amountReq.setCurrency("CNY");
//        FundsFromItem fundFromItem = new FundsFromItem();
//        fundFromItem.setAccount(Account.AVAILABLE);
//        fundFromItem.setAmount(1L);
//        List fundFromList = new ArrayList();
//        fundFromList.add(fundFromItem);
//        amountReq.setFrom(fundFromList);
        request.setAmount(amountReq);
        request.setTransactionId("4200001912202309157331783839");
        request.setOutRefundNo("RFDKTW9JQ_242504658724572C0196");
        request.setSubMchid("**********");
        request.setReason("服务商退款测试1 分");
        // 调用request.setXxx(val)设置所需参数，具体参数可见Request定义
        // 调用接口
        return service.create(request);
    }
    /** 查询单笔退款（通过商户退款单号） */
    public static Refund queryByOutRefundNo() {

        QueryByOutRefundNoRequest request = new QueryByOutRefundNoRequest();
        // 调用request.setXxx(val)设置所需参数，具体参数可见Request定义
        // 调用接口
        return service.queryByOutRefundNo(request);
    }
}
