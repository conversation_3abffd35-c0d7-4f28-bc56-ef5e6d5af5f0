package cn.com.duiba.paycenter.service.impl;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.utils.SpringEnvironmentUtils;
import cn.com.duiba.credits.sdk.CreditTool;
import cn.com.duiba.paycenter.config.WaitConfirmChargeOrderConfig;
import cn.com.duiba.paycenter.config.xib.XibPayConfig;
import cn.com.duiba.paycenter.dao.payment.ChargeOrderDao;
import cn.com.duiba.paycenter.dto.payment.WaitConfirmChargeOrderDto;
import cn.com.duiba.paycenter.dto.payment.charge.xib.charge.XibApiChargeReqData;
import cn.com.duiba.paycenter.dto.payment.charge.xib.charge.XibApiChargeRespData;
import cn.com.duiba.paycenter.dto.payment.charge.xib.charge.XibChargeRequestDTO;
import cn.com.duiba.paycenter.dto.payment.charge.xib.charge.XibChargeResponseDTO;
import cn.com.duiba.paycenter.dto.payment.charge.xib.notify.XibPayNotifyRequest;
import cn.com.duiba.paycenter.dto.payment.charge.xib.notify.XibPayNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.xib.query.XibQueryReqData;
import cn.com.duiba.paycenter.dto.payment.charge.xib.query.XibQueryResponseDTO;
import cn.com.duiba.paycenter.dto.payment.charge.xib.query.XibQueryResponseData;
import cn.com.duiba.paycenter.dto.payment.charge.xib.refund.XibApiRefundReqData;
import cn.com.duiba.paycenter.dto.payment.charge.xib.refund.XibApiRefundRespData;
import cn.com.duiba.paycenter.dto.payment.charge.xib.refund.XibRefundRequestDTO;
import cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity;
import cn.com.duiba.paycenter.entity.payment.RefundHandlerResult;
import cn.com.duiba.paycenter.entity.payment.RefundOrderEntity;
import cn.com.duiba.paycenter.enums.ChargeOrderStatusEnum;
import cn.com.duiba.paycenter.service.XibPayService;
import cn.com.duiba.paycenter.service.payment.WaitConfirmChargeOrderService;
import cn.com.duiba.paycenter.util.RedisKeyFactory;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.redis.RedisLock;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

/**
 * 厦门国际银行支付服务
 *
 * <AUTHOR>
 * @date 2022/10/24
 */
@Service
public class XibPayServiceImpl implements XibPayService {

    private static final Logger log = LoggerFactory.getLogger(XibPayServiceImpl.class);
    private static final long DEFAULT_VALID_MINUTES = 30L;

    /**
     * 币种 人民币
     */
    private final static String CNY_CODE = "CNY";

    private final static String STATUS_PAY_SUCCESS = "00000";

    private final static String STATUS_NOTICE_PAY_SUCCESS = "02";

    public final static String ORDER_TYPE = "41";

    public final static String ORDER_CODE_APPLY = "OrderCodeApply";

    public final static String CUSTOMER_CODE_REVERT = "CustomerCodeRevert";

    public final static String ORDER_PAY_STATUS_QUERY = "OrderPayStatusQuery";

    private static final String CHARACTER_ENCODE = "utf-8";
    /**
     * 厦门国际银行支付配置
     */
    @Autowired
    private XibPayConfig xibPayConfig;

    @Resource(name = "helloHttpClient")
    private CloseableHttpClient httpClient;

    @Resource(name = "redisTemplate")
    private RedisAtomicClient redisAtomicClient;

    @Resource
    private ChargeOrderDao chargeOrderDao;

    @Resource
    private WaitConfirmChargeOrderService waitConfirmChargeOrderService;

    @Resource
    private WaitConfirmChargeOrderConfig waitConfirmChargeOrderConfig;


    @Override
    public XibChargeResponseDTO doCreateCharge(XibChargeRequestDTO chargeRequest) {

        XibApiChargeReqData xibApiChargeReqData = new XibApiChargeReqData();
        // 回调地址
        xibApiChargeReqData.setNotifyUrl(StringUtils.isNotBlank(chargeRequest.getNotifyUrl()) ? chargeRequest.getNotifyUrl() : xibPayConfig.getNotifyUrl());
        // 币种
        xibApiChargeReqData.setCurrencyCode(CNY_CODE);
        // 订单类型
        xibApiChargeReqData.setOrderType(ORDER_TYPE);
        // 订单金额
        BigDecimal bigDecimal = new BigDecimal(chargeRequest.getTxnAmt());
        BigDecimal divide = bigDecimal.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        xibApiChargeReqData.setTxnAmt(divide.doubleValue());
        // 商品信息描 述
        xibApiChargeReqData.setOrderInfo(chargeRequest.getGoodsDesc());
        // 商户号
        xibApiChargeReqData.setMerchCd(xibPayConfig.getMerchants());
        // 订单编号
        xibApiChargeReqData.setMsgId(chargeRequest.getOrderNo());
        // 接口编码
        xibApiChargeReqData.setTxnType(ORDER_CODE_APPLY);
        // 生成sign
        Map<String, Object> map = BeanUtil.beanToMap(xibApiChargeReqData);
        String sign = createSign(map, xibPayConfig.getMd5Key());
        xibApiChargeReqData.setSign(sign);

        XibChargeResponseDTO xibChargeResponseDTO = new XibChargeResponseDTO();

        String url;
        if(StringUtils.isNotBlank(chargeRequest.getRedirect())) {
            url = chargeRequest.getRedirect();
        } else { // 原有逻辑
            CreditTool tool = new CreditTool(chargeRequest.getAppKey(), chargeRequest.getAppSecret());
            String orderUrl = xibPayConfig.getOrderUrl() + chargeRequest.getBizOrderNo().replace("C", "");
            Map<String, String> loginMap = Maps.newHashMap();
            loginMap.put("uid", chargeRequest.getUid());
            loginMap.put("redirect", orderUrl);
            url = tool.buildUrlWithSign(xibPayConfig.getHomeDomain(), loginMap);
        }

        try {
            String reqStr = JSONObject.toJSONString(xibApiChargeReqData);
            log.info("[厦门国际银行] 支付预下单请求入参:{}", reqStr);
            String resp = sendPost(xibPayConfig.getUrlHost(), reqStr, Charset.forName(CHARACTER_ENCODE));
            xibChargeResponseDTO.setOrderNo(chargeRequest.getOrderNo());
            log.info("[厦门国际银行] 支付预下单返回结果:{}", resp);
            if (StringUtils.isNotBlank(resp)) {
                XibApiChargeRespData xibApiChargeRespData = JSONObject.parseObject(resp, XibApiChargeRespData.class);
                if (STATUS_PAY_SUCCESS.equals(xibApiChargeRespData.getRespCode())) {
                    xibChargeResponseDTO.setSuccess(true);
                    // 下单时间，有效分钟数
                    String orderTime;
                    long validMinutes;
                    if(chargeRequest.getOrderTime() != null) {
                        orderTime = DateUtil.format(chargeRequest.getOrderTime(), DatePattern.PURE_DATETIME_PATTERN);
                        if(chargeRequest.getScanTime() != null) {
                            validMinutes = TimeUnit.MILLISECONDS.toMinutes(chargeRequest.getScanTime().getTime() - chargeRequest.getOrderTime().getTime());
                        } else {
                            validMinutes = DEFAULT_VALID_MINUTES;
                            chargeRequest.setScanTime(Date.from(Instant.ofEpochMilli(chargeRequest.getOrderTime().getTime() + TimeUnit.MINUTES.toMillis(validMinutes))));
                        }
                    } else {
                        orderTime = DateUtil.format(DateUtil.offsetMinute(chargeRequest.getScanTime(), (int) -DEFAULT_VALID_MINUTES), DatePattern.PURE_DATETIME_PATTERN);
                        validMinutes = DEFAULT_VALID_MINUTES;
                    }
                    String payUrl = xibApiChargeRespData.getPayUrl()
                            + "&orderNo=" + chargeRequest.getOrderNo()
                            + "&orderTime=" + orderTime
                            + "&validMinute=" + validMinutes
                            + "&url=" + URLEncoder.encode(url, "utf-8");
                    log.info("[厦门国际银行] 支付预下单payUrl:{}", payUrl);
                    xibChargeResponseDTO.setPayUrl(payUrl);
                } else {
                    xibChargeResponseDTO.setSuccess(false);
                    xibChargeResponseDTO.setMessage(xibApiChargeRespData.getRespMsg());
                }
            } else {
                xibChargeResponseDTO.setSuccess(false);
                xibChargeResponseDTO.setMessage("预支付请求失败");
                log.warn("[厦门国际银行] 预支付请求失败");
            }
        } catch (Exception e) {
            xibChargeResponseDTO.setSuccess(false);
            xibChargeResponseDTO.setMessage("厦门国际银行支付请求失败");
            log.warn("[厦门国际银行] 支付请求失败", e);
        }
        if (xibChargeResponseDTO.isSuccess()) {
            createWaitConfirmChargeOrder(chargeRequest);
        }

        return xibChargeResponseDTO;
    }

    /**
     * 创建下单确认记录表
     *
     * @param chargeReqDto 预支付请求
     */
    public void createWaitConfirmChargeOrder(XibChargeRequestDTO chargeReqDto) {
        //找到对应的tb_charge_order
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByOrderNo(chargeReqDto.getOrderNo());

        //已存在则不做操作
        WaitConfirmChargeOrderDto waitConfirmChargeOrderDto = waitConfirmChargeOrderService
                .findChargeByBizOrderNoAndBizTypeAndChannel(chargeReqDto.getBizOrderNo(), chargeOrderEntity.getBizType(), chargeOrderEntity.getChannelType());
        if (Objects.nonNull(waitConfirmChargeOrderDto)) {
            log.info("[厦门国际银行] WaitConfirmChargeOrder记录已存在，orderNum = {}", chargeReqDto.getOrderNo());
            return;
        }


        WaitConfirmChargeOrderDto dto = new WaitConfirmChargeOrderDto();

        dto.setAppId(chargeOrderEntity.getAppId());
        dto.setOrderNo(chargeOrderEntity.getOrderNo());
        dto.setBizOrderNo(chargeReqDto.getBizOrderNo());
        dto.setBizType(chargeOrderEntity.getBizType());
        dto.setChannelType(chargeOrderEntity.getChannelType());
        dto.setOrderType(WaitConfirmChargeOrderDto.ORDER_TYPE_CHARGE);
        dto.setOrderDate(chargeReqDto.getMerDate());
        dto.setNextTime(new Date(System.currentTimeMillis() + waitConfirmChargeOrderConfig.getRetryTimeIntervalMillis().get(0)));


        Long id = waitConfirmChargeOrderService.insert(dto);
        log.info("[厦门国际银行]预下单成功 入待查询库 id = {}, dto = {}", id, JSON.toJSONString(dto));
    }

    @Override
    public XibPayNotifyResponse orderNotify(XibPayNotifyRequest xibPayNotifyRequest) {
        log.info("厦门国际银行-支付通知入参:{}", JSON.toJSONString(xibPayNotifyRequest));
        //加锁，防止主动轮询和行方通知并发
        String key = RedisKeyFactory.K110 + xibPayNotifyRequest.getMerchOrderNum();
        try (RedisLock lock = redisAtomicClient.getLock(key, 10)) {
            if (Objects.isNull(lock)) {
                throw new BizException("网络繁忙请稍后再试");
            }
            //根据主订单号 + bizType找到tb_charge_order的记录
            ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByOrderNo(xibPayNotifyRequest.getMerchOrderNum());
            if (Objects.isNull(chargeOrderEntity)) {
                log.error(String.format("[厦门国际银行] methodName = 【%s】, 主订单 = 【%s】, chargeOrder为空", currentMethodName(), xibPayNotifyRequest.getMerchOrderNum()));
                XibPayNotifyResponse payNotifyResponse = new XibPayNotifyResponse();
                payNotifyResponse.setSuccess(false);
                return payNotifyResponse;
            }
            // 防止重复处理
            if (chargeOrderEntity.getChargeStatus() > ChargeOrderStatusEnum.PAY_FAIL.getCode()) {
                XibPayNotifyResponse payNotifyResponse = new XibPayNotifyResponse();
                payNotifyResponse.setSuccess(false);
                payNotifyResponse.setChargeSuccess(!ChargeOrderStatusEnum.PAY_FAIL.getCode().equals(chargeOrderEntity.getChargeStatus()));
                payNotifyResponse.setChargeOrderNo(chargeOrderEntity.getOrderNo());
                payNotifyResponse.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
                payNotifyResponse.setBizType(chargeOrderEntity.getBizType());
                payNotifyResponse.setTransactionNo(xibPayNotifyRequest.getTxnSeqId());
                return payNotifyResponse;
            }
            //验签
            Map<String, Object> map = BeanUtil.beanToMap(xibPayNotifyRequest);
            map.remove("sign");
            String sign = createSign(map, xibPayConfig.getMd5Key());
            if (!Objects.equals(sign, xibPayNotifyRequest.getSign())) {
                log.warn("[厦门国际银行] 验签失败 req.sign:{} sign:{},", xibPayNotifyRequest.getSign(), sign);
                XibPayNotifyResponse payNotifyResponse = new XibPayNotifyResponse();
                payNotifyResponse.setSuccess(false);
                payNotifyResponse.setChargeSuccess(!ChargeOrderStatusEnum.PAY_FAIL.getCode().equals(chargeOrderEntity.getChargeStatus()));
                payNotifyResponse.setChargeOrderNo(chargeOrderEntity.getOrderNo());
                payNotifyResponse.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
                payNotifyResponse.setBizType(chargeOrderEntity.getBizType());
                payNotifyResponse.setTransactionNo(xibPayNotifyRequest.getTxnSeqId());
                return payNotifyResponse;
            }
            //支付状态
            boolean isPaySuccess = STATUS_NOTICE_PAY_SUCCESS.equals(xibPayNotifyRequest.getTxnStatus());

            //更新记录
            boolean isUpdateSuccess = updateChargeOrder(xibPayNotifyRequest, chargeOrderEntity.getOrderNo(), isPaySuccess);

            XibPayNotifyResponse notifyResponse = buildPayNotifyResponse(xibPayNotifyRequest, isPaySuccess, isUpdateSuccess);
            log.info("[厦门国际银行] 支付通知响应 notifyResponse = {}", JSON.toJSONString(notifyResponse));
            return notifyResponse;
        } catch (Exception e) {
            log.warn("[厦门国际银行] 支付通知处理失败", e);
            XibPayNotifyResponse payNotifyResponse = new XibPayNotifyResponse();
            payNotifyResponse.setSuccess(false);
            payNotifyResponse.setChargeSuccess(false);
            return payNotifyResponse;
        }
    }

    @Override
    public RefundHandlerResult createRefund(RefundOrderEntity refundOrderEntity, ChargeOrderEntity chargeOrderEntity, XibRefundRequestDTO xibRefundRequestDTO) {

        XibApiRefundReqData xibApiChargeReqData = new XibApiRefundReqData();
        // 回调地址
//        xibApiChargeReqData.setNotifyUrl(xibPayConfig.getNotifyUrl());
        // 币种
        xibApiChargeReqData.setCurrencyCode(CNY_CODE);
        // 订单金额
        BigDecimal bigDecimal = new BigDecimal(xibRefundRequestDTO.getTxnAmt());
        BigDecimal divide = bigDecimal.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        xibApiChargeReqData.setTxnAmt(divide.doubleValue());
        // 商户号
        xibApiChargeReqData.setMerchCd(xibPayConfig.getMerchants());
        // 订单编号
        xibApiChargeReqData.setMsgId(xibRefundRequestDTO.getBizOrderNo());
        // 商户订单号
        xibApiChargeReqData.setMerOrderNum(xibRefundRequestDTO.getBizOrderNo());
        // 行方交易流水
        xibApiChargeReqData.setTxnSeqId(chargeOrderEntity.getTransactionNo());
        // 支付方式确认
        xibApiChargeReqData.setPayType("02");
        // 交易日期(交易日期传创建支付的的时间)
        xibApiChargeReqData.setTxnDate(DateUtil.format(chargeOrderEntity.getGmtCreate(), DatePattern.PURE_DATE_PATTERN));
        // 交易类型
        xibApiChargeReqData.setTransType("34");
        // 接口编码
        xibApiChargeReqData.setTxnType(CUSTOMER_CODE_REVERT);
        // 生成sign
        Map<String, Object> map = BeanUtil.beanToMap(xibApiChargeReqData);
        String sign = createSign(map, xibPayConfig.getMd5Key());
        xibApiChargeReqData.setSign(sign);

        RefundHandlerResult refundHandlerResult = new RefundHandlerResult();
        String reqStr = JSONObject.toJSONString(xibApiChargeReqData);
        try {
            log.info("厦门国际 out退款请求入参 req={}", reqStr);
            String resp = sendPost(xibPayConfig.getUrlHost(), reqStr, Charset.forName(CHARACTER_ENCODE));
            log.info("厦门国际 out退款返回数据 resp={}", resp);
            if (StringUtils.isNotBlank(resp)) {
                XibApiRefundRespData xibApiChargeRespData = JSONObject.parseObject(resp, XibApiRefundRespData.class);
                if (STATUS_PAY_SUCCESS.equals(xibApiChargeRespData.getRespCode())) {
                    refundHandlerResult.setSuccess(true);
                    refundHandlerResult.setRefundNo(xibApiChargeRespData.getRevertTxnSeqId());
                    //进件成功，记录轮询表，轮询退款订单状态
                    insertWaitConfirmRefundOrder(refundOrderEntity, chargeOrderEntity);

                } else {
                    refundHandlerResult.setSuccess(false);
                    refundHandlerResult.setFailMsg(xibApiChargeRespData.getRespMsg());
                }
            } else {
                log.warn("厦门国际 退款请求失败 req={} resp={}", reqStr, resp);
                refundHandlerResult.setSuccess(false);
                refundHandlerResult.setFailMsg("厦门国际银行退款请求失败");
            }
        } catch (Exception e) {
            log.warn("厦门国际 out退款请求异常 req={}", reqStr, e);
            refundHandlerResult.setSuccess(false);
            refundHandlerResult.setFailMsg("厦门国际银行退款请求异常");
        }
        return refundHandlerResult;
    }

    private void insertWaitConfirmRefundOrder(RefundOrderEntity refundOrderEntity, ChargeOrderEntity chargeOrderEntity) {
        WaitConfirmChargeOrderDto waitConfirmChargeOrderDto
                = waitConfirmChargeOrderService.findRefundByBizOrderNoAndBizTypeAndChannel(refundOrderEntity.getOrderNo(),
                chargeOrderEntity.getBizType(), chargeOrderEntity.getChannelType());
        if (waitConfirmChargeOrderDto != null) {
            log.info("厦门国际 记录轮询表 此记录已存在 退款订单号={}", refundOrderEntity.getOrderNo());
            return;
        }

        if (!waitConfirmChargeOrderConfig.getCustomQueryAppIds().contains(refundOrderEntity.getAppId())) {
            return;
        }

        WaitConfirmChargeOrderDto dto = new WaitConfirmChargeOrderDto();
        dto.setAppId(chargeOrderEntity.getAppId());
        dto.setOrderNo(refundOrderEntity.getOrderNo());
        dto.setBizOrderNo(refundOrderEntity.getBizOrderNo());
        dto.setBizType(chargeOrderEntity.getBizType());
        dto.setChannelType(chargeOrderEntity.getChannelType());
        dto.setOrderType(WaitConfirmChargeOrderDto.ORDER_TYPE_REFUND);
        dto.setOrderDate(new Date());
        //行方处理较慢，第一次查询延后60s开始
        dto.setNextTime(new Date(System.currentTimeMillis() + waitConfirmChargeOrderConfig.getRetryTimeIntervalMillis().get(0)));
        JSONObject extra = new JSONObject();
        extra.put("chargeNo", chargeOrderEntity.getOrderNo());
        dto.setExtra(extra.toJSONString());
        Long id = waitConfirmChargeOrderService.insert(dto);
        log.info("厦门国际 退款请求 进件成功 入库待查询库 id={} dto={}", id, JSONObject.toJSONString(dto));
    }

    @Override
    public XibPayNotifyResponse payNotify(XibPayNotifyRequest xibPayNotifyRequest) throws Exception {
        //加锁，防止主动轮询和行方通知并发
        String key = RedisKeyFactory.K112 + xibPayNotifyRequest.getMerchOrderNum();
        try (RedisLock lock = redisAtomicClient.getLock(key, 10)) {
            if (Objects.isNull(lock)) {
                throw new BizException("网络繁忙请稍后再试");
            }

            //根据主订单号 + bizType找到tb_charge_order的记录
            ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByOrderNo(xibPayNotifyRequest.getMerchOrderNum());
            if (Objects.isNull(chargeOrderEntity)) {
                log.error(String.format("[厦门国际银行] methodName = 【%s】, 主订单 = 【%s】, chargeOrder为空", currentMethodName(), xibPayNotifyRequest.getMerchOrderNum()));
                XibPayNotifyResponse payNotifyResponse = new XibPayNotifyResponse();
                payNotifyResponse.setSuccess(false);
                return payNotifyResponse;
            }

            log.info("厦门国际银行-支付通知请求 notifyRequestDto = {}", JSON.toJSONString(xibPayNotifyRequest));
            //防止重复处理
            if (chargeOrderEntity.getChargeStatus() > ChargeOrderStatusEnum.PAY_FAIL.getCode()) {
                XibPayNotifyResponse payNotifyResponse = new XibPayNotifyResponse();
                payNotifyResponse.setSuccess(false);
                payNotifyResponse.setChargeSuccess(!ChargeOrderStatusEnum.PAY_FAIL.getCode().equals(chargeOrderEntity.getChargeStatus()));
                return payNotifyResponse;
            }

            //支付状态
            boolean isPaySuccess = STATUS_NOTICE_PAY_SUCCESS.equals(xibPayNotifyRequest.getTxnStatus());

            //更新记录
            boolean isUpdateSuccess = updateChargeOrder(xibPayNotifyRequest, chargeOrderEntity.getOrderNo(), isPaySuccess);

            XibPayNotifyResponse notifyResponse = buildPayNotifyResponse(xibPayNotifyRequest, isPaySuccess, isUpdateSuccess);
            log.info("厦门国际银行-支付通知响应 notifyResponse = {}", JSON.toJSONString(notifyResponse));
            return notifyResponse;
        }
    }

    @Override
    public XibQueryResponseDTO orderQuery(String orderNo) {
        XibQueryReqData xibQueryReqData = new XibQueryReqData();
        xibQueryReqData.setMerOrderNum(orderNo);
        // 商户号
        xibQueryReqData.setMerchCd(xibPayConfig.getMerchants());
        // 订单编号
        xibQueryReqData.setMsgId(orderNo);
        // 接口编码
        xibQueryReqData.setTxnType(ORDER_PAY_STATUS_QUERY);
        // 生成sign
        Map<String, Object> map = BeanUtil.beanToMap(xibQueryReqData);
        String sign = createSign(map, xibPayConfig.getMd5Key());
        xibQueryReqData.setSign(sign);
        XibQueryResponseDTO xibQueryResponseDTO = new XibQueryResponseDTO();
        try {
            String reqStr = JSONObject.toJSONString(xibQueryReqData);
            log.info("[厦门国际银行] 查询订单入参:{}", reqStr);
            String resp = sendPost(xibPayConfig.getUrlHost(), reqStr, Charset.forName(CHARACTER_ENCODE));
            log.info("[厦门国际银行] 查询订单返回结果:{}", resp);
            if (StringUtils.isNotBlank(resp)) {
                XibQueryResponseData xibQueryResponseData = JSONObject.parseObject(resp, XibQueryResponseData.class);
                if (STATUS_PAY_SUCCESS.equals(xibQueryResponseData.getRespCode())) {
                    xibQueryResponseDTO.setSuccess(true);
                    xibQueryResponseDTO.setTxnSeqId(xibQueryResponseData.getTxnSeqId());
                    xibQueryResponseDTO.setTxnStatus(xibQueryResponseData.getTxnStatus());
                    xibQueryResponseDTO.setTxnAmt(xibQueryResponseData.getTxnAmt());
                } else {
                    xibQueryResponseDTO.setSuccess(false);
                    xibQueryResponseDTO.setTxnSeqId(xibQueryResponseData.getTxnSeqId());
                    xibQueryResponseDTO.setMessage(xibQueryResponseData.getRespMsg());
                }
            } else {
                xibQueryResponseDTO.setSuccess(false);
                xibQueryResponseDTO.setMessage("查询订单请求失败");
                log.warn("[厦门国际银行] 查询订单请求失败");
            }
        } catch (Exception e) {
            xibQueryResponseDTO.setSuccess(false);
            xibQueryResponseDTO.setMessage("查询订单请求失败");
            log.warn("[厦门国际银行] 查询订单请求失败", e);
        }
        return xibQueryResponseDTO;
    }

    private XibPayNotifyResponse buildPayNotifyResponse(XibPayNotifyRequest xibPayNotifyRequest, boolean isPaySuccess, boolean isUpdateSuccess) {

        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByOrderNo(xibPayNotifyRequest.getMerchOrderNum());

        XibPayNotifyResponse payNotifyResponse = new XibPayNotifyResponse();
        payNotifyResponse.setSuccess(isUpdateSuccess);
        payNotifyResponse.setChargeSuccess(isPaySuccess);
        payNotifyResponse.setChargeOrderNo(chargeOrderEntity.getOrderNo());
        payNotifyResponse.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
        payNotifyResponse.setBizType(chargeOrderEntity.getBizType());
        payNotifyResponse.setTransactionNo(xibPayNotifyRequest.getTxnSeqId());

        return payNotifyResponse;
    }

    /**
     * 更新chargeOrder记录
     *
     * @param notifyRequestDto 行方通知请求
     * @param orderNo          订单号
     * @param isPaySuccess     true: 支付成功
     */
    private boolean updateChargeOrder(XibPayNotifyRequest notifyRequestDto, String orderNo, boolean isPaySuccess) {
        ChargeOrderEntity updateEntity = new ChargeOrderEntity();
        updateEntity.setOrderNo(orderNo);
        if (isPaySuccess) {
            updateEntity.setPaidTime(new Date());
            updateEntity.setChargeStatus(ChargeOrderStatusEnum.PAY_SUCCESS.getCode());
            updateEntity.setTransactionNo(notifyRequestDto.getTxnSeqId());
        } else {
            updateEntity.setChargeStatus(ChargeOrderStatusEnum.PAY_FAIL.getCode());
            //失败信息
            updateEntity.setFailureMsg(notifyRequestDto.getPayCard());
        }

        if (SpringEnvironmentUtils.isTestEnv() || SpringEnvironmentUtils.isPreEnv()) {
            log.info("[厦门国际银行] 支付通知 更新tb_charge_order updateEntity = {}", JSON.toJSONString(updateEntity));
        }
        return chargeOrderDao.update(updateEntity) > 0;
    }

    /**
     * 获取方法名
     *
     * @return 方法名
     */
    private String currentMethodName() {
        StackTraceElement stackTraceElement = Thread.currentThread().getStackTrace()[2];
        return String.format("%s#%s", stackTraceElement.getClassName(), stackTraceElement.getMethodName());
    }

    /**
     * 创建签名
     *
     * @param parameters 入参
     * @param key        关键
     * @return {@link String}
     */
    public String createSign(Map<String, Object> parameters, String key) {
        StringBuffer sbkey = new StringBuffer();
        //1.将所有参与传参的参数按照accsii排序（升序)
        TreeMap<String, Object> treeMap = Maps.newTreeMap();
        treeMap.putAll(parameters);
        Set<Map.Entry<String, Object>> es = treeMap.entrySet();
        for (Map.Entry<String, Object> entry : es) {
            String k = entry.getKey();
            Object v = entry.getValue();
            //2.空值不传递，不参与签名组串
            if (null != v && !"".equals(v)) {
                sbkey.append(k).append("=").append(v).append("&");
            }
        }
        sbkey.append("key=").append(key);
        log.info("[厦门国际银行] sign加密前:{}", sbkey);
        //3.MD5加密,结果转换为大写字符
        String sign = MD5.create().digestHex(sbkey.toString()).toUpperCase();
        log.info("[厦门国际银行] sign加密后数据:{}", sign);
        return sign;
    }


    /**
     * 发送post请求
     *
     * @param url      请求地址
     * @param jsonStr  请求参数
     * @param encoding 编码
     * @return 请求结果
     */
    private String sendPost(String url, String jsonStr, Charset encoding) {
        String resp = "";
        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(RequestConfig.custom().
                setSocketTimeout(5000).
                setConnectionRequestTimeout(100).
                build());
        StringEntity requestEntity = new StringEntity(jsonStr, CHARACTER_ENCODE);
        requestEntity.setContentEncoding(CHARACTER_ENCODE);
        httpPost.setHeader("Content-type", "application/json");
        httpPost.setEntity(requestEntity);
        try (CloseableHttpResponse response = getHttpClient().execute(httpPost)) {
            resp = EntityUtils.toString(response.getEntity(), encoding);
        } catch (Exception e) {
            log.warn("厦门国际 发送post请求失败 url={} params={} error={}", url, jsonStr, e.getMessage(), e);
        }
        return resp;
    }

    /**
     * 获取httpClient
     * @return
     */
    private CloseableHttpClient getHttpClient() {
        if(SpringEnvironmentUtils.isProdEnv()) {
            return httpClient;
        }
        try {
            SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
            X509TrustManager tm = new X509TrustManager() {
                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }

                @Override
                public void checkServerTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
                }

                @Override
                public void checkClientTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
                }
            };
            sslContext.init(null, new TrustManager[] { tm }, null);
            return HttpClients.custom()
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .setSSLContext(sslContext)
                    .build();
        } catch (Exception e) {
            log.warn("[厦门国际银行] 获取httpClient异常", e);
            return httpClient;
        }
    }
}
