package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.xib.notify.XibPayNotifyRequest;
import cn.com.duiba.paycenter.dto.payment.charge.xib.notify.XibPayNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.xib.query.XibQueryResponseDTO;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteXibPayNotifyService;
import cn.com.duiba.paycenter.service.XibPayService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 厦门国际银行支付通知服务
 *
 * <AUTHOR>
 * @date 2022/10/25
 */
@RestController
public class RemoteXibPayNotifyServiceImpl implements RemoteXibPayNotifyService {

    @Resource
    private XibPayService xibPayService;

    @Override
    public XibPayNotifyResponse orderNotify(XibPayNotifyRequest xibPayNotifyRequest) throws BizException {
        return xibPayService.orderNotify(xibPayNotifyRequest);
    }

    @Override
    public XibQueryResponseDTO orderQuery(String orderCode){
        return xibPayService.orderQuery(orderCode);
    }
}
