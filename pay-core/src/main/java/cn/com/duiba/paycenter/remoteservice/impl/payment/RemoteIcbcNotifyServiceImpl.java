package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.netflix.feign.AdvancedFeignClient;
import cn.com.duiba.paycenter.dto.payment.charge.abc.AbcChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.IcbcChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.elife.IcbcELifeNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteIcbcNotifyService;
import cn.com.duiba.paycenter.service.payment.AbcNotifyService;
import cn.com.duiba.paycenter.service.payment.IcbcNotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
public class RemoteIcbcNotifyServiceImpl implements RemoteIcbcNotifyService {
    @Autowired
    private IcbcNotifyService icbcNotifyService;


    @Override
    public IcbcChargeNotifyResponse orderNotify(String notifyUrl, Map<String, String> map) throws BizException {
        return  icbcNotifyService.orderNotify(notifyUrl, map);
    }

    @Override
    public IcbcELifeNotifyResponse payNotify(String notifyParam) throws BizException {
        return icbcNotifyService.payNotify(notifyParam);
    }
}
