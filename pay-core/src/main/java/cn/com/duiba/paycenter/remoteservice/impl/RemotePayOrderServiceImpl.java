package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.dto.PayOrderDto;
import cn.com.duiba.paycenter.enums.PayOrderBizTypeEnum;
import cn.com.duiba.paycenter.remoteservice.RemotePayOrderService;
import cn.com.duiba.paycenter.service.PayOrderService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * author z<PERSON><PERSON><PERSON>
 * date 2018-12-29-14:00
 */
@RestController
public class RemotePayOrderServiceImpl implements RemotePayOrderService {

    @Resource
    private PayOrderService payOrderService;

    @Override
    public PayOrderDto findByExecutorBizId(String executorBizId) {
        return payOrderService.findByExecutorBizId(executorBizId);
    }

    @Override
    public List<PayOrderDto> findByBizTypeAndBizNoList(PayOrderBizTypeEnum bizType, List<String> bizNoList) {
        return payOrderService.findByBizTypeAndBizNoList(bizType,bizNoList);
    }

    @Override
    public Integer updateById(PayOrderDto dto){
        return payOrderService.updateById(dto);
    }
}
