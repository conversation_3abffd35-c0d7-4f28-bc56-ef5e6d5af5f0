package cn.com.duiba.paycenter.remoteservice.impl.duibaaccount;

import cn.com.duiba.paycenter.dto.duibaaccount.AppAccountDetailDto;
import cn.com.duiba.paycenter.dto.duibaaccount.AppAccountDetailPageDto;
import cn.com.duiba.paycenter.dto.duibaaccount.RechargeDetailPageDto;
import cn.com.duiba.paycenter.params.duibaaccount.AppAccountDetailQryParams;
import cn.com.duiba.paycenter.params.duibaaccount.RechargeDetailQryParams;
import cn.com.duiba.paycenter.remoteservice.duibaaccount.RemoteAppAccountDetailService;
import cn.com.duiba.paycenter.service.duibaaccount.AppAccountDetailService;
import cn.com.duiba.wolf.utils.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * author z<PERSON><PERSON><PERSON>
 * date 2018-11-27-13:54
 */
@RestController
public class RemoteAppAccountDetailServiceImpl implements RemoteAppAccountDetailService {

    @Resource
    private AppAccountDetailService appAccountDetailService;

    @Override
    public List<AppAccountDetailPageDto> find4page(AppAccountDetailQryParams params) {
        return appAccountDetailService.find4Page(params);
    }

    @Override
    public Long count4page(AppAccountDetailQryParams params) {
        return appAccountDetailService.count4page(params);
    }

    @Override
    public AppAccountDetailDto findByIdAndAppId(Long id, Long appId) {
        return BeanUtils.copy(appAccountDetailService.findByIdAndAppId(id, appId),AppAccountDetailDto.class);
    }

    @Override
    public List<AppAccountDetailDto> findByRelationAndAppId(String relationId, List<Integer> relationTypes, Long appId) {
        return BeanUtils.copyList(appAccountDetailService.findByRelationAndAppId(relationId,relationTypes, appId),AppAccountDetailDto.class);
    }

    @Override
    public List<AppAccountDetailPageDto> find4Export(AppAccountDetailQryParams params) {
        return appAccountDetailService.find4Export(params);
    }

    @Override
    public List<RechargeDetailPageDto> findPage4recharge(RechargeDetailQryParams params) {
        return appAccountDetailService.findPage4recharge(params);
    }

    @Override
    public Integer findCount4recharge(RechargeDetailQryParams params) {
        return appAccountDetailService.findCount4recharge(params);
    }
}
