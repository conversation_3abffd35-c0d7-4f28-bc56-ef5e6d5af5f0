package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.WxCouponResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxCouponSendRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.coupon.WxCouponQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.coupon.WxCouponQueryResp;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteWechatCouponService;
import cn.com.duiba.paycenter.service.payment.WechatCouponService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class RemoteWechatCouponServiceImpl implements RemoteWechatCouponService {

    @Autowired
    private WechatCouponService wechatCouponService;


    @Override
    public String getAccessUrl(String subject, String redirectUrl) {
        return wechatCouponService.getAccessUrl(subject,redirectUrl);
    }

    @Override
    public WxCouponQueryResp getWechatActivityInfo(WxCouponQueryRequest couponQueryRequest) {
        return wechatCouponService.getWechatActivityInfo(couponQueryRequest);
    }

    @Override
    public WxCouponResponse sendWxCouponByWxSubject(WxCouponSendRequest request) throws BizException {
        return wechatCouponService.sendWxCouponByWxSubject(request);
    }

    @Override
    public String queryWechatUserId(String subject, String authCode) {
        return wechatCouponService.queryWechatUserId(subject,authCode);
    }

    @Override
    public String queryWxAppIdBySubject(String subject) {
        return wechatCouponService.queryWxAppIdBySubject(subject);
    }
}
