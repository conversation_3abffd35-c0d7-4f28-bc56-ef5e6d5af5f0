package cn.com.duiba.paycenter.service.payment.impl;

import cn.com.duiba.api.enums.SubjectTypeEnum;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.developer.center.api.remoteservice.RemoteSubjectRecordService;
import cn.com.duiba.paycenter.config.Duia2WxpayConfig;
import cn.com.duiba.paycenter.config.DuibaWxpayConfig;
import cn.com.duiba.paycenter.config.DuijieWxpayConfig;
import cn.com.duiba.paycenter.config.FjgwWxpayConfig;
import cn.com.duiba.paycenter.dao.DsConstants;
import cn.com.duiba.paycenter.dao.payment.WxPayConfigDao;
import cn.com.duiba.paycenter.dto.payment.config.WxPayConfigDto;
import cn.com.duiba.paycenter.entity.payment.WxPayConfigEntity;
import cn.com.duiba.paycenter.enums.ChannelEnum;
import cn.com.duiba.paycenter.enums.ChannelModeEnum;
import cn.com.duiba.paycenter.enums.ChannelStatusEnum;
import cn.com.duiba.paycenter.params.miniProgram.MiniProgramConfigParam;
import cn.com.duiba.paycenter.service.payment.WxPayConfigService;
import cn.com.duiba.paycenter.util.Conditions;
import cn.com.duiba.paycenter.util.RedisKeyFactory;
import cn.com.duiba.wolf.cache.AdvancedCacheClient;
import cn.com.duiba.wolf.utils.BeanUtils;
import com.google.common.base.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/12/01
 */
@Service
@Slf4j
public class WxPayConfigServiceImpl implements WxPayConfigService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WxPayConfigServiceImpl.class);
    /**
     * 1%
     */
    private static final int RATE = 100;
    private static final String STAR = "****";
    @Resource
    private WxPayConfigDao wxPayConfigDao;
    @Resource
    private Validator validator;
    @Resource(name = "redisTemplate")
    private AdvancedCacheClient advancedCacheClient;
    @Resource
    private DuibaWxpayConfig duibaWxpayConfig;
    @Resource
    private Duia2WxpayConfig duia2WxpayConfig;
    @Resource
    private DuijieWxpayConfig duijieWxpayConfig;
    @Resource
    private FjgwWxpayConfig fjgwWxpayConfig;

    @Resource
    private RemoteAppService remoteAppService;

    @Resource
    private RemoteSubjectRecordService remoteSubjectRecordService;

    @Override
    public List<WxPayConfigDto> listByAppId(Long appId) throws BizException {
        if (appId == null) {
            throw new BizException("appId 不能为空");
        }
        List<WxPayConfigEntity> wxPayConfigEntityList = wxPayConfigDao.listByAppId(appId);
        //如果没有默认的配置需要生成默认的配置
        if (CollectionUtils.isEmpty(wxPayConfigEntityList)) {
            //1 微信H5支付配置
            WxPayConfigEntity wxPayWap = new WxPayConfigEntity();
            wxPayWap.setAppId(appId);
            wxPayWap.setChannelMode(ChannelModeEnum.DUIBA.getCode());
            wxPayWap.setChannelStatus(ChannelStatusEnum.ENABLE.getCode());
            wxPayWap.setChannelType(ChannelEnum.WX_WAP.getChannelType());
            wxPayWap.setChannelName(ChannelEnum.WX_WAP.getChannelName());
            wxPayWap.setRate(RATE);
            wxPayConfigEntityList.add(wxPayWap);

            WxPayConfigEntity wxPayMp = new WxPayConfigEntity();
            wxPayMp.setAppId(appId);
            wxPayMp.setChannelMode(ChannelModeEnum.DUIBA.getCode());
            wxPayMp.setChannelStatus(ChannelStatusEnum.ENABLE.getCode());
            wxPayMp.setChannelType(ChannelEnum.WX_PUB.getChannelType());
            wxPayMp.setChannelName(ChannelEnum.WX_PUB.getChannelName());
            wxPayMp.setRate(RATE);

            wxPayConfigEntityList.add(wxPayMp);
            wxPayConfigDao.batchInsert(wxPayConfigEntityList);
        }
        //不返回敏感信息不需要解密
        return wxPayConfigEntityList
                .stream()
                .map(wxPayConfigEntity -> convert2Dto(wxPayConfigEntity, true))
                .collect(Collectors.toList());
    }

    @Transactional(value = DsConstants.DATABASE_PAYMENT, rollbackFor = Exception.class)
    @Override
    public boolean update(WxPayConfigDto wxPayConfigDto) throws BizException {
        Set<ConstraintViolation<WxPayConfigDto>> errorSet = validator.validate(wxPayConfigDto);
        if (!errorSet.isEmpty()) {
            throw new BizException(errorSet.iterator().next().getMessage());
        }
        WxPayConfigEntity entity = wxPayConfigDao.findById(wxPayConfigDto.getId());
        if (entity == null) {
            throw new BizException("配置不存在");
        }
        if (ChannelModeEnum.INDEPENDENT.getCode().equals(wxPayConfigDto.getChannelMode())) {
            //改为自有模式需要判断必填项
            check(entity, wxPayConfigDto);
        }
        //不管是否成功先删除redis缓存
        advancedCacheClient.remove(getChannelRedisKey(entity.getAppId(), entity.getChannelType())
                , getWxRedisKey(entity.getWxAppId(), entity.getMchId()));
        if (wxPayConfigDto.getChannelMode() == null &&
                StringUtils.isBlank(wxPayConfigDto.getMchId()) && StringUtils.isBlank(wxPayConfigDto.getWxAppId())) {
            //如果不更改appid和mchid，可以直接更新配置
            return wxPayConfigDao.update(BeanUtils.copy(wxPayConfigDto, WxPayConfigEntity.class));
        } else {
            return updateWxPayConfig(wxPayConfigDto, entity);
        }
    }

    @Override
    public WxPayConfigDto findByAppIdAndChannelType(Long appId, String channelType) {
        if (appId == null || StringUtils.isBlank(channelType)) {
            return null;
        }
        String redisKey = getChannelRedisKey(appId, channelType);

        WxPayConfigDto wxPayConfigDto = advancedCacheClient.getWithCacheLoader(
                redisKey
                , 1
                , TimeUnit.DAYS
                , true
                , () -> convert2Dto(wxPayConfigDao.findByAppIdAndChannelType(appId, channelType), false));
        if (wxPayConfigDto == null && ChannelEnum.WX_LITE.getChannelType().equals(channelType)) {
            return null;
        }

        if (wxPayConfigDto != null && ChannelEnum.WX_LITE.getChannelType().equals(channelType)) {
            // 查询主体
            AppSimpleDto simpleApp = remoteAppService.getSimpleApp(wxPayConfigDto.getAppId()).getResult();
            String subjectType = remoteSubjectRecordService.getSubjectByDevelopIdAndData(simpleApp.getDeveloperId(), new Date());
            // 填充对应最新主体的商户配置
            fillDuibaWxPayConfig(subjectType, wxPayConfigDto);
            return wxPayConfigDto;
        }


        //如果没有配置则使用默认配置兑吧代收
        //如果有配置且是兑吧代收的
        //都使用兑吧的配置
        //兑吧配置兜底，不存在没有配置的情况
        if (wxPayConfigDto == null || ChannelModeEnum.DUIBA.getCode().equals(wxPayConfigDto.getChannelMode())) {
            wxPayConfigDto = duibaWxpayConfig.getDuibaWxpayConfig();

            wxPayConfigDto.setChannelType(channelType);
            wxPayConfigDto.setAppId(appId);
        }
        return wxPayConfigDto;
    }

    @Override
    public WxPayConfigDto findByWxAppIdAndMchId(String wxAppId, String mchId) throws BizException {
        if (StringUtils.isBlank(wxAppId) || StringUtils.isBlank(mchId)) {
            return null;
        }
        WxPayConfigDto wxPayConfigDto = advancedCacheClient
                .getWithCacheLoader(
                        getWxRedisKey(wxAppId, mchId)
                        , 1
                        , TimeUnit.DAYS
                        , true
                        , () -> convert2Dto(wxPayConfigDao.findByWxAppIdAndMchId(wxAppId, mchId), false));

        // 小程序环境使用代收模式（旧版兑吧代收数据库里配置了mchId）
        if (wxPayConfigDto != null && ChannelModeEnum.DUIBA.getCode().equals(wxPayConfigDto.getChannelMode()) && ChannelEnum.WX_LITE.getChannelType().equals(wxPayConfigDto.getChannelType())) {
            return wxPayConfigDto;
        }

        // 这里为空只可能是没有微信支付配置，或者配置了兑吧代收（新版兑吧代收不需要配置mchId，根据主体动态获取）
        if (wxPayConfigDto == null) {
            wxPayConfigDto = convert2Dto(wxPayConfigDao.findByAppIdAndChannelTypeAndWxAppId(null, ChannelEnum.WX_LITE.getChannelType(), wxAppId), false);
            if (wxPayConfigDto != null && ChannelModeEnum.DUIBA.getCode().equals(wxPayConfigDto.getChannelMode())) {
                // 查询主体
                AppSimpleDto simpleApp = remoteAppService.getSimpleApp(wxPayConfigDto.getAppId()).getResult();
                Conditions.expectTrue(simpleApp != null, "该兑吧应用不存在");
                String subjectType = remoteSubjectRecordService.getSubjectByDevelopIdAndData(simpleApp.getDeveloperId(), new Date());
                // 填充对应最新主体的商户配置
                fillDuibaWxPayConfig(subjectType, wxPayConfigDto);
                advancedCacheClient.set(
                        getWxRedisKey(wxAppId, wxPayConfigDto.getMchId()),
                        wxPayConfigDto,
                        1,
                        TimeUnit.DAYS);
            } else {
                wxPayConfigDto = null;
            }
        }

        //如果是代收模式数据没有存配置需显式设置
        if (wxPayConfigDto == null ||
                (ChannelModeEnum.DUIBA.getCode().equals(wxPayConfigDto.getChannelMode())) && !ChannelEnum.WX_LITE.getChannelType().equals(wxPayConfigDto.getChannelType())) {
            wxPayConfigDto = duibaWxpayConfig.getDuibaWxpayConfig();

            //如果找不到之前的配置则无法解密报错
            if (!wxPayConfigDto.getWxAppId().equals(wxAppId) || !wxPayConfigDto.getMchId().equals(mchId)) {
                throw new BizException("找不到wxAppId=" + wxAppId + ",mchId=" + mchId + "的配置");
            }
        }
        return wxPayConfigDto;
    }

    @Override
    public Integer getChannelMode(Long appId, String channelType) {
        if (appId == null || StringUtils.isBlank(channelType)) {
            return null;
        }

        WxPayConfigDto wxPayConfigDto = advancedCacheClient.getWithCacheLoader(
                getChannelRedisKey(appId, channelType)
                , 1
                , TimeUnit.DAYS
                , true
                , () -> convert2Dto(wxPayConfigDao.findByAppIdAndChannelType(appId, channelType), false));
        if (wxPayConfigDto == null) {
            return ChannelModeEnum.DUIBA.getCode();
        }
        return wxPayConfigDto.getChannelMode();
    }

    @Override
    public WxPayConfigDto findByAppIdAndChannelTypeSubjectType(Long appId, String channelType, String subjectType) {
        if (appId == null || StringUtils.isBlank(channelType)) {
            return null;
        }
        String redisKey = getChannelRedisKey(appId, channelType);

//        WxPayConfigDto wxPayConfigDto = convert2Dto(wxPayConfigDao.findByAppIdAndChannelType(appId, channelType), false);
        WxPayConfigDto wxPayConfigDto = advancedCacheClient.getWithCacheLoader(
                redisKey
                , 1
                , TimeUnit.DAYS
                , true
                , () -> convert2Dto(wxPayConfigDao.findByAppIdAndChannelType(appId, channelType), false));

        //如果没有配置则使用默认配置兑吧代收
        //如果有配置且是兑吧代收的
        //都使用兑吧的配置
        //兑吧配置兜底，不存在没有配置的情况
        //小程序支付只能使用服务商模式和自有模式
        wxPayConfigDto = getWxPayConfigDto(appId, channelType, subjectType, wxPayConfigDto);
        return wxPayConfigDto;
    }

    @Override
    public WxPayConfigDto findWxPayConfigByAppIdAndChannelTypeSubjectType(Long appId, String channelType) {
        if (appId == null || StringUtils.isBlank(channelType)) {
            return null;
        }
        String redisKey = getChannelRedisKey(appId, channelType);

//        WxPayConfigDto wxPayConfigDto = convert2Dto(wxPayConfigDao.findByAppIdAndChannelType(appId, channelType), false);
        WxPayConfigDto wxPayConfigDto = advancedCacheClient.getWithCacheLoader(
                redisKey
                , 1
                , TimeUnit.DAYS
                , true
                , () -> convert2Dto(wxPayConfigDao.findByAppIdAndChannelType(appId, channelType), false));
        return wxPayConfigDto;
    }

    @Override
    public WxPayConfigDto findByAppIdAndChannelTypeSubjectTypeWxAppId(Long appId, String channelType, String subjectType,String wxAppId) {
        if (appId == null || StringUtils.isBlank(channelType)) {
            return null;
        }
        String redisKey = getChannelWithWxAppIdRedisKey(appId, channelType,wxAppId);

        WxPayConfigDto wxPayConfigDto = advancedCacheClient.getWithCacheLoader(
                redisKey
                , 1
                , TimeUnit.DAYS
                , true
                , () -> convert2Dto(wxPayConfigDao.findByAppIdAndChannelTypeAndWxAppId(appId, channelType,wxAppId), false));
        if (wxPayConfigDto == null && ChannelEnum.WX_LITE.getChannelType().equals(channelType)) {
            return null;
        }
        wxPayConfigDto = getWxPayConfigDto(appId, channelType, subjectType, wxPayConfigDto);
        return wxPayConfigDto;
    }

    @Override
    public Integer updateMiniProgramConfigById(MiniProgramConfigParam miniProgramConfigParam)  throws BizException{
        WxPayConfigEntity wxPayConfigEntity = wxPayConfigDao.findById(miniProgramConfigParam.getId());

        Conditions.expectTrue(wxPayConfigEntity !=null ,"原记录不存在，无法修改");
        Conditions.expectTrue(wxPayConfigEntity.getAppId().equals(miniProgramConfigParam.getDuibaAppId()),"修改应用与配置应用id不一致");
        Conditions.expectTrue(ChannelEnum.WX_LITE.getChannelType().equals( wxPayConfigEntity.getChannelType()),"只支持微信小程序渠道修改");


        WxPayConfigEntity configEntity = wxPayConfigDao.findByAppIdAndChannelTypeAndWxAppId(miniProgramConfigParam.getDuibaAppId(), ChannelEnum.WX_LITE.getChannelType(), miniProgramConfigParam.getWxAppId());

        //判断变更的wxappid 是否已被使用
        if(miniProgramConfigParam.getWxAppId()!=null &&
                !wxPayConfigEntity.getWxAppId().equals(miniProgramConfigParam.getWxAppId()) &&
                configEntity!=null && !configEntity.getId().equals(wxPayConfigEntity.getId())){
            throw new BizException("微信wxAppid已被应用"+configEntity.getAppId()+"占用");
        }


        // 只允许修改部分字段
        WxPayConfigDto  wxPayConfigSaveDto = new WxPayConfigDto();
        wxPayConfigSaveDto.setChannelMode(miniProgramConfigParam.getChannelMode());
        wxPayConfigSaveDto.setApiKey(miniProgramConfigParam.getApiKey());
        wxPayConfigSaveDto.setApiCert(miniProgramConfigParam.getApiCert());
        wxPayConfigSaveDto.setMchId(miniProgramConfigParam.getMurId());
        wxPayConfigSaveDto.setWxAppId(miniProgramConfigParam.getWxAppId());
        wxPayConfigSaveDto.setAppSecret(miniProgramConfigParam.getWxAppSercert());
        wxPayConfigSaveDto.setId(wxPayConfigEntity.getId());

        return update(wxPayConfigSaveDto) ? 1 : 0;
    }

    @Override
    public Integer insertMiniProgram(MiniProgramConfigParam miniProgramConfigParam)  throws BizException{

        WxPayConfigEntity configEntity = wxPayConfigDao.findByAppIdAndChannelTypeAndWxAppId(miniProgramConfigParam.getDuibaAppId(), ChannelEnum.WX_LITE.getChannelType(), miniProgramConfigParam.getWxAppId());

        Conditions.expectTrue(configEntity==null,"微信wxappid已被"+miniProgramConfigParam.getDuibaAppId()+"占用");

        configEntity  = wxPayConfigDao.findByAppIdAndChannelType(miniProgramConfigParam.getDuibaAppId(), ChannelEnum.WX_LITE.getChannelType());

        Conditions.expectTrue(configEntity == null,"该应用已入库");

        WxPayConfigEntity entity = new WxPayConfigEntity();
        entity.setChannelType(ChannelEnum.WX_LITE.getChannelType());
        entity.setChannelName("微信小程序支付");
        entity.setChannelMode(miniProgramConfigParam.getChannelMode());
        entity.setChannelStatus(ChannelStatusEnum.ENABLE.getCode());
        entity.setWxAppId(miniProgramConfigParam.getWxAppId());
        entity.setAppId(miniProgramConfigParam.getDuibaAppId());
        entity.setMchId(miniProgramConfigParam.getMurId());
        entity.setApiKey(miniProgramConfigParam.getApiKey());
        entity.setRate(miniProgramConfigParam.getRate());
        entity.setAppSecret(miniProgramConfigParam.getWxAppSercert());
        entity.setApiCert(miniProgramConfigParam.getApiCert());
        //失效之前查询的key
        inValideCacheKey(entity);
        return wxPayConfigDao.insert(entity) ? 1 :0;
    }

    @Override
    public void inValideCacheKey(Long id) {
        WxPayConfigEntity wxPayConfigEntity = wxPayConfigDao.findById(id);
        if(wxPayConfigEntity == null){
            return;
        }
        inValideCacheKey(wxPayConfigEntity);
    }

    @NotNull
    private WxPayConfigDto getWxPayConfigDto(Long appId, String channelType, String subjectType, WxPayConfigDto wxPayConfigDto) {
        //如果没有配置则使用默认配置兑吧代收
        //如果有配置且是兑吧代收的
        //都使用兑吧的配置
        //兑吧配置兜底，不存在没有配置的情况
        //小程序支付只能使用服务商模式和自有模式
        if (wxPayConfigDto == null || ChannelModeEnum.DUIBA.getCode().equals(wxPayConfigDto.getChannelMode())) {
            wxPayConfigDto = getDuibaWxPayConfigDto(ChannelModeEnum.DUIBA.getCode(), subjectType);
            if (wxPayConfigDto == null) {
                LOGGER.error("WxPayConfigService findByAppIdAndChannelType 未获取到相应的业务主体信息，默认走兑吧配置，appId={}, channelType={}, subjectType={}", appId, channelType, subjectType);
                wxPayConfigDto = duibaWxpayConfig.getDuibaWxpayConfig();
            }
            wxPayConfigDto.setChannelType(channelType);
            wxPayConfigDto.setAppId(appId);
        }
        return wxPayConfigDto;
    }

    public WxPayConfigDto getDuibaWxPayConfigDto(Integer channelMode, String subjectType) {
        WxPayConfigDto wxPayConfigDto = null;
        if (ChannelModeEnum.DUIBA.getCode().equals(channelMode)) {
            if (Objects.equal(SubjectTypeEnum.DUIBA.getType(), subjectType)) {
                wxPayConfigDto = duibaWxpayConfig.getDuibaWxpayConfig();
            } else if (Objects.equal(SubjectTypeEnum.DUIA.getType(), subjectType)) {
                wxPayConfigDto = duia2WxpayConfig.getDuibaWxpayConfig();
            } else if (Objects.equal(SubjectTypeEnum.DUIJIE.getType(), subjectType)) {
                wxPayConfigDto = duijieWxpayConfig.getDuibaWxpayConfig();
            } else if (Objects.equal(SubjectTypeEnum.FUJIAN_GUANWEN.getType(), subjectType)) {
                wxPayConfigDto = fjgwWxpayConfig.getDuibaWxpayConfig();
            }
        } else if (ChannelModeEnum.SERVICE_PROVIDER.getCode().equals(channelMode)) {
            if (Objects.equal(SubjectTypeEnum.DUIBA.getType(), subjectType)) {
                wxPayConfigDto = duibaWxpayConfig.getServiceProviderConfig();
            } else if (Objects.equal(SubjectTypeEnum.DUIA.getType(), subjectType)) {
                wxPayConfigDto = duia2WxpayConfig.getServiceProviderConfig();
            } else if (Objects.equal(SubjectTypeEnum.DUIJIE.getType(), subjectType)) {
                wxPayConfigDto = duijieWxpayConfig.getServiceProviderConfig();
            } else if (Objects.equal(SubjectTypeEnum.FUJIAN_GUANWEN.getType(), subjectType)) {
                wxPayConfigDto = fjgwWxpayConfig.getServiceProviderConfig();
            }
        }
        return wxPayConfigDto;
    }

    @Override
    public void fillDuibaWxPayConfig(String subjectType, WxPayConfigDto wxPayConfigDto) {
        if (wxPayConfigDto == null || wxPayConfigDto.getChannelMode() == null || !ChannelEnum.WX_LITE.getChannelType().equals(wxPayConfigDto.getChannelType())) {
            return;
        }

        WxPayConfigDto duibaWxPayConfigDto = getDuibaWxPayConfigDto(wxPayConfigDto.getChannelMode(), subjectType);
        if (duibaWxPayConfigDto == null) {
            return;
        }

        // 填充对应最新主体的商户配置
        if (ChannelModeEnum.DUIBA.getCode().equals(wxPayConfigDto.getChannelMode())) {
            wxPayConfigDto.setMchId(duibaWxPayConfigDto.getMchId());
            wxPayConfigDto.setApiKey(duibaWxPayConfigDto.getApiKey());
            wxPayConfigDto.setApiV3Key(duibaWxPayConfigDto.getApiV3Key());
            wxPayConfigDto.setApiCert(duibaWxPayConfigDto.getApiCert());
        } else if (ChannelModeEnum.SERVICE_PROVIDER.getCode().equals(wxPayConfigDto.getChannelMode())) {
            wxPayConfigDto.setApiKey(duibaWxPayConfigDto.getApiKey());
            wxPayConfigDto.setApiV3Key(duibaWxPayConfigDto.getApiV3Key());
            wxPayConfigDto.setApiCert(duibaWxPayConfigDto.getApiCert());
        }
    }

    @Override
    public WxPayConfigDto findByWxAppIdAndMchIdBySubjectTypeForWxLite(String wxAppId, String mchId, String subjectType) throws BizException {
        if (StringUtils.isBlank(wxAppId) || StringUtils.isBlank(mchId)) {
            return null;
        }
        WxPayConfigDto wxPayConfigDto = advancedCacheClient
                .getWithCacheLoader(
                        getWxRedisKey(wxAppId, mchId)
                        , 1
                        , TimeUnit.DAYS
                        , true
                        , () -> convert2Dto(wxPayConfigDao.findByWxAppIdAndMchId(wxAppId, mchId), false));

        //1. 判断当前是否属于自由支付模式
        if (wxPayConfigDto != null && ChannelModeEnum.INDEPENDENT.getCode().equals(wxPayConfigDto.getChannelMode())) {
            return wxPayConfigDto;
        }

        // 小程序环境使用代收模式（旧版兑吧代收数据库里配置了mchId）
        if (wxPayConfigDto != null && ChannelModeEnum.DUIBA.getCode().equals(wxPayConfigDto.getChannelMode()) && ChannelEnum.WX_LITE.getChannelType().equals(wxPayConfigDto.getChannelType())) {
            return wxPayConfigDto;
        }

        // 这里为空只可能是没有微信支付配置，或者配置了兑吧代收（兑吧代收不需要配置mchId，根据主体动态获取）
        if (wxPayConfigDto == null) {
            wxPayConfigDto = convert2Dto(wxPayConfigDao.findByAppIdAndChannelTypeAndWxAppId(null, ChannelEnum.WX_LITE.getChannelType(), wxAppId), false);
            if (wxPayConfigDto != null && ChannelModeEnum.DUIBA.getCode().equals(wxPayConfigDto.getChannelMode())) {
                // 填充对应最新主体的商户配置
                fillDuibaWxPayConfig(subjectType, wxPayConfigDto);
                advancedCacheClient.set(
                        getWxRedisKey(wxAppId, wxPayConfigDto.getMchId()),
                        wxPayConfigDto,
                        1,
                        TimeUnit.DAYS);
                return wxPayConfigDto;
            } else {
                wxPayConfigDto = null;
            }
        }

        //2. 如果不是自由支付模式，则需要根据业务主体类型获取相应的服务商模式的兑吧微信支付配置
        if (Objects.equal(SubjectTypeEnum.DUIBA.getType(), subjectType)) {
            wxPayConfigDto = duibaWxpayConfig.getServiceProviderConfig();
        } else if (Objects.equal(SubjectTypeEnum.DUIA.getType(), subjectType)) {
            wxPayConfigDto = duia2WxpayConfig.getServiceProviderConfig();
        } else if (Objects.equal(SubjectTypeEnum.DUIJIE.getType(), subjectType)) {
            wxPayConfigDto = duijieWxpayConfig.getServiceProviderConfig();
        } else {
            LOGGER.error("WxPayNotifyService findByWxAppIdAndMchIdBySubjectTypeForWxLite 未获取到相应的业务主体信息，默认走兑吧配置  业务主体为={}, 渠道类型={}", subjectType, ChannelEnum.WX_LITE.getChannelType());
            wxPayConfigDto = duibaWxpayConfig.getServiceProviderConfig();
        }
        return wxPayConfigDto;
    }

    @Override
    public WxPayConfigDto findByWxAppIdAndMchIdBySubjectType(String wxAppId, String mchId, String subjectType) throws BizException {
        if (StringUtils.isBlank(wxAppId) || StringUtils.isBlank(mchId)) {
            return null;
        }
        WxPayConfigDto wxPayConfigDto = advancedCacheClient
                .getWithCacheLoader(
                        getWxRedisKey(wxAppId, mchId)
                        , 1
                        , TimeUnit.DAYS
                        , true
                        , () -> convert2Dto(wxPayConfigDao.findByWxAppIdAndMchId(wxAppId, mchId), false));

        //1. 判断当前是否属于自由支付模式
        if(wxPayConfigDto != null && ChannelModeEnum.INDEPENDENT.getCode().equals(wxPayConfigDto.getChannelMode())){
            return wxPayConfigDto;
        }

        // 这里为空只可能是没有微信支付配置，或者配置了兑吧代收（兑吧代收不需要配置mchId，根据主体动态获取）
        if (wxPayConfigDto == null) {
            wxPayConfigDto = convert2Dto(wxPayConfigDao.findByAppIdAndChannelTypeAndWxAppId(null, ChannelEnum.WX_LITE.getChannelType(), wxAppId), false);
            if (wxPayConfigDto != null && ChannelModeEnum.DUIBA.getCode().equals(wxPayConfigDto.getChannelMode())) {
                // 填充对应最新主体的商户配置
                fillDuibaWxPayConfig(subjectType, wxPayConfigDto);
                advancedCacheClient.set(
                        getWxRedisKey(wxAppId, wxPayConfigDto.getMchId()),
                        wxPayConfigDto,
                        1,
                        TimeUnit.DAYS);
            } else {
                wxPayConfigDto = null;
            }
        }

        //2. 缺少配置则使用兑吧代收--走的是兑吧作为服务商模式
        //如果是代收模式数据没有存配置需显式设置
        if (wxPayConfigDto == null ||
                (ChannelModeEnum.DUIBA.getCode().equals(wxPayConfigDto.getChannelMode())) && !ChannelEnum.WX_LITE.getChannelType().equals(wxPayConfigDto.getChannelType())) {
            if (Objects.equal(SubjectTypeEnum.DUIBA.getType(), subjectType)) {
                wxPayConfigDto = duibaWxpayConfig.getDuibaWxpayConfig();
            } else if (Objects.equal(SubjectTypeEnum.DUIA.getType(), subjectType)) {
                wxPayConfigDto = duia2WxpayConfig.getDuibaWxpayConfig();
            } else if (Objects.equal(SubjectTypeEnum.DUIJIE.getType(), subjectType)) {
                wxPayConfigDto = duijieWxpayConfig.getDuibaWxpayConfig();
            } else if (Objects.equal(SubjectTypeEnum.FUJIAN_GUANWEN.getType(), subjectType)) {
                wxPayConfigDto = fjgwWxpayConfig.getDuibaWxpayConfig();
            } else {
                LOGGER.error("WxPayConfigService findByWxAppIdAndMchIdBySubjectType 未获取到相应的业务主体信息，默认走兑吧配置，wxAppId={}, mchId={}, 业务主体为={}", wxAppId, mchId, subjectType);
                wxPayConfigDto = duibaWxpayConfig.getDuibaWxpayConfig();
            }

            //如果找不到之前的配置则无法解密报错
            if (!wxPayConfigDto.getWxAppId().equals(wxAppId) || !wxPayConfigDto.getMchId().equals(mchId)) {
                throw new BizException("找不到wxAppId=" + wxAppId + ",mchId=" + mchId + "的配置");
            }
        }
        return wxPayConfigDto;
    }

    private boolean updateWxPayConfig(WxPayConfigDto wxPayConfigDto, WxPayConfigEntity wxPayConfigEntity) {
        wxPayConfigEntity.setAppSecret(
                StringUtils.isBlank(wxPayConfigDto.getAppSecret())
                        ? wxPayConfigEntity.getAppSecret() : wxPayConfigDto.getAppSecret());
        wxPayConfigEntity.setApiKey(StringUtils.isBlank(wxPayConfigDto.getApiKey())
                ? wxPayConfigEntity.getApiKey() : wxPayConfigDto.getApiKey());
        wxPayConfigEntity.setApiCert(StringUtils.isBlank(wxPayConfigDto.getApiCert())
                ? wxPayConfigEntity.getApiCert() : wxPayConfigDto.getApiCert());

        //如果是第一次配置自有模式下的配置直接更新
        if (StringUtils.isBlank(wxPayConfigEntity.getMchId()) && StringUtils.isBlank(wxPayConfigEntity.getWxAppId())) {
            wxPayConfigEntity.setWxAppId(wxPayConfigDto.getWxAppId());
            wxPayConfigEntity.setMchId(wxPayConfigDto.getMchId());
            return wxPayConfigDao.update(wxPayConfigEntity);
        }
        wxPayConfigEntity.setMchId(StringUtils.isBlank(wxPayConfigDto.getMchId())
                ? wxPayConfigEntity.getMchId() : wxPayConfigDto.getMchId());
        wxPayConfigEntity.setWxAppId(StringUtils.isBlank(wxPayConfigDto.getWxAppId())
                ? wxPayConfigEntity.getWxAppId() : wxPayConfigDto.getWxAppId());
        WxPayConfigEntity updateEntity = new WxPayConfigEntity();

        wxPayConfigEntity.setChannelMode(wxPayConfigDto.getChannelMode());
        // 兑吧代收：商户（商户 id、商户 apikey、商户 apicert）是兑吧的（不填）
        // 自主模式：全都必填
        // 服务商模式：商户 apiKey 和 apiCert 是兑吧的（不填）
        if (ChannelModeEnum.DUIBA.getCode().equals(wxPayConfigDto.getChannelMode())) {
            wxPayConfigEntity.setMchId(null);
            wxPayConfigEntity.setApiKey(null);
            wxPayConfigEntity.setApiCert(null);
        } else if (ChannelModeEnum.SERVICE_PROVIDER.getCode().equals(wxPayConfigDto.getChannelMode())) {
            wxPayConfigEntity.setApiKey(null);
            wxPayConfigEntity.setApiCert(null);
        }

        updateEntity.setId(wxPayConfigEntity.getId());
        updateEntity.setChannelStatus(ChannelStatusEnum.DISABLE.getCode());
        boolean isInsert = wxPayConfigDao.insert(wxPayConfigEntity);
        return wxPayConfigDao.update(updateEntity) && isInsert;
    }

    /**
     * entity转换dto,当sensitive=true不隐藏敏感字段
     * 敏感字段如果存在则返回****
     * 不存在则为null
     */
    private WxPayConfigDto convert2Dto(WxPayConfigEntity wxPayConfigEntity, boolean sensitive) {
        if (wxPayConfigEntity == null) {
            return null;
        }
        WxPayConfigDto wxPayConfigDto = BeanUtils.copy(wxPayConfigEntity, WxPayConfigDto.class);
        if (sensitive) {
            wxPayConfigDto.setAppSecret(StringUtils.isBlank(wxPayConfigEntity.getAppSecret()) ? null : STAR);
            wxPayConfigDto.setApiKey(StringUtils.isBlank(wxPayConfigEntity.getApiKey()) ? null : STAR);
            wxPayConfigDto.setApiCert(StringUtils.isBlank(wxPayConfigEntity.getApiCert()) ? null : STAR);
        }
        return wxPayConfigDto;
    }

    private String getWxRedisKey(String wxAppId, String mchId) {
        return RedisKeyFactory.K003.toString() +
                wxAppId +
                "_" +
                mchId;
    }

    private String getChannelRedisKey(Long appid, String channelType) {
        return RedisKeyFactory.K002.toString() +
                appid +
                "_" +
                channelType;
    }

    private String getChannelWithWxAppIdRedisKey(Long appid, String channelType,String wxAppId) {
        return RedisKeyFactory.K002.toString() +
                appid +
                "_" +
                channelType + "_"+wxAppId;
    }

    /**
     * 如果是自营模式需要校验参数是否为空
     */
    private void check(WxPayConfigEntity wxPayConfigEntity, WxPayConfigDto wxPayConfigDto) throws BizException {
        if (StringUtils.isBlank(wxPayConfigEntity.getApiCert()) && StringUtils.isBlank(wxPayConfigDto.getApiCert())) {
            throw new BizException("证书不能为空");
        }
        if (StringUtils.isBlank(wxPayConfigEntity.getApiKey()) && StringUtils.isBlank(wxPayConfigDto.getApiKey())) {
            throw new BizException("api key 不能为空");
        }
        if (StringUtils.isBlank(wxPayConfigEntity.getAppSecret()) && StringUtils.isBlank(wxPayConfigDto.getAppSecret())) {
            throw new BizException("app secret 不能为空");
        }
        if (StringUtils.isBlank(wxPayConfigEntity.getMchId()) && StringUtils.isBlank(wxPayConfigDto.getMchId())) {
            throw new BizException("商户号不能为空");
        }
        if (StringUtils.isBlank(wxPayConfigEntity.getWxAppId()) && StringUtils.isBlank(wxPayConfigDto.getWxAppId())) {
            throw new BizException("微信app id不能为空");
        }
    }

    public void inValideCacheKey(WxPayConfigEntity entity) {
        advancedCacheClient.remove(getChannelRedisKey(entity.getAppId(), entity.getChannelType()), getWxRedisKey(entity.getWxAppId(), entity.getMchId()));
    }
}
