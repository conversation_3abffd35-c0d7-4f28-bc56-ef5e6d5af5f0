package cn.com.duiba.paycenter.service.payment.impl;

import cn.com.duiba.api.enums.SubjectTypeEnum;
import cn.com.duiba.api.tools.MoneyUtil;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.config.DuibaAlipayConfig;
import cn.com.duiba.paycenter.dao.payment.ChargeOrderDao;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.config.AlipayConfigDto;
import cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity;
import cn.com.duiba.paycenter.enums.BizTypeEnum;
import cn.com.duiba.paycenter.enums.ChargeOrderStatusEnum;
import cn.com.duiba.paycenter.service.payment.AlipayConfigService;
import cn.com.duiba.paycenter.service.payment.AlipayNotifyService;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayConstants;
import com.alipay.api.internal.util.AlipaySignature;
import com.google.common.base.Objects;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/12/11
 */
@Service
public class AlipayNotifyServiceImpl implements AlipayNotifyService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AlipayNotifyServiceImpl.class);
    /**
     * 交易状态TRADE_FINISHED的通知触发条件是商户签约的产品不支持退款功能的前提下，买家付款成功；
     * 或者，商户签约的产品支持退款功能的前提下，交易已经成功并且已经超过可退款期限。
     */
    private static final String TRADE_FINISHED = "TRADE_FINISHED";
    /**
     * 状态TRADE_SUCCESS的通知触发条件是商户签约的产品支持退款功能的前提下，买家付款成功；
     */
    private static final String TRADE_SUCCESS = "TRADE_SUCCESS";
    /**
     * 交易关闭
     */
    private static final String TRADE_CLOSED = "TRADE_CLOSED";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Resource
    private AlipayConfigService alipayConfigService;
    @Resource
    private ChargeOrderDao chargeOrderDao;
    @Resource
    private DuibaAlipayConfig duibaAlipayConfig;

    /**
     * 1.在通知返回参数列表中，除去sign、sign_type两个参数外，凡是通知返回回来的参数皆是待验签的参数
     * 2.查支付配置
     * 2.使用sdk验签
     * 3.更新支付状态
     */
    @Override
    public AlipayChargeNotifyResponse orderNotify(Map<String, String> params) throws BizException {
        String aliAppId = params.get("app_id");
        //兑吧支付流水号
        String outTradeNo = params.get("out_trade_no");
        AlipayChargeNotifyResponse response = new AlipayChargeNotifyResponse();
        response.setSuccess(false);
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByOrderNo(outTradeNo);
        if (chargeOrderEntity == null) {
            return response;
        }
        AlipayConfigDto alipayConfigDto;
        //如果是充值的订单则都是使用兑吧的账户配置
        if (BizTypeEnum.RCG.getCode().equals(chargeOrderEntity.getBizType())) {
            alipayConfigDto = duibaAlipayConfig.getDuibaRechargeAlipayConfig();
        } else {
            alipayConfigDto = alipayConfigService.findByAliAppId(aliAppId);
        }

        return handleOrderNotify(alipayConfigDto, params, chargeOrderEntity, response);
    }

    @Override
    public AlipayChargeNotifyResponse orderNotifyBySubjectType(Map<String, String> params, String subjectType) throws BizException {
        String aliAppId = params.get("app_id");
        //兑吧支付流水号
        String outTradeNo = params.get("out_trade_no");
        AlipayChargeNotifyResponse response = new AlipayChargeNotifyResponse();
        response.setSuccess(false);
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByOrderNo(outTradeNo);
        if (chargeOrderEntity == null) {
            return response;
        }
        AlipayConfigDto alipayConfigDto;
        //如果是充值的订单则都是使用兑吧的账户配置
        if (BizTypeEnum.RCG.getCode().equals(chargeOrderEntity.getBizType())) {
            if (Objects.equal(SubjectTypeEnum.DUIBA.getType(), subjectType)) {
                alipayConfigDto = duibaAlipayConfig.getDuibaRechargeAlipayConfig();
            } else if (Objects.equal(SubjectTypeEnum.DUIA.getType(), subjectType)) {
                alipayConfigDto = duibaAlipayConfig.getDuia2RechargeAlipayConfig();
            } else if (Objects.equal(SubjectTypeEnum.DUIJIE.getType(), subjectType)) {
                alipayConfigDto = duibaAlipayConfig.getDuijieRechargeAlipayConfig();
            } else {
                LOGGER.error("AlipayNotifyServiceImpl 支付宝回调未获取到相应的业务主体信息，默认走兑吧配置，params={}, subjectType={}", JSONObject.toJSONString(params), subjectType);
                alipayConfigDto = duibaAlipayConfig.getDuibaRechargeAlipayConfig();
            }
        } else {
            alipayConfigDto = alipayConfigService.findByAliAppIdAndSubjectType(aliAppId, subjectType);
        }
        return handleOrderNotify(alipayConfigDto, params, chargeOrderEntity, response);
    }

    private AlipayChargeNotifyResponse handleOrderNotify(AlipayConfigDto alipayConfigDto, Map<String, String> params, ChargeOrderEntity chargeOrderEntity, AlipayChargeNotifyResponse response) {
        try {
            //验签
            boolean isSuccess = AlipaySignature.rsaCheckV1(params
                    , alipayConfigDto.getPublicKey()
                    , AlipayConstants.CHARSET_UTF8
                    , params.get("sign_type"));
            if (!isSuccess) {
                return response;
            }

        } catch (AlipayApiException e) {
            LOGGER.warn("支付宝验签失败, params={}", params, e);
        }
        //支付宝流水号
        String tradeNo = params.get("trade_no");
        String tradeStatus = params.get("trade_status");
        Long totalAmount = MoneyUtil.convertToCent(params.get("total_amount"));

        //验证金额是否正确
        if (!Long.valueOf(chargeOrderEntity.getAmount()).equals(totalAmount)) {
            return response;
        }
        response.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
        response.setBizType(chargeOrderEntity.getBizType());
        response.setChargeOrderNo(chargeOrderEntity.getOrderNo());
        response.setTransactionNo(tradeNo);
        if (chargeOrderEntity.getChargeStatus() >= ChargeOrderStatusEnum.PAY_FAIL.getCode()) {
            response.setSuccess(true);
            response.setChargeSuccess(
                    !ChargeOrderStatusEnum.PAY_FAIL.getCode().equals(chargeOrderEntity.getChargeStatus()));
            return response;
        }
        response.setSuccess(true);
        ChargeOrderEntity updateEntity = new ChargeOrderEntity();
        updateEntity.setOrderNo(chargeOrderEntity.getOrderNo());
        updateEntity.setTransactionNo(tradeNo);
        switch (tradeStatus) {
            case TRADE_CLOSED:
                response.setChargeSuccess(false);
                updateEntity.setChargeStatus(ChargeOrderStatusEnum.PAY_FAIL.getCode());
                break;
            case TRADE_FINISHED:
            case TRADE_SUCCESS:
                String paidTime = params.get("gmt_payment");
                if (StringUtils.isNotBlank(paidTime)) {
                    updateEntity.setPaidTime(Date
                            .from(LocalDateTime.parse(paidTime, FORMATTER).atZone(ZoneId.systemDefault()).toInstant()));
                }
                updateEntity.setChargeStatus(ChargeOrderStatusEnum.PAY_SUCCESS.getCode());
                response.setChargeSuccess(true);
                break;
            default:
                LOGGER.info("unknow trade status, params={}", params);
                response.setChargeSuccess(false);
                break;
        }
        chargeOrderDao.update(updateEntity);
        return response;
    }
}
