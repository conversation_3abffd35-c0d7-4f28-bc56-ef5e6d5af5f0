package cn.com.duiba.paycenter.executor;

import cn.com.duiba.paycenter.config.AlipayTryConfig;
import cn.com.duiba.paycenter.config.DuibaAlipayConfig;
import cn.com.duiba.paycenter.dto.AlipayRiskResultDto;
import cn.com.duiba.paycenter.dto.PayOrderDto;
import cn.com.duiba.paycenter.enums.*;
import cn.com.duiba.paycenter.param.AlipayRiskQueryParam;
import cn.com.duiba.paycenter.params.FundTransferRequestParams;
import cn.com.duiba.paycenter.result.FundTransferResult;
import cn.com.duiba.paycenter.service.PayOrderService;
import cn.com.duiba.paycenter.util.InnerLogTool;
import cn.com.duiba.paycenter.util.ParamValidateTool;
import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.request.AlipayFundTransOrderQueryRequest;
import com.alipay.api.request.AlipayFundTransToaccountTransferRequest;
import com.alipay.api.request.AlipaySecurityRiskCustomerriskQueryRequest;
import com.alipay.api.response.AlipayFundTransOrderQueryResponse;
import com.alipay.api.response.AlipayFundTransToaccountTransferResponse;
import com.alipay.api.response.AlipaySecurityRiskCustomerriskQueryResponse;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by xiaoxuda on 2017/11/1.
 */
@Component
public class Alipay2AlipayExecutor extends AbstractExecutor {
    private static Logger logger = LoggerFactory.getLogger(Alipay2AlipayExecutor.class);
    @Autowired
    private PayOrderService payOrderService;
    @Autowired
    private AlipayClientPool alipayClientPool;
    @Autowired
    private DuibaAlipayConfig duibaAlipayConfig;
    @Autowired
    private AlipayExecutorCurrency alipayExecutorCurrency;

    private final ImmutableMap<PayOrderBizTypeEnum, String> bizTitleMap;
    {
        Map<PayOrderBizTypeEnum, String> tempMap = new HashMap<>();
        tempMap.put(PayOrderBizTypeEnum.WITHDRAWAL_ALIPAY,"兑吧积分商城开发者收入提现");
        tempMap.put(PayOrderBizTypeEnum.SUPPLIER_ALIPAY,"兑吧商城支付宝转账");
        bizTitleMap = ImmutableMap.copyOf(tempMap);
    }

    private final String DUIBA_ALIPAY_NAME = "兑吧积分商城";

    private final static String IDENTITY_TYPE = "ALIPAY_LOGON_ID";

    //订单重试次数
    @Autowired
    private AlipayTryConfig alipayTryConfig;

    @Override
    public FundTransferResult doTransfer(FundTransferRequestParams params) {
        //幂等校验
        PayOrderDto payOrder = payOrderService.findByUniqueIndex(params.getBizType(), params.getBizNo());
        logger.info("进入支付宝转账,{}", JSON.toJSONString(params));
        if (payOrder != null) {
            boolean noChange = payOrder.getPayeeNo().equals(params.getPayeeNo())  && payOrder.getPayeeName().equals(params.getPayeeName());
            //如果本次请求的支付宝账户和姓名发生了变更,则更新掉
            if(!noChange){
                payOrder.setPayeeNo(params.getPayeeNo());
                payOrder.setPayeeName(params.getPayeeName());
                payOrderService.updateById(payOrder);
            }
            //异常订单允许重试
            if (PayOrderStatusEnum.EXCEPTION.equals(payOrder.getPayStatus())
                    || PayOrderStatusEnum.PROCESSING.equals(payOrder.getPayStatus())
                    || (!noChange && PayOrderStatusEnum.FAIL.equals(payOrder.getPayStatus()))) {
                return tryAgain(payOrder.getId());
            } else {
                return new FundTransferResult(payOrder.getPayStatus(), payOrder.getRemark());
            }
        } else {
            return newPayOrder(params);
        }
    }

    /**
     * 创建新的支付订单
     *
     * @param params
     * @return
     */
    private FundTransferResult newPayOrder(FundTransferRequestParams params) {
        PayOrderDto payOrder = new PayOrderDto();
        payOrder.setBizType(params.getBizType());
        payOrder.setBizNo(params.getBizNo());
        payOrder.setAmount(params.getAmount());
        payOrder.setPayeeType(params.getPayeeType());
        payOrder.setPayeeNo(params.getPayeeNo());
        payOrder.setPayeeName(params.getPayeeName());
        payOrder.setPayStatus(PayOrderStatusEnum.PROCESSING);
        payOrder.setPayerType(PayAccountTypeEnum.ALIPAY);
        payOrder.setPayerNo(alipayExecutorCurrency.getNewOrOldPayerNo(params.getBizType(), params.getSubjectType(), params.getMerchantType(), params.getMerchantNo()));
        payOrder.setPayerName(DUIBA_ALIPAY_NAME);
        payOrder.setRetryCount(0);
        payOrder.setBizRemark(StringUtils.isNotBlank(params.getRemark()) ? params.getRemark() : bizTitleMap.get(params.getBizType()));
        payOrder.setId(payOrderService.insert(payOrder));
        logger.info("进入支付宝,创建新订单,payOrder:{}", JSON.toJSONString(payOrder));
        if (payOrder.getId() == null) {
            return new FundTransferResult(PayOrderStatusEnum.EXCEPTION, "创建订单失败");
        }
        return getNewOrOld(payOrder);
    }


    public FundTransferResult getNewOrOld(PayOrderDto payOrder) {
//        if (alipayClientPool.getIsAlipaySwitch() > 0) {
            logger.info("进入支付宝,走新的转账逻辑Alipay2AlipayExecutor");
            Triple<PayOrderDto, String, FundTransferResult> triple = queryPayOrderAndName(payOrder.getId());
            if (triple.getRight() != null) {
                return triple.getRight();
            }
            return alipayExecutorCurrency.alipayCallBack(triple.getLeft(), alipayExecutorCurrency.doTransferExecute(triple.getLeft(), triple.getMiddle(), IDENTITY_TYPE));
//        } else {
//            return callAlipay(payOrder.getId());
//        }
    }


    /**
     * 订单重试
     *
     * @param payOrderId
     * @return
     */
    @Override
    public FundTransferResult tryAgain(Long payOrderId) {
        PayOrderDto payOrder = payOrderService.findById(payOrderId);
        if (payOrder.getRetryCount() > alipayTryConfig.getRetryLimit()) {
            logger.info("订单重试次数超过限制,payOrder:{}", JSON.toJSONString(payOrder));
            return new FundTransferResult(payOrder.getPayStatus(), payOrder.getRemark());
        }

//        FundTransferResult reuslt = callAlipay(payOrder.getId());
        FundTransferResult reuslt = getNewOrOld(payOrder);

        PayOrderDto update = new PayOrderDto();
        update.setId(payOrderId);
        update.setRetryCount(payOrder.getRetryCount() + 1);
        payOrderService.updateById(update);

        return reuslt;
    }

    private String generateOutbizNo(PayOrderDto payOrder){
        return payOrder.getBizType() + "_" + payOrder.getBizNo();
    }

    /**
     * 调用支付宝
     *
     * @param orderId
     */
    private FundTransferResult callAlipay(Long orderId) {
        Triple<PayOrderDto, String, FundTransferResult> triple = queryPayOrderAndName(orderId);
        if (triple.getRight() != null) {
            return triple.getRight();
        }
        AlipayFundTransToaccountTransferRequest request = new AlipayFundTransToaccountTransferRequest();
        request.setBizContent("{" +
                "\"out_biz_no\":\"" + generateOutbizNo(triple.getLeft()) + "\"," +
                "\"payee_type\":\"ALIPAY_LOGONID\"," +
                "\"payee_account\":\"" + triple.getLeft().getPayeeNo() + "\"," +
                "\"amount\":\"" + triple.getLeft().getAmount() / 100.00 + "\"," +
                "\"payee_real_name\":\"" + triple.getMiddle() + "\"," +
                "\"remark\":\"["+ triple.getLeft().getBizRemark() +"]支付宝转账\"" +
                "}");
        AlipayFundTransToaccountTransferResponse response = null;
        try {
            logger.info("Alipay2AlipayExecutor callAlipay request:{}",request.getBizContent());
            response = alipayClientPool.getAlipayClientByAlipayAppId(triple.getLeft().getPayerNo()).execute(request);
        } catch (AlipayApiException e) {
            logger.error("调用支付宝转账发生异常", e);
        }
        return alipayExecutorCurrency.alipayCallBack(triple.getLeft(), response);
    }

    private Triple<PayOrderDto, String, FundTransferResult> queryPayOrderAndName(Long orderId) {
        PayOrderDto payOrder = payOrderService.findById(orderId);
        // 调用支付宝同人风险校验接口
//        FundTransferResult riskResult = riskValid(payOrder);
//        if (riskResult != null) {
//            return Triple.of(null, null, riskResult);
//        }
        String realName = StringEscapeUtils.unescapeHtml4(StringUtils.trimToNull(payOrder.getPayeeName()));
        return Triple.of(payOrder, realName, null);
    }

    /**
     * 同人实名校验
     * @param payOrder 支付订单
     * @return
     */
    private FundTransferResult riskValid(PayOrderDto payOrder) {
        try {
            return doRiskValid(payOrder);
        } catch (Exception e) {
            logger.error("同人实名校验失败, orderId={}", payOrder.getId(), e);
            // 降级处理, 出现任何异常, 直接认为认证通过
            return null;
        }
    }

    /**
     * do调用支付宝同人实名校验接口
     * @param orderId
     * @param payOrder
     * @return
     */
    private FundTransferResult doRiskValid(PayOrderDto payOrder) {
        /**
         * 支付宝实名认证的接口不支持企业账号的验证
         * 针对开发者提现类型的支付宝转账请求直接放过
         * rdc地址： https://rdc.aliyun.com/req/1642730
         */
        if(null != payOrder.getBizType()
                && payOrder.getBizType().equals(PayOrderBizTypeEnum.WITHDRAWAL_ALIPAY)){
            return null;
        }

        // 如果没有打开开关
        if (!Boolean.TRUE.equals(duibaAlipayConfig.getRiskGoSwitch())) {
            return null;
        }

        // request
        AlipaySecurityRiskCustomerriskQueryRequest request = new AlipaySecurityRiskCustomerriskQueryRequest();
        AlipayRiskQueryParam bizParam = new AlipayRiskQueryParam();
        String account = payOrder.getPayeeNo();
        if (StringUtils.isNumeric(account)) {
            // mobile
            bizParam.setMobileNo(account);
        } else {
            // email
            bizParam.setEmailAddress(account);
        }
        // pid
        bizParam.setPid(duibaAlipayConfig.getRiskGoPid());
        // username
        bizParam.setUserName(payOrder.getPayeeName());
        request.setBizContent(JSON.toJSONString(bizParam));

        AlipaySecurityRiskCustomerriskQueryResponse response = null;
        try {
            logger.info("Alipay2AlipayExecutor doRiskValid request:{}",request.getBizContent());
            response = alipayClientPool.getAlipayClientByAlipayAppId(duibaAlipayConfig.getAppId2c()).execute(request);
        } catch (Exception e) {
            logger.error("支付宝同人实名接口调用发生异常, orderId={}, param={}", payOrder.getId(), JSON.toJSONString(request), e);
        }

        return riskValidCallBack(payOrder, request, response);
    }

    private FundTransferResult riskValidCallBack(PayOrderDto payOrder, AlipaySecurityRiskCustomerriskQueryRequest request,
                                                 AlipaySecurityRiskCustomerriskQueryResponse response) {
        if (response == null || !response.isSuccess()) {
            logger.error("支付宝同人实名接口调用失败, orderId={}, request={}, response={}",
                    payOrder.getId(), JSON.toJSONString(request), JSON.toJSONString(response));
            return null;
        }

        AlipayRiskResultDto riskResult = JSON.parseObject(response.getRiskResult(), AlipayRiskResultDto.class);
        if (riskResult == null || riskResult.getRank() == null) {
            logger.error("支付宝同人实名接口调用返回结果不完整, orderId={}, request={}, response={}",
                    payOrder.getId(), JSON.toJSONString(request), JSON.toJSONString(response));
            return null;
        }
        //统计同人数据
        InnerLogTool.alipaySecurityRiskCustomerriskLog(request, response);
        // 同人实名校验通过
        if (AlipayRiskRankEnum.RANK3.getCode().equalsIgnoreCase(riskResult.getRank())) {
            return null;
        }

        // 同人校验不通过
        PayOrderDto update = new PayOrderDto();
        update.setId(payOrder.getId());
        update.setPayStatus(PayOrderStatusEnum.FAIL);
        String remark = "支付宝同人实名校验不通过, rank:" + riskResult.getRank() + ", desc:" + response.getRiskResultDesc();
        update.setRemark(remark);
        payOrderService.updateById(update);

        // 参数有误或者未识别到
        if (AlipayRiskRankEnum.RANK0.getCode().equalsIgnoreCase(riskResult.getRank())) {
            return new FundTransferResult(update.getPayStatus(), "提供的账号姓名错误，请重新输入");
        }
        // 非同人
        if (AlipayRiskRankEnum.RANK1.getCode().equalsIgnoreCase(riskResult.getRank())) {
            return new FundTransferResult(update.getPayStatus(), "提供的账号姓名不匹配，请重新输入");
        }
        // 未实名
        if (AlipayRiskRankEnum.RANK2.getCode().equalsIgnoreCase(riskResult.getRank())) {
            return new FundTransferResult(update.getPayStatus(), "该账户未实名认证，请在支付宝官方实名后，再执行操作");
        }
        return new FundTransferResult(update.getPayStatus(), "账号校验失败, 请联系客服处理");
    }

    @Override
    public String paramValid(FundTransferRequestParams params) {
        String validMsg = ParamValidateTool.validate(params);
        if (validMsg != null) {
            return validMsg;
        }
        if (!PayAccountTypeEnum.ALIPAY.equals(params.getPayeeType())) {
            return "Alipay2AlipayExecutor{不支持的收款账户类型}";
        }
        if (AlipayMerchantTypeEnum.CUSTOM.equals(params.getMerchantType())) {
            if (StringUtils.isBlank(params.getMerchantNo())) {
                return "缺少参数";
            }
        }
        return null;
    }

    @Override
    public FundTransferExecutorEnum suportType() {
        return FundTransferExecutorEnum.ALIPAY2ALIPAY;
    }

    @Override
    public FundTransferResult transferStatusQuery(PayOrderDto payOrder) {
        AlipayFundTransOrderQueryRequest request = new AlipayFundTransOrderQueryRequest();
        request.setBizContent("{" +
                "\"out_biz_no\":\""+ generateOutbizNo(payOrder) +"\"," +
                "\"order_id\":\""+ (payOrder.getExecutorBizId() == null ? "" : payOrder.getExecutorBizId()) +"\"" +
                "  }");
        AlipayFundTransOrderQueryResponse response = null;
        try {
            response = alipayClientPool.getAlipayClientByAlipayAppId(payOrder.getPayerNo()).execute(request);
        } catch (AlipayApiException e) {
            logger.error("调用alipay查询订单失败", e);
        }
        if(response.isSuccess()){
            PayOrderDto update = new PayOrderDto();
            update.setId(payOrder.getId());
            update.setExecutorBizId(response.getOrderId());
            if("SUCCESS".equals(response.getStatus())){
                update.setPayStatus(PayOrderStatusEnum.SUCCESS);
                update.setRemark("");
                payOrderService.updateById(update);
            }
            payOrder = payOrderService.findById(payOrder.getId());
        }
        return new FundTransferResult(payOrder.getPayStatus(), payOrder.getRemark());
    }

}
