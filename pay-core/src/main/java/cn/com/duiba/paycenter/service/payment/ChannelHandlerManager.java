package cn.com.duiba.paycenter.service.payment;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 支付渠道都需要在这里注册才能使用
 * <AUTHOR>
 * @date 2018/11/14
 */
public class ChannelHandlerManager {

    private ChannelHandlerManager() {

    }
    private static final Map<String, ChannelHandler> HANDLERS = Maps.newHashMap();

    public static void register(String channelType, ChannelHandler channelHandler) {
        HANDLERS.put(channelType, channelHandler);
    }

    public static ChannelHandler getHandller(String channelType) {
        return HANDLERS.get(channelType);
    }
}
