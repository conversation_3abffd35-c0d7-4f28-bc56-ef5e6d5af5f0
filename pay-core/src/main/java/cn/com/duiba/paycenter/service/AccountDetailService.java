package cn.com.duiba.paycenter.service;

import cn.com.duiba.paycenter.dto.ADeveloperFlowQuery;
import cn.com.duiba.paycenter.dto.AccountDetailDto;
import cn.com.duiba.paycenter.dto.AccountDetailTotalDto;
import cn.com.duiba.paycenter.dto.AccountPageInfo;
import cn.com.duiba.paycenter.params.AccountChangeParam;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface AccountDetailService {
	
	public AccountDetailDto insert(AccountDetailDto ad);
	
	public AccountDetailDto find(Long id);
	
	public List<AccountDetailDto> findAllByPageInfo(AccountPageInfo pageInfo);

	public Long getCountByPageInfo(AccountPageInfo pageInfo);

	/**
	 * ID倒叙排列
	 */
	public List<AccountDetailDto> findAllByOrder(Long orderId);

	/**
	 * 测试用途
	 */
	public List<AccountDetailDto> findLastDetails(Integer count);

	/**
	 * 根据developerId查询明细
	 */
	public List<AccountDetailDto> findPageByDeveloperId(ADeveloperFlowQuery query);

	/**
	 * 分页查询总数
	 */
	public Long findPageByDeveloperIdCount(ADeveloperFlowQuery query);

	/**
	 * 开发者资金明细页面专用
	 */
	public List<AccountDetailDto> findAccountDetailByPageInfo(AccountPageInfo pageInfo);

	public Long countAccountDetailByPageInfo(AccountPageInfo pageInfo);

	public Map<String, Long> getRowAndMax(AccountPageInfo pageInfo);

	public List<AccountDetailDto> findAccountDetailExport(AccountPageInfo pageInfo);

	public int update(AccountDetailDto accountDetailDO);

	/**
	 * 根据变动类型和日期统计变动金额
	 * @return
	 */
	Long getSumByTypeAndDay(AccountChangeParam param);

	/**
	 * 统计 开发者 充值金额
	 * */
	List<AccountDetailTotalDto> getSumByIdsAndDate(List<Long> developIds, Date startdate, Date endDate);

	/**
	 *
	 * @param orderIds
	 * @param appId
	 * @return
	 */
	List<AccountDetailDto> getDetailByOrderIds(List<Long> orderIds, Long appId);
}
