package cn.com.duiba.paycenter.config.citic;

import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Set;


/**
 * <AUTHOR>
 * @date 2021/08/05 11:48
 */
@Configuration
@ConfigurationProperties(prefix = "pay.center.beijingcitic")
public class BeiJingCiticConfig extends CiticCommonConfig implements BaseCiticConfig {
    /**
     * 应用id集合
     */
    private Set<Long> appIdSet = Sets.newHashSet(75046L);

    //开放平台商户号
    private String openMerCode = "IATP91330106574371572Y05";
    //开放平台商户名称
    private String openMerName = "兑吧直播小程序";
    //商户业务类型
    private String openBusiType = "1019";
    //业务，商户编号
    private String merno = "119913350000011";
    //业务，商户名称
    private String merchantName = "杭州兑吧网络科技有限公司";
    //业务，商户全称
    private String merchantFullName = "杭州兑吧网络科技有限公司";


    //加密公钥，用于加密请求报文数据
    private String publiEncryptKey = "MIIDPzCCAiegAwIBAgIBMDANBgkqhkiG9w0BAQUFADA5MQswCQYDVQQGEwJDTjENMAsGA1UECwwEQ05DQjEbMBkGA1UEAwwSaWZvcC5jaXRpY2JhbmsuY29tMCAXDTE4MTIwNjA4MzIzN1oYDzIxMTgxMTEyMDgzMjM3WjA5MQswCQYDVQQGEwJDTjENMAsGA1UECwwEQ05DQjEbMBkGA1UEAwwSaWZvcC5jaXRpY2JhbmsuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkd1Y8aodFkzyh1zClcnhkdvMsvxJ51+pOO27qmXc5HQiCHzEU+XZaSokgegjxM4qSwtBNXLdj6cZvt0XQDr6PXDsm5npvyGhYiEuYge/dLHQgaDpKz+bb2GtSpcc1+PT5f+cFT6TEKB0P6NwnmBR+owkLBaYq3rWJkrEFJ6JS6JXvnXuhUZzNuZhh7uFIvCvADWIeklbdgZsLfy+VXzwvMFnGmasyoAH9m6OdYuHOITfI5HGlCMkZOh9W46nCcL+02dDiehYS5wHJmuHWX/gfNr3XK41Hu8QP68dSS5YmD2pmGRpBbCCoYhNd8UBlHvNq+sXZ9fT2Evo12kgvCj/IQIDAQABo1AwTjAdBgNVHQ4EFgQUc8Yd9sorjSTuxOi7ShIH6952EIYwHwYDVR0jBBgwFoAUc8Yd9sorjSTuxOi7ShIH6952EIYwDAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQUFAAOCAQEAGEaZoKSMypnYFL7mleFBxWl0cKuSQINlqq1aKbkatZdCPzR/GXeWw/1WBlvfPU636HSlwdMhXS1UfjGJsjvSf4XJvQPp7fZDGQzNkpRMX2+Ye58ND3dqZJMnH/CTjvjXAhLqapKk6W0rIqJ3yHe2iAN1gm4ueBRWLVxS8Wtjjhrm62/gkL/DxvkE7Uw/TZ9z9Jx1ule3mvhcjprg3JevgphaOJtxvOhMenkrDS9NLoYbJHmNG1XwEPPfmOUFbug6MRmu2aIiACw1ogSZS/hwGyDV4PmdrZc6u1F1WWKgc7KUVgs4d0g7GwNqI3a9x2P1/WR4C01i/vIxqtfy7pLx0A==";

    //验签公钥，用于对返回报文数据验签
    private String verifyPubKey = "MIIDPzCCAiegAwIBAgIBMDANBgkqhkiG9w0BAQUFADA5MQswCQYDVQQGEwJDTjENMAsGA1UECwwEQ05DQjEbMBkGA1UEAwwSaWZvcC5jaXRpY2JhbmsuY29tMCAXDTE4MDcwOTExMDE0OVoYDzIxMTgwNjE1MTEwMTUyWjA5MQswCQYDVQQGEwJDTjENMAsGA1UECwwEQ05DQjEbMBkGA1UEAwwSaWZvcC5jaXRpY2JhbmsuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvd0t3VCgtRrRHnx8eAi4kTx7GS5uS3GwUZ/lOClgVu4YKbFoQWkjgkhFA09+WUYdV1gvJCEzi5tarsppyX93dblA3IoIXHMJ/2QCPk2w6/xSSxTNEMQOGqzcA8APs81hd9umPJCDoE/J6hZUmBG9joJL2nLY413n5G0Fv9u8R1FerfhGxtgf67ZPP58TXAMW1Z7Vg44QQG8p56MKxTVfaACaLtzaxeNnvQ8GhxQZG4FSR4I/trzM7cQARBTF5skhASUjCdu3y+7sSqk1SE4pWH4bhrhC7sRpVZfwnDSuClt9rAYFVjOpcy2K3UGr7jpMpqUS9/mG6mlA/zoSF8K1CQIDAQABo1AwTjAdBgNVHQ4EFgQUF0mIOV5jqONs/aA3UcAP5WqGSBowHwYDVR0jBBgwFoAUF0mIOV5jqONs/aA3UcAP5WqGSBowDAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQUFAAOCAQEAmB8JGc9JxtqN/TqVjMhtKz1uSzq+iVtbI1ebNx+sNlOI1n4qqFr57tYtEXIiR9R4OBp7aAAiBpVuhaqeJOYM0LrM6t2xuo4JdwHy66aBqR874GOb60q/llh3b0/I4D/9yITCsDf9GXOVkNUKuhURZFOK+1QZRktWCWWj/QYEdZAqjsS4dKa565wjeqqI52+IXSMQlObGzE7oJvJ+bCv8VaZc5dvtXYo+0lBLnLSXuUZvuSMAmQd06WvQIZKs7AgUA3yPumXS6L7noZQ6EfQ5aX0UpXdtnoT2s39Tsxutb/bgZt53jdRp2tNFonUKtrUpF3Di4ADT6RFQ/ChJMRdnxg==";

    //签名私钥及私钥的密码文件，用于对请求报文数据加签
    private String privateSignKey = "A1JTQaMD4IoyRhN3dVYl5Ty6JGcqIkaPtCjATtmh43T3k+HlhIDL02utVYHop5DJeq168OqejfCCI0bB+WZrUrt+yGhPiiCWumEDz8M+n+P0kOKPUroKVs6u+laEGvNapAA/zk2TyxdDkMa8GhjFJeMqT7ANv5im8vn3tydSJXJYA/Pty4SnEO7b0b6hyCXnrQNvKAujx7niak7Q9keHW9R919hJnpn0JfVeK8aKdOjjjNPDq4lk4kBB6X+Nq9I+2zGc2IH7TX2ZvzO9TFBHNVHOINAgIVaVWaC/S1Mhz9JQgXmTDc7ZqfO29d68d5uZiX350KZjy99kDdHKAzfKLMuHZ+9DM173pb3ouk2CG0NfzWTrJTUrIxRnHU/a2dR5gf4Z3Gsx6klnNw5REcrj2Pzzw7r6SevlT96yWVGWSD7Y6Ql2w7QIn8IgOftKifD5D2LaYoNkN4wpcU6onx5mCn0nJSa9zoryYfx94UQF/1ovKP65inxMiTujVbFANhAuHOu4jZ66/OXlb6ITZVMzLgfHs8djF2mW+XEeLAsaPvXgD+N7rD2IurDoqpu6FM3z0OBtNZi3cWtxdlxa7KUje70zMqn+NxMWwIB1g/tcYEDPyqd0jWOr6QmNfdD0Er19CIW1uXbVhWmXFCsfGPeheIYmBei75tuVyIRyh3YyNZ3mFOXMC3QB4jAioffXMGrS39qbT4elXWUBkNPb5x2ZihITQByLxNes5d1d4v7+0M4vzPhntEqKgbtvvejWXK6MTjkFTumZ0gTNYjnsj2zEdCuTZW9AN1mmj+2AHEAQ4i6ZabU9jv6STlxigk+WZMhc/N+L9UW5vw0CU0mpSbEnQZn7RUj0UTsfF7kJzuOPO0rY6JRzn1jwelPQKdF33shX0mXX4crbDWOfqSAl7adL1fzSRT8aPdKoau+ZCAL+Zs4f4YKp9RzVY7mwXQshwxD7Qe4HX1L7hHH65K4+JYJLqUBG40yNmOOCtGQigxT86b/DeN/AYSCB5S4/TvzyigK21Rt0z/nf86oBROzxlVTBKBuHRvpSMFz3GLK7E8W3/Smu0MG33mkJ5uXmIknyZyPbGFwQ97SCZQBds3lB/aLzZzk2Nubct8y6oYBx/f+LI/+tbQQ93+vrXHHnJ7QXQxrI4xeWudicfxchunalWm/mBav20ixlYG9ZyKE1QPsfV0mFt3KIijM+k3dQf8Bk15HN+VR+tBtagUq01kSTUiqrfizErrMar74IYsOcqY8UFtFyd1sqSNLjiV+qrL/zzqBW9Sy+crIlwi1RpkKfhZkM1U0b/544+I8X3w6QDnK35h0zcPVRIv7zrHDiC5tW51CFs+ZOAYrwpMuUeHLgne8HKgLtS2eK9Pe4Trdsnz5ANzCZYYbSgbEYENzZcB7/yjBIDeGnqKYt8dRn7mkmTI89Sf6AUmEREIizvcF/uLd7fjadFLtMrTWY2qb9pCEyHCluMAZmXGdS8jGP2bG/m/Sul1PWB9z3YvJKykddqEAakoAXNAKc/vB4O+ag5wV1Mrd+AXRNxd8hvKXQapojSzfvGLa1YwV4G7Z2RStImMR8g+jIftbsgt9DHrcGPf4b9qFYmJ6a/abrJNIReSSvpRyN7ZN7NpmaIlss9JCJzlp4ILJ4rH17iOujVcIQe3o=";
    //设置加签私钥，联调测试阶段可以从config\citic\key\signature.priv.pwd 获取，获取方式可自行确定，从文件复制到代码里写死，或者存入数据库，或者读文件
    private String privateSignKeyCode = "xUHa46q";

    //解密私钥及相应的密码文件,用于解密返回报文数据
    private String privateDecryptKey = "A1JTQbjIueBjzQCV1gfAcBOKV+wvXdjv+fpe4Y8EO/q1DWan70plBm5vT+9MA8xrkd9IyPG7XIzB8wtf0WcHaSF3ONbCRmFy39D6mPTEBvjOtPZrjdMzPHmYgzQVfOcoBkrJmMCEta477dQdQH1ruKNSyXJMj2Xn9qyWCUGHT3WG00UK2QmVoOOAAdk5aAqtpfQ79KDGUBzuLR68X78oj4irrOfI7yzMpkyd+fv0M4/3FEKJOzns0MdJyLGDyJ4bzy/JCJpfbe9PyvSp1BMYY7e2QrimZVcShrF1n/QcdPNs/Ogiukcx26kUaVOLiS8WRDPXDnjxQCghZ6b5E8EolzP44KxxS4Lb45iXN+W6HK7AVdYejJXk9zSQ0uuxQ/f4ceWIScEl1XeuqUhXPWBpmzbc/afS8nqqihZPJdAekhVW604bDntn/dWcGN4RzX8VrIxzyJPz3WGpao4o1jO+Nht4BdVPDjeb5923Eawm72ahr5xKbJ8kERKvxypwkaA9ov5WGOoSjpUyB4IhVaxPALBzqa5gYSAg6f41w+Z9I4F002uEWQmATheKPvFGe7mIp3Kozsxu/rIaX7RFSK8w6wQS06Z8TN9TnzQtRsdfXhnH0Bf126bZoPOPoRE1En4j1p5aXMFOOnMqlzvQhr6g22/ZJ7U6N4YmdxrdW55va+e52vicAaDJHYM8KlH9ysXsJ4X8ZX9ZZiF3bDRnxKk8Y/W0Qp7rBU9N6+rC9kV9woGPQwZ9KmzTyGKC+r+4n2D/hXX6m6PFi3qbKGnC8Hx+Z339vzJF8TEiuODmj16lGaiIwzv5YtXd6qfnG/HI6Viijs7PVbqZCnwVzHZ/+dI8BPuOEJl3gxArVHqwoEwuun2fS5w/qdwAdD94/bJq26uXfEGkK3jY0GhMFLikV/s+pnkZONJM8LXsWnqDUD4qaK+Rye4+BWZJEVZkVs93ROf83rEoNOjbcxUMDVadg37IK1LwQz28Yi0nv9bp99dY/xgqo+NHj7ugjRSaJc4xcOc1G5YlhBWnTSsjPoGv5aM02hE0kcVz256sFW83RRPPg8Jwed8VcCZMlNxeK6J0TljH0cbVi1ij+Di0aZNzM3AEimlZmASWkl40JxBNRi/ncHqe87ns8vEZfyYTyfPu4wohRH8KdADPIbXaEoNcqXG7ow5/Q/ntBlu2yJLLYbDJSi4JH/iPmEcC52g7SANkGq6qw23zejHXjEbe8KgjtHXX8xNd1q4dWF1oj4Rz5xnVnxFgoIieMT8Uc1dVCSfrlxX/ySR9dAMgd6VOVSnS3zMFZCe/wHFoSaf6rGxbefQ9ILAU+IHFIi3Ao0jpttNO2lyzo102rz53FrJhT5UNPsnFP3qpvZu0/D9h+ivQwM5cCF+EWeyvOrjktORP25e8FCpl+ybOekud8XnhwsOJtAOkI6dyzTi8qOr4ytTMTY1lYm3Yk90wqmKC3lvqHMhN/A8OsMCSDWhHQ43cRhumfL7w07X1UmWRH07Krj5MhZtf60Q37VrQr32fdnJpb2+HBEr1ohpfieThYNZQL1Q4J63i/Ybeflw3/9Tr09G5305cTEoP1P4JLMZurxepUf61h72V3TRaXbqNYC4hmXe3LwqqAhuYKaPln3FlhvTu52uNovy/V5ASdQ/z1btxQ2w=";
    private String privateDecryptKeyCode = "gXfHWkw";

    //回调地址
    private String callbackNotifyUrl = "https://activity.m.duiba.com.cn/taw/citic/citicOrderNotify";

    @Autowired
    private CiticConfigManager citicConfigManager;

    @Override
    public Set<Long> getConfigAppIds() {
        return appIdSet;
    }

    @PostConstruct
    public void init() {
        //将自身注册到管理器
        citicConfigManager.registBaseCiticConfig(this);
    }

    @PreDestroy
    public void destroy() {
        //将自身注销
        citicConfigManager.unregistBaseCiticConfig(this);
    }

    public Set<Long> getAppIdSet() {
        return appIdSet;
    }

    public void setAppIdSet(Set<Long> appIdSet) {
        this.appIdSet = appIdSet;
    }

    @Override
    public String getOpenMerCode(Integer supplierTag) {
        return openMerCode;
    }

    public void setOpenMerCode(String openMerCode) {
        this.openMerCode = openMerCode;
    }

    @Override
    public String getOpenMerName(Integer supplierTag) {
        return openMerName;
    }

    public void setOpenMerName(String openMerName) {
        this.openMerName = openMerName;
    }

    @Override
    public String getOpenBusiType(Integer supplierTag) {
        return openBusiType;
    }

    public void setOpenBusiType(String openBusiType) {
        this.openBusiType = openBusiType;
    }

    @Override
    public String getPubliEncryptKey() {
        return publiEncryptKey;
    }

    public void setPubliEncryptKey(String publiEncryptKey) {
        this.publiEncryptKey = publiEncryptKey;
    }

    @Override
    public String getVerifyPubKey() {
        return verifyPubKey;
    }

    public void setVerifyPubKey(String verifyPubKey) {
        this.verifyPubKey = verifyPubKey;
    }

    @Override
    public String getPrivateSignKey() {
        return privateSignKey;
    }

    public void setPrivateSignKey(String privateSignKey) {
        this.privateSignKey = privateSignKey;
    }

    @Override
    public String getPrivateSignKeyCode() {
        return privateSignKeyCode;
    }

    public void setPrivateSignKeyCode(String privateSignKeyCode) {
        this.privateSignKeyCode = privateSignKeyCode;
    }

    @Override
    public String getPrivateDecryptKey() {
        return privateDecryptKey;
    }

    public void setPrivateDecryptKey(String privateDecryptKey) {
        this.privateDecryptKey = privateDecryptKey;
    }

    @Override
    public String getPrivateDecryptKeyCode() {
        return privateDecryptKeyCode;
    }

    public void setPrivateDecryptKeyCode(String privateDecryptKeyCode) {
        this.privateDecryptKeyCode = privateDecryptKeyCode;
    }

    @Override
    public String getMerno(Integer supplierTag) {
        return merno;
    }

    public void setMerno(String merno) {
        this.merno = merno;
    }

    @Override
    public String getMerchantName(Integer supplierTag) {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    @Override
    public String getMerchantFullName(Integer supplierTag) {
        return merchantFullName;
    }

    public void setMerchantFullName(String merchantFullName) {
        this.merchantFullName = merchantFullName;
    }

    public void setCallbackNotifyUrl(String callbackNotifyUrl) {
        this.callbackNotifyUrl = callbackNotifyUrl;
    }

    @Override
    public String getCallbackNotifyUrl() {
        return callbackNotifyUrl;
    }
}
