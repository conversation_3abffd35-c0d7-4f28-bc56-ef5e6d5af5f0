package cn.com.duiba.paycenter.executor;

import cn.com.duiba.api.tools.MoneyUtil;
import cn.com.duiba.paycenter.config.AlipayTryConfig;
import cn.com.duiba.paycenter.dto.PayOrderDto;
import cn.com.duiba.paycenter.enums.AlipayMerchantTypeEnum;
import cn.com.duiba.paycenter.enums.FundTransferExecutorEnum;
import cn.com.duiba.paycenter.enums.PayAccountTypeEnum;
import cn.com.duiba.paycenter.enums.PayOrderStatusEnum;
import cn.com.duiba.paycenter.params.FundTransferRequestParams;
import cn.com.duiba.paycenter.result.FundTransferResult;
import cn.com.duiba.paycenter.service.PayOrderService;
import com.alipay.api.AlipayApiException;
import com.alipay.api.domain.AlipayFundTransOrderQueryModel;
import com.alipay.api.domain.AlipayFundTransToaccountTransferModel;
import com.alipay.api.request.AlipayFundTransOrderQueryRequest;
import com.alipay.api.request.AlipayFundTransToaccountTransferRequest;
import com.alipay.api.response.AlipayFundTransOrderQueryResponse;
import com.alipay.api.response.AlipayFundTransToaccountTransferResponse;
import com.alipay.api.response.AlipayFundTransUniTransferResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019/02/12
 */
@Component
public class Alipay2AlipayByUserIdExecutor extends AbstractExecutor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Alipay2AlipayByUserIdExecutor.class);
    @Autowired
    private PayOrderService payOrderService;
    @Autowired
    private AlipayClientPool alipayClientPool;
    @Autowired
    private AlipayExecutorCurrency alipayExecutorCurrency;

    private static final String DUIBA_ALIPAY_NAME = "兑吧积分商城";

    public final static String IDENTITY_TYPE = "ALIPAY_USER_ID";

    /**
     * 订单重试次数
     */
    @Autowired
    private AlipayTryConfig alipayTryConfig;
    /**
     * 最大转账1000元
     */
    private static final Long MAX_AMOUNT = 100000L;
    /**
     * 最小转账0.1元
     */
    private static final Long MIN_AMOUNT = 10L;

    @Override
    public FundTransferExecutorEnum suportType() {
        return FundTransferExecutorEnum.ALIPAY2ALIPAYBYUSERID;
    }

    @Override
    public String paramValid(FundTransferRequestParams params) {
        if (!PayAccountTypeEnum.ALIPAY_USERID.equals(params.getPayeeType())) {
            return "Alipay2AlipayByUserIdExecutor{不支持的收款账户类型}";
        }
        if (params.getBizType() == null || StringUtils.isBlank(params.getBizNo())
                || params.getAmount() == null || params.getPayeeType() == null
                || StringUtils.isBlank(params.getPayeeNo())) {
            return "缺少参数";
        }
        if (params.getAmount() > MAX_AMOUNT) {
            return "最多转账100000";
        }
        if (params.getAmount() < MIN_AMOUNT) {
            return "最少转账10";
        }
        if (AlipayMerchantTypeEnum.CUSTOM.equals(params.getMerchantType())) {
            if (StringUtils.isBlank(params.getMerchantNo())) {
                return "缺少参数";
            }
        }
        return null;
    }

    @Override
    public FundTransferResult doTransfer(FundTransferRequestParams params) {
        PayOrderDto payOrder = payOrderService.findByUniqueIndex(params.getBizType(), params.getBizNo());
        if (payOrder != null) {
            //异常订单允许重试
            if (PayOrderStatusEnum.EXCEPTION.equals(payOrder.getPayStatus())
                    || PayOrderStatusEnum.PROCESSING.equals(payOrder.getPayStatus())) {
                return tryAgain(payOrder.getId());
            } else {
                return new FundTransferResult(payOrder.getPayStatus(), payOrder.getRemark());
            }
        } else {
            return newPayOrder(params);
        }
    }

    @Override
    public FundTransferResult tryAgain(Long payOrderId) {
        PayOrderDto payOrder = payOrderService.findById(payOrderId);
        if (payOrder.getRetryCount() > alipayTryConfig.getRetryLimit()) {
            return new FundTransferResult(payOrder.getPayStatus(), payOrder.getRemark());
        }

//        FundTransferResult reuslt = callAlipay(payOrder);
        FundTransferResult reuslt = getNewOrOld(payOrder);
        PayOrderDto update = new PayOrderDto();
        update.setId(payOrderId);
        update.setRetryCount(payOrder.getRetryCount() + 1);
        payOrderService.updateById(update);

        return reuslt;
    }

    @Override
    public FundTransferResult transferStatusQuery(PayOrderDto payOrder) {
        AlipayFundTransOrderQueryRequest request = new AlipayFundTransOrderQueryRequest();
        AlipayFundTransOrderQueryModel model = new AlipayFundTransOrderQueryModel();

        model.setOrderId(payOrder.getExecutorBizId() == null ? "" : payOrder.getExecutorBizId());
        model.setOutBizNo(payOrder.getBizType() + "_" + payOrder.getBizNo());

        AlipayFundTransOrderQueryResponse response = null;
        try {
            response = alipayClientPool.getAlipayClientByAlipayAppId(payOrder.getPayerNo()).execute(request);
        } catch (AlipayApiException e) {
            LOGGER.error("调用alipay查询订单失败", e);
        }
        if (response != null && response.isSuccess()) {
            PayOrderDto update = new PayOrderDto();
            update.setId(payOrder.getId());
            update.setExecutorBizId(response.getOrderId());
            if ("SUCCESS".equals(response.getStatus())) {
                update.setPayStatus(PayOrderStatusEnum.SUCCESS);
                update.setRemark("");
                payOrder.setPayStatus(update.getPayStatus());
                payOrder.setRemark("");
                payOrderService.updateById(update);
            }
        }
        return new FundTransferResult(payOrder.getPayStatus(), payOrder.getRemark());
    }

    private FundTransferResult newPayOrder(FundTransferRequestParams params) {
        PayOrderDto payOrder = new PayOrderDto();
        payOrder.setBizType(params.getBizType());
        payOrder.setBizNo(params.getBizNo());
        payOrder.setAmount(params.getAmount());
        payOrder.setPayeeType(params.getPayeeType());
        payOrder.setPayeeNo(params.getPayeeNo());
        //按用户id转账没有姓名
        payOrder.setPayeeName(params.getPayeeName());
        payOrder.setPayStatus(PayOrderStatusEnum.PROCESSING);
        payOrder.setPayerType(PayAccountTypeEnum.ALIPAY_USERID);
        payOrder.setPayerNo(alipayExecutorCurrency.getNewOrOldPayerNo(params.getBizType(), params.getSubjectType(), params.getMerchantType(), params.getMerchantNo()));
        payOrder.setPayerName(DUIBA_ALIPAY_NAME);
        payOrder.setRetryCount(0);
        payOrder.setBizRemark(StringUtils.isNotBlank(params.getRemark()) ? params.getRemark() : "支付宝转账");
        payOrder.setId(payOrderService.insert(payOrder));

        if (payOrder.getId() == null) {
            return new FundTransferResult(PayOrderStatusEnum.EXCEPTION, "创建订单失败");
        }
        return getNewOrOld(payOrder);
    }

    public FundTransferResult getNewOrOld(PayOrderDto payOrder) {
//        if (alipayClientPool.getIsAlipaySwitch() > 0) {
            LOGGER.info("进入支付宝,走新的转账逻辑,Alipay2AlipayByUserIdExecutor");
            AlipayFundTransUniTransferResponse response = alipayExecutorCurrency.doTransferExecute(payOrder, payOrder.getPayeeName(), IDENTITY_TYPE);
            return alipayExecutorCurrency.alipayCallBack(payOrder, response);
//        } else {
//            return callAlipay(payOrder);
//        }
    }

    private FundTransferResult callAlipay(PayOrderDto payOrder) {
        AlipayFundTransToaccountTransferRequest request = new AlipayFundTransToaccountTransferRequest();

        AlipayFundTransToaccountTransferModel model = new AlipayFundTransToaccountTransferModel();

        model.setOutBizNo(payOrder.getBizType() + "_" + payOrder.getBizNo());
        model.setPayeeType("ALIPAY_USERID");
        model.setPayeeAccount(payOrder.getPayeeNo());
        model.setAmount(MoneyUtil.formatMoneyToKeep2Point(payOrder.getAmount()));
        model.setRemark(payOrder.getBizRemark());
        request.setBizModel(model);

        AlipayFundTransToaccountTransferResponse response = null;
        try {
            response = alipayClientPool.getAlipayClientByAlipayAppId(payOrder.getPayerNo()).execute(request);
        } catch (AlipayApiException e) {
            LOGGER.error("调用支付宝转账发生异常", e);
        }
        return alipayExecutorCurrency.alipayCallBack(payOrder, response);
    }
}
