package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.model.TpcDuibaIncomeDetailDO;
import cn.com.duiba.paycenter.remoteservice.RemoteTpcDuibaIncomeDetailService;
import cn.com.duiba.paycenter.service.TpcDuibaIncomeDetailService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/25
 */
@RestController
public class RemoteTpcDuibaIncomeDetailServiceImpl implements RemoteTpcDuibaIncomeDetailService {

    @Resource
    private TpcDuibaIncomeDetailService tpcDuibaIncomeDetailService;

    @Override
    public List<TpcDuibaIncomeDetailDO> findPageList(Date startDate, Date endDate, Long relationId, Integer offset, Integer max) {
        return tpcDuibaIncomeDetailService.findPageList(startDate,endDate,relationId,offset,max);
    }

    @Override
    public Long findPageCount(Date startDate, Date endDate, Long relationId) {
        return tpcDuibaIncomeDetailService.findPageCount(startDate,endDate,relationId);
    }

    @Override
    public Long findCountIncome() {
        return tpcDuibaIncomeDetailService.findCountIncome();
    }
}
