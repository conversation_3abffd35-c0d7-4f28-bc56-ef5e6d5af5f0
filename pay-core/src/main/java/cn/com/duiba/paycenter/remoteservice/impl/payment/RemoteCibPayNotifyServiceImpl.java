package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.cib.CibPayNotifyRequestDTO;
import cn.com.duiba.paycenter.dto.payment.charge.cib.CibPayNotifyRespDTO;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteCibNotifyService;
import cn.com.duiba.paycenter.service.CibWxPayService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 兴业银行支付通知服务
 *
 * <AUTHOR>
 * @date 2022/10/26
 */
@RestController
public class RemoteCibPayNotifyServiceImpl implements RemoteCibNotifyService {

    @Resource
    private CibWxPayService cibWxPayService;

    @Override
    public CibPayNotifyRespDTO orderNotify(CibPayNotifyRequestDTO payNotifyRequest) throws BizException {
        return cibWxPayService.orderNotify(payNotifyRequest);
    }
}
