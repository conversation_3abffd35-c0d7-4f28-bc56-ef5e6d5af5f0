package cn.com.duiba.paycenter.remoteservice.impl;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import cn.com.duiba.paycenter.biz.PayChargeBiz;
import cn.com.duiba.paycenter.constant.PayCenterErrorCode;
import cn.com.duiba.paycenter.params.PayChargeExtraParams;
import cn.com.duiba.paycenter.result.PayChargeResult;
import cn.com.duiba.paycenter.service.PayChargeService;
import cn.com.duiba.paycenter.util.SignUtil;
import org.springframework.web.bind.annotation.RestController;

/**
 * 账号充值相关服务
 * <AUTHOR>
 *
 */
@RestController
public class RemotePayChargeServiceImpl implements PayChargeService {
	
	private static Logger log=LoggerFactory.getLogger(RemotePayChargeServiceImpl.class);
	@Autowired
	private PayChargeBiz payChargeBiz;
	
	@Override
	public PayChargeResult chargeMoneyByManual(Long developerId, Long manualApplyId,
			Long money,String sign,PayChargeExtraParams p){
		log.debug(getClass().getName()+".chargeMoneyByManual("+developerId+","+manualApplyId+","+money+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("relationId", manualApplyId+"");
		params.put("money", money+"");
		String checksign=SignUtil.sign(params);
		
		if(!checksign.equals(sign)){
			return new PayChargeResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		
		return payChargeBiz.chargeMoneyByManual(developerId, manualApplyId, money, p);
	}

	@Override
	public PayChargeResult chargeMoneyByOnline(Long developerId, Long onlineApplyId,
			Long money,String sign,PayChargeExtraParams p) {
		log.debug(getClass().getName()+".chargeMoneyByOnline("+developerId+","+onlineApplyId+","+money+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("relationId", onlineApplyId+"");
		params.put("money", money+"");
		String checksign=SignUtil.sign(params);
		
		if(!checksign.equals(sign)){
			return new PayChargeResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		
		return payChargeBiz.chargeMoneyByOnline(developerId, onlineApplyId, money, p);
	}

	@Override
	public PayChargeResult reduceMoneyByManual(Long developerId, Long manualApplyId,
			Long money,String sign,PayChargeExtraParams p)  {
		log.debug(getClass().getName()+".reduceMoneyByManual("+developerId+","+manualApplyId+","+money+","+sign+")");
		Map<String, String> params=new HashMap<>();
		params.put("developerId", developerId+"");
		params.put("relationId", manualApplyId+"");
		params.put("money", money+"");
		String checksign=SignUtil.sign(params);
		
		if(!checksign.equals(sign)){
			return new PayChargeResult(false,PayCenterErrorCode.CodeParamsSignError, null);
		}
		
		return payChargeBiz.reduceMoneyByManual(developerId, manualApplyId, money, p);
	}
	
	
}
