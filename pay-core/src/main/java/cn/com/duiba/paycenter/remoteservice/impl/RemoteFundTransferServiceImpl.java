package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.biz.FundTransferBiz;
import cn.com.duiba.paycenter.dto.PayOrderDto;
import cn.com.duiba.paycenter.dto.payment.WxTransferPayDto;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxTransferPaySendRequest;
import cn.com.duiba.paycenter.enums.ChannelEnum;
import cn.com.duiba.paycenter.enums.PayOrderBizTypeEnum;
import cn.com.duiba.paycenter.enums.PayOrderStatusEnum;
import cn.com.duiba.paycenter.message.FundTransferRequestMessage;
import cn.com.duiba.paycenter.params.FundTransferRequestParams;
import cn.com.duiba.paycenter.remoteservice.RemoteFundTransferService;
import cn.com.duiba.paycenter.result.FundTransferResult;
import cn.com.duiba.paycenter.service.PayOrderService;
import cn.com.duiba.paycenter.service.payment.WxTransferPayService;
import cn.com.duiba.paycenter.util.RedisKeyFactory;
import cn.com.duiba.wolf.cache.AdvancedCacheClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Created by xiaoxuda on 2017/11/1.
 */
@RestController
public class RemoteFundTransferServiceImpl implements RemoteFundTransferService {
    private static Logger logger = LoggerFactory.getLogger(RemoteFundTransferServiceImpl.class);

    @Autowired
    private FundTransferBiz fundTransferBiz;
    @Autowired
    private PayOrderService payOrderService;
    @Autowired
    private WxTransferPayService wxTransferService;


    @Resource(name = "wxTransferExecutorService")
    private ExecutorService wxTransferExecutorService;

    @Resource(name = "redisTemplate")
    private AdvancedCacheClient advancedCacheClient;


    @Override
    public FundTransferResult fundTransfer(FundTransferRequestParams params) {
        try {
            return fundTransferBiz.doTransfer(params);
        }catch (Exception e){
            logger.error("请求资金转账失败", e);
            return new FundTransferResult(PayOrderStatusEnum.EXCEPTION, e.getMessage());
        }
    }

    @Override
    public FundTransferResult asynFundTransfer(FundTransferRequestMessage message) {
        try{
            return fundTransferBiz.doAsynTransfer(message);
        }catch (Exception e){
            logger.error("请求资金转账失败", e);
            return new FundTransferResult(PayOrderStatusEnum.EXCEPTION, e.getMessage());
        }
    }

    @Override
    public FundTransferResult findByBizTypeAndBizNo(PayOrderBizTypeEnum bizType, String bizNo) {
        PayOrderDto payOrder = payOrderService.findByUniqueIndex(bizType, bizNo);
        if(payOrder == null){
            return null;
        }
        return new FundTransferResult(payOrder.getPayStatus(), payOrder.getRemark());
    }

    @Override
    public FundTransferResult asynWxTransfer(WxTransferPaySendRequest request) {
        request.setChannelType(ChannelEnum.WX_RED_PACKET.getChannelType());
        return fundTransferBiz.doWxAsynTransfer(request);
    }

    @Override
    public FundTransferResult wxTransfer(WxTransferPaySendRequest request) {
        try {
            wxTransferExecutorService.execute(()->{
                request.setChannelType(ChannelEnum.WX_CHANGE.getChannelType());
                wxTransferService.sendWxTransfer(request);
            });
        } catch (RejectedExecutionException e) {
            return new FundTransferResult(PayOrderStatusEnum.FAIL, "队列已满");
        }
        return new FundTransferResult(PayOrderStatusEnum.PROCESSING, null);
    }

    @Override
    public WxTransferPayDto queryWxTransfer(String bizOrderNo, String bizRelationId, Integer bizRelationType) {
        return  advancedCacheClient.getWithCacheLoader(
                getWxTransferRedisKey(bizOrderNo,bizRelationId,bizRelationType)
                , 5
                , TimeUnit.MINUTES
                , true
                , () -> convert2Dto(wxTransferService.getByBizInfo(bizOrderNo, bizRelationId,bizRelationType)));
    }

    private static String getWxTransferRedisKey(String bizOrderNo, String bizRelationId, Integer bizRelationType) {
        return RedisKeyFactory.K013.toString().concat(String.valueOf(bizRelationType)).concat("_")
                .concat(bizRelationId).concat("_").concat(bizOrderNo);
    }


    private WxTransferPayDto convert2Dto(WxTransferPayDto dto) {
        if (dto == null) {
            return null;
        }
        //不返还商户号信息
        dto.setMchId("");
        return dto;
    }

//    @Override
//    public WxTransferPayDto queryWxTransfer(WxTransferQueryRequest request) throws BizException {
//        request.setChannelType(ChannelEnum.WX_RED_PACKET.getChannelType());
//        return wxTransferService.queryWxTransfer(request);
//    }
}
