package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayChargeNotifyResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayLiteNotifyV3Request;
import cn.com.duiba.paycenter.dto.payment.refund.wxpay.WxPayRefundNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteWxPayNotifyService;
import cn.com.duiba.paycenter.service.payment.WxPayNotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2018/11/21
 */
@RestController
public class RemoteWxPayNotifyServiceImpl implements RemoteWxPayNotifyService {
    @Autowired
    private WxPayNotifyService wxPayNotifyService;

    @Override
    public WxPayChargeNotifyResponse orderNotify(String xmlData) throws BizException {
        return wxPayNotifyService.orderNotify(xmlData);
    }

    @Override
    public WxPayRefundNotifyResponse refundNotify(String xmlData) throws BizException {
        return wxPayNotifyService.refundNotify(xmlData);
    }

    @Override
    public WxPayRefundNotifyResponse refundNotifyV3(WxPayLiteNotifyV3Request request) {
        return wxPayNotifyService.refundNotifyV3(request);
    }

    @Override
    public WxPayChargeNotifyResponse orderNotifyBySubjectType(String xmlData, String subjectType) throws BizException {
        return wxPayNotifyService.orderNotifyBySubjectType(xmlData, subjectType);
    }

    @Override
    public WxPayRefundNotifyResponse refundNotifyBySubjectType(String xmlData, String subjectType) throws BizException {
        return wxPayNotifyService.refundNotifyBySubjectType(xmlData, subjectType);
    }
}
