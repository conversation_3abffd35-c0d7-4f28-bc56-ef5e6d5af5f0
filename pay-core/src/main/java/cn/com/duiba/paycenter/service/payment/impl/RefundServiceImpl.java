package cn.com.duiba.paycenter.service.payment.impl;

import cn.com.duiba.api.enums.SubjectTypeEnum;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.developer.center.api.utils.WhiteAccessUtil;
import cn.com.duiba.message.service.api.dto.DingGeneralMsgRquest;
import cn.com.duiba.message.service.api.enums.DingMsgTypeEnum;
import cn.com.duiba.message.service.api.remoteservice.RemoteDingMsgService;
import cn.com.duiba.paycenter.config.BocConfig;
import cn.com.duiba.paycenter.config.ChargeConstant;
import cn.com.duiba.paycenter.config.EmailRecipientConfig;
import cn.com.duiba.paycenter.config.duibalive.DuibaLiveMpPayConfig;
import cn.com.duiba.paycenter.config.lshm.LshmPayConfig;
import cn.com.duiba.paycenter.config.wxlite.WxLiteCustomConfig;
import cn.com.duiba.paycenter.constant.DuibaLiveConstant;
import cn.com.duiba.paycenter.dao.payment.ChargeOrderDao;
import cn.com.duiba.paycenter.dao.payment.RefundOrderDao;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifeCreateRefundResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifeRefundRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbLifeRefundResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cus.YbsRefundOrderReq;
import cn.com.duiba.paycenter.dto.payment.charge.cus.YbsRefundOrderResp;
import cn.com.duiba.paycenter.dto.payment.charge.elife.IcbcELifeCreateRefundResponse;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.refund.NbcbRefundRequestDto;
import cn.com.duiba.paycenter.dto.payment.charge.shenzhenumsunionpay.UnionPayUmsRefundRequest;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayRefundRequest;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayRefundResponse;
import cn.com.duiba.paycenter.dto.payment.charge.xib.refund.XibRefundRequestDTO;
import cn.com.duiba.paycenter.dto.payment.refund.RefundOrderDto;
import cn.com.duiba.paycenter.dto.payment.refund.RefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.RefundResponse;
import cn.com.duiba.paycenter.dto.payment.refund.abc.AbcRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.alipay.AlipayRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.boc.BocRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.citic.CiticRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.cmbonenet.CmbOneNetRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.duibaLive.bf.DuibaLiveBFRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.duibaLive.installment.DuibaLiveInstallmentRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.duibaLive.mp.DuibaLiveMpRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.icbc.IcbcRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.icbc.elife.IcbcELifeRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.mock.MockRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.shouxin.ShouxinPayRefundRequest;
import cn.com.duiba.paycenter.dto.payment.refund.shouxin.ShouxinPayRefundResponse;
import cn.com.duiba.paycenter.dto.payment.refund.wjrcb.WjrcbRefundRequest;
import cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity;
import cn.com.duiba.paycenter.entity.payment.RefundHandlerResult;
import cn.com.duiba.paycenter.entity.payment.RefundOrderEntity;
import cn.com.duiba.paycenter.enums.BizTypeEnum;
import cn.com.duiba.paycenter.enums.ChannelEnum;
import cn.com.duiba.paycenter.enums.ChannelModeEnum;
import cn.com.duiba.paycenter.enums.ChargeOrderStatusEnum;
import cn.com.duiba.paycenter.enums.RefundErrorCodeEnum;
import cn.com.duiba.paycenter.enums.RefundOrderStatusEnum;
import cn.com.duiba.paycenter.enums.ybs.YbsRefundStatusEnum;
import cn.com.duiba.paycenter.service.XibPayService;
import cn.com.duiba.paycenter.service.payment.ChannelHandler;
import cn.com.duiba.paycenter.service.payment.ChannelHandlerManager;
import cn.com.duiba.paycenter.service.payment.RefundService;
import cn.com.duiba.paycenter.service.payment.UnionPayUmsService;
import cn.com.duiba.paycenter.service.payment.YbsPayService;
import cn.com.duiba.paycenter.service.payment.impl.ningboBank.NingboBankPayHandler;
import cn.com.duiba.paycenter.util.RandomUtils;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duibabiz.component.email.DuibaEmailHelper;
import cn.com.duibabiz.component.email.DuibaSendEmailBean;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @date 2018/11/27
 */
@Service
public class RefundServiceImpl implements RefundService {
    private static final Logger LOGGER = LoggerFactory.getLogger(RefundServiceImpl.class);
    private static final int MSG_MAX_LENGTH = 255;
    @Resource
    private RefundOrderDao refundOrderDao;
    @Resource
    private ChargeOrderDao chargeOrderDao;
    @Resource
    private Validator validator;
    private static final String SUFFIX = "RFD";
    @Resource
    private ExecutorService executorService;
    @Resource
    private DuibaEmailHelper duibaEmailHelper;
    @Autowired
    private EmailRecipientConfig emailRecipientConfig;
    @Autowired
    private RemoteDingMsgService remoteDingMsgService;
    @Autowired
    private BocConfig bocConfig;

    @Autowired
    private WxLiteCustomConfig wxLiteCustomConfig;

    @Resource
    private NingboBankPayHandler ningboBankPayHandler;

    @Resource
    private YbsPayService ybsPayService;

    @Resource
    private XibPayService xibPayService;

    @Resource
    private UnionPayUmsService unionPayUmsService;

    @Resource
    private LshmPayConfig llshmPayConfig;

    /**
     * 1.参数检查
     * 2.查找支付订单，单笔订单现在支持退一笔
     * 3.支付订单状态是否支持退款
     * 4.金额是否合法(<=实际支付金额)
     * 5.新增一条退款记录或者更新退款失败的记录
     * 6.异步发起退款
     */
    @Override
    public RefundResponse refund(RefundRequest request) throws BizException {
        LOGGER.info("RefundServiceImpl refund request:{}", JSONObject.toJSONString(request));
        Set<ConstraintViolation<RefundRequest>> errorSet = validator.validate(request);
        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }

        ChargeOrderEntity chargeOrderEntity = getOrder(request);
        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        if (StringUtils.isNotBlank(chargeOrderEntity.getExtra())
                && !StringUtils.equals(chargeOrderEntity.getChannelType(), ChannelEnum.BANK_OF_ABC_PAY.getChannelType())) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(chargeOrderEntity.getExtra());
                String mchId = jsonObject.getString("mchId");
                String wxAppId = jsonObject.getString("wxAppId");
                Long appId = chargeOrderEntity.getAppId();

                //如果配置中有  则这个appId下面的小程序appId,商户号在自主模式下也能退款
                boolean canRefund = wxLiteCustomConfig.wxLiteCanRefund(appId, mchId, wxAppId);
                if (ChannelModeEnum.INDEPENDENT.getCode().equals(jsonObject.getIntValue(ChargeConstant.CHANNEL_MODE))
                        && !BizTypeEnum.KJJ_RED_PACKET.getCode().equals(chargeOrderEntity.getBizType())
                        && !canRefund) {
                    refundResponse.setCode(RefundErrorCodeEnum.NOT_SUPPORT.getCode());
                    refundResponse.setMsg(RefundErrorCodeEnum.NOT_SUPPORT.getDesc());
                    return refundResponse;
                }
            } catch (Exception e) {
                LOGGER.warn("json 格式错误,json={}, chargeOrderId={}", chargeOrderEntity.getExtra(), chargeOrderEntity.getId());
                refundResponse.setCode(RefundErrorCodeEnum.DATA_ERROR.getCode());
                refundResponse.setMsg(RefundErrorCodeEnum.DATA_ERROR.getDesc());

                return refundResponse;
            }
        }


        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(chargeOrderEntity.getChannelType());
        if (channelHandler == null) {
            LOGGER.error("handler not exist, channelType={}", chargeOrderEntity.getChannelType());
            refundResponse.setCode(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());

            return refundResponse;
        }
        if (chargeOrderEntity.getAmount() < request.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = refundOrderDao.findByChargeOrderNo(request.getChargeOrderNo(), request.getOrdersItemId());
        if (refundOrderEntity == null) {
            refundOrderEntity = new RefundOrderEntity();

            refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
            refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
            refundOrderEntity.setExtra(chargeOrderEntity.getExtra());
            refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
            refundOrderEntity.setApplyRefundAmount(request.getAmount());
            refundOrderEntity.setDescription(request.getDescription());
            refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
            refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
            refundOrderEntity.setOrdersItemId(request.getOrdersItemId());

            refundOrderDao.insert(refundOrderEntity);
        } else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            //第三方收单失败可以再次发起请求
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderDao.update(entity);
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());

            return refundResponse;
        }
        final RefundOrderEntity entity = refundOrderEntity;
        entity.setCallbackUrl(request.getCallbackUrl());
        executorService.submit(() -> asyncRefund(entity, channelHandler, request.getSubjectType()));
        refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());

        return refundResponse;
    }

    /**
     * 1.参数检查
     * 2.查找支付订单，单笔订单现在支持退一笔
     * 3.支付订单状态是否支持退款
     * 4.金额是否合法(<=实际支付金额)
     * 5.新增一条退款记录或者更新退款失败的记录
     * 6.异步发起退款
     */
    @Override
    public RefundResponse commonRefund(RefundRequest request) throws BizException {
        LOGGER.info("RefundServiceImpl refund request:{}", JSONObject.toJSONString(request));
        Set<ConstraintViolation<RefundRequest>> errorSet = validator.validate(request);
        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }

        ChargeOrderEntity chargeOrderEntity = getOrder(request);
        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(chargeOrderEntity.getChannelType());
        if (channelHandler == null) {
            LOGGER.error("handler not exist, channelType={}", chargeOrderEntity.getChannelType());
            refundResponse.setCode(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());

            return refundResponse;
        }
        if (chargeOrderEntity.getAmount() < request.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = refundOrderDao.findByChargeOrderNo(request.getChargeOrderNo(), request.getOrdersItemId());
        if (refundOrderEntity == null) {
            refundOrderEntity = new RefundOrderEntity();
            refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
            if (llshmPayConfig.getAppIds().contains(chargeOrderEntity.getAppId())){
                refundOrderEntity.setOrderNo(generateRefundOrderNoByLshm(chargeOrderEntity.getOrderNo()));
            }
            refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
            refundOrderEntity.setExtra(chargeOrderEntity.getExtra());
            refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
            refundOrderEntity.setApplyRefundAmount(request.getAmount());
            refundOrderEntity.setDescription(request.getDescription());
            refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
            refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
            refundOrderEntity.setOrdersItemId(request.getOrdersItemId());

            refundOrderDao.insert(refundOrderEntity);
        } else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            //第三方收单失败可以再次发起请求
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderDao.update(entity);
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());

            return refundResponse;
        }
        final RefundOrderEntity entity = refundOrderEntity;
        entity.setCallbackUrl(request.getCallbackUrl());
        executorService.submit(() -> asyncRefund(entity, channelHandler, request.getSubjectType()));
        refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());

        return refundResponse;
    }

    @Override
    public RefundResponse alipayRefund(AlipayRefundRequest alipayRefundRequest) throws BizException {
        RefundResponse refundResponse = new RefundResponse();
        Set<ConstraintViolation<AlipayRefundRequest>> errorSet = validator.validate(alipayRefundRequest);
        if (!errorSet.isEmpty()) {
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            return refundResponse;
        }
        ChargeOrderEntity chargeOrderEntity;
        if (StringUtils.isNotBlank(alipayRefundRequest.getTradeNo())) {
            chargeOrderEntity = chargeOrderDao.findByTransactionNo(alipayRefundRequest.getTradeNo());
        } else if (StringUtils.isNotBlank(alipayRefundRequest.getChargeOrderNo())) {
            chargeOrderEntity = chargeOrderDao.findByOrderNo(alipayRefundRequest.getChargeOrderNo());
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.PARAM_ERROR.getDesc());
            return refundResponse;
        }
        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());
            return refundResponse;
        }
        if (StringUtils.isNotBlank(chargeOrderEntity.getExtra())) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(chargeOrderEntity.getExtra());
                if (ChannelModeEnum.INDEPENDENT.getCode().equals(jsonObject.getIntValue(ChargeConstant.CHANNEL_MODE))) {
                    refundResponse.setMsg(RefundErrorCodeEnum.NOT_SUPPORT.getDesc());
                    refundResponse.setCode(RefundErrorCodeEnum.NOT_SUPPORT.getCode());

                    return refundResponse;
                }
            } catch (Exception e) {
                LOGGER.warn("json 格式错误,json={}, chargeOrderId={}", chargeOrderEntity.getExtra(), chargeOrderEntity.getId());
                refundResponse.setMsg(RefundErrorCodeEnum.DATA_ERROR.getDesc());
                refundResponse.setCode(RefundErrorCodeEnum.DATA_ERROR.getCode());

                return refundResponse;
            }
        }
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());

            return refundResponse;
        }
        if (chargeOrderEntity.getAmount() < alipayRefundRequest.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }
        return getRefundResponse(alipayRefundRequest, chargeOrderEntity);

    }

    @Override
    public RefundResponse wjrcbRefund(WjrcbRefundRequest refundRequest) throws BizException {
        Set<ConstraintViolation<WjrcbRefundRequest>> errorSet = validator.validate(refundRequest);
        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }

        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                refundRequest.getBizOrderNo(), BizTypeEnum.ORD.getCode());
        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());

            return refundResponse;
        }

        if (chargeOrderEntity.getAmount() < refundRequest.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }

        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());

            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = new RefundOrderEntity();

        refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
        refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
        refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
        refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
        refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
        refundOrderEntity.setApplyRefundAmount(refundRequest.getAmount());
        refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
        refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
        refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
        refundOrderEntity.setOrdersItemId(refundRequest.getOrdersItemId());

        refundOrderDao.insert(refundOrderEntity);
        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(ChannelEnum.WJRCB_WX_PUB.getChannelType());

        RefundHandlerResult result = channelHandler.createRefund(refundOrderEntity, SubjectTypeEnum.DUIBA.getType());

        if (result.isSuccess()) {
            refundOrderDao.updateRefundNo(refundOrderEntity.getOrderNo(), result.getRefundNo());
            refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.ORDER_FAILED.getCode());
            entity.setFailureCode(result.getFailCode());
            entity.setFailureMsg(subString(result.getFailMsg()));
            //防止错误超出数据库长度
            refundOrderDao.update(entity);
        }

        return refundResponse;
    }

    @Override
    public RefundResponse cmbOneNetPayRefund(CmbOneNetRefundRequest refundRequest) throws BizException {
        Set<ConstraintViolation<CmbOneNetRefundRequest>> errorSet = validator.validate(refundRequest);
        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                refundRequest.getBizOrderNo(), BizTypeEnum.ORD.getCode());
        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        if (chargeOrderEntity.getAmount() < refundRequest.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());

            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = new RefundOrderEntity();

        refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
        refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
        refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
        refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
        refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
        refundOrderEntity.setApplyRefundAmount(refundRequest.getAmount());
        refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
        refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
        refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
        refundOrderEntity.setOrdersItemId(refundRequest.getOrdersItemId());

        refundOrderDao.insert(refundOrderEntity);
        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(ChannelEnum.ONE_NET_PAY_OF_CMB.getChannelType());

        RefundHandlerResult result = channelHandler.createRefund(refundOrderEntity, null);

        if (result.isSuccess()) {
            refundOrderDao.updateRefundNo(refundOrderEntity.getOrderNo(), result.getRefundNo());
            refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.ORDER_FAILED.getCode());
            entity.setFailureCode(result.getFailCode());
            entity.setFailureMsg(subString(result.getFailMsg()));
            //防止错误超出数据库长度
            refundOrderDao.update(entity);
            refundResponse.setMsg(result.getFailMsg());
        }

        return refundResponse;
    }

    @Override
    public ShouxinPayRefundResponse shouxinPayRefund(ShouxinPayRefundRequest refundRequest) throws BizException {
        Set<ConstraintViolation<ShouxinPayRefundRequest>> errorSet = validator.validate(refundRequest);
        ShouxinPayRefundResponse refundResponse = new ShouxinPayRefundResponse();
        refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                refundRequest.getBizOrderNo(), BizTypeEnum.ORD.getCode());
        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        if (chargeOrderEntity.getAmount() < refundRequest.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());

            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = new RefundOrderEntity();

        refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
        refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
        refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
        refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
        refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
        refundOrderEntity.setApplyRefundAmount(refundRequest.getAmount());
        refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
        refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
        refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
        refundOrderEntity.setExtra(JSONObject.toJSONString(refundRequest));
        refundOrderEntity.setOrdersItemId(refundRequest.getOrdersItemId());
        refundOrderDao.insert(refundOrderEntity);
        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(ChannelEnum.SHOUXIN_PAY.getChannelType());

        RefundHandlerResult result = channelHandler.createRefund(refundOrderEntity, null);
        RefundOrderEntity entity = new RefundOrderEntity();
        if (result.isSuccess()) {
            refundOrderDao.updateRefundNo(refundOrderEntity.getOrderNo(), result.getRefundNo());
            if (StringUtils.equals(result.getStatus(), "success")) {
                entity.setId(refundOrderEntity.getId());
                entity.setRefundStatus(RefundOrderStatusEnum.SUCCEEDED.getCode());
                entity.setRefundAmount(refundRequest.getAmount());
                entity.setRefundTime(new Date());
                refundOrderDao.update(entity);
            }
            refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
            refundResponse.setStatus(result.getStatus());
        } else {
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.ORDER_FAILED.getCode());
            entity.setFailureCode(result.getFailCode());
            entity.setFailureMsg(subString(result.getFailMsg()));
            refundOrderDao.update(entity);
            refundResponse.setMsg(result.getFailMsg());
            refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
        }
        return refundResponse;
    }

    @Override
    public RefundOrderDto findByTransactionNo(String transactionNo) {
        if (StringUtils.isBlank(transactionNo)) {
            return null;
        }
        return BeanUtils.copy(refundOrderDao.findByTransactionNo(transactionNo), RefundOrderDto.class);
    }

    @Override
    public RefundResponse abcRefund(AbcRefundRequest request) {
        Set<ConstraintViolation<AbcRefundRequest>> errorSet = validator.validate(request);
        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }

        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                request.getBizOrderNo(), null == request.getBizType() ? BizTypeEnum.ORD.getCode() : request.getBizType());
        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());

            return refundResponse;
        }

        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(chargeOrderEntity.getChannelType());
        if (channelHandler == null) {
            LOGGER.error("handler not exist, channelType={}", chargeOrderEntity.getChannelType());
            refundResponse.setCode(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());

            return refundResponse;
        }
        if (chargeOrderEntity.getAmount() < request.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = refundOrderDao.findByChargeOrderNo(chargeOrderEntity.getOrderNo(), request.getOrdersItemId());
        if (refundOrderEntity == null) {
            refundOrderEntity = new RefundOrderEntity();

            refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
            refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
            refundOrderEntity.setExtra(chargeOrderEntity.getExtra());
            refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
            refundOrderEntity.setApplyRefundAmount(request.getAmount());
            refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
            refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
            refundOrderEntity.setOrdersItemId(request.getOrdersItemId());

            refundOrderDao.insert(refundOrderEntity);
        } else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            //第三方收单失败可以再次发起请求
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderDao.update(entity);
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());

            return refundResponse;
        }
        refundOrderEntity.setDescription(request.getDuibaAbcAccountType() != null ? String.valueOf(request.getDuibaAbcAccountType()) : null);
        RefundHandlerResult refundHandlerResult = channelHandler.createRefund(refundOrderEntity, null);
        if (refundHandlerResult.isSuccess()) {
            refundOrderDao.updateRefundNoWithStatus(refundOrderEntity.getOrderNo(), refundHandlerResult.getRefundNo(), RefundOrderStatusEnum.SUCCEEDED);
            refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.ORDER_FAILED.getCode());
            entity.setFailureCode(refundHandlerResult.getFailCode());
            entity.setFailureMsg(subString(refundHandlerResult.getFailMsg()));
            //防止错误超出数据库长度
            refundOrderDao.update(entity);
            LOGGER.warn("农行银行 退款失败 send sms error {}", String.format("支付订单号：%s，失败原因：%s", refundOrderEntity.getChargeOrderNo(), refundHandlerResult.getFailMsg()));
        }


        return refundResponse;
    }

    @Override
    public RefundResponse citicRefund(CiticRefundRequest request) {
        Set<ConstraintViolation<CiticRefundRequest>> errorSet = validator.validate(request);
        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                request.getBizOrderNo(), request.getBizType() != null ? request.getBizType() : BizTypeEnum.ORD.getCode());

        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());

            return refundResponse;
        }

        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(chargeOrderEntity.getChannelType());
        if (channelHandler == null) {
            LOGGER.error("handler not exist, channelType={}", chargeOrderEntity.getChannelType());
            refundResponse.setCode(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());

            return refundResponse;
        }
        if (chargeOrderEntity.getAmount() < request.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = refundOrderDao.findByChargeOrderNo(chargeOrderEntity.getOrderNo(), request.getOrdersItemId());
        if (refundOrderEntity == null) {
            refundOrderEntity = new RefundOrderEntity();

            refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
            refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
            refundOrderEntity.setExtra(chargeOrderEntity.getExtra());
            refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
            refundOrderEntity.setApplyRefundAmount(request.getAmount());
            refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
            refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
            refundOrderEntity.setOrdersItemId(request.getOrdersItemId());

            refundOrderDao.insert(refundOrderEntity);
        } else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            //第三方收单失败可以再次发起请求
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderDao.update(entity);
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());

            return refundResponse;
        }
        String subjectType = request.getSupplierTag() == null ? null : String.valueOf(request.getSupplierTag());
        RefundHandlerResult refundHandlerResult = channelHandler.createRefund(refundOrderEntity, subjectType);

        //退款成功
        if (refundHandlerResult.isSuccess()) {
            RefundOrderEntity upEntity = new RefundOrderEntity();
            upEntity.setId(refundOrderEntity.getId());
            upEntity.setRefundAmount(refundHandlerResult.getReundAmt());
            upEntity.setRefundStatus(RefundOrderStatusEnum.SUCCEEDED.getCode());
            upEntity.setRefundTime(refundHandlerResult.getTranEndDate());
            //更新退款成功
            refundOrderDao.update(upEntity);
            //更新第三方退款成功
            if (StringUtils.isNotBlank(refundHandlerResult.getRefundNo())) {
                refundOrderDao.updateRefundNoWithStatus(refundOrderEntity.getOrderNo(), refundHandlerResult.getRefundNo(), RefundOrderStatusEnum.SUCCEEDED);
            }
            refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
        } else {
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.ORDER_FAILED.getCode());
            entity.setFailureCode(refundHandlerResult.getFailCode());
            entity.setFailureMsg(subString(refundHandlerResult.getFailMsg()));
            //防止错误超出数据库长度，直接更新为第三方退款失败
            refundOrderDao.update(entity);

            refundResponse.setMsg(refundHandlerResult.getFailMsg());
            refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
        }

        return refundResponse;
    }

    @Override
    public RefundResponse bocRefund(BocRefundRequest request) {
        Set<ConstraintViolation<BocRefundRequest>> errorSet = validator.validate(request);
        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                request.getBizOrderNo(), BizTypeEnum.ORD.getCode());

        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());

            return refundResponse;
        }

        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(chargeOrderEntity.getChannelType());
        if (channelHandler == null) {
            LOGGER.error("handler not exist, channelType={}", chargeOrderEntity.getChannelType());
            refundResponse.setCode(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());

            return refundResponse;
        }
        if (chargeOrderEntity.getAmount() < request.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = refundOrderDao.findByChargeOrderNo(chargeOrderEntity.getOrderNo(), request.getOrdersItemId());
        if (refundOrderEntity == null) {
            refundOrderEntity = new RefundOrderEntity();

            refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
            refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
            refundOrderEntity.setExtra(chargeOrderEntity.getExtra());
            refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
            refundOrderEntity.setApplyRefundAmount(request.getAmount());
            refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
            refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
            refundOrderEntity.setOrdersItemId(request.getOrdersItemId());

            refundOrderDao.insert(refundOrderEntity);
        } else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            //第三方收单失败可以再次发起请求
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderDao.update(entity);
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());

            return refundResponse;
        }
        RefundHandlerResult refundHandlerResult = channelHandler.createRefund(refundOrderEntity, null);
        if (refundHandlerResult.isSuccess()) {
            refundOrderDao.updateRefundNoWithStatus(refundOrderEntity.getOrderNo(), refundHandlerResult.getRefundNo(), RefundOrderStatusEnum.SUCCEEDED);
            refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
        } else {
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.ORDER_FAILED.getCode());
            entity.setFailureCode(refundHandlerResult.getFailCode());
            entity.setFailureMsg(subString(refundHandlerResult.getFailMsg()));
            //防止错误超出数据库长度
            refundOrderDao.update(entity);
            try {
                DingGeneralMsgRquest dingGeneralMsgRquest = new DingGeneralMsgRquest();
                dingGeneralMsgRquest.setAtAdminIds(bocConfig.getRefundAdminIds());
                dingGeneralMsgRquest.setTitle(bocConfig.getRefundTitle());
                dingGeneralMsgRquest.setContent(String.format(bocConfig.getRefundContent(), refundOrderEntity.getChargeOrderNo(), refundOrderEntity.getAppId(), refundHandlerResult.getFailMsg()));
                remoteDingMsgService.sendRobotByToken(bocConfig.getRefundToken(), DingMsgTypeEnum.text, dingGeneralMsgRquest);
            } catch (Exception e) {
                LOGGER.warn("中国银行 退款失败 send sms error", e);
            }
        }


        return refundResponse;
    }

    @Override
    public RefundResponse wandaRefund(RefundRequest request) {
        LOGGER.info("wandaRefund refund request:{}", JSONObject.toJSONString(request));
        Set<ConstraintViolation<RefundRequest>> errorSet = validator.validate(request);
        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }

        ChargeOrderEntity chargeOrderEntity = getOrder(request);
        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(chargeOrderEntity.getChannelType());
        if (channelHandler == null) {
            LOGGER.error("handler not exist, channelType={}", chargeOrderEntity.getChannelType());
            refundResponse.setCode(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());

            return refundResponse;
        }
        if (chargeOrderEntity.getAmount() < request.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = refundOrderDao.findByChargeOrderNo(chargeOrderEntity.getOrderNo(), request.getOrdersItemId());
        if (refundOrderEntity == null) {
            refundOrderEntity = new RefundOrderEntity();

            refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
            refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
            refundOrderEntity.setExtra(chargeOrderEntity.getExtra());
            refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
            refundOrderEntity.setApplyRefundAmount(request.getAmount());
            refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
            refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
            refundOrderEntity.setOrdersItemId(request.getOrdersItemId());

            refundOrderDao.insert(refundOrderEntity);
        } else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            //第三方收单失败可以再次发起请求
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderDao.update(entity);
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());

            return refundResponse;
        }
        RefundHandlerResult refundHandlerResult = channelHandler.createRefund(refundOrderEntity, null);
        if (refundHandlerResult.isSuccess()) {
            refundOrderDao.updateRefundNoWithStatus(refundOrderEntity.getOrderNo(), refundHandlerResult.getRefundNo(), RefundOrderStatusEnum.SUCCEEDED);
            refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
        } else {
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.ORDER_FAILED.getCode());
            entity.setFailureCode(refundHandlerResult.getFailCode());
            entity.setFailureMsg(subString(refundHandlerResult.getFailMsg()));
            //防止错误超出数据库长度
            refundOrderDao.update(entity);
        }
        return refundResponse;
    }

    @Override
    public RefundOrderDto findByRefundOrderNo(String refundOrderNo) {
        if (StringUtils.isBlank(refundOrderNo)) {
            return null;
        }
        return BeanUtils.copy(refundOrderDao.findByRefundOrderNo(refundOrderNo), RefundOrderDto.class);
    }

    @Override
    public List<RefundOrderDto> findByBizNo(String bizOrderNo) {
        if (StringUtils.isBlank(bizOrderNo)) {
            return Collections.emptyList();
        }
        return BeanUtils.copyList(refundOrderDao.selectByBizNo(bizOrderNo), RefundOrderDto.class);
    }

    @Override
    public List<RefundOrderDto> selectByBizOrderNosAndTypeForRefundSuccess(List<String> bizOrderNos, Integer bizType) {
        return BeanUtils.copyList(refundOrderDao.selectByBizOrderNosAndTypeForRefundSuccess(bizOrderNos,bizType), RefundOrderDto.class);
    }

    private RefundResponse getRefundResponse(AlipayRefundRequest alipayRefundRequest
            , ChargeOrderEntity chargeOrderEntity) {
        RefundResponse refundResponse = new RefundResponse();
        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(ChannelEnum.ALIPAY_WAP.getChannelType());
        RefundOrderEntity refundOrderEntity = refundOrderDao.findByChargeOrderNo(alipayRefundRequest.getChargeOrderNo(), alipayRefundRequest.getOrdersItemId());
        if (refundOrderEntity == null) {
            refundOrderEntity = buildRefundOrderEntity(alipayRefundRequest, chargeOrderEntity);

            //refundOrderEntity会在方法内改变内容
            RefundHandlerResult refundHandlerResult = channelHandler.createRefund(refundOrderEntity, alipayRefundRequest.getSubjectType());
            refundOrderDao.insert(refundOrderEntity);

            if (refundHandlerResult.isSuccess()) {
                refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
                refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
            } else {
                refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
                refundResponse.setMsg(RefundErrorCodeEnum.FAIL.getDesc());
            }
            return refundResponse;
        } else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            //第三方收单失败可以再次发起请求
            RefundHandlerResult refundHandlerResult = channelHandler.createRefund(refundOrderEntity, alipayRefundRequest.getSubjectType());
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(refundOrderEntity.getRefundStatus());
            if (refundHandlerResult.isSuccess()) {
                entity.setRefundTime(refundOrderEntity.getRefundTime());
                entity.setRefundAmount(refundOrderEntity.getRefundAmount());
                entity.setRefundNo(refundOrderEntity.getRefundNo());

                refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
                refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
            } else {
                entity.setFailureMsg(refundOrderEntity.getFailureMsg());

                refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
                refundResponse.setMsg(RefundErrorCodeEnum.FAIL.getDesc());
            }
            refundOrderDao.update(entity);
            return refundResponse;
        } else {
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getCode());

            return refundResponse;
        }
    }

    private RefundOrderEntity buildRefundOrderEntity(AlipayRefundRequest alipayRefundRequest
            , ChargeOrderEntity chargeOrderEntity) {
        RefundOrderEntity refundOrderEntity = new RefundOrderEntity();

        refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
        refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
        refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
        refundOrderEntity.setExtra(chargeOrderEntity.getExtra());
        refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
        refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
        //退款可以少于支付金额
        refundOrderEntity.setApplyRefundAmount(alipayRefundRequest.getAmount());
        refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
        refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
        refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
        refundOrderEntity.setOrdersItemId(alipayRefundRequest.getOrdersItemId());

        return refundOrderEntity;
    }

    private void asyncRefund(RefundOrderEntity refundOrderEntity, ChannelHandler channelHandler, String subjectType) {
        RefundHandlerResult refundHandlerResult = channelHandler.createRefund(refundOrderEntity, subjectType);
        if(refundHandlerResult.isFinishSuccess()){
            refundOrderDao.updateRefundNoWithStatus(refundOrderEntity.getOrderNo(), refundHandlerResult.getRefundNo(), RefundOrderStatusEnum.SUCCEEDED);
            return;
        }
        if (refundHandlerResult.isSuccess()) {
            refundOrderDao.updateRefundNo(refundOrderEntity.getOrderNo(), refundHandlerResult.getRefundNo());
        } else {
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.ORDER_FAILED.getCode());
            entity.setFailureCode(refundHandlerResult.getFailCode());
            entity.setFailureMsg(subString(refundHandlerResult.getFailMsg()));
            //防止错误超出数据库长度
            refundOrderDao.update(entity);
            LOGGER.warn("微信退款失败 {}",String.format("支付订单号：%s，失败原因：%s"
                    , refundOrderEntity.getChargeOrderNo(), refundHandlerResult.getFailMsg()));
        }
    }

    private String subString(String failMsg) {
        if (StringUtils.isBlank(failMsg)) {
            return failMsg;
        }
        if (failMsg.length() > MSG_MAX_LENGTH) {
            return failMsg.substring(0, MSG_MAX_LENGTH);
        }
        return failMsg;
    }

    private ChargeOrderEntity getOrder(RefundRequest request) {
        ChargeOrderEntity chargeOrderEntity = null;
        if (StringUtils.isNotBlank(request.getTransactionNo())) {
            chargeOrderEntity = chargeOrderDao.findByTransactionNo(request.getTransactionNo());
        } else if (StringUtils.isNotBlank(request.getChargeOrderNo())) {
            chargeOrderEntity = chargeOrderDao.findByOrderNo(request.getChargeOrderNo());
        }
        return chargeOrderEntity;
    }

    @Override
    public RefundResponse mockRefund(MockRefundRequest request) {
        Set<ConstraintViolation<MockRefundRequest>> errorSet = validator.validate(request);
        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                request.getBizOrderNo(), BizTypeEnum.ORD.getCode());

        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());

            return refundResponse;
        }

        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(chargeOrderEntity.getChannelType());
        if (channelHandler == null) {
            LOGGER.error("handler not exist, channelType={}", chargeOrderEntity.getChannelType());
            refundResponse.setCode(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());

            return refundResponse;
        }
        if (chargeOrderEntity.getAmount() < request.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = refundOrderDao.findByChargeOrderNo(chargeOrderEntity.getOrderNo(), request.getOrdersItemId());
        if (refundOrderEntity == null) {
            refundOrderEntity = new RefundOrderEntity();

            refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
            refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
            refundOrderEntity.setExtra(chargeOrderEntity.getExtra());
            refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
            refundOrderEntity.setApplyRefundAmount(request.getAmount());
            refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
            refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
            refundOrderEntity.setOrdersItemId(request.getOrdersItemId());

            refundOrderDao.insert(refundOrderEntity);
        } else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            //第三方收单失败可以再次发起请求
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderDao.update(entity);
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());

            return refundResponse;
        }

        RefundOrderEntity upEntity = new RefundOrderEntity();
        upEntity.setId(refundOrderEntity.getId());
        upEntity.setRefundAmount(request.getAmount());
        upEntity.setRefundStatus(RefundOrderStatusEnum.SUCCEEDED.getCode());
        upEntity.setRefundTime(new Date());
        //更新退款成功
        refundOrderDao.update(upEntity);
        refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());

        return refundResponse;
    }

    @Override
    public UnionPayRefundResponse unionPayRefund(UnionPayRefundRequest request) {
        Set<ConstraintViolation<UnionPayRefundRequest>> errorSet = validator.validate(request);
        UnionPayRefundResponse refundResponse = new UnionPayRefundResponse();
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                request.getBizOrderNo(), request.getBizType() != null ? request.getBizType() : BizTypeEnum.ORD.getCode());

        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());

            return refundResponse;
        }

        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(chargeOrderEntity.getChannelType());
        if (channelHandler == null) {
            LOGGER.error("handler not exist, channelType={}", chargeOrderEntity.getChannelType());
            refundResponse.setCode(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());

            return refundResponse;
        }
        if (chargeOrderEntity.getAmount() < request.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = refundOrderDao.findByChargeOrderNo(chargeOrderEntity.getOrderNo(), request.getOrdersItemId());
        if (refundOrderEntity == null) {
            refundOrderEntity = new RefundOrderEntity();

            refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
            refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
            refundOrderEntity.setExtra(chargeOrderEntity.getExtra());
            refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
            refundOrderEntity.setApplyRefundAmount(request.getAmount());
            refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
            refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
            refundOrderEntity.setOrdersItemId(request.getOrdersItemId());

            if(Objects.equals(chargeOrderEntity.getBizType(), BizTypeEnum.XST.getCode())){
                refundOrderEntity.setCallbackUrl(request.getCallbackUrl());
            }

            refundOrderDao.insert(refundOrderEntity);
        } else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            //第三方收单失败可以再次发起请求
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            if(Objects.equals(chargeOrderEntity.getBizType(), BizTypeEnum.XST.getCode())){
                refundOrderEntity.setCallbackUrl(request.getCallbackUrl());
            }
            refundOrderDao.update(entity);
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());

            return refundResponse;
        }
        RefundHandlerResult refundHandlerResult = channelHandler.createRefund(refundOrderEntity, null);
        if (refundHandlerResult.isSuccess()) {
            // 更新为退款收单成功
            refundOrderDao.updateRefundNo(refundOrderEntity.getOrderNo(), refundHandlerResult.getRefundNo());
            refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
            refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
        } else {
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.ORDER_FAILED.getCode());
            entity.setFailureCode(refundHandlerResult.getFailCode());
            entity.setFailureMsg(subString(refundHandlerResult.getFailMsg()));
            //防止错误超出数据库长度
            refundOrderDao.update(entity);

            refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.FAIL.getDesc());
        }
        return refundResponse;
    }

    @Override
    public RefundResponse icbcRefund(IcbcRefundRequest request) {
        Set<ConstraintViolation<IcbcRefundRequest>> errorSet = validator.validate(request);
        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                request.getBizOrderNo(), request.getBizType() != null ? request.getBizType() : BizTypeEnum.ORD.getCode());

        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());

            return refundResponse;
        }

        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(chargeOrderEntity.getChannelType());
        if (channelHandler == null) {
            LOGGER.error("handler not exist, channelType={}", chargeOrderEntity.getChannelType());
            refundResponse.setCode(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());
            return refundResponse;
        }
        if (chargeOrderEntity.getAmount() < request.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = refundOrderDao.findByChargeOrderNo(chargeOrderEntity.getOrderNo(), request.getOrdersItemId());
        if (refundOrderEntity == null) {
            refundOrderEntity = new RefundOrderEntity();

            refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
            refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
            refundOrderEntity.setApplyRefundAmount(request.getAmount());
            refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
            refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
            refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
            refundOrderEntity.setExtra(chargeOrderEntity.getExtra());
            refundOrderEntity.setOrdersItemId(request.getOrdersItemId());

            refundOrderDao.insert(refundOrderEntity);
        } else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            //第三方收单失败可以再次发起请求
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderDao.update(entity);
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());

            return refundResponse;
        }
        RefundHandlerResult refundHandlerResult = channelHandler.createRefund(refundOrderEntity, null);

        //退款申请成功
        if (refundHandlerResult.isSuccess()) {
            RefundOrderEntity upEntity = new RefundOrderEntity();
            upEntity.setId(refundOrderEntity.getId());
            upEntity.setRefundAmount(refundHandlerResult.getReundAmt());
            upEntity.setRefundStatus(RefundOrderStatusEnum.ORDER_SUCCEEDED.getCode());
            //更新退款申请成功
            refundOrderDao.update(upEntity);
            //更新第三方退款订单号
            if (StringUtils.isNotBlank(refundHandlerResult.getRefundNo())) {
                refundOrderDao.updateRefundNoWithStatus(refundOrderEntity.getOrderNo(), refundHandlerResult.getRefundNo(), RefundOrderStatusEnum.ORDER_SUCCEEDED);
            }
            refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
        } else {
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.ORDER_FAILED.getCode());
            entity.setFailureCode(refundHandlerResult.getFailCode());
            entity.setFailureMsg(subString(refundHandlerResult.getFailMsg()));
            //防止错误超出数据库长度，直接更新为第三方退款失败
            refundOrderDao.update(entity);

            refundResponse.setMsg(refundHandlerResult.getFailMsg());
            refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
        }

        return refundResponse;
    }

    @Override
    public RefundResponse duibaLiveMpPayPayRefund(DuibaLiveMpRefundRequest request) {
        Set<ConstraintViolation<DuibaLiveMpRefundRequest>> errorSet = validator.validate(request);
        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                request.getBizOrderNo(), BizTypeEnum.ORD.getCode());

        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());
            return refundResponse;
        }

        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(chargeOrderEntity.getChannelType());
        if (channelHandler == null) {
            LOGGER.error("handler not exist, channelType={}", chargeOrderEntity.getChannelType());
            refundResponse.setCode(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());
            return refundResponse;
        }
        if (chargeOrderEntity.getAmount() < request.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = refundOrderDao.findByChargeOrderNo(chargeOrderEntity.getOrderNo(), request.getOrdersItemId());
        if (refundOrderEntity == null) {
            refundOrderEntity = new RefundOrderEntity();

            refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
            refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
            refundOrderEntity.setApplyRefundAmount(request.getAmount());
            refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
            refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
            refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
            JSONObject extra = new JSONObject();
            extra.put("channelType", chargeOrderEntity.getChannelType());
            //填充微信appId
            fillWechatAppId(chargeOrderEntity, extra);
            refundOrderEntity.setExtra(extra.toJSONString());
            refundOrderEntity.setOrdersItemId(request.getOrdersItemId());
            refundOrderDao.insert(refundOrderEntity);
        } else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            //第三方收单失败可以再次发起请求
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderDao.update(entity);
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());

            return refundResponse;
        }
        //自己根据业务定义了一下subjectType的含义 1代表兑吧直播的支付  2代表银联支付
        RefundHandlerResult refundHandlerResult = channelHandler.createRefund(refundOrderEntity, chargeOrderEntity.getExpireTime() == null ? DuibaLiveMpPayConfig.UNION : DuibaLiveMpPayConfig.DUIBA);

        //退款申请成功
        if (refundHandlerResult.isSuccess()) {
            RefundOrderEntity upEntity = new RefundOrderEntity();
            upEntity.setId(refundOrderEntity.getId());
            upEntity.setRefundAmount(refundHandlerResult.getReundAmt());
            upEntity.setRefundStatus(RefundOrderStatusEnum.SUCCEEDED.getCode());
            //更新退款申请成功
            refundOrderDao.update(upEntity);
            //更新第三方退款订单号
            if (StringUtils.isNotBlank(refundHandlerResult.getRefundNo())) {
                refundOrderDao.updateRefundNoWithStatus(refundOrderEntity.getOrderNo(), refundHandlerResult.getRefundNo(), RefundOrderStatusEnum.ORDER_SUCCEEDED);
            }
            refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
        } else {
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.ORDER_FAILED.getCode());
            entity.setFailureCode(refundHandlerResult.getFailCode());
            entity.setFailureMsg(subString(refundHandlerResult.getFailMsg()));
            //防止错误超出数据库长度，直接更新为第三方退款失败
            refundOrderDao.update(entity);

            refundResponse.setMsg(refundHandlerResult.getFailMsg());
            refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
        }
        return refundResponse;
    }

    private void fillWechatAppId(ChargeOrderEntity chargeOrderEntity, JSONObject extra) {
        try {
            String orderEntityExtra = chargeOrderEntity.getExtra();
            if (StringUtils.isNotBlank(orderEntityExtra)) {
                JSONObject jo = JSON.parseObject(orderEntityExtra);
                extra.put(DuibaLiveConstant.WECHAT_APP_ID, jo.getString(DuibaLiveConstant.WECHAT_APP_ID));
            }
        } catch (Exception e) {
            LOGGER.error("填充AppId失败", e);
        }
    }

    @Override
    public RefundResponse duibaLiveInstallmentPayPayRefund(DuibaLiveInstallmentRefundRequest request) {
        Set<ConstraintViolation<DuibaLiveInstallmentRefundRequest>> errorSet = validator.validate(request);
        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                request.getBizOrderNo(), BizTypeEnum.ORD.getCode());

        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());
            return refundResponse;
        }

        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(chargeOrderEntity.getChannelType());
        if (channelHandler == null) {
            LOGGER.error("handler not exist, channelType={}", chargeOrderEntity.getChannelType());
            refundResponse.setCode(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());
            return refundResponse;
        }
        if (chargeOrderEntity.getAmount() < request.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = refundOrderDao.findByChargeOrderNo(chargeOrderEntity.getOrderNo(), request.getOrdersItemId());
        if (refundOrderEntity == null) {
            refundOrderEntity = new RefundOrderEntity();

            refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
            refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
            refundOrderEntity.setApplyRefundAmount(request.getAmount());
            refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
            refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
            refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
            refundOrderEntity.setOrdersItemId(request.getOrdersItemId());

            refundOrderDao.insert(refundOrderEntity);
        } else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            //第三方收单失败可以再次发起请求
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderDao.update(entity);
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());

            return refundResponse;
        }
        RefundHandlerResult refundHandlerResult = channelHandler.createRefund(refundOrderEntity, null);

        //退款申请成功
        if (refundHandlerResult.isSuccess()) {
            RefundOrderEntity upEntity = new RefundOrderEntity();
            upEntity.setId(refundOrderEntity.getId());
            upEntity.setRefundAmount(refundHandlerResult.getReundAmt());
            upEntity.setRefundStatus(RefundOrderStatusEnum.ORDER_SUCCEEDED.getCode());
            //更新退款申请成功
            refundOrderDao.update(upEntity);
            //更新第三方退款订单号
            if (StringUtils.isNotBlank(refundHandlerResult.getRefundNo())) {
                refundOrderDao.updateRefundNoWithStatus(refundOrderEntity.getOrderNo(), refundHandlerResult.getRefundNo(), RefundOrderStatusEnum.ORDER_SUCCEEDED);
            }
            refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
        } else {
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.ORDER_FAILED.getCode());
            entity.setFailureCode(refundHandlerResult.getFailCode());
            entity.setFailureMsg(subString(refundHandlerResult.getFailMsg()));
            //防止错误超出数据库长度，直接更新为第三方退款失败
            refundOrderDao.update(entity);

            refundResponse.setMsg(refundHandlerResult.getFailMsg());
            refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
        }
        return refundResponse;
    }

    @Override
    public RefundResponse duibaLiveBFPayPayRefund(DuibaLiveBFRefundRequest request) {
        Set<ConstraintViolation<DuibaLiveBFRefundRequest>> errorSet = validator.validate(request);
        RefundResponse refundResponse = new RefundResponse();
        refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                request.getBizOrderNo(), BizTypeEnum.ORD.getCode());

        if (chargeOrderEntity == null) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());
            return refundResponse;
        }

        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(chargeOrderEntity.getChannelType());
        if (channelHandler == null) {
            LOGGER.error("handler not exist, channelType={}", chargeOrderEntity.getChannelType());
            refundResponse.setCode(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getDesc());

            return refundResponse;
        }
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());
            return refundResponse;
        }
        if (chargeOrderEntity.getAmount() < request.getAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());

            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = refundOrderDao.findByChargeOrderNo(chargeOrderEntity.getOrderNo(), request.getOrdersItemId());
        if (refundOrderEntity == null) {
            refundOrderEntity = new RefundOrderEntity();

            refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
            refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
            refundOrderEntity.setApplyRefundAmount(request.getAmount());
            refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
            refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
            refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
            JSONObject extra = new JSONObject();
            extra.put("channelType", chargeOrderEntity.getChannelType());
            extra.put("consumerId", request.getConsumerId());
            //填充微信appId
            fillWechatAppId(chargeOrderEntity, extra);
            refundOrderEntity.setExtra(extra.toJSONString());
            refundOrderEntity.setOrdersItemId(request.getOrdersItemId());
            refundOrderDao.insert(refundOrderEntity);
        } else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            //第三方收单失败可以再次发起请求
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderDao.update(entity);
        } else {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());

            return refundResponse;
        }
        //自己根据业务定义了一下subjectType的含义
        RefundHandlerResult refundHandlerResult = channelHandler.createRefund(refundOrderEntity, null);

        //退款申请成功
        if (refundHandlerResult.isSuccess()) {
            RefundOrderEntity upEntity = new RefundOrderEntity();
            upEntity.setId(refundOrderEntity.getId());
            upEntity.setRefundAmount(refundHandlerResult.getReundAmt());
            upEntity.setRefundStatus(RefundOrderStatusEnum.SUCCEEDED.getCode());
            //更新退款申请成功
            refundOrderDao.update(upEntity);
            //更新第三方退款订单号
            if (StringUtils.isNotBlank(refundHandlerResult.getRefundNo())) {
                refundOrderDao.updateRefundNoWithStatus(refundOrderEntity.getOrderNo(), refundHandlerResult.getRefundNo(), RefundOrderStatusEnum.ORDER_SUCCEEDED);
            }
            refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
        } else {
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.ORDER_FAILED.getCode());
            entity.setFailureCode(refundHandlerResult.getFailCode());
            entity.setFailureMsg(subString(refundHandlerResult.getFailMsg()));
            //防止错误超出数据库长度，直接更新为第三方退款失败
            refundOrderDao.update(entity);

            refundResponse.setMsg(refundHandlerResult.getFailMsg());
            refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
        }
        return refundResponse;
    }

    @Override
    public RefundResponse ningboBankPayRefund(NbcbRefundRequestDto nbcbRefundRequestDto) {
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                nbcbRefundRequestDto.getBizOrderNo(), BizTypeEnum.ORD.getCode());

        //预校验
        Pair<Boolean, RefundResponse> validResult = preValidArgs(chargeOrderEntity, nbcbRefundRequestDto);
        if (validResult.getLeft()) {
            return validResult.getRight();
        }

        //新增 或 刷新退款订单（失败可再次发起退款）
        RefundOrderEntity refundOrderEntity;
        try {
            refundOrderEntity = createOrRefreshRefundOrder(chargeOrderEntity, nbcbRefundRequestDto);
        } catch (BizException e) {
            RefundResponse refundResponse = new RefundResponse();
            refundResponse.setMsg(e.getMessage());
            return refundResponse;
        }

        //调用开发者接口退款
        RefundHandlerResult refundResult = ningboBankPayHandler.createRefund(refundOrderEntity, nbcbRefundRequestDto);

        LOGGER.info("ningboBankPayRefund, refundResult={}",JSON.toJSONString(refundResult));
        //解析开发者接口响应信息
        return handleNingBoBankDeveloperRefundResult(refundResult, refundOrderEntity);

    }


    /**
     * 前置校验
     *
     * @param chargeOrderEntity 支付订单
     * @param nbcbRefundRequestDto 退款请求
     * @return {是否通过校验, 响应}
     */
    private Pair<Boolean, RefundResponse> preValidArgs(ChargeOrderEntity chargeOrderEntity, NbcbRefundRequestDto nbcbRefundRequestDto) {
        RefundResponse refundResponse = new RefundResponse();

        //校验入参
        Set<ConstraintViolation<NbcbRefundRequestDto>> errorSet = validator.validate(nbcbRefundRequestDto);
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return Pair.of(Boolean.TRUE,refundResponse);
        }

        //支付订单校验
        if (Objects.isNull(chargeOrderEntity)) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());
            return Pair.of(Boolean.TRUE,refundResponse);
        }

        //退款金额校验
        if (chargeOrderEntity.getAmount() < nbcbRefundRequestDto.getTransAmt()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());
            return Pair.of(Boolean.TRUE,refundResponse);
        }

        //支付状态校验
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());
            return Pair.of(Boolean.TRUE,refundResponse);
        }

        return Pair.of(Boolean.FALSE, null);
    }


    private RefundOrderEntity createOrRefreshRefundOrder(ChargeOrderEntity chargeOrderEntity, NbcbRefundRequestDto nbcbRefundRequestDto) throws BizException {
        //TODO 整笔取消时，检查上游是否传了子订单号
        RefundOrderEntity refundOrderEntity = refundOrderDao.findByChargeOrderNo(chargeOrderEntity.getOrderNo(), nbcbRefundRequestDto.getOrdersItemId());

        //创建退款记录
        if (Objects.isNull(refundOrderEntity)) {
            refundOrderEntity = createNingboBankRefundOrder(chargeOrderEntity, nbcbRefundRequestDto);
        }

        //第三方收单失败可以再次发起请求
        else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            refundOrderEntity.setId(refundOrderEntity.getId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderDao.update(refundOrderEntity);
        }

        //不是可退款状态
        else {
            throw new BizException(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());
        }

        return refundOrderEntity;
    }
    /**
     * 创建宁波银行退款订单
     *
     * @param chargeOrderEntity 支付订单
     * @param nbcbRefundRequestDto 退款请求
     * @return 退款订单实体类
     */
    private RefundOrderEntity createNingboBankRefundOrder(ChargeOrderEntity chargeOrderEntity, NbcbRefundRequestDto nbcbRefundRequestDto) throws BizException {
        RefundOrderEntity refundOrderEntity = new RefundOrderEntity();

        refundOrderEntity.setOrderNo(genNbcbRefundOrderNo(chargeOrderEntity.getOrderNo()));
        refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
        refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
        refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
        //开发者流水号
        refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
        refundOrderEntity.setExtra(chargeOrderEntity.getExtra());
        //下单金额
        refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
        //申请退款的金额
        refundOrderEntity.setApplyRefundAmount(nbcbRefundRequestDto.getTransAmt());
        //BizTypeEnum.ORD
        refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
        //主订单
        refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
        refundOrderEntity.setOrdersItemId(nbcbRefundRequestDto.getOrdersItemId());

        if (refundOrderDao.insert(refundOrderEntity) != 1) {
            throw new BizException(String.format("创建退款订单失败 refundOrderEntity = %s", JSON.toJSONString(refundOrderEntity)));
        }
        return refundOrderEntity;
    }


    /**
     * 宁波银行因为超时比较多，依赖于我们轮训结果来，推动订单流转
     * 失败的情况下，updateRefundEntity 将改为初始化状态，方便轮训器，进行轮训 而不是改成失败
     * 退款结果解析
     *
     * @param refundResult 开发者退款结果
     * @param refundOrderEntity 退款订单
     * @return 结果
     */
    private RefundResponse handleNingBoBankDeveloperRefundResult(RefundHandlerResult refundResult, RefundOrderEntity refundOrderEntity) {
        RefundOrderEntity updateRefundEntity = new RefundOrderEntity();
        RefundResponse refundResponse = new RefundResponse();

        //退款请求受理
        if (refundResult.isSuccess()) {
            if(StringUtils.isNotBlank(refundResult.getRefundNo())) {
                //更新tb_refund_order第三方退款订单号 和 退款状态
                refundOrderDao.updateRefundNoWithStatus(refundOrderEntity.getOrderNo(), refundResult.getRefundNo(), RefundOrderStatusEnum.SUCCEEDED);
            } else {
                //更新tb_refund_order退款状态为成功
                updateRefundEntity.setId(refundOrderEntity.getId());
                updateRefundEntity.setRefundStatus(RefundOrderStatusEnum.SUCCEEDED.getCode());
                refundOrderDao.update(updateRefundEntity);
            }
            refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
            refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
        }
        //退款受理失败
        else {
            updateRefundEntity.setId(refundOrderEntity.getId());
            //更新售后订单状态为 申请失败
            updateRefundEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            //更新失败信息
            updateRefundEntity.setFailureCode(refundResult.getFailCode());
            updateRefundEntity.setFailureMsg(subString(refundResult.getFailMsg()));
            refundOrderDao.update(updateRefundEntity);

            refundResponse.setMsg(refundResult.getFailMsg());
            refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
        }

        return refundResponse;
    }

    /**
     * 避免退款单流水号过长接3位, 避免超过32位
     * @param refundNo 上游业务单号
     * @return 退款系统流水号
     */
    private String generateRefundOrderNo(String refundNo) {
        if(refundNo.indexOf("_") > -1){
            String[] arr = refundNo.split("_");
            return RandomUtils.getRandomString(3) + "_" + arr[1];
        }else{
            return RandomUtils.getRandomString(3) + "_" + refundNo;
        }
    }

    /**
     * 避免退款单流水号过长接3位, 避免超过32位
     * @param refundNo 上游业务单号
     * @return 退款系统流水号
     */
    private String generateRefundOrderNoByLshm(String refundNo) {
        if(refundNo.contains("_")){
            String[] arr = refundNo.split("_");
            return RandomUtils.getRandomString(3) + "_" + arr[1].split("C")[0];
        }else{
            return RandomUtils.getRandomString(3) + "_" + refundNo.split("C")[0];
        }
    }

    /**
     * 【宁波银行】生成refundOrderNo
     *
     * @param chargeOrderNo 支付订单号
     * @return refundOrderNo
     */
    private String genNbcbRefundOrderNo(String chargeOrderNo) {
        if (chargeOrderNo.contains("_")) {
            String[] arr = chargeOrderNo.split("_");
            return RandomUtils.getRandomString(3) + arr[1] + SUFFIX;
        } else {
            return RandomUtils.getRandomString(3) + chargeOrderNo + SUFFIX;
        }
    }

    @Override
    public YbsRefundOrderResp activity62VipRefund(YbsRefundOrderReq request) {
        Set<ConstraintViolation<YbsRefundOrderReq>> errorSet = validator.validate(request);
        YbsRefundOrderResp resp = new YbsRefundOrderResp();
        if (CollectionUtils.isNotEmpty(errorSet)) {
            resp.setCode(YbsRefundStatusEnum.PARAM_ERROR.getCode());
            resp.setMsg(errorSet.iterator().next().getMessage());
            return resp;
        }
        // 交易号核定单号 至少一个不为空
        String bizOrderNo = request.getBizOrderNo();
        String chargeOrderNo = request.getChargeOrderNo();
        if (StringUtils.isAllBlank(bizOrderNo, chargeOrderNo)) {
            resp.setCode(YbsRefundStatusEnum.PARAM_ERROR.getCode());
            resp.setMsg("bizOrderNo与chargeOrderNo 至少一个不为空");
        }
        // 支付订单校验
        ChargeOrderEntity chargeOrder = StringUtils.isNotBlank(chargeOrderNo) ? chargeOrderDao.findByOrderNo(chargeOrderNo):chargeOrderDao.findByBizOrderNoAndBizType(bizOrderNo, BizTypeEnum.XST.getCode());
        if (Objects.isNull(chargeOrder)) {
            resp.setCode(YbsRefundStatusEnum.ORDER_NOT_EXIST.getCode());
            resp.setMsg(YbsRefundStatusEnum.ORDER_NOT_EXIST.getDesc());
            return resp;
        }
        // 是否已支付成功 删除已支付校验
//        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrder.getChargeStatus())) {
//            resp.setCode(YbsRefundStatusEnum.ORDER_NOT_PAID.getCode());
//            resp.setMsg(YbsRefundStatusEnum.ORDER_NOT_PAID.getDesc());
//            return resp;
//        }
        // 退款金额 不能大于 支付金额
        if (chargeOrder.getAmount() < request.getAmount()) {
            resp.setCode(YbsRefundStatusEnum.REFUND_AMOUNT_ERROR.getCode());
            resp.setMsg(YbsRefundStatusEnum.REFUND_AMOUNT_ERROR.getDesc());
            return resp;
        }
        // 退款订单校验
        RefundOrderEntity refundOrder = refundOrderDao.findByChargeOrderNo(chargeOrder.getOrderNo(), null);
        if (Objects.nonNull(refundOrder)) {
            if (!request.getRefundNo().equals(refundOrder.getRefundNo())) {
                resp.setCode(YbsRefundStatusEnum.PARAM_ERROR.getCode());
                resp.setMsg("refundNo " + YbsRefundStatusEnum.PARAM_ERROR.getDesc());
                return resp;
            }

            if(RefundOrderStatusEnum.ORDER_SUCCEEDED.getCode().equals(refundOrder.getRefundStatus())) {
                resp.setCode(YbsRefundStatusEnum.REFUND_ORDER_STATUS_ERROR.getCode());
                resp.setMsg(YbsRefundStatusEnum.REFUND_ORDER_STATUS_ERROR.getDesc());
                return resp;
            }
        }

        if (Objects.isNull(refundOrder)) {
            refundOrder = new RefundOrderEntity();

            refundOrder.setOrderNo(generateRefundOrderNo(chargeOrder.getOrderNo()) + SUFFIX);
            refundOrder.setChargeOrderNo(chargeOrder.getOrderNo());
            refundOrder.setAppId(chargeOrder.getAppId());
            refundOrder.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            String transactionNo = chargeOrder.getTransactionNo();
            // 流水号如果不存在，则使用订单号
            if (StringUtils.isBlank(transactionNo)) {
                transactionNo = chargeOrder.getOrderNo();
            }
            refundOrder.setTransactionNo(transactionNo);
            refundOrder.setExtra(chargeOrder.getExtra());
            refundOrder.setChargeAmount(chargeOrder.getAmount());
            refundOrder.setApplyRefundAmount(request.getAmount());
            refundOrder.setBizType(chargeOrder.getBizType());
            refundOrder.setBizOrderNo(chargeOrder.getBizOrderNo());

            // 如果退款失败，用原退款单号重新发起
            refundOrder.setRefundNo(request.getRefundNo());
            refundOrderDao.insert(refundOrder);
        }
        return ybsPayService.doRefund(chargeOrder, refundOrder);
    }


    @Override
    public RefundResponse xibPayRefund(XibRefundRequestDTO xibRefundRequestDTO) {

        RefundResponse refundResponse = new RefundResponse();

        //校验入参
        Set<ConstraintViolation<XibRefundRequestDTO>> errorSet = validator.validate(xibRefundRequestDTO);
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }

        //支付订单校验
        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                xibRefundRequestDTO.getBizOrderNo(),
                xibRefundRequestDTO.getBizType() != null ? xibRefundRequestDTO.getBizType() : BizTypeEnum.ORD.getCode()
        );
        if (Objects.isNull(chargeOrderEntity)) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());
            return refundResponse;
        }

        //退款金额校验
        if (chargeOrderEntity.getAmount() < xibRefundRequestDTO.getTxnAmt()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());
            return refundResponse;
        }

        //支付状态校验
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());
            return refundResponse;
        }
        //新增 或 刷新退款订单（失败可再次发起退款）
        RefundOrderEntity refundOrderEntity;
        try {
            refundOrderEntity = xibCreateOrRefreshRefundOrder(chargeOrderEntity, xibRefundRequestDTO);
        } catch (BizException e) {
            LOGGER.warn("厦门国际 创建退款订单 异常 xibRefundRequestDTO={}", JSON.toJSONString(xibRefundRequestDTO), e);
            refundResponse.setMsg(e.getMessage());
            return refundResponse;
        }

        //调用开发者接口退款
        RefundHandlerResult refundResult = xibPayService.createRefund(refundOrderEntity, chargeOrderEntity, xibRefundRequestDTO);

        LOGGER.info("厦门国际 调用开发者接口退款 处理结果 xibRefundRequestDTO={} refundResult={}",
                JSON.toJSONString(xibRefundRequestDTO), JSON.toJSONString(refundResult));
        //解析开发者接口响应信息
        RefundOrderEntity updateRefundEntity = new RefundOrderEntity();
        //退款请求受理
        if (refundResult.isSuccess()) {
            if(StringUtils.isNotBlank(refundResult.getRefundNo())) {
                //更新tb_refund_order第三方退款订单号 和 退款状态
                refundOrderDao.updateRefundNoWithStatus(refundOrderEntity.getOrderNo(), refundResult.getRefundNo(), RefundOrderStatusEnum.ORDER_SUCCEEDED);
            } else {
                updateRefundEntity.setId(refundOrderEntity.getId());
                updateRefundEntity.setRefundStatus(RefundOrderStatusEnum.ORDER_SUCCEEDED.getCode());
                refundOrderDao.update(updateRefundEntity);
            }
            refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
            refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
        }
        //退款受理失败
        else {
            updateRefundEntity.setId(refundOrderEntity.getId());
            //更新售后订单状态为 申请失败
            //由于请求开发者异常情况也做失败处理了，这里直接设置退款失败。
            updateRefundEntity.setRefundStatus(RefundOrderStatusEnum.FAILED.getCode());
            //更新失败信息
            updateRefundEntity.setFailureCode(refundResult.getFailCode());
            updateRefundEntity.setFailureMsg(subString(refundResult.getFailMsg()));
            refundOrderDao.update(updateRefundEntity);

            refundResponse.setMsg(refundResult.getFailMsg());
            refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
        }

        return refundResponse;

    }

    @Override
    public RefundResponse unionPayUmsRefund(UnionPayUmsRefundRequest unionPayUmsRefundRequest) {

        RefundResponse refundResponse = new RefundResponse();

        //校验入参
        Set<ConstraintViolation<UnionPayUmsRefundRequest>> errorSet = validator.validate(unionPayUmsRefundRequest);
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(RefundErrorCodeEnum.PARAM_ERROR.getCode());
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }

        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                unionPayUmsRefundRequest.getBizOrderNo(),Integer.valueOf(unionPayUmsRefundRequest.getBizType()));

        //支付订单校验
        if (Objects.isNull(chargeOrderEntity)) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_EXIST.getDesc());
            return refundResponse;
        }


        //退款金额校验
        if (chargeOrderEntity.getAmount() < unionPayUmsRefundRequest.getRefundAmount()) {
            refundResponse.setCode(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.REFUND_AMOUNT_ERROR.getDesc());
            return refundResponse;
        }

        //支付状态校验
        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(RefundErrorCodeEnum.ORDER_NOT_PAID.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.ORDER_NOT_PAID.getDesc());
            return refundResponse;
        }


        // 处理器校验
        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(chargeOrderEntity.getChannelType());
        if (channelHandler == null) {
            LOGGER.error("handler not exist, channelType={}", chargeOrderEntity.getChannelType());
            refundResponse.setCode(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getCode());
            refundResponse.setMsg(RefundErrorCodeEnum.HANDLER_NOT_EXIST.getDesc());
            return refundResponse;
        }

            // 插入退款记录，失败情况可重试退款
        RefundOrderEntity unionPayRefundOrderEntity ;
        try {
            unionPayRefundOrderEntity = createUnionPayUmsRefundOrderEntity(chargeOrderEntity, unionPayUmsRefundRequest);
        } catch (BizException e) {
            refundResponse.setMsg(e.getMessage());
            return refundResponse;
        }

        RefundHandlerResult refundResult = channelHandler.createRefund(unionPayRefundOrderEntity, chargeOrderEntity.getChannelType());

        //解析开发者接口响应信息
        RefundOrderEntity updateRefundEntity = new RefundOrderEntity();
        refundResponse.setRefundOrderNo(unionPayRefundOrderEntity.getOrderNo());

        //退款请求受理
        if (refundResult.isSuccess()) {
            if(StringUtils.isNotBlank(refundResult.getRefundNo())) {
                //更新tb_refund_order第三方退款订单号 和 退款状态
                refundOrderDao.updateRefundNoWithStatus(unionPayRefundOrderEntity.getOrderNo(), refundResult.getRefundNo(), RefundOrderStatusEnum.ORDER_SUCCEEDED);
            } else {
                updateRefundEntity.setId(unionPayRefundOrderEntity.getId());
                updateRefundEntity.setRefundStatus(RefundOrderStatusEnum.ORDER_SUCCEEDED.getCode());
                refundOrderDao.update(updateRefundEntity);
            }
            refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        }
        //退款受理失败
        else {
            updateRefundEntity.setId(unionPayRefundOrderEntity.getId());
            //更新售后订单状态为 申请失败
            //由于请求开发者异常情况也做失败处理了，这里直接设置退款失败。
            updateRefundEntity.setRefundStatus(RefundOrderStatusEnum.FAILED.getCode());
            //更新失败信息
            updateRefundEntity.setFailureCode(refundResult.getFailCode());
            updateRefundEntity.setFailureMsg(subString(refundResult.getFailMsg()));
            refundOrderDao.update(updateRefundEntity);

            refundResponse.setMsg(refundResult.getFailMsg());
            refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
        }
        return refundResponse;
    }

    @Override
    public CmbLifeCreateRefundResponse cmbLifeRefund(CmbLifeRefundRequest refundRequest) {
        Set<ConstraintViolation<CmbLifeRefundRequest>> errorSet = validator.validate(refundRequest);
        CmbLifeCreateRefundResponse refundResponse = new CmbLifeCreateRefundResponse();
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(CmbLifeRefundResponse.DEFAULT_ERROR_CODE);
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }

        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                refundRequest.getBizOrderNo(), BizTypeEnum.XST.getCode());

        if (chargeOrderEntity == null) {
            refundResponse.setCode(CmbLifeRefundResponse.DEFAULT_ERROR_CODE);
            refundResponse.setMsg("支付订单不存在");
            return refundResponse;
        }

        if (chargeOrderEntity.getAmount() < Integer.parseInt(refundRequest.getAmount())) {
            refundResponse.setCode(CmbLifeRefundResponse.DEFAULT_ERROR_CODE);
            refundResponse.setMsg("退款金额大于支付金额");
            return refundResponse;
        }

        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(CmbLifeRefundResponse.DEFAULT_ERROR_CODE);
            refundResponse.setMsg("订单未支付");
            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = new RefundOrderEntity();

        // 下单-行方订单号
        refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
        // 退款-行方订单号
        refundOrderEntity.setOrderNo(generateRefundOrderNo(refundRequest.getRefundToken()) + SUFFIX);
        refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
        refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
        refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
        refundOrderEntity.setApplyRefundAmount(Integer.parseInt(refundRequest.getAmount()));
        refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
        refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
        refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());

        refundOrderDao.insert(refundOrderEntity);
        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(ChannelEnum.CMB_LIFE_PAY.getChannelType());

        RefundOrderEntity updateRefundEntity = new RefundOrderEntity();
        RefundHandlerResult refundResult = channelHandler.createRefund(refundOrderEntity, null);
        refundResponse.setStatus(refundResult.getStatus());
        refundResponse.setRefundRefNum(refundResult.getRefundNo());
        refundResponse.setRefundAmount(refundResult.getReundAmt());
        refundResponse.setTranEndDate(refundResult.getTranEndDate());
        refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
        if (refundResult.isSuccess()) {
            // refundResult.getRefundNo()-退款订单号
            refundOrderDao.updateRefundNo(refundOrderEntity.getOrderNo(), refundResult.getRefundNo());
            refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        } else {
            updateRefundEntity.setId(refundOrderEntity.getId());
            //更新售后订单状态为 申请失败
            // 退货结果 1：待退款，2：退款成功，3：退款失败，4：退款未知
            updateRefundEntity.setRefundStatus(Objects.equals("3", refundResult.getStatus()) ? RefundOrderStatusEnum.FAILED.getCode() : RefundOrderStatusEnum.INIT.getCode());
            //更新失败信息
            updateRefundEntity.setFailureCode(refundResult.getFailCode());
            updateRefundEntity.setFailureMsg(subString(refundResult.getFailMsg()));
            refundOrderDao.update(updateRefundEntity);

            refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
            refundResponse.setMsg(refundResult.getFailMsg());
        }

        return refundResponse;
    }

    @Override
    public IcbcELifeCreateRefundResponse icbcELifePayRefund(IcbcELifeRefundRequest refundRequest) {
        Set<ConstraintViolation<IcbcELifeRefundRequest>> errorSet = validator.validate(refundRequest);
        IcbcELifeCreateRefundResponse refundResponse = new IcbcELifeCreateRefundResponse();
        if (!errorSet.isEmpty()) {
            refundResponse.setCode(CmbLifeRefundResponse.DEFAULT_ERROR_CODE);
            refundResponse.setMsg(errorSet.iterator().next().getMessage());
            return refundResponse;
        }

        ChargeOrderEntity chargeOrderEntity = chargeOrderDao.findByBizOrderNoAndBizType(
                refundRequest.getBizOrderNo(), BizTypeEnum.XST.getCode());

        if (chargeOrderEntity == null) {
            refundResponse.setCode(CmbLifeRefundResponse.DEFAULT_ERROR_CODE);
            refundResponse.setMsg("支付订单不存在");
            return refundResponse;
        }

        if (chargeOrderEntity.getAmount() < refundRequest.getRejectAmt()) {
            refundResponse.setCode(CmbLifeRefundResponse.DEFAULT_ERROR_CODE);
            refundResponse.setMsg("退款金额大于支付金额");
            return refundResponse;
        }

        if (!ChargeOrderStatusEnum.PAY_SUCCESS.getCode().equals(chargeOrderEntity.getChargeStatus())) {
            refundResponse.setCode(CmbLifeRefundResponse.DEFAULT_ERROR_CODE);
            refundResponse.setMsg("订单未支付");
            return refundResponse;
        }
        RefundOrderEntity refundOrderEntity = new RefundOrderEntity();

        // 下单-行方订单号
        refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
        // 退款-行方订单号
        refundOrderEntity.setOrderNo(generateRefundOrderNo(refundRequest.getRefundOrderNo()) + SUFFIX);
        refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
        refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
        refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
        refundOrderEntity.setApplyRefundAmount(refundRequest.getRejectAmt());
        refundOrderEntity.setDescription(refundRequest.getRejectReason());
        refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
        refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
        refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
        refundOrderEntity.setRefundTime(new Date(refundRequest.getTransactionTimestamp()));
        refundOrderEntity.setExtra(refundRequest.getMerID());

        refundOrderDao.insert(refundOrderEntity);
        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(ChannelEnum.BANK_OF_ICBC_ELIFE_PAY.getChannelType());

        RefundOrderEntity updateRefundEntity = new RefundOrderEntity();
        RefundHandlerResult refundResult = channelHandler.createRefund(refundOrderEntity, null);
        refundResponse.setStatus(refundResult.getStatus());
        refundResponse.setRefundAmount(refundResult.getReundAmt());
        refundResponse.setTranEndDate(refundResult.getTranEndDate());
        refundResponse.setRefundOrderNo(refundOrderEntity.getOrderNo());
        if (refundResult.isSuccess()) {
            updateRefundEntity.setId(refundOrderEntity.getId());
            updateRefundEntity.setOrderNo(refundOrderEntity.getOrderNo());
            updateRefundEntity.setRefundNo(refundResult.getRefundNo());
            updateRefundEntity.setExtra(refundResult.getExtra());
            updateRefundEntity.setRefundStatus(RefundOrderStatusEnum.ORDER_SUCCEEDED.getCode());
            // refundResult.getRefundNo()-退款订单号
            refundOrderDao.update(updateRefundEntity);
            refundResponse.setCode(RefundErrorCodeEnum.SUCCESS.getCode());
        } else {
            updateRefundEntity.setId(refundOrderEntity.getId());
            //更新售后订单状态为 申请失败
            // 退货结果 1：退货成功;2：退货失败;3：退货处理中;4：申请失败，该订单存在未完成退货的退款申请
            updateRefundEntity.setRefundStatus(
                    Objects.equals("2", refundResult.getStatus()) || Objects.equals("4", refundResult.getStatus())
                    ? RefundOrderStatusEnum.FAILED.getCode() : RefundOrderStatusEnum.INIT.getCode());
            //更新失败信息
            updateRefundEntity.setExtra(refundResult.getExtra());
            updateRefundEntity.setFailureCode(refundResult.getFailCode());
            updateRefundEntity.setFailureMsg(subString(refundResult.getFailMsg()));
            refundOrderDao.update(updateRefundEntity);

            refundResponse.setCode(RefundErrorCodeEnum.FAIL.getCode());
            refundResponse.setMsg(refundResult.getFailMsg());
        }

        return refundResponse;
    }

    private RefundOrderEntity createUnionPayUmsRefundOrderEntity(ChargeOrderEntity chargeOrderEntity,UnionPayUmsRefundRequest unionPayUmsRefundRequest) throws BizException {

        RefundOrderEntity refundOrderEntity = refundOrderDao.findByRefundOrderNo(chargeOrderEntity.getOrderNo());
        if (refundOrderEntity == null) {
            refundOrderEntity = new RefundOrderEntity();
            refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
            refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
            refundOrderEntity.setExtra(chargeOrderEntity.getExtra());
            refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
            refundOrderEntity.setApplyRefundAmount(unionPayUmsRefundRequest.getRefundAmount().intValue());
            refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
            refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
            refundOrderDao.insert(refundOrderEntity);
        } else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            //第三方收单失败可以再次发起请求
            RefundOrderEntity entity = new RefundOrderEntity();
            entity.setId(refundOrderEntity.getId());
            entity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderDao.update(entity);
        } else {
            throw new BizException(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());
        }
        return refundOrderEntity;
    }

    private RefundOrderEntity xibCreateOrRefreshRefundOrder(ChargeOrderEntity chargeOrderEntity, XibRefundRequestDTO xibRefundRequestDTO) throws BizException {

        RefundOrderEntity refundOrderEntity = refundOrderDao.findByChargeOrderNo(chargeOrderEntity.getOrderNo(), xibRefundRequestDTO.getOrdersItemId());

        //创建退款记录
        if (Objects.isNull(refundOrderEntity)) {
            refundOrderEntity = new RefundOrderEntity();
            refundOrderEntity.setOrderNo(generateRefundOrderNo(chargeOrderEntity.getOrderNo()) + SUFFIX);
            refundOrderEntity.setChargeOrderNo(chargeOrderEntity.getOrderNo());
            refundOrderEntity.setAppId(chargeOrderEntity.getAppId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            //开发者流水号
            refundOrderEntity.setTransactionNo(chargeOrderEntity.getTransactionNo());
            refundOrderEntity.setExtra(chargeOrderEntity.getExtra());
            //下单金额
            refundOrderEntity.setChargeAmount(chargeOrderEntity.getAmount());
            //申请退款的金额
            refundOrderEntity.setApplyRefundAmount(xibRefundRequestDTO.getTxnAmt().intValue());
            //BizTypeEnum.ORD
            refundOrderEntity.setBizType(chargeOrderEntity.getBizType());
            //主订单
            refundOrderEntity.setBizOrderNo(chargeOrderEntity.getBizOrderNo());
            refundOrderEntity.setOrdersItemId(xibRefundRequestDTO.getOrdersItemId());

            if (refundOrderDao.insert(refundOrderEntity) != 1) {
                throw new BizException(String.format("厦门国际 创建退款订单失败 refundOrderEntity=%s", JSON.toJSONString(refundOrderEntity)));
            }
        } else if (RefundOrderStatusEnum.ORDER_FAILED.getCode().equals(refundOrderEntity.getRefundStatus())) {
            //第三方收单失败可以再次发起请求
            refundOrderEntity.setId(refundOrderEntity.getId());
            refundOrderEntity.setRefundStatus(RefundOrderStatusEnum.INIT.getCode());
            refundOrderDao.update(refundOrderEntity);
        } else {
            //不是可退款状态
            LOGGER.warn("厦门国际 不是可退款状态 orderNum={}", chargeOrderEntity.getBizOrderNo());
            throw new BizException(RefundErrorCodeEnum.REFUND_ORDER_STATUS_ERROR.getDesc());
        }
        return refundOrderEntity;
    }
}
