package cn.com.duiba.paycenter.remoteservice.impl.equity;

import cn.com.duiba.paycenter.dto.equity.request.BaseEquityRequest;
import cn.com.duiba.paycenter.dto.equity.request.wx.favor.EquityWxFavorUserCouponsRequest;
import cn.com.duiba.paycenter.dto.equity.response.BaseEquityResultResponse;
import cn.com.duiba.paycenter.dto.equity.response.EquityResponse;
import cn.com.duiba.paycenter.dto.equity.response.wx.favor.EquityWxFavorUserCouponsResponse;
import cn.com.duiba.paycenter.dto.equity.response.wx.favor.WxFavorStocksDto;
import cn.com.duiba.paycenter.handler.equity.impl.EquityWxFavorHandler;
import cn.com.duiba.paycenter.remoteservice.equity.RemoteEquityWxFavorService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/5/21 4:02 PM
 */
@RestController
public class RemoteEquityWxFavorServiceImpl implements RemoteEquityWxFavorService {
    
    @Resource
    private EquityWxFavorHandler equityWxFavorHandler;

    @Override
    public EquityResponse distribute(BaseEquityRequest<EquityWxFavorUserCouponsRequest> request) {
        return equityWxFavorHandler.distribute(request);
    }

    @Override
    public BaseEquityResultResponse<EquityWxFavorUserCouponsResponse> distributeResult(Integer bizType, String bizNo) {
        return equityWxFavorHandler.distributeResult(bizType, bizNo);
    }

    @Override
    public EquityWxFavorUserCouponsRequest getReqParam(Integer bizType, String bizNo) {
        return equityWxFavorHandler.getReqParam(bizType, bizNo);
    }

    @Override
    public WxFavorStocksDto getStocksInfo(String mchId, String stockId, String stockCreatorMchId) {
        return equityWxFavorHandler.getStocksInfo(mchId, stockId, stockCreatorMchId);
    }
}
