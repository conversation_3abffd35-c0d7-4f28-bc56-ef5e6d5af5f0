package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.biz.SaasAgentAccountPayOrderBiz;
import cn.com.duiba.paycenter.constant.PayCenterErrorCode;
import cn.com.duiba.paycenter.params.PayOrdersExtraParams;
import cn.com.duiba.paycenter.remoteservice.RemoteSaasAgentAccountPayOrderService;
import cn.com.duiba.paycenter.result.PayOrdersResult;
import cn.com.duiba.paycenter.util.SignUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/23 09:37
 */
@RestController
public class RemoteSaasAgentAccountPayOrderServiceImpl implements RemoteSaasAgentAccountPayOrderService {

    private static final Logger logger = LoggerFactory.getLogger(RemoteSaasAgentAccountPayOrderServiceImpl.class);

    @Autowired
    private SaasAgentAccountPayOrderBiz saasAgentAccountPayOrderBiz;

    @Override
    public PayOrdersResult payOrder(Long agentId, Long orderId, Long money, String sign, PayOrdersExtraParams p) {
        logger.info("代理商下单扣款，请求参数为:agentId:{},orderId:{},money:{},sign:{}.", agentId, orderId, money, sign);
        if (!checkSign(agentId, orderId, money, sign)) {
            return new PayOrdersResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        return saasAgentAccountPayOrderBiz.payOrder(agentId, orderId, money, p);
    }

    @Override
    public PayOrdersResult backPayOrder(Long agentId, Long orderId, Long money, String sign, PayOrdersExtraParams p) {
        logger.info("代理商下单失败还款，请求参数为:agentId:{},orderId:{},money:{},sign:{}.", agentId, orderId, money, sign);
        if(!checkSign(agentId, orderId, money, sign)){
            return new PayOrdersResult(false,PayCenterErrorCode.CodeParamsSignError, null);
        }
        return saasAgentAccountPayOrderBiz.backpayOrder(agentId, orderId, money, p);
    }

    @Override
    public boolean checkActionSuccess(Long orderId, String actionType) {
        logger.info("查询代理商下单结果，请求参数为:orderId:{},actionType:{}.", orderId, actionType);
        return saasAgentAccountPayOrderBiz.checkActionSuccess(orderId, actionType);
    }

    private boolean checkSign(Long agentId, Long orderId, Long money, String sign) {
        Map<String, String> params = new HashMap<>();
        params.put("agentId", agentId + "");
        params.put("orderId", orderId + "");
        params.put("money", money + "");
        String checksign = SignUtil.sign(params);
        return checksign.equals(sign);
    }
}
