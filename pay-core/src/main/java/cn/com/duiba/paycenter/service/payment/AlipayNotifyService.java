package cn.com.duiba.paycenter.service.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayChargeNotifyResponse;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/12/11
 */
public interface AlipayNotifyService {
    /**
     * 支付订单异步通知接口
     *
     * @param params 支付宝通知参数
     * @return AlipayChargeNotifyResponse
     * @throws BizException bizException
     */
    AlipayChargeNotifyResponse orderNotify(Map<String, String> params) throws BizException;

    /**
     * 支付订单异步通知接口
     *
     * @param params 支付宝通知参数
     * @return AlipayChargeNotifyResponse
     * @throws BizException bizException
     */
    AlipayChargeNotifyResponse orderNotifyBySubjectType(Map<String, String> params, String subjectType) throws BizException;
}
