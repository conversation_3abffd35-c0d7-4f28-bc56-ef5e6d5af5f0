package cn.com.duiba.paycenter.service.payment.impl;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.config.Duia2WxpayConfig;
import cn.com.duiba.paycenter.config.DuibaWxpayConfig;
import cn.com.duiba.paycenter.config.FujianDuibaWxpayConfig;
import cn.com.duiba.paycenter.config.WxCouponSubjectContainer;
import cn.com.duiba.paycenter.config.WxSubjectContainer;
import cn.com.duiba.paycenter.constant.WxCouponSubjectEnum;
import cn.com.duiba.paycenter.dao.payment.ChargeOrderDao;
import cn.com.duiba.paycenter.dao.payment.WxRedpacketOrderDao;
import cn.com.duiba.paycenter.dto.payment.WxCouponDetailDto;
import cn.com.duiba.paycenter.dto.payment.WxRedpacketOrderDto;
import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.ChargeOrderDto;
import cn.com.duiba.paycenter.dto.payment.charge.WxCouponResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.BuildAuthorizationUrlRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxCloseOrderRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxCouponSendRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayRedPacketQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayRedPacketSendRequest;
import cn.com.duiba.paycenter.dto.payment.config.WxPayConfigDto;
import cn.com.duiba.paycenter.entity.payment.ChargeOrderEntity;
import cn.com.duiba.paycenter.entity.payment.WxRedpacketOrderEntity;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayCloseOrderRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayRedPacketQueryXmlRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayRedPacketSendXmlRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPayRedPacketQueryXmlResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPayRedPacketSendXmlResult;
import cn.com.duiba.paycenter.enums.ChannelEnum;
import cn.com.duiba.paycenter.enums.ChargeOrderStatusEnum;
import cn.com.duiba.paycenter.enums.WxPaySignTypeEnum;
import cn.com.duiba.paycenter.service.payment.ChannelHandler;
import cn.com.duiba.paycenter.service.payment.ChannelHandlerManager;
import cn.com.duiba.paycenter.service.payment.ChargeService;
import cn.com.duiba.paycenter.service.payment.WxMpService;
import cn.com.duiba.paycenter.service.payment.WxPayConfigService;
import cn.com.duiba.paycenter.service.payment.WxPayService;
import cn.com.duiba.paycenter.util.RandomUtils;
import cn.com.duiba.paycenter.util.RedisKeyFactory;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.redis.RedisLock;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/11/13
 */
@Service
public class ChargeServiceImpl implements ChargeService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ChargeServiceImpl.class);
    @Resource
    private Validator validator;
    @Resource
    private ChargeOrderDao chargeOrderDao;
    @Resource
    private WxMpService wxMpService;
    @Resource
    private WxPayConfigService wxPayConfigService;
    @Resource
    private WxPayService wxPayService;
    @Resource
    private DuibaWxpayConfig duibaWxpayConfig;
    @Resource
    private Duia2WxpayConfig duia2WxpayConfig;
    @Resource
    private WxRedpacketOrderDao wxRedpacketOrderDao;
    @Resource(name = "redisTemplate")
    private RedisAtomicClient redisAtomicClient;

    @Autowired
    private WxSubjectContainer wxSubjectContainer;

    @Resource
    private FujianDuibaWxpayConfig fujianDuibaWxpayConfig;

    @Autowired
    private WxCouponSubjectContainer wxCouponSubjectContainer;

    /**
     * 1.获取渠道     * 1.获取渠道配置配置
     * 2.获取渠道处理器
     * 3.查看是否已经收过单，没有则新增支付订单
     * 4.渠道处理器去第三方支付平台下单
     * 5.更新支付状态
     * 6.返回支付结果等待用户支付
     */
    @Override
    public <T extends BaseChargeResponse, E extends BaseChargeRequest> T createCharge(E chargeRequest)
            throws BizException {
        Set<ConstraintViolation<BaseChargeRequest>> errorSet = validator.validate(chargeRequest);
        if (!errorSet.isEmpty()) {
            throw new BizException(errorSet.iterator().next().getMessage());
        }
        RedisLock lock = redisAtomicClient.getLock(getCreateLockKey(chargeRequest), 2);
        if (Objects.isNull(lock)) {
            LOGGER.warn("重复支付,request:{}", JSON.toJSONString(chargeRequest));
            throw new BizException("正在支付请耐心等待");
        }
        ChannelHandler channelHandler = ChannelHandlerManager.getHandller(chargeRequest.getChannelType());
        try {
            if (channelHandler == null) {
                LOGGER.warn("支付渠道未实现, channelType={}", chargeRequest.getChannelType());
                throw new BizException("支付渠道未实现");
            }
            //每个渠道只有一笔支付订单
            ChargeOrderEntity entity = chargeOrderDao.findByBizOrderNoAndBizTypeAndChannel(
                    chargeRequest.getBizOrderNo(), chargeRequest.getBizType(), chargeRequest.getChannelType());
            if (entity == null) {
                chargeRequest.setOrderNo(generateChargeOrderNo(chargeRequest.getBizOrderNo()));
                createChargeOrder(channelHandler, chargeRequest);
            } else if (entity.getChargeStatus() >= ChargeOrderStatusEnum.PAY_SUCCESS.getCode()) {
                //如果支付成功了则不能再支付
                throw new BizException("订单无法重新支付");
            } else if (ChargeOrderStatusEnum.INIT.getCode().equals(entity.getChargeStatus())) {
                //在请求第三方支付时上游又来请求支付
                throw new BizException("正在支付请耐心等待");
            } else {
                //重新请求第三方支付进行支付
                //同一笔订单金额无法更改
                if (chargeRequest.getAmount().equals(entity.getAmount())) {
                    chargeRequest.setOrderNo(entity.getOrderNo());
                } else {
                    //如果金额发生变化需要重启发起支付
                    throw new BizException("金额发生变化，请重启发起订单支付");
                }
            }
            setMetadata(chargeRequest, entity);
        } catch (Exception e) {
            LOGGER.warn("重复支付,request:{},error:{}",JSON.toJSONString(chargeRequest), e.getMessage());
            throw new BizException("正在支付请耐心等待");
        } finally {
            lock.unlock();
        }
        T chargeResponse = channelHandler.createCharge(chargeRequest);
        //需要更新支付订单状态
        handlerResponse(chargeResponse);

        return chargeResponse;
    }

    @NotNull
    private <E extends BaseChargeRequest> String getCreateLockKey(E chargeRequest) {
        return RedisKeyFactory.K014.join(chargeRequest.getBizOrderNo()) + "_" + chargeRequest.getChannelType();
    }

    /**
     * 不支持幂等，需要将支付参数缓存起来
     * response.extra->entity.extra->request.metadata->entity.metadata
     *
     * @param chargeRequest
     * @param entity
     * @param <E>
     */
    private <E extends BaseChargeRequest> void setMetadata(E chargeRequest, ChargeOrderEntity entity) {
        if (ChannelEnum.WJRCB_WX_PUB.getChannelType().equals(chargeRequest.getChannelType()) && entity != null) {
            //苏州农商行定制，不支持幂等，需要将支付参数缓存起来
            chargeRequest.setMetadata(entity.getExtra());
        }
        if (ChannelEnum.BANK_OF_CCB_PAY.getChannelType().equals(chargeRequest.getChannelType()) && entity != null) {
            chargeRequest.setMetadata(entity.getExtra());
        }
        if (ChannelEnum.BANK_OF_ABC_PAY.getChannelType().equals(chargeRequest.getChannelType()) && null != entity) {
            chargeRequest.setMetadata(entity.getExtra());
        }
        if (ChannelEnum.HELLO_PAY.getChannelType().equals(chargeRequest.getChannelType()) && null != entity) {
            chargeRequest.setMetadata(entity.getExtra());
        }
        if (ChannelEnum.SHOUXIN_PAY.getChannelType().equals(chargeRequest.getChannelType()) && null != entity) {
            chargeRequest.setMetadata(entity.getExtra());
        }
        if (ChannelEnum.BANK_OF_BOC_PAY.getChannelType().equals(chargeRequest.getChannelType()) && null != entity) {
            chargeRequest.setMetadata(entity.getExtra());
        }
        if (ChannelEnum.BANK_OF_CITIC_PAY.getChannelType().equals(chargeRequest.getChannelType()) && null != entity) {
            chargeRequest.setMetadata(entity.getExtra());
        }
        if (ChannelEnum.UNION_PAY.getChannelType().equals(chargeRequest.getChannelType()) && null != entity) {
            // 商户代码 merId、商户订单号 orderId、订单发送时间 txnTime 三要素唯一确定一笔交易
            // 需要讲支付参数存起来
            chargeRequest.setMetadata(entity.getExtra());
        }
        if (ChannelEnum.BANK_OF_ICBC_PAY_H5.getChannelType().equals(chargeRequest.getChannelType()) && null != entity) {
            chargeRequest.setMetadata(entity.getExtra());
        }
        if (ChannelEnum.DUIBA_LIVE_INSTALLMENT_PAY.getChannelType().equals(chargeRequest.getChannelType()) && null != entity) {
            chargeRequest.setMetadata(entity.getExtra());
        }
        if (ChannelEnum.DUIBA_LIVE_MP_PAY.getChannelType().equals(chargeRequest.getChannelType()) && null != entity) {
            chargeRequest.setMetadata(entity.getExtra());
        }
        if (ChannelEnum.DUIBA_LIVE_BF_PAY.getChannelType().equals(chargeRequest.getChannelType()) && null != entity) {
            chargeRequest.setMetadata(entity.getExtra());
        }
        if (ChannelEnum.LSHM_PAY.getChannelType().equals(chargeRequest.getChannelType()) && null != entity) {
            chargeRequest.setMetadata(entity.getExtra());
        }
    }

    @Override
    public ChargeOrderDto findByOrderNo(String orderNo) throws BizException {
        if (StringUtils.isBlank(orderNo)) {
            throw new BizException("订单号为空");
        }
        ChargeOrderEntity orderEntity = chargeOrderDao.findByOrderNo(orderNo);
        ChargeOrderDto orderDto = BeanUtils.copy(orderEntity, ChargeOrderDto.class);
        orderDto.setExtraStr(orderEntity.getExtra());
        return orderDto;
    }

    @Override
    public List<ChargeOrderDto> findByBizOrderNo(String bizOrderNo) throws BizException {
        if (StringUtils.isBlank(bizOrderNo)) {
            return Collections.emptyList();
        }
        return BeanUtils.copyList(chargeOrderDao.selectByBizNo(bizOrderNo), ChargeOrderDto.class);
    }

    @Override
    public List<ChargeOrderDto> batchFindByOrderNo(List<String> orderNos) throws BizException {
        if (CollectionUtils.isEmpty(orderNos)) {
            return Collections.emptyList();
        }
        return BeanUtils.copyList(chargeOrderDao.batchFindByOrderNo(orderNos), ChargeOrderDto.class);
    }

    @Override
    public List<ChargeOrderDto> batchFindByTransactionNo(List<String> transactionNos) throws BizException {
        if (CollectionUtils.isEmpty(transactionNos)) {
            return Collections.emptyList();
        }
        return BeanUtils.copyList(chargeOrderDao.batchFindByTransactionNo(transactionNos), ChargeOrderDto.class);
    }

    @Override
    public String oauth2buildAuthorizationUrl(Long appId, String redirectURI) throws BizException {
        if (appId == null || StringUtils.isBlank(redirectURI)) {
            throw new BizException("参数不能为空");
        }
        //小程序无需调用该方法,可以直接获取临时授权码
        //配置不会为空默认使用兑吧配置
        WxPayConfigDto wxPayConfigDto = wxPayConfigService.findByAppIdAndChannelType(appId
                , ChannelEnum.WX_PUB.getChannelType());
        return getAuthUrl(redirectURI, wxPayConfigDto);
    }

    @Override
    public ChargeOrderDto findByBizNoAndBizType(String bizNo, Integer bizType) {
        if (StringUtils.isBlank(bizNo) || bizType == null) {
            return null;
        }
        return BeanUtils.copy(chargeOrderDao.findByBizOrderNoAndBizType(bizNo, bizType), ChargeOrderDto.class);
    }

    @Override
    public List<ChargeOrderDto> selectByBizNosAndBizType(List<String> bizNos, Integer bizType) {
        if (CollectionUtils.isEmpty(bizNos) || bizType == null) {
            return Collections.emptyList();
        }
        return BeanUtils.copyList(chargeOrderDao.selectByBizNosAndBizType(bizNos, bizType), ChargeOrderDto.class);
    }

    @Override
    public List<ChargeOrderDto> listByBizNosAndBizType(List<String> bizNos, Integer bizType) {
        if (CollectionUtils.isEmpty(bizNos) || bizType == null) {
            return Collections.emptyList();
        }
        return chargeOrderDao.selectByBizNosAndBizType(bizNos, bizType).stream().map(a -> {
            ChargeOrderDto copy = BeanUtils.copy(a, ChargeOrderDto.class);
            copy.setExtraStr(a.getExtra());
            return copy;
        }).collect(Collectors.toList());
    }

    @Override
    public String oauth2buildAuthorizationUrl(BuildAuthorizationUrlRequest request) throws BizException {
        if (request.getAppId() == null || StringUtils.isBlank(request.getRedirectURI())) {
            throw new BizException("参数不能为空");
        }
        //小程序无需调用该方法,可以直接获取临时授权码
        //配置不会为空默认使用兑吧配置
        WxPayConfigDto wxPayConfigDto = wxPayConfigService.findByAppIdAndChannelTypeSubjectType(request.getAppId()
                , ChannelEnum.WX_PUB.getChannelType(), request.getSubjectType());
        return getAuthUrl(request.getRedirectURI(), wxPayConfigDto);
    }

    @Override
    public void closeOrder(WxCloseOrderRequest request) throws BizException {
        Set<ConstraintViolation<WxCloseOrderRequest>> errorSet = validator.validate(request);
        if (!errorSet.isEmpty()) {
            throw new BizException(errorSet.iterator().next().getMessage());
        }
        WxPayConfigDto wxPayConfigDto = wxPayConfigService
                .findByAppIdAndChannelTypeSubjectType(request.getAppId(), request.getChannelType(), request.getSubjectType());
        if (wxPayConfigDto == null) {
            LOGGER.warn("支付缺少配置，appId={}", request.getAppId());
            return;
        }
        WxPayCloseOrderRequest wxPayCloseOrderRequest = new WxPayCloseOrderRequest();
        wxPayCloseOrderRequest.setAppId(wxPayConfigDto.getWxAppId());
        wxPayCloseOrderRequest.setMchId(wxPayConfigDto.getMchId());
        wxPayCloseOrderRequest.setSignType(WxPaySignTypeEnum.MD5.toString());
        wxPayCloseOrderRequest.setNonceStr(RandomUtils.getRandomString());
        wxPayCloseOrderRequest.setOutTradeNo(request.getPayOrderNo());
        wxPayService.closeOrder(wxPayCloseOrderRequest, wxPayConfigDto.getApiKey());

    }

    @Override
    public WxRedpacketOrderDto sendWxRedPacket(WxPayRedPacketSendRequest request) throws BizException {
        Set<ConstraintViolation<WxPayRedPacketSendRequest>> errorSet = validator.validate(request);
        if (!errorSet.isEmpty()) {
            throw new BizException(errorSet.iterator().next().getMessage());
        }
        WxPayConfigDto wxPayConfigDto = wxPayConfigService.findByAppIdAndChannelTypeSubjectType(request.getAppId(), request.getChannelType(), request.getSubjectType());
        if (Objects.isNull(wxPayConfigDto)) {
            LOGGER.warn("〖Message〗: 微信现金红包发放异常，缺少支付配置;〖Parameters〗: [request={}]", JSON.toJSONString(request));
            throw new BizException("缺少支付配置");
        }

        RedisLock lock = redisAtomicClient.getLock(getLockKey(request.getBizRelationType(), request.getBizRelationId(), request.getBizOrderNo()), 2, 3, 100);
        if (Objects.isNull(lock)) {
            throw new BizException("操作频繁，请稍后重试");
        }
        try {
            //  检查是否重试订单
            WxRedpacketOrderEntity oldOrderInfo = wxRedpacketOrderDao.getByBizInfo(request.getBizRelationType(), request.getBizRelationId(), request.getBizOrderNo());

            //  构造请求参数
            WxPayRedPacketSendXmlRequest sendXmlRequest = BeanUtils.copy(request, WxPayRedPacketSendXmlRequest.class);
            sendXmlRequest.setWxappid(wxPayConfigDto.getWxAppId());
            sendXmlRequest.setMchId(wxPayConfigDto.getMchId());
            sendXmlRequest.setNonceStr(RandomUtils.getRandomString());
            //  无效参数，设置只是为了父类字段校验通过
            sendXmlRequest.setAppId(wxPayConfigDto.getWxAppId());


            WxRedpacketOrderEntity currentOrderInfo = BeanUtils.copy(request, WxRedpacketOrderEntity.class);
            //  重试 or 新下单
            if (Objects.isNull(oldOrderInfo)) {
                sendXmlRequest.setMchBillno(generateMchBillNo(sendXmlRequest.getMchId()));
                //  新下单时，需创建发送流水/订单
                BeanUtils.copy(sendXmlRequest, currentOrderInfo);
                wxRedpacketOrderDao.save(currentOrderInfo);
            } else {
                sendXmlRequest.setMchBillno(oldOrderInfo.getMchBillno());
            }

            //  执行请求
            WxPayRedPacketSendXmlResult sendXmlResult = wxPayService.sendWxRedPacket(sendXmlRequest, wxPayConfigDto.getApiKey(), wxPayConfigDto.getApiCert(), request.getSubjectType());
            //  请求成功，将微信订单号更新到记录里
            BeanUtils.copy(sendXmlResult, currentOrderInfo);
            wxRedpacketOrderDao.updateById(currentOrderInfo);
            return BeanUtils.copy(currentOrderInfo, WxRedpacketOrderDto.class);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public WxRedpacketOrderDto queryWxRedPacketInfo(WxPayRedPacketQueryRequest request) throws BizException {
        Set<ConstraintViolation<WxPayRedPacketQueryRequest>> errorSet = validator.validate(request);
        if (!errorSet.isEmpty()) {
            throw new BizException(errorSet.iterator().next().getMessage());
        }
        WxPayConfigDto wxPayConfigDto = wxPayConfigService.findByAppIdAndChannelTypeSubjectType(request.getAppId(), request.getChannelType(), request.getSubjectType());
        if (Objects.isNull(wxPayConfigDto)) {
            LOGGER.warn("〖Message〗: 微信现金红包查询异常，缺少支付配置;〖Parameters〗: [request={}]", JSON.toJSONString(request));
            throw new BizException("缺少支付配置");
        }

        WxRedpacketOrderEntity record = wxRedpacketOrderDao.getByBizInfo(request.getBizRelationType(), request.getBizRelationId(), request.getBizOrderNo());
        if (Objects.isNull(record)) {
            return null;
        }

        WxPayRedPacketQueryXmlRequest queryXmlRequest = BeanUtils.copy(request, WxPayRedPacketQueryXmlRequest.class);
        queryXmlRequest.setAppId(wxPayConfigDto.getWxAppId());
        queryXmlRequest.setMchId(wxPayConfigDto.getMchId());
        queryXmlRequest.setNonceStr(RandomUtils.getRandomString());
        queryXmlRequest.setMchBillno(record.getMchBillno());
        WxPayRedPacketQueryXmlResult queryXmlResult = wxPayService.queryWxRedPacketInfo(queryXmlRequest, wxPayConfigDto.getApiKey(), wxPayConfigDto.getApiCert(), request.getSubjectType());

        BeanUtils.copy(queryXmlResult, record);
        wxRedpacketOrderDao.updateById(record);
        return BeanUtils.copy(record, WxRedpacketOrderDto.class);
    }

    @Override
    public WxCouponResponse sendWxCoupon(WxCouponSendRequest request) throws BizException {
        //校验参数
        Set<ConstraintViolation<WxCouponSendRequest>> errorSet = validator.validate(request);
        if (!errorSet.isEmpty()) {
            throw new BizException(errorSet.iterator().next().getMessage());
        }
        //根据subjectType获取对应的业务主体，请确保wxconfig已经put wxSubjectContainer
        WxPayConfigDto wxPayConfigDto = wxSubjectContainer.get(request.getSubjectType());
        //生成请求体
        //生成唯一订单号
        String outRequestNo = wxPayConfigDto.getMchId() + DateUtils.getDayNumber(new Date()) + request.getBizId();

        Map<String, Object> map = new HashMap<>();
        map.put("out_request_no", outRequestNo);
        map.put("appid", wxPayConfigDto.getWxAppId());
        map.put("stock_id", request.getStockId());
        //如果上游传入制券商户号，则使用上游制券商户号，否则默认是有兑吧商户号
        map.put("stock_creator_mchid", StringUtils.isBlank(request.getStockCreatorMchid()) ? wxPayConfigDto.getMchId() : request.getStockCreatorMchid());
        return wxPayService.sendWxCoupon(wxPayConfigDto, JSON.toJSONString(map), request.getOpenid());

    }

    @Override
    public WxCouponResponse sendFujianDuibaWxCoupon(WxCouponSendRequest request) throws BizException {
        request.setWxCouponSubject(WxCouponSubjectEnum.FJ_DB_WXLJJ);
        return sendWxCouponByWxSubject(request);
    }

    @Override
    public WxCouponResponse sendWxCouponByWxSubject(WxCouponSendRequest request) throws BizException {
        //校验参数
        Set<ConstraintViolation<WxCouponSendRequest>> errorSet = validator.validate(request);
        if (!errorSet.isEmpty()) {
            throw new BizException(errorSet.iterator().next().getMessage());
        }
        WxPayConfigDto wxPayConfigDto = wxCouponSubjectContainer.get(request.getWxCouponSubject().getCode());
        //生成请求体
        //生成唯一订单号
        String outRequestNo = wxPayConfigDto.getMchId() + request.getBizId();

        // 根据上游参数替换配置
        String wxAppId = wxPayConfigDto.getWxAppId();
        if (StringUtils.isNotBlank(request.getWxAppId())) {
            wxAppId = request.getWxAppId();
        }

        String mchId = wxPayConfigDto.getMchId();
        if (StringUtils.isNotBlank(request.getStockCreatorMchid())) {
            mchId = request.getStockCreatorMchid();
        }

        Map<String, Object> map = new HashMap<>();
        map.put("out_request_no", outRequestNo);
        map.put("appid", wxAppId);
        map.put("stock_id", request.getStockId());
        map.put("stock_creator_mchid", mchId);
        if (request.getWxCouponValue() != null) {
            map.put("coupon_value", request.getWxCouponValue());
        }
        if (request.getWxCouponMinimum() != null) {
            map.put("coupon_minimum", request.getWxCouponMinimum());
        }
        return wxPayService.sendWxCoupon(wxPayConfigDto, JSON.toJSONString(map), request.getOpenid());

    }

    @Override
    public WxCouponResponse reissueWxCoupon(WxCouponSendRequest request) throws BizException {
        //校验参数
        Set<ConstraintViolation<WxCouponSendRequest>> errorSet = validator.validate(request);
        if (!errorSet.isEmpty()) {
            throw new BizException(errorSet.iterator().next().getMessage());
        }
        WxPayConfigDto wxPayConfigDto = wxCouponSubjectContainer.get(request.getWxCouponSubject().getCode());
        //生成请求体
        //生成唯一订单号
        String outRequestNo = wxPayConfigDto.getMchId() + request.getBizId();

        try(RedisLock lock = redisAtomicClient.getLock(RedisKeyFactory.K103 + outRequestNo , 5)) {
            if(Objects.isNull(lock)) {
                LOGGER.warn("获取锁为空,request={}", JSON.toJSONString(request));
                return null;
            }
            // 根据上游参数替换配置
            String wxAppId = wxPayConfigDto.getWxAppId();
            if (StringUtils.isNotBlank(request.getWxAppId())) {
                wxAppId = request.getWxAppId();
            }

            String mchId = wxPayConfigDto.getMchId();
            if (StringUtils.isNotBlank(request.getStockCreatorMchid())) {
                mchId = request.getStockCreatorMchid();
            }

            Map<String, Object> map = new HashMap<>();
            map.put("out_request_no", outRequestNo);
            map.put("appid", wxAppId);
            map.put("stock_id", request.getStockId());
            map.put("stock_creator_mchid", mchId);
            if (request.getWxCouponValue() != null) {
                map.put("coupon_value", request.getWxCouponValue());
            }
            if (request.getWxCouponMinimum() != null) {
                map.put("coupon_minimum", request.getWxCouponMinimum());
            }
            return wxPayService.sendWxCoupon(wxPayConfigDto, JSON.toJSONString(map), request.getOpenid());
        } catch (Exception e) {
            LOGGER.warn("补发异常, request={}", JSON.toJSONString(request),e);
        }
        return null;
    }



    private String getAuthUrl(String redirectURI, WxPayConfigDto wxPayConfigDto) throws BizException {
        try {
            return wxMpService.oauth2buildAuthorizationUrl(wxPayConfigDto.getWxAppId()
                    , redirectURI, "snsapi_base", null);
        } catch (Exception e) {
            LOGGER.error("url encode失败,redirectURI={}", redirectURI, e);
            throw new BizException("url encode失败");
        }
    }

    private <T extends BaseChargeResponse> void handlerResponse(T chargeResponse) {
        ChargeOrderEntity chargeOrderEntity = new ChargeOrderEntity();
        chargeOrderEntity.setOrderNo(chargeResponse.getOrderNo());
        chargeOrderEntity.setExtra(chargeResponse.getExtra());
        if (chargeResponse.isSuccess()) {
            chargeOrderEntity.setChargeStatus(ChargeOrderStatusEnum.ORDER_SUCCESS.getCode());
        } else {
            chargeOrderEntity.setFailureMsg(chargeResponse.getMessage());
            chargeOrderEntity.setChargeStatus(ChargeOrderStatusEnum.ORDER_FAIL.getCode());
        }
        chargeOrderEntity.setTransactionNo(chargeResponse.getTransactionNo());
        chargeOrderDao.update(chargeOrderEntity);
    }

    private void createChargeOrder(ChannelHandler channelHandler, BaseChargeRequest chargeRequest) throws BizException {
        ChargeOrderEntity entity = channelHandler.createChargeOrderEntity(chargeRequest);
        if (chargeOrderDao.insert(entity) == 0) {
            String jsonString = JSONObject.toJSONString(chargeRequest);
            LOGGER.error("生成支付订单出错: chargeRequest={}", jsonString);
            throw new BizException("生成支付订单出错");
        }
    }

    /**
     * 总长度32位=6位随机字符+_+25位业务方流水号
     * 微信out_trade_no最长32位
     *
     * @param orderNo 上游业务单号
     * @return 支付系统流水号
     */
    private String generateChargeOrderNo(String orderNo) {
        return RandomUtils.getRandomString(6) + "_" + orderNo;
    }

    /**
     * 微信现金红包-生成商户订单号
     * 生成规则：10位mchId + yyyyMMdd字符串 + Redis自增后的值，其值不足十位左位补0，构成的10位字符串
     * 每个订单号必须唯一，取值范围：0~9，a~z，A~Z，长度32
     * 接口根据商户订单号支持重入，如出现超时可再调用。
     *
     * @param mchId 微信支付分配的商户号
     * @return {@link String}
     */
    public String generateMchBillNo(String mchId) {
        String key = String.format("%s%s", RedisKeyFactory.K007.toString(), LocalDate.now().toString());
        Long incrValue = redisAtomicClient.incrBy(key, 1, DateUtils.getToTomorrowSeconds(), TimeUnit.SECONDS);
        String code = StringUtils.leftPad(String.valueOf(incrValue), 10, "0");
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return MessageFormat.format("{0}{1}{2}", mchId, dateStr, code);
    }

    /**
     * 获取分布式锁key
     *
     * @param bizType    活动类型
     * @param bizId      活动id
     * @param bizOrderNo 业务订单号
     * @return {@link String}
     */
    public String getLockKey(Integer bizType, String bizId, String bizOrderNo) {
        return String.format("%s%d_%s_%s", RedisKeyFactory.K008.toString(), bizType, bizId, bizOrderNo);
    }

    @Override
    public WxCouponDetailDto findWxCouponDetail(String wxAppId, String couponId, String openId) throws BizException {
        if (StringUtils.isBlank(couponId) || StringUtils.isBlank(openId)) {
            throw new BizException("参数不能为空");
        }
        return BeanUtils.copy(wxPayService.findWxCouponDetail(wxAppId, couponId, openId, fujianDuibaWxpayConfig.getWxPayConfigDto()), WxCouponDetailDto.class);
    }

    @Override
    public WxCouponDetailDto findWxCouponDetailBySubject(String wxAppId, String couponId, String openId, String subjectCode) throws BizException {
        if (StringUtils.isBlank(couponId) || StringUtils.isBlank(openId)) {
            throw new BizException("参数不能为空");
        }
        WxPayConfigDto wxPayConfigDto = wxCouponSubjectContainer.get(subjectCode);
        return BeanUtils.copy(wxPayService.findWxCouponDetail(wxAppId, couponId, openId, wxPayConfigDto), WxCouponDetailDto.class);
    }



}
