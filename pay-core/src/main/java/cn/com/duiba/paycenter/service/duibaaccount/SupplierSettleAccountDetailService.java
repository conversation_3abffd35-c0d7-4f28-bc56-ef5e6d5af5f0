package cn.com.duiba.paycenter.service.duibaaccount;

import cn.com.duiba.paycenter.dto.duibaaccount.SupplierAccountDetailPageDto;
import cn.com.duiba.paycenter.entity.duibaaccount.SupplierSettleAccountDetailEntity;
import cn.com.duiba.paycenter.params.duibaaccount.SupplierAccountDetailQryParams;

import java.util.List;

/**
 * 供应商待结算账户service
 * author z<PERSON><PERSON><PERSON>
 * date 2018-12-13-14:21
 */
public interface SupplierSettleAccountDetailService {

    /**
     * 分页查询
     * @param params
     * @return
     */
    List<SupplierAccountDetailPageDto> find4Page(SupplierAccountDetailQryParams params);

    /**
     * 分页查询总数
     * @param params
     * @return
     */
    Integer count4page(SupplierAccountDetailQryParams params);

    /**
     * 根据主键和SupplierId查询
     * @param id
     * @return
     */
    SupplierSettleAccountDetailEntity findByIdAndSupplierId(Long id, Long supplierId);

    /**
     * 根据业务ID和业务类型查询当前供应商账户明细
     * @param relationId
     * @param relationTypes
     * @param supplierId
     * @return
     */
    List<SupplierSettleAccountDetailEntity> findByRelationAndSupplierId(String relationId, List<Integer> relationTypes, Long supplierId);
}
