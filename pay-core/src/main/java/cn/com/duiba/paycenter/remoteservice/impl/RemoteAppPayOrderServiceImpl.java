package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.bo.AppPayOrderBo;
import cn.com.duiba.paycenter.constant.PayCenterErrorCode;
import cn.com.duiba.paycenter.remoteservice.RemoteAppPayOrderService;
import cn.com.duiba.paycenter.result.PayCenterResult;
import cn.com.duiba.paycenter.util.SignUtil;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * author z<PERSON><PERSON><PERSON>
 * date 2018/11/14-11:45 AM
 */
@RestController
public class RemoteAppPayOrderServiceImpl implements RemoteAppPayOrderService {

    private static final String DEVELOPER_ID = "developerId";
    private static final String RELATION_ID = "relationId";
    private static final String MONEY = "money";
    private static final String APP_ID = "appId";

    @Resource
    private AppPayOrderBo appPayOrderBo;

    @Override
    public PayCenterResult orderSettle(Long orderId, <PERSON> duiba<PERSON>oney, Long devMoney, String memo, String sign) {
        Map<String, String> params=new HashMap<>();
        params.put("orderId", Objects.toString(orderId));
        params.put("duibaMoney", Objects.toString(duibaMoney));
        params.put("devMoney", Objects.toString(devMoney));

        String checkSign= SignUtil.sign(params);

        if(!checkSign.equals(sign)){
            return new PayCenterResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        return appPayOrderBo.orderSettle(orderId, duibaMoney, devMoney, memo);
    }

    @Override
    public PayCenterResult purchasePayback(Long developerId, Long appId, Long money, String relationId, String memo, String sign) {
        Map<String, String> params=new HashMap<>();
        params.put(DEVELOPER_ID, Objects.toString(developerId));
        params.put(RELATION_ID, relationId);
        params.put(MONEY, Objects.toString(money));
        params.put(APP_ID,Objects.toString(appId));
        String checkSign= SignUtil.sign(params);

        if(!checkSign.equals(sign)){
            return new PayCenterResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        return appPayOrderBo.purchasePayback(developerId, appId, money, relationId, memo);
    }

}
