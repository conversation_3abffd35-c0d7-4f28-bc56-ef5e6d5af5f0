package cn.com.duiba.paycenter.remoteservice.impl;

import cn.com.duiba.paycenter.biz.SaasAgentAccountPayChargeBiz;
import cn.com.duiba.paycenter.constant.PayCenterErrorCode;
import cn.com.duiba.paycenter.params.PayChargeExtraParams;
import cn.com.duiba.paycenter.remoteservice.RemoteSaasAgentAccountPayChargeService;
import cn.com.duiba.paycenter.result.PayChargeResult;
import cn.com.duiba.paycenter.util.SignUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/5/23 09:37
 */
@RestController
public class RemoteSaasAgentAccountPayChargeServiceImpl implements RemoteSaasAgentAccountPayChargeService {

    private static final Logger logger = LoggerFactory.getLogger(RemoteSaasAgentAccountPayChargeServiceImpl.class);

    @Autowired
    private SaasAgentAccountPayChargeBiz saasAgentAccountPayChargeBiz;

    @Override
    public PayChargeResult chargeMoneyByManual(Long agentId, Long manualApplyId, Long money, String sign, PayChargeExtraParams p) {
        logger.info("{}.chargeMoneyByManual({},{},{},{})", getClass().getName(), agentId, manualApplyId, money, sign);
        if (!checkSign(agentId, manualApplyId, money, sign)) {
            return new PayChargeResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        return saasAgentAccountPayChargeBiz.chargeMoneyByManual(agentId, manualApplyId, money, p);
    }

    @Override
    public PayChargeResult reduceMoneyByManual(Long agentId, Long manualApplyId, Long money, String sign, PayChargeExtraParams p) {
        logger.info("{}.chargeMoneyByManual({},{},{},{})", getClass().getName(), agentId, manualApplyId, money, sign);
        if (!checkSign(agentId, manualApplyId, money, sign)) {
            return new PayChargeResult(false, PayCenterErrorCode.CodeParamsSignError, null);
        }
        return saasAgentAccountPayChargeBiz.reduceMoneyByManual(agentId, manualApplyId, money, p);
    }

    private boolean checkSign(Long agentId, Long manualApplyId, Long money, String sign) {
        Map<String, String> params = new HashMap<>();
        params.put("agentId", agentId + "");
        params.put("manualApplyId", manualApplyId + "");
        params.put("money", money + "");
        String checksign = SignUtil.sign(params);
        return checksign.equals(sign);
    }
}
