package cn.com.duiba.paycenter.remoteservice.impl.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayChargeNotifyResponse;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteAlipayNotifyService;
import cn.com.duiba.paycenter.service.payment.AlipayNotifyService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/12/11
 */
@RestController
public class RemoteAlipayNotifyServiceImpl implements RemoteAlipayNotifyService {
    @Resource
    private AlipayNotifyService alipayNotifyService;

    @Override
    public AlipayChargeNotifyResponse orderNotify(Map<String, String> params) throws BizException {
        return alipayNotifyService.orderNotify(params);
    }

    @Override
    public AlipayChargeNotifyResponse orderNotifyBySubjectType(Map<String, String> params, String subjectType) throws BizException {
        return alipayNotifyService.orderNotifyBySubjectType(params, subjectType);
    }
}
