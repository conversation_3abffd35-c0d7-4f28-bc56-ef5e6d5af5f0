package cn.com.duiba.paycenter.service.payment;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.paycenter.dto.payment.charge.WxCouponResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.coupon.WxCouponQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.coupon.WxCouponQueryResp;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.coupon.WxJsSessionResp;
import cn.com.duiba.paycenter.dto.payment.config.WxPayConfigDto;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponNotifyUrlSetResponse;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponUserFlowDetail;
import cn.com.duiba.paycenter.entity.payment.wxpay.notify.WxPayOrderNotifyResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayAuthcode2OpenidRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayCloseOrderRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayOrderQueryRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayRedPacketQueryXmlRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayRedPacketSendXmlRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayRefundRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayUnifiedOrderCouponRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxPayUnifiedOrderRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxTransferPayQueryXmlRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.request.WxTransferPayXmlRequest;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxCouponDetailResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPayCloseOrderResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPayOrderQueryResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPayRedPacketQueryXmlResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPayRedPacketSendXmlResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPayRefundResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPayUnifiedOrderResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxPlatformCert;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxTransferPayQueryXmlResult;
import cn.com.duiba.paycenter.entity.payment.wxpay.result.WxTransferPaySendXmlResult;
import com.wechat.pay.java.service.refund.model.CreateRequest;
import com.wechat.pay.java.service.refund.model.Refund;

import java.util.List;

/**
 * 微信支付接口
 *
 * <AUTHOR>
 * @date 2018/11/15
 */
public interface WxPayService {

    /**
     * https://pay.weixin.qq.com/wiki/doc/api/H5.php?chapter=9_3&index=3
     * 关单
     *
     * @param request
     * @param apiKey
     * @return
     */
    WxPayCloseOrderResult closeOrder(WxPayCloseOrderRequest request, String apiKey) throws BizException;

    /**
     * 发送get请求并同步返回string response
     *
     * @param url get request
     * @return get response
     * @throws BizException bizException
     */
    String sendGetRequest(String url) throws BizException;

    /**
     * 统一下单(详见https://pay.weixin.qq.com/wiki/doc/api/app/app.php?chapter=9_1)
     * 在发起微信支付前，需要调用统一下单接口，获取"预支付交易会话标识"
     * 接口地址：https://api.mch.weixin.qq.com/pay/unifiedorder
     *
     * @param request 请求对象
     * @param apiKey  公众号的apiKey
     * @return the wx pay unified order result
     * @throws BizException bizException
     */
    WxPayUnifiedOrderResult unifiedOrder(WxPayUnifiedOrderRequest request, String apiKey) throws BizException;

    /**
     * 授权码查询OPENID接口.
     * 通过授权码查询公众号Openid，调用查询后，该授权码只能由此商户号发起扣款，直至授权码更新。
     * 文档地址：
     * https://pay.weixin.qq.com/wiki/doc/api/micropay.php?chapter=9_13&index=9
     * 接口链接:
     * https://api.mch.weixin.qq.com/tools/authcodetoopenid
     *
     * @param request 请求对象
     * @param apiKey  公众号的apiKey
     * @return openid string
     * @throws BizException bizException
     */
    String authcode2Openid(WxPayAuthcode2OpenidRequest request, String apiKey) throws BizException;

    /**
     * 查询订单（适合于需要自定义子商户号和子商户appid的情形）.
     * 详见https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=9_2
     * 该接口提供所有微信支付订单的查询，商户可以通过查询订单接口主动查询订单状态，完成下一步的业务逻辑。
     * 需要调用查询接口的情况：
     * 1 当商户后台、网络、服务器等出现异常，商户系统最终未接收到支付通知；
     * 2 调用支付接口后，返回系统错误或未知交易状态情况；
     * 3 调用被扫支付API，返回USERPAYING的状态；
     * 4 调用关单或撤销接口API之前，需确认支付状态；
     * 接口地址：https://api.mch.weixin.qq.com/pay/orderquery
     *
     * @param request 查询订单请求对象
     * @param apiKey  公众号的apiKey
     * @return the wx pay order query result
     * @throws BizException bizException
     */
    WxPayOrderQueryResult queryOrder(WxPayOrderQueryRequest request, String apiKey) throws BizException;

    /**
     * 解析支付结果通知.
     * 本接口没有做验证签名，需要调用方自行获取apiKey校验
     * 详见https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=9_7
     *
     * @param xmlData the xml data
     * @return the wx pay order notify result
     * @throws BizException bizException
     */
    WxPayOrderNotifyResult parseOrderNotifyResult(String xmlData) throws BizException;

    /**
     * 微信支付-申请退款.
     * 详见 https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=9_4
     * 接口链接：https://api.mch.weixin.qq.com/secapi/pay/refund
     *
     * @param wxPayRefundRequest 请求对象
     * @param apiKey             公众号apiKey
     * @param apiCert            公众号api证书
     * @return 退款对象
     * @throws BizException bizException
     */
    WxPayRefundResult refund(WxPayRefundRequest wxPayRefundRequest, String apiKey, String apiCert) throws BizException;

    /**
     * 微信支付-申请退款.
     * 详见 https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=9_4
     * 接口链接：https://api.mch.weixin.qq.com/secapi/pay/refund
     *
     * @param wxPayRefundRequest 请求对象
     * @param apiKey             公众号apiKey
     * @param apiCert            公众号api证书
     * @param subjectType        业务主体标识
     * @return 退款对象
     * @throws BizException bizException
     */
    WxPayRefundResult refundBySubjectType(WxPayRefundRequest wxPayRefundRequest, String apiKey, String apiCert, String subjectType) throws BizException;



    /**
     * 微信支付-申请退款 通用 APIV3.
     * 详见 https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=9_4
     * 接口链接：https://api.mch.weixin.qq.com/v3/refund/domestic/refunds
     *
     * @param createRequest 请求对象
     * @return 退款对象
     * @throws BizException bizException
     */
    Refund refundBySubjectTypeByAPIV3(CreateRequest createRequest) throws BizException;

    /**
     * 发送微信红包
     *
     * @param request     请求对象
     * @param apiKey      公众号apiKey
     * @param apiCert     公众号api证书
     * @param subjectType 业务主体标识
     * @return {@link WxPayRedPacketSendXmlResult}
     * @throws BizException 业务异常
     * @see https://pay.weixin.qq.com/wiki/doc/api/tools/cash_coupon.php?chapter=13_4&index=3
     */
    WxPayRedPacketSendXmlResult sendWxRedPacket(WxPayRedPacketSendXmlRequest request, String apiKey, String apiCert, String subjectType) throws BizException;

    /**
     * 微信红包订单信息查询
     *
     * @param request     请求对象
     * @param apiKey      公众号apiKey
     * @param apiCert     公众号api证书
     * @param subjectType 业务主体标识
     * @return {@link WxPayRedPacketSendXmlResult}
     * @throws BizException 业务异常
     * @see https://pay.weixin.qq.com/wiki/doc/api/tools/cash_coupon.php?chapter=13_4&index=3
     */
    WxPayRedPacketQueryXmlResult queryWxRedPacketInfo(WxPayRedPacketQueryXmlRequest request, String apiKey, String apiCert, String subjectType) throws BizException;


    /**
     * 发送微信优惠券
     *
     * @param wxPayConfigDto 微信证书相关信息
     * @param body           请求体
     * @param openId         发券商户下对应的openid
     * @return
     * @throws BizException
     * @see https://pay.weixin.qq.com/wiki/doc/apiv3/wxpay/marketing/convention/chapter3_2.shtml
     */
    WxCouponResponse sendWxCoupon(WxPayConfigDto wxPayConfigDto, String body, String openId) throws BizException;

    /**
     * 微信企业转账
     *
     * @param request     请求对象
     * @param apiKey      公众号apiKey
     * @param apiCert     公众号api证书
     * @param subjectType 业务主体标识
     * @return {@link WxTransferPaySendXmlResult}
     * @throws BizException 业务异常
     * @see https://pay.weixin.qq.com/wiki/doc/api/tools/mch_pay.php?chapter=14_2
     */
    WxTransferPaySendXmlResult sendWxTransfer(WxTransferPayXmlRequest request, String apiKey, String apiCert, String subjectType) throws BizException;

    /**
     * 微信企业转账查询
     *
     * @param request     请求对象
     * @param apiKey      公众号apiKey
     * @param apiCert     公众号api证书
     * @param subjectType 业务主体标识
     * @return {@link WxTransferPayQueryXmlResult}
     * @throws BizException 业务异常
     * @see https://pay.weixin.qq.com/wiki/doc/api/tools/mch_pay.php?chapter=14_2
     */
    WxTransferPayQueryXmlResult queryWxTransfer(WxTransferPayQueryXmlRequest request, String apiKey, String apiCert, String subjectType) throws BizException;

    /**
     * 兑吧直播定制
     * @param buildUnifiedOrderRequest
     * @param apiKey
     * @return
     */
    WxPayUnifiedOrderResult unifiedOrderDuibaLive(WxPayUnifiedOrderCouponRequest buildUnifiedOrderRequest, String apiKey) throws BizException;

    /**
     * 查询代金券详情
     * @param couponId
     * @param openId
     * @return
     */
    WxCouponDetailResult findWxCouponDetail(String wxAppId, String couponId, String openId,WxPayConfigDto wxPayConfigDto) throws BizException;

    /**
     * 获取微信平台证书
     *
     * @throws BizException
     */
    List<WxPlatformCert> getWxPlatformCert(WxPayConfigDto wxPayConfigDto);

    /**
     * 设置立减金核销通知地址
     * @param notifyUrl 通知地址
     * @param wxPayConfigDto 配置
     * @return
     * @throws BizException
     */
    WxCouponNotifyUrlSetResponse setNotifyUrl( String notifyUrl, WxPayConfigDto wxPayConfigDto)  throws BizException;


    /**
     * 查询微信批次信息
     *
     * @param couponQueryRequest 息查询请求
     * @param wxPayConfigDto     wx支付配置dto
     * @return {@link WxCouponQueryResp}
     */
    WxCouponQueryResp getWechatStocksInfo(WxCouponQueryRequest couponQueryRequest, WxPayConfigDto wxPayConfigDto);

    /**
     * 小程序授权
     *
     * @param authCode      身份验证代码
     * @param wxPayConfigDto wx支付配置dto
     * @return {@link String}
     */
    WxJsSessionResp authApplets(String authCode, WxPayConfigDto wxPayConfigDto);

    /**
     * 微信立减金批次核销明细下载链接
     * @param stockId
     * @param wxPayConfigDto
     * @return
     */
    void getWxCouponUserFlowDownloadUrl(String stockId, WxPayConfigDto wxPayConfigDto) throws BizException;
}
