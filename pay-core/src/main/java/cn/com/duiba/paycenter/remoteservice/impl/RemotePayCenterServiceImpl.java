package cn.com.duiba.paycenter.remoteservice.impl;


import cn.com.duiba.paycenter.constant.ActionTypes;
import cn.com.duiba.paycenter.constant.RelationTypes;
import cn.com.duiba.paycenter.constant.SettleStatusEnum;
import cn.com.duiba.paycenter.dao.AccountChangeRecordDAO;
import cn.com.duiba.paycenter.dao.DuibaAccountChangeRecordDAO;
import cn.com.duiba.paycenter.dao.DuibaRemainingMoneyDao;
import cn.com.duiba.paycenter.dao.RemainingMoneyDao;
import cn.com.duiba.paycenter.entity.DuibaRemainingMoneyEntity;
import cn.com.duiba.paycenter.entity.RemainingMoneyEntity;
import cn.com.duiba.paycenter.model.AccountChangeRecordDO;
import cn.com.duiba.paycenter.model.DuibaAccountChangeRecordDO;
import cn.com.duiba.paycenter.service.PayCenterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class RemotePayCenterServiceImpl implements PayCenterService {
	
	private static Logger log=LoggerFactory.getLogger(RemotePayCenterServiceImpl.class);
	@Autowired
	private RemainingMoneyDao remainingMoneyDAO;
	@Autowired
	private AccountChangeRecordDAO accountChangeRecordDAO;
	@Autowired
	private DuibaAccountChangeRecordDAO duibaAccountChangeRecordDAO;
	@Autowired
	private DuibaRemainingMoneyDao duibaRemainingMoneyDAO;

	@Override
	public Long getBalance(Long developerId){
		log.debug(getClass().getName()+".getBalance("+developerId+")");
		RemainingMoneyEntity rm=remainingMoneyDAO.findByDeveloperId(developerId);
		return rm.getMoney().longValue();
	}
	
	@Override
	public Long getDuibaBalance() {
		log.debug(getClass().getName()+".getDuibaBalance()");
		DuibaRemainingMoneyEntity drm=duibaRemainingMoneyDAO.findRecord();
		return drm.getMoney();
	}

	@Override
	public List<AccountChangeRecordDO> find(String relationType,
			Long relationId) {
		log.debug(getClass().getName()+".find("+relationType+","+relationId+")");
		return accountChangeRecordDAO.findAllByRelation(relationType, relationId);
	}
	
	@Override
	public List<DuibaAccountChangeRecordDO> findDuibaRecords(
			String relationType, Long relationId) {
		log.debug(getClass().getName()+".findDuibaRecords("+relationType+","+relationId+")");
		return duibaAccountChangeRecordDAO.findAllByRelation(relationType, relationId);
	}
	@Override
	public List<AccountChangeRecordDO> findAllGreaterId(Long lastId,
			int batchSize) {
		log.debug(getClass().getName()+".findAllGreaterId("+lastId+","+batchSize+")");
		return accountChangeRecordDAO.findAllGreaterId(lastId, batchSize);
	}

	@Override
	public List<DuibaAccountChangeRecordDO> findAllDuibaGreaterId(Long lastId,
			int batchSize) {
		log.debug(getClass().getName()+".findAllDuibaGreaterId("+lastId+","+batchSize+")");
		return duibaAccountChangeRecordDAO.findAllGreaterId(lastId, batchSize);
	}

	@Override
	public Boolean successPayRecord(Long orderId){
		//更新 扣费记录字段 settle_status 为 成功状态
		AccountChangeRecordDO recordDO = accountChangeRecordDAO.findExistRecord(RelationTypes.RTOrders,orderId, ActionTypes.OrdersActionType.PayOrder.getKey());
		if(recordDO != null && recordDO.getId() != null){
			int result = accountChangeRecordDAO.updateSettleStatus(recordDO.getId(), SettleStatusEnum.SETTLED_STATUS.getStatus());
			if(result < 1){
				log.warn("开发者结算成功后，更新 扣费记录字段 settle_status 失败，orderId-{}",orderId);
				return false;
			}
			return true;
		}
		log.warn("开发者结算成功后，更新 扣费记录字段 settle_status 失败，原因：未查询到 支付记录，orderId-{}",orderId);
		return false;
	}
}
