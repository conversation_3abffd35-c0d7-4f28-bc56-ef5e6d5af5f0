package cn.com.duiba;

import cn.com.duiba.wolf.utils.ClassUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableDuibaFeignClients;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;

@Configuration
@ImportResource({"classpath:/spring-config.xml"})
@SpringBootApplication(exclude = {DataSourceTransactionManagerAutoConfiguration.class, DataSourceAutoConfiguration.class})
@EnableDiscoveryClient
@EnableCircuitBreaker
@EnableDuibaFeignClients
public class Application extends SpringApplication {

    public static void main(final String[] args) throws Exception {

		ClassUtils.checkSerializable("cn.com.duiba.paycenter.dto","cn.com.duiba.paycenter.model","cn.com.duiba.paycenter.params");

		SpringApplication sa=new SpringApplication(Application.class);
		sa.run(args);
    }

}
