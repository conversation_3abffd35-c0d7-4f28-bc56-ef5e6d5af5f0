package cn.com.duiba.paycenter.dao.payment;

import cn.com.duiba.paycenter.TransactionalTestCaseBase;
import cn.com.duiba.paycenter.dao.DsConstants;
import cn.com.duiba.paycenter.entity.payment.ChannelEntity;
import cn.com.duiba.paycenter.enums.ChannelEnum;
import cn.com.duiba.paycenter.enums.ChannelModeEnum;
import cn.com.duiba.paycenter.enums.ChannelStatusEnum;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 * @date 2018/11/06
 */
@Transactional(DsConstants.DATABASE_PAYMENT)
public class ChannelDaoTest extends TransactionalTestCaseBase {
    @Autowired
    private ChannelDao channelDao;
    private ChannelEntity channelEntity;
    private static final Long APP_ID = 999L;

    @Before
    public void setup() {
        channelEntity = new ChannelEntity();

        channelEntity.setAppId(APP_ID);
        channelEntity.setChannelType(ChannelEnum.ALIPAY.getChannelType());
        channelEntity.setChannelName(ChannelEnum.ALIPAY.getChannelName());
        channelEntity.setChannelStatus(ChannelStatusEnum.DISABLE.getCode());
        channelEntity.setChannelMode(ChannelModeEnum.DUIBA.getCode());
        channelEntity.setDescription("description");
        channelEntity.setScheme("wx://lalala");
        channelEntity.setFeeRate(20);
        channelEntity.setParam("");

        channelDao.insert(channelEntity);
    }

    @Test
    public void shouldInsert() {
        assertThat(channelEntity.getId()).isNotNull().isGreaterThan(0);
    }

    @Test
    public void shouldUpdate() {
        ChannelEntity entity = new ChannelEntity();
        entity.setId(channelEntity.getId());
        entity.setParam("{}");
        entity.setScheme("alipay://");
        entity.setChannelMode(ChannelModeEnum.INDEPENDENT.getCode());
        entity.setChannelStatus(ChannelStatusEnum.ENABLE.getCode());

        assertThat(channelDao.update(entity)).isGreaterThan(0);
    }

    @Test
    public void shouldFindByAppIdAndChannelId() {
        ChannelEntity entity = channelDao.findByAppIdAndChannelType(APP_ID, ChannelEnum.ALIPAY.getChannelType());
        assertThat(entity).isNotNull();
        assertThat(entity.getAppId()).isEqualTo(APP_ID);
        assertThat(entity.getChannelType()).isEqualTo(ChannelEnum.ALIPAY.getChannelType());
    }

    @Test
    public void shouldListByAppId() {
        List<ChannelEntity> list = channelDao.listByAppId(APP_ID);
        assertThat(list).isNotNull().hasSize(1);
    }
}
