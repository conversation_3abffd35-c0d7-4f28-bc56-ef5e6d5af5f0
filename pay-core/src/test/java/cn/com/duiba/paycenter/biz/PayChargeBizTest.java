package cn.com.duiba.paycenter.biz;

import static org.junit.Assert.assertEquals;
import junit.BaseJunit4Test;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import cn.com.duiba.paycenter.entity.RemainingMoneyEntity;
import cn.com.duiba.paycenter.params.PayChargeExtraParams;
import cn.com.duiba.paycenter.result.PayChargeResult;

public class PayChargeBizTest extends BaseJunit4Test {
	@Autowired
	private PayChargeBiz payChargeBiz;
	@Test
	public void testChargeMoneyByManual() throws Exception {
		
		Long developerId=addEnoughMoney();
		Long manualApplyId=123L;
		Long money=100L;
		
		PayChargeExtraParams p=new PayChargeExtraParams();
		p.setAdminId(12L);
		p.setMemo("test");
		
		RemainingMoneyEntity beforerm=remainingMoneyDAO.findByDeveloperId(developerId);
		
		PayChargeResult result=payChargeBiz.chargeMoneyByManual(developerId, manualApplyId, money, p);
		assertEquals(true, result.isBizSuccess());
		
		RemainingMoneyEntity rm=remainingMoneyDAO.findByDeveloperId(developerId);
		assertEquals(beforerm.getMoney()+money, rm.getMoney().intValue());
	}

	@Test
	public void testChargeMoneyByOnline() throws Exception {
		Long developerId=addEnoughMoney();
		Long manualApplyId=123L;
		Long money=100L;
		
		PayChargeExtraParams p=new PayChargeExtraParams();
		p.setAdminId(12L);
		p.setMemo("test");
		
		RemainingMoneyEntity beforerm=remainingMoneyDAO.findByDeveloperId(developerId);
		
		PayChargeResult result=payChargeBiz.chargeMoneyByOnline(developerId, manualApplyId, money, p);
		assertEquals(true, result.isBizSuccess());
		
		RemainingMoneyEntity rm=remainingMoneyDAO.findByDeveloperId(developerId);
		assertEquals(beforerm.getMoney()+money, rm.getMoney().intValue());
	}

	@Test
	public void testReduceMoneyByManual() throws Exception {
		Long developerId=addEnoughMoney();
		Long manualApplyId=123L;
		Long money=100L;
		
		PayChargeExtraParams p=new PayChargeExtraParams();
		p.setAdminId(12L);
		p.setMemo("test");
		
		RemainingMoneyEntity beforerm=remainingMoneyDAO.findByDeveloperId(developerId);
		
		PayChargeResult result=payChargeBiz.reduceMoneyByManual(developerId, manualApplyId, money, p);
		assertEquals(true, result.isBizSuccess());
		
		RemainingMoneyEntity rm=remainingMoneyDAO.findByDeveloperId(developerId);
		assertEquals(beforerm.getMoney()-money, rm.getMoney().intValue());
		
		result=payChargeBiz.reduceMoneyByManual(developerId, manualApplyId, money, p);
		assertEquals(true, result.isBizSuccess());
		assertEquals(false, result.isCurrentSuccess());
		
		rm=remainingMoneyDAO.findByDeveloperId(developerId);
		assertEquals(beforerm.getMoney()-money, rm.getMoney().intValue());
	}

}
