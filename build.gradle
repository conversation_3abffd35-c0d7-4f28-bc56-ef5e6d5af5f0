buildscript {
	ext["duibaExtVersion"] = "2.0.0-h80"
	ext["springBootVersion"] = "2.2.7.RELEASE"
	ext["springCloudVersion"] = "Hoxton.SR6"
	ext["hazelcast.version"] = "3.11"
	ext["guava.version"] = "31.1-jre"
	ext["lombok.version"] = "1.18.12"

	repositories {
		maven { url "https://nexus.dui88.com/nexus/content/groups/public/" }
		maven { url "https://plugins.gradle.org/m2/" } //sonarqube
		maven { url "http://repo.spring.io/plugins-release" }
		maven { url "http://maven.aliyun.com/nexus/content/groups/public/"}
		mavenCentral()
		mavenLocal()
	}
	dependencies {
		classpath "io.spring.gradle:dependency-management-plugin:1.0.5.RELEASE"
		classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:2.5" //sonarqube
		classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
		classpath "org.springframework.build.gradle:propdeps-plugin:0.0.7"
	}
}

apply plugin: "org.sonarqube"
sonarqube {
	properties {
		property "sonar.projectName", "paycenter-service"
		property "sonar.projectKey", "cn.com.duiba:paycenter-service"
		property "sonar.host.url","http://sonar.dui88.com"
		property "sonar.sourceEncoding", "utf-8"
	}
}

allprojects {
	apply plugin: "maven"
	apply plugin: "java"
	apply plugin: "idea"
	apply plugin: "eclipse"
	apply plugin: "jacoco"
	apply plugin: "io.spring.dependency-management"
	apply plugin: "propdeps"
	test {
		ignoreFailures = true
	}
	group = "cn.com.duiba"
	version = "3.5.31"
}

subprojects{
    [compileJava,compileTestJava,javadoc]*.options*.encoding = "UTF-8"

	sourceCompatibility = 1.8
	targetCompatibility = 1.8

	configurations{
		all*.exclude group: "log4j", module:"log4j"
		all*.exclude group: "org.slf4j", module: "slf4j-log4j12"
		all*.exclude group: "javax.servlet", module: "servlet-api" //servlet 2.5
		all*.exclude group: "com.fasterxml.jackson.dataformat", module: "jackson-dataformat-xml"
		all*.exclude group: "com.alibaba", module: "dubbo"
		all*.exclude group: "com.zaxxer", module: "HikariCP-java7"
		all*.exclude group: "org.apache.logging.log4j"
	}
	
	dependencyManagement {
		dependencies {
			imports {
				mavenBom "cn.com.duiba.boot:spring-boot-ext-dependencies:${duibaExtVersion}"
				mavenBom "org.springframework.boot:spring-boot-dependencies:${springBootVersion}"
				mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
			}
			/*dependency "cn.com.duiba.boot:spring-boot-starter-rocketmq:1.2.218"
			dependency "cn.com.duiba.boot:spring-boot-starter-redis:1.2.218"*/
			dependency "cn.com.duiba:biz-tool:1.6.1"
			dependency "org.aspectj:aspectjrt:1.8.9"
			dependency "org.aspectj:aspectjweaver:1.8.9"
			dependency "cn.com.duiba:dcommons-base:0.3"
			dependency("org.apache.zookeeper:zookeeper:3.4.8")
			dependency("com.github.sgroschupf:zkclient:0.1")
			dependency "org.hibernate.validator:hibernate-validator:6.0.2.Final"
			dependency "com.alipay.sdk:alipay-sdk-java:4.19.0.ALL"
			/*dependency("org.mybatis:mybatis:3.4.1")
			dependency("org.mybatis:mybatis-spring:1.3.0")*/
			dependency("org.apache.zookeeper:zookeeper:3.4.8")
			dependency("com.github.sgroschupf:zkclient:0.1")
			dependency("com.thoughtworks.xstream:xstream:********")
			dependency("cn.com.duiba:duiba-api:1.0.5")
			//南京中信银行支付
			dependency("org.bouncycastle:bcprov-jdk14:1.45")
			dependency("com.citicbank:cbframework.base:1.0")
			dependency("com.citic:openbank-sdk:1.0")
			dependency("com.citicbank:cfca.sadk:*******")
			dependency("com.citicbank:bcmail145:1.0.0")
			dependency("com.citicbank:bcprov145:1.0.0")
			dependency("com.citicbank:CNCBCryptoPkg:1.0.0")
//			dependency('com.github.wechatpay-apiv3:wechatpay-java:0.2.11')
			dependency 'com.wechat:apiv3:0.2.11'
			dependency 'com.wechat:apiv3-service:0.2.11'
			dependency "com.squareup.okhttp3:okhttp:4.10.0"

			//建行支付签名使用
			dependency("cn.com.duiba:netpay:1.0")
			dependency "com.alibaba:fastjson:1.2.70"
			//农行支付  
			dependency("com.abc:TrustPayClient:3.1.6")
            dependency("org.kie.modules:org-apache-commons-httpclient:6.1.0.Beta1")
			dependency("cn.com.duiba.credits:duiba-java-sdk:0.0.23")
			dependency("cn.com.duiba:mysql-generator:0.2.0") // http://gitlab2.dui88.com/sunchangji/mysql-generator
			dependency("com.icbc:hsm-software-share:1.0.3")
			dependency("com.icbc:icbc-api-sdk-cop:1.0")
			dependency("com.icbc:icbc-api-sdk-cop-io:1.0")
			dependency("com.icbc:icbc-ca:1.0")
			dependency("com.icbc:InfosecCrypto:1.0")
			dependency("com.icbc:proguard:1.0")
			dependency("cn.hutool:hutool-all:5.6.5")
			//兑吧直播业务用api
			dependency("cn.com.duiba:duiba-live-wechat-service-api:0.0.27")
			//工商银行支付sdk
			dependency("com.icbc:api-client-internet:1.0")
			//中台短信依赖包
			dependency "cn.com.duiba:message-service-api:0.1.25"
			dependency("cn.com.duiba:duiba-live-supplier-center-api:0.0.4")
			dependency("cn.com.duiba:wolf:2.2.48")
			dependency ('org.eclipse.persistence:org.eclipse.persistence.moxy:2.7.10')

			//宁波银行支付api
			dependency("cn.com.nbcb:pluto-open-sdk:2.0.0")
			dependency("cn.com.nbcb:bcprov-jdk16:1.45")
			dependency("cn.com.nbcb:commons-codec:1.6")
//			dependency "org.apache.curator:curator-client:4.2.0"
//			dependency "org.apache.curator:curator-framework:4.2.0"
//			dependency "org.apache.curator:curator-recipes:4.2.0"
//			dependency "org.apache.curator:curator-x-discovery:4.2.0"

			dependency('cn.com.duiba.developer-center:developer-center-api:3.11.5')
		}
	}
	
    repositories {
		maven { url "http://nexus.dui88.com:8081/nexus/content/groups/public/" }
		mavenCentral()
		mavenLocal()
    }

	uploadArchives {
		repositories {
			mavenDeployer {
				repository(url: "https://nexus.dui88.com/nexus/content/repositories/releases/"){
					authentication(userName: "admin", password: "admin123")
				}
				snapshotRepository(url: "https://nexus.dui88.com/nexus/content/repositories/snapshots/") {
					authentication(userName: "admin", password: "admin123")
				}

				pom.project {
					name project.name
					packaging "jar"
					description "paycenter client"
					url "www.duiba.com.cn"

					scm {
						url "scm:*****************************:xuhengfei/paycenter-service.git"
						connection "scm:*****************************:xuhengfei/paycenter-service.git"
						developerConnection "*****************************:xuhengfei/paycenter-service.git"
					}

					licenses {
						license {
							name "No License"
							url "http://www.duiba.com.cn"
							distribution "repo"
						}
					}

					developers {
						developer {
							id "xuhengfei"
							name "Hengfei Xu"
						}
					}
				}
			}
		}
	}
	
	task sourcesJar(type: Jar, dependsOn: classes) {
		classifier = "sources"
		from sourceSets.main.allSource
	}

	artifacts {
		archives sourcesJar
	}

}

configurations.all{
	resolutionStrategy.cacheChangingModulesFor 24, "hours"
}
